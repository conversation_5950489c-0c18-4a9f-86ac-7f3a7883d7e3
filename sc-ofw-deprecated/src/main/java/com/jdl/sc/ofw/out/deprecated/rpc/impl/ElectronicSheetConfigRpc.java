package com.jdl.sc.ofw.out.deprecated.rpc.impl;

import com.jd.eclp.bbp.so.dto.CustomExtend;
import com.jd.eclp.bbp.so.service.SoPddWaybillApi;
import com.jd.eclp.bbp.so.service.dto.request.QuerySoPddWaybillActiveRequest;
import com.jd.eclp.bbp.so.service.dto.response.QuerySoPddWaybillActiveResponse;
import com.jd.eclp.core.ApiRequest;
import com.jd.eclp.core.ApiResponse;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetPlatform;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetRequest;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ElectronicSheetConfigRpc implements ElectronicSheetConfigAcl {

    @Resource
    private SoPddWaybillApi soPddWaybillApi;

    /**
     * 马甲电子面单配置异常话术
     */
    public static final String MJ_CONFIG_ERROR = "未获取到商家的电子面单配置信息，请联系商家配置。操作路径：商家工作台-配置管理-平台电子面单";

    @Override
    public ElectronicSheetInfo queryConfig(ElectronicSheetRequest electronicSheetRequest) {
        ElectronicSheetInfo electronicSheetInfo = queryConfigForPrint(electronicSheetRequest);
        if (StringUtils.isBlank(electronicSheetInfo.getAuthToken())) {
            log.error("未获取到商家的电子面单配置信息");
            throw new PauseException(PauseReasonEnum.TIK_TOK_TOKEN_EXCEPTION)
                    .withCustom(MJ_CONFIG_ERROR);
        }
        return electronicSheetInfo;
    }

    @Override
    public ElectronicSheetInfo queryConfigForPrint(ElectronicSheetRequest electronicSheetRequest) {
        ApiRequest<QuerySoPddWaybillActiveRequest> apiRequest = buildRequest(electronicSheetRequest);
        ApiResponse<QuerySoPddWaybillActiveResponse> waybillResponse = soPddWaybillApi.querySoPddWaybillActive(apiRequest);
        if (waybillResponse == null
                || waybillResponse.getCode() != 1
                || waybillResponse.getData() == null) {
            log.error("抖音打印时,未获取到商家的电子面单配置信息");
            throw new PauseException(PauseReasonEnum.TIK_TOK_TOKEN_EXCEPTION)
                    .withCustom("抖音打印时,未获取到商家的电子面单配置信息");
        }
        return buildResponse(waybillResponse);
    }

    @Override
    public ElectronicSheetInfo queryConfigInfo(ElectronicSheetRequest electronicSheetRequest) {
        ApiRequest<QuerySoPddWaybillActiveRequest> apiRequest = buildRequest(electronicSheetRequest);
        ApiResponse<QuerySoPddWaybillActiveResponse> waybillResponse = soPddWaybillApi.querySoPddWaybillActive(apiRequest);
        if (waybillResponse == null
                || waybillResponse.getCode() != 1
                || waybillResponse.getData() == null) {
            return null;
        }
        return buildResponse(waybillResponse);
    }

    private ElectronicSheetInfo buildResponse(ApiResponse<QuerySoPddWaybillActiveResponse> waybillResponse) {
        CustomExtend customExtend = waybillResponse.getData().getCustomExtend();

        return ElectronicSheetInfo.builder()
                .authToken(waybillResponse.getData().getAuthToken())
                .addrProvince(waybillResponse.getData().getAddrProvince())
                .addrCounty(waybillResponse.getData().getAddrCounty())
                .addrCity(waybillResponse.getData().getAddrCity())
                .addrTown(waybillResponse.getData().getAddrTown())
                .addrDetail(waybillResponse.getData().getAddrDetail())
                .senderName(waybillResponse.getData().getSenderName())
                .senderMobile(waybillResponse.getData().getSenderMobile())
                .standardTemplateUrl(waybillResponse.getData().getStandardTemplateUrl())
                .customTemplateUrl(waybillResponse.getData().getCustomTemplateUrl())
                .customerTemplateItemLs(build(waybillResponse.getData().getCustomerTemplateItemLs()))
                .expressCompanyCode(waybillResponse.getData().getExpressCompanyCode())
                .standardTemplateId(waybillResponse.getData().getStandardTemplateId())
                .customExtend(customExtend == null ? null :
                        ElectronicSheetInfo.CustomExtend.builder()
                                .appointedShopId(customExtend.getAppointedShopId())
                                .appointedShopNick(customExtend.getAppointedShopNick())
                                .expressProductCode(customExtend.getExpressProductCode())
                                .extData(customExtend.getExtData())
                                .netSiteCode(customExtend.getNetSiteCode())
                                .netSiteName(customExtend.getNetSiteName())
                                .platformAuthCode(customExtend.getPlatformAuthCode())
                                .platformClientId(customExtend.getPlatformClientId())
                                .platformClientSecret(customExtend.getPlatformClientSecret())
                                .platformSecretKey(customExtend.getPlatformSecretKey())
                                .platformSellerId(customExtend.getPlatformSellerId())
                                .sellerExpressAccount(customExtend.getSellerExpressAccount())
                                .settleAccount(customExtend.getSettleAccount())
                                .vipShopId(customExtend.getVipShopId())
                                .vendorCode(customExtend.getVendorCode())
                                .kdNetSiteCode(customExtend.getKdNetSiteCode())
                                .kyNetSiteCode(customExtend.getKyNetSiteCode())
                                .branchCode(customExtend.getBranchCode())
                                .brandCode(customExtend.getBrandCode())
                                .addressId(customExtend.getAddressId())
                                .build())
                .userId(waybillResponse.getData().getUserId())
                .decryptServCode(waybillResponse.getData().getDecryptServCode())
                .authCode(waybillResponse.getData().getAuthCode())
                .build();
    }


    private ApiRequest<QuerySoPddWaybillActiveRequest> buildRequest(ElectronicSheetRequest electronicSheetRequest) {
        ApiRequest<QuerySoPddWaybillActiveRequest> apiRequest = new ApiRequest<>();
        QuerySoPddWaybillActiveRequest querySoPddWaybillActiveRequest = new QuerySoPddWaybillActiveRequest();
        querySoPddWaybillActiveRequest.setDeptNo(electronicSheetRequest.getAccountNo());
        querySoPddWaybillActiveRequest.setShipperNo(electronicSheetRequest.getShipperNo());
        querySoPddWaybillActiveRequest.setWarehouseNo(electronicSheetRequest.getWarehouseNo());
        querySoPddWaybillActiveRequest.setShopNo(electronicSheetRequest.getShopNo());
        querySoPddWaybillActiveRequest.setPlatform(electronicSheetRequest.getPlatform().getCode());
        querySoPddWaybillActiveRequest.setPlatDirectConn(electronicSheetRequest.getPlatDirectConn());
        querySoPddWaybillActiveRequest.setMultiWarehouse(electronicSheetRequest.isMultiWarehouse());
        querySoPddWaybillActiveRequest.setOutWarehouseId(electronicSheetRequest.getOutWarehouseId());
        querySoPddWaybillActiveRequest.setStoreId(electronicSheetRequest.getStoreId());
        buildExpressCompanyCode(electronicSheetRequest, querySoPddWaybillActiveRequest);
        apiRequest.setData(querySoPddWaybillActiveRequest);
        return apiRequest;
    }

    private void buildExpressCompanyCode(ElectronicSheetRequest electronicSheetRequest, QuerySoPddWaybillActiveRequest querySoPddWaybillActiveRequest) {
        tikTokExpressCompanyCode(electronicSheetRequest, querySoPddWaybillActiveRequest);
        ksExpressCompanyCode(electronicSheetRequest, querySoPddWaybillActiveRequest);
        canNiaoExpressCompanyCode(electronicSheetRequest, querySoPddWaybillActiveRequest);
        xhsExpressCompanyCode(electronicSheetRequest, querySoPddWaybillActiveRequest);
    }

    /**
     * 抖音京配增加ExpressCompanyCode赋值
     * https://joyspace.jd.com/pages/CcWZIBb5YwS4WU0RuW4d
     */
    private void tikTokExpressCompanyCode(ElectronicSheetRequest electronicSheetRequest, QuerySoPddWaybillActiveRequest querySoPddWaybillActiveRequest) {
        if (!ElectronicSheetPlatform.DY.equals(electronicSheetRequest.getPlatform())
                && !ElectronicSheetPlatform.DY_INSTANT.equals(electronicSheetRequest.getPlatform())) {
            return;
        }
        if (!ShipperConstants.JD_SHIPPER_NO.equals(electronicSheetRequest.getShipperNo())) {
            return;
        }
        if (electronicSheetRequest.isExistFreightProduct()) {
            querySoPddWaybillActiveRequest.setExpressCompanyCode("jingdongkuaiyun");
        } else {
            querySoPddWaybillActiveRequest.setExpressCompanyCode("jd");
        }
    }

    /**
     * 快手加密 京配 ExpressCompanyCode赋值
     */
    private void ksExpressCompanyCode(ElectronicSheetRequest electronicSheetRequest, QuerySoPddWaybillActiveRequest querySoPddWaybillActiveRequest) {
        if (!ElectronicSheetPlatform.KS.equals(electronicSheetRequest.getPlatform())) {
            return;
        }
        if (!ShipperConstants.JD_SHIPPER_NO.equals(electronicSheetRequest.getShipperNo())) {
            return;
        }
        if (electronicSheetRequest.isExistFreightProduct()) {
            querySoPddWaybillActiveRequest.setExpressCompanyCode(ShipperConstants.EXPRESS_COMPANY_JDKY);
        }
    }


    private List<ElectronicSheetInfo.CustomerTemplateItem> build(List<QuerySoPddWaybillActiveResponse.CustomerTemplateItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream()
                .map(item -> ElectronicSheetInfo.CustomerTemplateItem.builder()
                        .type(item.getType())
                        .code(item.getCode())
                        .name(item.getName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 菜鸟加密 京配 ExpressCompanyCode赋值
     */
    private void canNiaoExpressCompanyCode(ElectronicSheetRequest electronicSheetRequest, QuerySoPddWaybillActiveRequest querySoPddWaybillActiveRequest) {
        if (!ElectronicSheetPlatform.CN.equals(electronicSheetRequest.getPlatform())) {
            return;
        }
        if (!ShipperConstants.JD_SHIPPER_NO.equals(electronicSheetRequest.getShipperNo())) {
            return;
        }
        querySoPddWaybillActiveRequest.setExpressCompanyCode(electronicSheetRequest.getExpressCompanyCode());
    }


    /**
     * 小红书且京配时，需要区分快递快运
     */
    private void xhsExpressCompanyCode(ElectronicSheetRequest electronicSheetRequest, QuerySoPddWaybillActiveRequest querySoPddWaybillActiveRequest) {
        // 非京配不赋值
        if (!ShipperConstants.JD_SHIPPER_NO.equals(electronicSheetRequest.getShipperNo())) {
            return;
        }
        String expressCompanyCode;
        if (electronicSheetRequest.getPlatform() == ElectronicSheetPlatform.XHS) {
            if (electronicSheetRequest.isExistFreightProduct()) {
                expressCompanyCode = "jingdongkuaiyun";
            } else {
                expressCompanyCode = "jd";
            }
            querySoPddWaybillActiveRequest.setExpressCompanyCode(expressCompanyCode);
        }
    }
}
