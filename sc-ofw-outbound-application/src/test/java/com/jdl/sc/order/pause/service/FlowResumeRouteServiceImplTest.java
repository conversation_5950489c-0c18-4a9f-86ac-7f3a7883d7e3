package com.jdl.sc.order.pause.service;

import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.ofw.out.app.service.FlowResumeServiceImpl;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.api.FlowResumeService;
import com.jdl.sc.ofw.outbound.dto.FlowResponse;
import com.jdl.sc.ofw.outbound.dto.FlowRestartRequest;
import com.jdl.sc.ofw.outbound.dto.FlowResumeRequest;
import com.jdl.sc.order.hold.common.Response;
import com.jdl.sc.order.hold.spi.dto.OrderRestartRequest;
import com.jdl.sc.order.hold.spi.dto.OrderResumeRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FlowResumeRouteServiceImplTest {

    @Mock
    private FlowResumeService mockFlowResumeService;

    private FlowResumeRouteServiceImpl flowResumeRouteServiceImplUnderTest;

    @InjectMocks
    private FlowResumeServiceImpl flowResumeServiceImpl;

    @Mock
    private OutboundModelRepository outboundModelRepository;

    @Mock
    private WorkflowControlService workflowControlService;

    @Mock
    private OrderWorkflowLockAbility orderWorkflowLockAbility;

    @Mock
    private ReentrantLock lock;

    @Before
    public void setUp() {
        flowResumeRouteServiceImplUnderTest = new FlowResumeRouteServiceImpl(mockFlowResumeService);
    }

    @Test
    public void testResume() {

        final OrderResumeRequest orderResumeRequest = OrderResumeRequest.builder()
                .traceId("traceId")
                .id(0L)
                .orderNo("orderNo")
                .orderType("orderType")
                .operator("operator")
                .code("code")
                .customizeSnapshot("customizeSnapshot")
                .build();
        when(mockFlowResumeService.resume(any(FlowResumeRequest.class)))
                .thenReturn(FlowResponse.failed());

        final Response<Void> result = flowResumeRouteServiceImplUnderTest.resume(orderResumeRequest);

        Assert.assertTrue(!result.success());
    }

    @Test
    public void testResume_FlowResumeServiceReturnsNoItem() {
        final OrderResumeRequest orderResumeRequest = OrderResumeRequest.builder()
                .traceId("traceId")
                .id(0L)
                .orderNo("orderNo")
                .orderType("orderType")
                .operator("operator")
                .code("code")
                .customizeSnapshot("customizeSnapshot")
                .build();
        when(mockFlowResumeService.resume(any(FlowResumeRequest.class))).thenReturn(FlowResponse.ofSuccess());
        final Response<Void> result = flowResumeRouteServiceImplUnderTest.resume(orderResumeRequest);
        Assert.assertTrue(result.success());
    }

    @Test
    public void testRestart_FlowResumeServiceReturnsNoItem() {
        // Setup
        final OrderRestartRequest orderRestartRequest = OrderRestartRequest.builder()
                .traceId("traceId")
                .id(0L)
                .orderNo("orderNo")
                .orderType("orderType")
                .operator("operator")
                .code("code")
                .build();
        when(mockFlowResumeService.restart(any(FlowRestartRequest.class))).thenReturn(FlowResponse.ofSuccess());

        // Run the test
        final Response<Void> result = flowResumeRouteServiceImplUnderTest.restart(orderRestartRequest);

        Assert.assertTrue(result.success());
    }

    @Test
    public void testRestart_FlowResumeServiceReturnsFailure() {
        // Setup
        final OrderRestartRequest orderRestartRequest = OrderRestartRequest.builder()
                .traceId("traceId")
                .id(0L)
                .orderNo("orderNo")
                .orderType("orderType")
                .operator("operator")
                .code("code")
                .build();
        when(mockFlowResumeService.restart(any(FlowRestartRequest.class))).thenReturn(FlowResponse.failed());

        // Run the test
        final Response<Void> result = flowResumeRouteServiceImplUnderTest.restart(orderRestartRequest);

        Assert.assertTrue(!result.success());
    }

    @Test(expected = NullPointerException.class)
    public void testResumeFulfillmentCommand(){
        final FlowResumeRequest orderResumeRequest = FlowResumeRequest.builder()
                .traceId("traceId")
                .id(0L)
                .orderNo("orderNo")
                .orderType("orderType")
                .operator("operator")
                .code("8050628")
                .customizeSnapshot("customizeSnapshot")
                .resumeInstructions(1)
                .build();

        when(outboundModelRepository.getModelByOrderNo(anyString()))
                .thenReturn(OrderFulfillmentModel.builder()
                        .build());

        final FlowResponse<Void> result = flowResumeServiceImpl.resume(orderResumeRequest);
        Assert.assertTrue(result.success());

    }
}
