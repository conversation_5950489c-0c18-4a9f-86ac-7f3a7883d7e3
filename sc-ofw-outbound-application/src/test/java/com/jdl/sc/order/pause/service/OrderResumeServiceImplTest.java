package com.jdl.sc.order.pause.service;

import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.api.FlowResumeService;
import com.jdl.sc.ofw.outbound.dto.FlowResponse;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.spi.dto.OrderResumeRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderResumeServiceImplTest {

    @Mock
    private OutboundModelRepository outboundModelRepository;

    @Mock
    private WorkflowControlService workflowControlService;

    @Mock
    private FlowResumeService flowResumeService;

    @InjectMocks
    private FlowResumeRouteServiceImpl orderResumeService;

    private final OrderResumeRequest restartRequest = getOrderResumeRequest(PauseReasonEnum.START_BATRIX_EXCEPTION.getCode());
    private final OrderResumeRequest resumeRequest = getOrderResumeRequest(PauseReasonEnum.BD_OWNER_NO_DISABLE.getCode());


    OrderFulfillmentModel model = OrderFulfillmentModel.builder()
            .orderNo("ESL00000090000000003")
            .orderType(OrderTypeEnum.DEFAULT)
            .build();

    @Test
    public void restartTest() {
        when(flowResumeService.resume(any())).thenReturn(FlowResponse.ofSuccess());
        orderResumeService.resume(restartRequest);
    }

    @Test
    public void resumeTest() {
        when(flowResumeService.resume(any())).thenReturn(FlowResponse.ofSuccess());
        orderResumeService.resume(resumeRequest);
    }



    private OrderResumeRequest getOrderResumeRequest(String code){
        return OrderResumeRequest.builder()
                .orderNo("ESL00000090000000003")
                .orderType("100")
                .code(code)
                .customizeSnapshot("{\"action\":\"sc_trans_wms\",\"actionSource\":\"cn_jdl_sc.outbound_sale_sc_ofc.receive.sc_finance\",\"actionBizId\":\"outbound_ESL00000010000004070\",\"traceId\":\"0128e42c013c4952bf1ee1f9546c8fff\",\"modelClassName\":\"com.jdl.sc.ofw.out.domain.model.EventModel\",\"domainModel\":{\"orderNo\":\"ESL00000010000004070\",\"ignoreCurrentNode\":false}}")
                .build();
    }
}
