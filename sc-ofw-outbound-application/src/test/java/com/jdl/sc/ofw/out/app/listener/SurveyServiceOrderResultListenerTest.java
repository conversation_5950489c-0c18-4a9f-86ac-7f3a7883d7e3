package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import com.jdl.sc.test.TestFileLoader;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;
/**
 * This class is used for unit testing the SurveyServiceOrderResultListener class.(Generated by JoyCoder)
 * @author: fanchangliang
 * @date: 2025-05-22
 */
@RunWith(MockitoJUnitRunner.class)
public class SurveyServiceOrderResultListenerTest {

	@Mock
	private OutboundModelRepository outboundModelRepository;

	@Mock
	private OrderHoldAcl orderHoldAcl;

    @InjectMocks
    private SurveyServiceOrderResultListener surveyServiceOrderResultListener;

    @Mock
    private MessageReceive messageReceive;

    @Test
    public void testOnMessage() throws Exception {
        String message = TestFileLoader.loadTestFile("data/outbound/model/tms_service_feedback.json");
        // generated by JoyCoder taskId 46d1064c8bec
        SurveyServiceOrderResultListener listener = new SurveyServiceOrderResultListener(messageReceive);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().build();
        surveyServiceOrderResultListener.onMessage(message);
        // Verify the log.info message
    }

    @Test
    public void testOnMessageStentSelfProvidedIs0() throws Exception {
        String message = TestFileLoader.loadTestFile("data/outbound/model/tms_service_feedback_0.json");
        // generated by JoyCoder taskId 46d1064c8bec
        SurveyServiceOrderResultListener listener = new SurveyServiceOrderResultListener(messageReceive);
        surveyServiceOrderResultListener.onMessage(message);
        // Verify the log.info message
    }

    @Test
    public void testOnMessageHoldIsNull() throws Exception {
        String message = TestFileLoader.loadTestFile("data/outbound/model/tms_service_feedback.json");
        // generated by JoyCoder taskId 46d1064c8bec
        SurveyServiceOrderResultListener listener = new SurveyServiceOrderResultListener(messageReceive);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().build();
        surveyServiceOrderResultListener.onMessage(message);
        // Verify the log.info message
    }

}