package com.jdl.sc.ofw.out.app.listener.mutong;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.mutong.MuTongCollectAndDispatchResult;
import com.jdl.sc.ofw.out.app.listener.dto.mutong.MuTongDispatchStatus;
import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.pojo.OrderPausePojo;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseStatusEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MuTongCollectAndDispatchListenerTest {
    @Mock
    private OutboundModelService outboundModelService;

    @Mock
    private OutboundModelRepository outboundModelRepository;

    @Mock
    private OrderHoldHandler orderHoldHandler;

    @Mock
    private OrderConvertServiceAcl orderConvertServiceAcl;

    @Mock
    private OrderCenterAcl orderCenterAcl;
    @InjectMocks
    private MuTongCollectAndDispatchListener classToTest;

    @Test
    public void testOnMessageValidResultTransScheduling() throws Exception {
        MuTongCollectAndDispatchResult result = MuTongCollectAndDispatchResult.builder()
                .orderNo("12345")
                .dispatchStatus(MuTongDispatchStatus.TRANS_SCHEDULING.getName())
                .build();

        when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("convertedOrderNo");
        when(orderCenterAcl.checkOrderExist("convertedOrderNo")).thenReturn(true);

        classToTest.onMessage(JsonUtil.stringify(result));

        verify(outboundModelService, times(1)).modifyStatus(any());
    }

    @Test
    public void testOnMessageValidResultTransSchedulingCompleted() throws Exception {
        MuTongCollectAndDispatchResult result = MuTongCollectAndDispatchResult.builder()
                .orderNo("12345")
                .dispatchStatus(MuTongDispatchStatus.TRANS_SCHEDULING_COMPLETED.getName())
                .deliveryNetworkResults(1)
                .consolidationPointCode("code")
                .consolidationPointName("name")
                .dispatchNo("D123")
                .collectBatchNo("CB123")
                .build();
        when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("convertedOrderNo");
        when(orderCenterAcl.checkOrderExist("convertedOrderNo")).thenReturn(true);
        when(orderHoldHandler.queryOne(any(String.class), any(PauseReasonEnum.class), any(PauseStatusEnum.class)))
                .thenReturn(new OrderPausePojo());
        when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(OrderFulfillmentModel.builder()
                        .orderNo("123")
                        .batrixInfo(BatrixInfo.builder()
                                .businessUnit("unit")
                                .businessType("type")
                                .build())
                .build());


        classToTest.onMessage(JsonUtil.stringify(result));

        verify(outboundModelService, times(1)).modify(any(), any());
        verify(orderHoldHandler, times(1)).resume(anyString(), anyString(), anyString());
    }

}