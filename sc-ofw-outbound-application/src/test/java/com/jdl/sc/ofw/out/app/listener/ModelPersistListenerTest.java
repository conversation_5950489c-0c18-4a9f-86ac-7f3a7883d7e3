package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.dto.HbasePersistMqMessage;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModelPersistListenerTest {

    @InjectMocks
    ModelPersistListener modelPersistListener;

    @Mock
    OutboundModelRepository repository;

    private HbasePersistMqMessage hbasePersistMqMessage;

    @Test
    public void testOnMessage() throws Exception {
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("ESL00001")
                .orderStatus(OrderStatusEnum.CANCELLED)
                .build();
        hbasePersistMqMessage = HbasePersistMqMessage.builder()
                .orderNo("ESL00001")
                .timeStamp(System.currentTimeMillis())
                .model(model)
                .build();
        modelPersistListener.onMessage(JsonUtil.toJsonSafe(hbasePersistMqMessage));
    }
}
