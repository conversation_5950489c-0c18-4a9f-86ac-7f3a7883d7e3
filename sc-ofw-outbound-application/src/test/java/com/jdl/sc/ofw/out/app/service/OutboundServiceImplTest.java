package com.jdl.sc.ofw.out.app.service;

import com.jd.eclp.core.constant.ResponseConstant;
import com.jdl.sc.core.cache.jimdb.JimClient;
import com.jd.eclp.core.constant.ResponseConstant;
import com.jdl.sc.core.jsf.JsfConstants;
import com.jdl.sc.ofw.out.app.translator.OutboundModelTranslator;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.DomainServiceException;
import com.jdl.sc.ofw.out.common.specification.JSR303Specification;
import com.jdl.sc.ofw.out.common.specification.Notification;
import com.jdl.sc.ofw.out.domain.ability.PersistenceAbility;
import com.jdl.sc.ofw.out.domain.dto.CancelCommand;
import com.jdl.sc.ofw.out.domain.dto.CancelResult;
import com.jdl.sc.ofw.out.domain.dto.CloseResult;
import com.jdl.sc.ofw.out.domain.dto.InterceptCommand;
import com.jdl.sc.ofw.out.domain.dto.InterceptResult;
import com.jdl.sc.ofw.out.domain.dto.ModifyCommand;
import com.jdl.sc.ofw.out.domain.dto.ModifyStatusCommand;
import com.jdl.sc.ofw.out.domain.dto.PullbackCommand;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.service.OutboundCancelService;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundCloseRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCloseResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundInterceptRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundInterceptResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundPullbackRequest;
import com.jdl.sc.ofw.outbound.dto.callback.OutboundStatusRequest;
import com.jdl.sc.ofw.outbound.dto.callback.OutboundStatusResponse;
import com.jdl.sc.ofw.outbound.spec.enums.CancelResultStateEnum;
import com.jdl.sc.ofw.outbound.spec.enums.InterceptStateEnum;
import com.jdl.sc.ofw.outbound.spec.utils.UuidUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.Date;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class OutboundServiceImplTest {

    @Mock
    private JSR303Specification jsr303Specification;

    @Mock
    private OutboundModelService modelService;

    @Mock
    private OutboundCancelService cancelService;

    @Mock
    private OutboundModelTranslator outboundModelTranslator;

    @Mock
    private WorkflowControlService workflowControlService;

    @InjectMocks
    OutboundServiceImpl outboundService;


    @Mock
    private JimClient jimClient;

    @Mock
    private PersistenceAbility persistenceAbility;


    @Test(expected = RuntimeException.class)
    public void testCreate() throws Exception {
        String tid = UuidUtil.getTerseUuid();
        OutboundCreateRequest request = new OutboundCreateRequest();

        OrderFulfillmentModel model = OrderFulfillmentModel.builder().orderNo("111").batrixInfo(new BatrixInfo()).build();

        MDC.put(JsfConstants.LOG4J_MDC_TRACE_ID, tid);
//        doNothing().when(modelService).create(model, tid, false);
        OutboundCreateResponse result = outboundService.create(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(outboundModelTranslator).toModel(request);
        verify(modelService).create(model, tid, false);
    }

    @Test(expected = RuntimeException.class)
    public void testRedrive() throws Exception {
        String tid = UuidUtil.getTerseUuid();

        OutboundCreateRequest request = new OutboundCreateRequest();

        OrderFulfillmentModel model = OrderFulfillmentModel.builder().orderNo("111").batrixInfo(new BatrixInfo()).build();

        MDC.put(JsfConstants.LOG4J_MDC_TRACE_ID, tid);
//        doNothing().when(modelService).create(model, tid, true);
        OutboundCreateResponse result = outboundService.redrive(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(outboundModelTranslator).toModel(request);
        verify(modelService).create(model, tid, true);
    }

    @Test(expected = RuntimeException.class)
    public void testRedriveException() throws Exception {
        String tid = UuidUtil.getTerseUuid();

        OutboundCreateRequest request = new OutboundCreateRequest();
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().orderNo("111").batrixInfo(new BatrixInfo()).build();

        MDC.put(JsfConstants.LOG4J_MDC_TRACE_ID, tid);
//        doThrow(new RuntimeException()).when(modelService).create(model, tid, true);
        OutboundCreateResponse result = outboundService.redrive(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(outboundModelTranslator).toModel(request);
        verify(modelService).create(model, tid, true);
    }

    @Test
    public void testModify() throws Exception {

        OutboundModifyRequest request = OutboundModifyRequest.builder().orderNo("123").build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);

        OutboundModifyResponse result = outboundService.modify(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
    }

    @Test
    public void testModifyException() throws Exception {
        OutboundModifyRequest request = OutboundModifyRequest.builder().orderNo("123").build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        when(persistenceAbility.getModelByOrderNo(anyString())).thenReturn(OrderFulfillmentModel.builder().orderNo("111").batrixInfo(BatrixInfo.builder().build()).build());
        doThrow(new RuntimeException()).when(modelService).modify(any(ModifyCommand.class), any(OrderFulfillmentContext.class));
        OutboundModifyResponse result = outboundService.modify(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(modelService).modify(any(ModifyCommand.class), any(OrderFulfillmentContext.class));
    }

    @Test
    public void testModifyRequestNullException() throws Exception {
        OutboundModifyRequest request = null;
        OutboundModifyResponse result = outboundService.modify(request);
        assertTrue(result != null);
    }

    @Test
    public void testCancel() throws Exception {
        OutboundCancelRequest request = OutboundCancelRequest.builder()
                .cancelType(1)
                .cancelMode(0)
                .requestId("123")
                .remark("123")
                .orderNo("123")
                .operator("123")
                .channelOperateTime(new Date())
                .build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        CancelResult cancelResult = CancelResult.builder().state(CancelResultStateEnum.SUCCESS)
                .message("123")
                .requestId("123")
                .build();
        when(cancelService.cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class))).thenReturn(cancelResult);
        OutboundCancelResponse result = outboundService.cancel(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(cancelService).cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class));
    }

    @Test(expected = RuntimeException.class)
    public void testCancelException() throws Exception {
        OutboundCancelRequest request = OutboundCancelRequest.builder()
                .cancelType(1)
                .cancelMode(0)
                .requestId("123")
                .remark("123")
                .orderNo("123")
                .operator("123")
                .channelOperateTime(new Date())
                .build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        when(cancelService.cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class))).thenThrow(new RuntimeException());
        OutboundCancelResponse result = outboundService.cancel(request);
        assertEquals("3", result.getCode());
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(cancelService).cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class));
    }

    @Test
    public void testIntercept() throws Exception {
        OutboundInterceptRequest request = OutboundInterceptRequest.builder()
                .operationType(1)
                .source("POP")
                .build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        InterceptResult interceptResult = InterceptResult.builder().state(InterceptStateEnum.INTERCEPTING)
                .message("123")
                .build();

        when(cancelService.intercept(any(InterceptCommand.class))).thenReturn(interceptResult);
        OutboundInterceptResponse result = outboundService.intercept(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(cancelService).intercept(any(InterceptCommand.class));
    }

    @Test
    public void testModifyStatus() throws Exception {
        OutboundStatusRequest request = OutboundStatusRequest.builder().orderNo("123").build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        doNothing().when(modelService).modifyStatus(any(ModifyStatusCommand.class));
        OutboundStatusResponse result = outboundService.modifyStatus(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(modelService).modifyStatus(any(ModifyStatusCommand.class));
    }

    @Test(expected = RuntimeException.class)
    public void testModifyStatusException() throws Exception {
        OutboundStatusRequest request = OutboundStatusRequest.builder().orderNo("123").build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        doThrow(new RuntimeException()).when(modelService).modifyStatus(any(ModifyStatusCommand.class));
        OutboundStatusResponse result = outboundService.modifyStatus(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(modelService).modifyStatus(any(ModifyStatusCommand.class));
    }


    @Test(expected = DomainServiceException.class)
    public void testModifyStatusDomainException() throws Exception {
        OutboundStatusRequest request = OutboundStatusRequest.builder().orderNo("123").build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        doThrow(new DomainServiceException(UnifiedErrorSpec.Business.ORDER_NO_EXISTS)).when(modelService).modifyStatus(any(ModifyStatusCommand.class));
        OutboundStatusResponse result = outboundService.modifyStatus(request);
        assertTrue(result != null);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(modelService).modifyStatus(any(ModifyStatusCommand.class));
    }



    @Test
    public void pullback() throws Exception {
        OutboundPullbackRequest request = OutboundPullbackRequest.builder().orderNo("123").build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        doNothing().when(modelService).pullback(any(PullbackCommand.class));
        outboundService.pullback(request);
        verify(jsr303Specification).isSatisfiedBy(eq(request), any(Notification.class));
        verify(modelService).pullback(any(PullbackCommand.class));
    }

    @Test
    public void closeTest() throws Exception {
        OutboundCloseRequest outboundCloseRequest = OutboundCloseRequest.builder()
                .orderNo("123")
                .operator("123")
                .operateTime(new Date())
                .requestId("123")
                .build();
        when(jsr303Specification.isSatisfiedBy(eq(outboundCloseRequest), any(Notification.class))).thenReturn(true);
        CloseResult closeResult = CloseResult.builder()
                .code(ResponseConstant.SUCCESS_CODE)
                .message("")
                .requestId("123")
                .orderNo("123")
                .build();
        when(cancelService.close(any())).thenReturn(closeResult);
        OutboundCloseResponse close = outboundService.close(outboundCloseRequest);
        Integer successCode = 1;
        assertEquals(successCode, close.getCode());
    }


    @Test
    public void wmsCancelTmsIntercept() throws Exception {

        OutboundCancelRequest request = OutboundCancelRequest.builder()
                .cancelType(1)
                .cancelMode(0)
                .requestId("123")
                .remark("123")
                .orderNo("123")
                .operator("123")
                .channelOperateTime(new Date())
                .wmsCancelResult(2)
                .build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        CancelResult cancelResult = CancelResult.builder().state(CancelResultStateEnum.SUCCESS)
                .message("123")
                .requestId("123")
                .build();
        when(jimClient.get(any())).thenReturn("1");
        when(cancelService.cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class))).thenReturn(cancelResult);
        OutboundCancelResponse result = outboundService.cancel(request);
        assertNotNull(result);
        assertEquals(1, (int) result.getCode());
        verify(cancelService).cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class));
    }

    @Test
    public void omsCancelTmsIntercept() throws Exception {

        OutboundCancelRequest request = OutboundCancelRequest.builder()
                .cancelType(1)
                .cancelMode(0)
                .requestId("123")
                .remark("123")
                .orderNo("123")
                .operator("123")
                .channelOperateTime(new Date())
                .extendProps(new HashMap<String, String>(){{
                    put("deliveryIntercept","1");
                }})
                .build();
        when(jsr303Specification.isSatisfiedBy(eq(request), any(Notification.class))).thenReturn(true);
        CancelResult cancelResult = CancelResult.builder().state(CancelResultStateEnum.SUCCESS)
                .message("123")
                .requestId("123")
                .build();
        when(cancelService.cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class))).thenReturn(cancelResult);
        OutboundCancelResponse result = outboundService.cancel(request);
        assertNotNull(result);
        assertEquals(1, (int) result.getCode());
        verify(cancelService).cancel(any(CancelCommand.class), any(OrderFulfillmentContext.class));
    }
}