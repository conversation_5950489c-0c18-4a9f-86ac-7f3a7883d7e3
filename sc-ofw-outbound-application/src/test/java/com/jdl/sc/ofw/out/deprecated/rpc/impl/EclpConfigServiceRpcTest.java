package com.jdl.sc.ofw.out.deprecated.rpc.impl;

import com.jd.eclp.bbp.so.service.SoWaybillInfoService;
import com.jd.eclp.bbp.so.service.dto.request.WaybillInfoDTO;
import com.jd.eclp.core.ApiResponse;
import com.jd.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class EclpConfigServiceRpcTest {

    @InjectMocks
    private EclpConfigServiceRpc eclpConfigServiceRpc;

    @Mock
    private SoWaybillInfoService soWaybillInfoService;

    String waybillNo = "JDVB22484152239";

    String waybillInfo = "{\"bdOwnerNo\":\"021K1153264\",\"waybillNo\":\"JDVB22484152239\",\"consigneeName\":\"赵雪丽\",\"consigneePhone\":\"\",\"consigneeMobile\":\"15929971648\",\"provinceName\":\"陕西省\",\"cityName\":\"西安市\",\"countyName\":\"灞桥区\",\"consigneeAddress\":\"洪庆街道向阳一区32号楼二单元\",\"expressProductType\":\"1\"}";
    @Test
    public void queryWaybillInfoTest() {
        WaybillInfoDTO waybillInfoDTO = JSONObject.parseObject(this.waybillInfo, WaybillInfoDTO.class);
        ApiResponse<WaybillInfoDTO> response = new ApiResponse<>();
        response.setCode(1);
        response.setMsg("查询成功");
        response.setData(waybillInfoDTO);
        when(soWaybillInfoService.queryWaybillInfo(eq(waybillNo))).thenReturn(response);
        eclpConfigServiceRpc.queryWaybillInfo(waybillNo);
    }

}