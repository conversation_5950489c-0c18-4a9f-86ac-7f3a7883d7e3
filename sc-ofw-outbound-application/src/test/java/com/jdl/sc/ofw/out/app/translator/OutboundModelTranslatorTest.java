package com.jdl.sc.ofw.out.app.translator;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ReflectionUtils;
import com.jdl.sc.ofw.out.common.dict.PreSaleStageEnum;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.orderclob.IClobParser;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.out.horiz.infra.jfs.JfsClient;
import com.jdl.sc.ofw.out.horiz.infra.jimkv.DtcJimKvAgent;
import com.jdl.sc.ofw.out.horiz.infra.orderclob.OrderClobFactory;
import com.jdl.sc.ofw.out.horiz.infra.orderclob.parser.CargoClobParser;
import com.jdl.sc.ofw.out.horiz.infra.orderclob.parser.GoodsClobParser;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.vo.*;
import com.jdl.sc.ofw.outbound.dto.vo.Cargo;
import com.jdl.sc.ofw.outbound.dto.vo.CargoServiceInfo;
import com.jdl.sc.ofw.outbound.dto.vo.Fulfillment;
import com.jdl.sc.ofw.outbound.dto.vo.Goods;
import com.jdl.sc.ofw.outbound.dto.vo.Shipment;
import com.jdl.sc.ofw.outbound.dto.vo.Solution;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.*;
import com.jdl.sc.ofw.outbound.dto.vo.Channel;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OutboundModelTranslatorTest {
    @InjectMocks
    private OutboundModelTranslator classToTest = new OutboundModelTranslator();
    @Mock
    private JfsClient jfsClient;

    @Mock
    private DtcJimKvAgent dtcJimKvAgent;

    @Mock
    private CargoClobParser cargoClobParser;
    @Mock
    private GoodsClobParser goodsClobParser;

    private OrderClobFactory orderClobFactory;

    @Test
    public void testHappy() throws Exception {
        String requestStr = "{\n" +
                "  \"businessUnit\": \"cn_jdl_sc\",\n" +
                "  \"businessType\": \"outbound_sale_sc_ofc\",\n" +
                "  \"orderNo\": \"ESL0010000002016\",\n" +
                "  \"orderType\": \"100\",\n" +
                "  \"orderBusinessType\": 1,\n" +
                "  \"basicInfo\": {\n" +
                "    \"agentSales\": false\n" +
                "  },\n" +
                "  \"customer\": {\n" +
                "    \"accountNo\": \"EBU0000000000569\",\n" +
                "    \"accountName\": \"vmi22\"\n" +
                "  },\n" +
                "  \"cargos\": [\n" +
                "    {\n" +
                "      \"name\": \"test_splz1   测试 玫瑰红色 S\",\n" +
                "      \"no\": \"EMG4398059117452\",\n" +
                "      \"level\": \"100\",\n" +
                "      \"levelName\": \"良品自定义1\",\n" +
                "      \"quantity\": {\n" +
                "        \"value\": 1,\n" +
                "        \"unit\": \"袋\"\n" +
                "      },\n" +
                "      \"occupyQuantity\": {\n" +
                "        \"value\": 0,\n" +
                "        \"unit\": \"袋\"\n" +
                "      },\n" +
                "      \"refGoodsType\": 1,\n" +
                "      \"refGoodsNo\": \"ESG4398058530259\",\n" +
                "      \"isvCargoNo\": \"***********\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"name\": \"test_splz1   测试 天蓝色 XL\",\n" +
                "      \"no\": \"EMG4398059117375\",\n" +
                "      \"level\": \"100\",\n" +
                "      \"levelName\": \"良品自定义1\",\n" +
                "      \"quantity\": {\n" +
                "        \"value\": 2,\n" +
                "        \"unit\": \"袋\"\n" +
                "      },\n" +
                "      \"occupyQuantity\": {\n" +
                "        \"value\": 0,\n" +
                "        \"unit\": \"袋\"\n" +
                "      },\n" +
                "      \"refGoodsType\": 1,\n" +
                "      \"refGoodsNo\": \"ESG4398058530257\",\n" +
                "      \"isvCargoNo\": \"10000150559\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"goods\": [\n" +
                "    {\n" +
                "      \"type\": \"2\",\n" +
                "      \"no\": \"ESG4398058530258\",\n" +
                "      \"name\": \"test_splz1   测试 红色 S\",\n" +
                "      \"price\": {\n" +
                "        \"amount\": 132.0,\n" +
                "        \"currencyCode\": \"CNY\"\n" +
                "      },\n" +
                "      \"amount\": {\n" +
                "        \"currencyCode\": \"CNY\"\n" +
                "      },\n" +
                "      \"quantity\": {\n" +
                "        \"value\": 1\n" +
                "      },\n" +
                "      \"combinationVersion\": 1,\n" +
                "      \"channelNo\": \"10000150545\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"type\": \"1\",\n" +
                "      \"no\": \"ESG4398058530257\",\n" +
                "      \"name\": \"test_splz1   测试 天蓝色 XL\",\n" +
                "      \"price\": {\n" +
                "        \"currencyCode\": \"CNY\"\n" +
                "      },\n" +
                "      \"amount\": {\n" +
                "        \"currencyCode\": \"CNY\"\n" +
                "      },\n" +
                "      \"quantity\": {\n" +
                "        \"value\": 2\n" +
                "      },\n" +
                "      \"channelNo\": \"10000150559\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"type\": \"1\",\n" +
                "      \"no\": \"ESG4398058530259\",\n" +
                "      \"name\": \"test_splz1   测试 玫瑰红色 S\",\n" +
                "      \"price\": {\n" +
                "        \"currencyCode\": \"CNY\"\n" +
                "      },\n" +
                "      \"amount\": {\n" +
                "        \"currencyCode\": \"CNY\"\n" +
                "      },\n" +
                "      \"quantity\": {\n" +
                "        \"value\": 1\n" +
                "      },\n" +
                "      \"channelNo\": \"***********\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"consignee\": {\n" +
                "    \"address\": {\n" +
                "      \"provinceNo\": \"1\",\n" +
                "      \"cityNo\": \"72\",\n" +
                "      \"countyNo\": \"2799\",\n" +
                "      \"townNo\": \"0\",\n" +
                "      \"detailAddress\": \"北京朝阳区三环以内未访问\"\n" +
                "    },\n" +
                "    \"name\": \"AutoTest\",\n" +
                "    \"phone\": \"01088888888\",\n" +
                "    \"mobile\": \"13512345678\",\n" +
                "    \"email\": \"<EMAIL>\"\n" +
                "  },\n" +
                "  \"shipment\": {\n" +
                "    \"shipperNo\": \"CYS0000010\",\n" +
                "    \"shipperType\": 1,\n" +
                "    \"shipperName\": \"京东配送\",\n" +
                "    \"endStationNo\": \"38\",\n" +
                "    \"endStationName\": \"\",\n" +
                "    \"halfReceive\": 2,\n" +
                "    \"deliveryType\": 1\n" +
                "  },\n" +
                "  \"warehouse\": {\n" +
                "    \"warehouseNo\": \"110005442\",\n" +
                "    \"warehouseType\": 1,\n" +
                "    \"warehouseName\": \"北京测试仓\"\n" +
                "  },\n" +
                "  \"finance\": {\n" +
                "    \"paymentType\": 0,\n" +
                "    \"settlementType\": 1\n" +
                "  },\n" +
                "  \"refOrderInfo\": {\n" +
                "    \"giftType\": 1,\n" +
                "    \"mainOrderNos\": \"1\",\n" +
                "    \"giftType\": \"2\"\n" +
                "  },\n" +
                "  \"channel\": {\n" +
                "    \"pin\": \"sop_order\",\n" +
                "    \"channelSource\": \"POP\",\n" +
                "    \"channelNo\": \"1\",\n" +
                "    \"channelName\": \"京东商城\",\n" +
                "    \"channelShopNo\": \"58726\",\n" +
                "    \"channelOrderNo\": \"56325137634\",\n" +
                "    \"channelOrderCreateTime\": 1623931620000,\n" +
                "    \"shopNo\": \"ESP0000000004185\",\n" +
                "    \"shopName\": \"促销在用，请勿修改店铺，谢谢\",\n" +
                "  },\n" +
                "  \"products\": [],\n" +
                "  \"solution\": {\n" +
                "    \"no\": \"sc-s-0005\",\n" +
                "    \"name\": \"供应链测试2\",\n" +
                "    \"attributes\": {\n" +
                "      \"shouldPayMoney\": \"92.9\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"nonstandardProducts\": {\n" +
                "    \"flashSaleGoodsType\": \"0\",\n" +
                "    \"selfPickupType\": \"0\",\n" +
                "    \"nextDayAgingPromotion\": \"0\",\n" +
                "    \"expectOutboundTime\": 1623931620000\n" +
                "  },\n" +
                "  \"createTime\": 1623931620000,\n" +
                "  \"fulfillment\": {\n" +
                "    \"occupyResult\": true " +
                "    \"ecpFirstOrderNo\": \"123456789\",\n" +
                "    \"ecpOrderType\": \"1\"\n" +
                "  },\n" +
                "  \"ediRemark\": \"jfs/t850/14/926631252/971/742dc933d8781436Y\",\n" +
                "  \"extendProps\": {\n" +
                "    \"soMainParamJfsKey\": \"jfs/t4/63/704827100/1205/feb1416a3ad61648Y\"\n" +
                "  },\n" +
                "  \"orderSign\": {\n" +
                "    \"batchOrder\": \"1\",\n" +
                "    \"lettering\": \"1\"\n" +
                "  }\n" +
                "}";
        OutboundCreateRequest request = JsonUtil.readValue(requestStr, OutboundCreateRequest.class);
//        when(jfsClient.load("jfs/t850/14/926631252/971/742dc933d8781436Y")).thenReturn("{\n" +
//                "    \"orderLines\":{\n" +
//                "        \"orderLine\":[\n" +
//                "            {\n" +
//                "                \"itemId\":\"\",\n" +
//                "                \"inventoryType\":\"ZP\",\n" +
//                "                \"itemName\":\"油酸多多花生油900ml*1\",\n" +
//                "                \"orderLineNo\":\"6039769\",\n" +
//                "                \"planQty\":\"1\",\n" +
//                "                \"extCode\":\"\",\n" +
//                "                \"actualPrice\":\"33.1\",\n" +
//                "                \"ownerCode\":\"BJ4418054854676\",\n" +
//                "                \"sourceOrderCode\":\"24523033094492910\",\n" +
//                "                \"itemCode\":\"CP001541\",\n" +
//                "                \"extendProps\":{\n" +
//                "                    \"isGift\":\"0\",\n" +
//                "                    \"shop_sku_id\":\"\"\n" +
//                "                }\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    },\n" +
//                "    \"extendProps\":{\n" +
//                "        \"pay_info\":\"{\\\"payment\\\":\\\"供销支付\\\"}\",\n" +
//                "        \"drp_info\":\"{\\\"drp_co_id\\\":12176940,\\\"drp_co_name\\\":\\\"上海岳灵贸易有限公司（电商）\\\"}\"\n" +
//                "    },\n" +
//                "    \"deliveryOrder\":{\n" +
//                "        \"orderType\":\"JYCK\",\n" +
//                "        \"buyerNick\":\"岳灵\",\n" +
//                "        \"payTime\":\"2023-03-30 14:21:10\",\n" +
//                "        \"freight\":\"0\",\n" +
//                "        \"operateTime\":\"2023-03-30 14:35:47\",\n" +
//                "        \"receiverInfo\":{\n" +
//                "            \"area\":\"桂平市\",\n" +
//                "            \"province\":\"广西壮族自治区\",\n" +
//                "            \"city\":\"贵港市\",\n" +
//                "            \"mobile\":\"15994558208\",\n" +
//                "            \"name\":\"黄福强\",\n" +
//                "            \"detailAddress\":\"麻垌镇麻垌镇南乔村\",\n" +
//                "            \"extendProps\":\"\"\n" +
//                "        },\n" +
//                "        \"discountAmount\":\"0.00\",\n" +
//                "        \"logisticsCode\":\"JD\",\n" +
//                "        \"remark\":\"24523033094492910\",\n" +
//                "        \"deliveryOrderCode\":\"5060424\",\n" +
//                "        \"sourcePlatformName\":\"商家官网\",\n" +
//                "        \"warehouseCode\":\"WH8064651\",\n" +
//                "        \"logisticsName\":\"青岛-京东\",\n" +
//                "        \"placeOrderTime\":\"2023-03-30 09:00:00\",\n" +
//                "        \"payNo\":\"24523033094492910\",\n" +
//                "        \"shopNick\":\"鲁花订单导入\",\n" +
//                "        \"totalAmount\":\"33.10\",\n" +
//                "        \"senderInfo\":{\n" +
//                "            \"area\":\"胶州市\",\n" +
//                "            \"province\":\"山东省\",\n" +
//                "            \"city\":\"青岛市\",\n" +
//                "            \"mobile\":\"13505354935\",\n" +
//                "            \"name\":\"WBJD青岛KA仓\",\n" +
//                "            \"detailAddress\":\"九龙街道交大大道西400米物流园3库\"\n" +
//                "        },\n" +
//                "        \"sourcePlatformCode\":\"GW\",\n" +
//                "        \"createTime\":\"2023-03-30 14:35:47\",\n" +
//                "        \"itemAmount\":\"33.1\",\n" +
//                "        \"gotAmount\":\"33.10\",\n" +
//                "        \"extendProps\":{\n" +
//                "            \"isDecrypt\":\"false\",\n" +
//                "            \"subOrderType\":\"Self\",\n" +
//                "            \"isJitx\":\"false\",\n" +
//                "            \"isJdMobileDecrypt\":\"true\",\n" +
//                "            \"oid\":\"6891144\",\n" +
//                "            \"shopId\":\"13992331\",\n" +
//                "            \"labels\":\"小包装,禁发中通\"\n" +
//                "        },\n" +
//                "        \"sellerMessage\":\"\"\n" +
//                "    },\n" +
//                "    \"tictokDaifaShopId\":\"岳灵\"\n" +
//                "}");
        List<Cargo> cargos = new ArrayList<>();
        request.setCargos(cargos);
        OrderFulfillmentModel model = classToTest.toModel(request);

        assertEquals(request.getOrderNo(), model.getOrderNo());
        for (Map.Entry<String, String> entry : request.getNonstandardProducts().entrySet()) {
            assertEquals(entry.getValue(), model.getNonstandardProducts().getDetail(entry.getKey()));
        }
    }

    @Test
    public void toShipmentTest() {
        Shipment shipment = Shipment.builder()
                .transportCount("100")
                .build();
        com.jdl.sc.ofw.out.domain.model.vo.Shipment shipmentResult = classToTest.toShipment(shipment);
        assertEquals(shipment.getTransportCount(), shipmentResult.getTransportCount());

    }

    @Test
    public void toBasicInfoTest() {
        Map<String, String> extendProps = new HashMap<>();
        extendProps.put("preSale", "false");
        BasicInfo basicInfo = classToTest.toBasicInfo(extendProps);
        assertEquals(basicInfo.getPreSaleStage(), PreSaleStageEnum.DEFAULT);
    }

    @Test
    public void toCargoModelTest() {

        final List<com.jdl.sc.ofw.out.domain.model.vo.Cargo> cargoList = classToTest.toCargoModel(new ArrayList<Cargo>() {{
            add(Cargo.builder()
                    .cargoServiceInfo(Collections.singletonList(CargoServiceInfo.builder().serviceCode("sc-0001").build()))
                    .cargoBrand("cargoBrand")
                    .productList(Arrays.asList(Product.builder().build()))
                    .build());
        }});
        Assert.assertNotNull(cargoList);
    }

    @Test
    public void toFulfillmentTest() {
        OutboundModifyRequest request =
                OutboundModifyRequest.builder()
                        .fulfillment(Fulfillment.builder().crossDockType("1").build())
                        .build();
        com.jdl.sc.ofw.out.domain.model.vo.Fulfillment
                fulfillment = classToTest.toFulfillment(request);
        request =
                OutboundModifyRequest.builder()
                        .fulfillment(Fulfillment.builder().build())
                        .build();
        fulfillment = classToTest.toFulfillment(request);

    }

    @Test
    public void toSolutionTest() {
        OutboundModifyRequest request = OutboundModifyRequest.builder()
                .solution(Solution.builder().attributes(new HashMap<>()).build())
                .shouldPayMoney("12")
                .build();
        classToTest.toSolution(request);
    }

    @Test(expected = InvocationTargetException.class)
    public void fulfillmentBuilderCreateTest() throws InstantiationException, IllegalAccessException, InvocationTargetException {

        final Class<OutboundModelTranslator> aClass = OutboundModelTranslator.class;
        final OutboundModelTranslator newInstance = aClass.newInstance();
        final Method declaredMethod = ReflectionUtils.getDeclaredMethod(aClass, "fulfillmentCommandCreate", OutboundCreateRequest.class);
        declaredMethod.setAccessible(true);
        final OutboundCreateRequest createRequest = OutboundCreateRequest.builder()
                .orderClob(1)
                .goods(new ArrayList<Goods>() {{
                    add(Goods.builder().build());
                }})
                .build();

        orderClobFactory = new OrderClobFactory(
                new ArrayList<IClobParser>(){{
                    add(new GoodsClobParser());
                }}
        );

//        when(OrderClobHandler.getProcessor(ClobTypeEnums.CARGO)).thenReturn(cargoClobProcessor);
//        when(OrderClobHandler.getProcessor(ClobTypeEnums.GOODS)).thenReturn(goodsClobProcessor);
        declaredMethod.invoke(newInstance, createRequest);
    }

    @Test
    public void toChannelTest() {
        Channel channel = Channel.builder().extendProps(new HashMap() {{
            put("channelTaskCode", "code-10111");
        }}).build();
        classToTest.toChannel(channel);
    }
}
