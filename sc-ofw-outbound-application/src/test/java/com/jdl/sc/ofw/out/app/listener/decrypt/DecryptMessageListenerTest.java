package com.jdl.sc.ofw.out.app.listener.decrypt;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ReflectionUtils;
import com.jdl.sc.ofw.out.app.listener.dto.AddProduct;
import com.jdl.sc.ofw.out.app.listener.dto.DecryptMessage;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.horiz.infra.util.CainiaoJpUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.TiktokJpUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.XhsNewProcessUtil;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@SuppressWarnings("all")
public class DecryptMessageListenerTest {

    @InjectMocks
    private DecryptMessageListener classToTest;

    @Mock
    private OutboundService outboundService;

    @Mock
    private OrderHoldAcl orderHoldAcl;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Mock
    private OutboundModelRepository outboundModelRepository;
    @Mock
    private CainiaoJpUtils cainiaoJpUtils;

    @Mock
    private XhsNewProcessUtil xhsNewProcessUtil;

    @Mock
    private OrderCenterAcl orderCenterAcl;
    @Mock
    private TiktokJpUtils tiktokJpUtils;

    @Test
    public void onMessageTest() throws Exception {

        // when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder().code(ResponseConstant.SUCCESS_CODE).build());
        // doNothing().when(orderHoldAcl).orderHoldResume(any());
        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(mockModel());

        String jsonMsg = "{\"cityName\":\"北京市\",\"consigneeAddress\":\"科创十******\",\"consigneeName\":\"王鑫\",\"countyName\":\"\",\"orderNo\":\"ESL00000021990071733\",\"productCode\":\"fr-m-0004\",\"provinceName\":\"北京市\",\"townName\":\"亦庄地区\",\"ts\":\"2024-08-29 12:14:10\",\"waybillNo\":\"JDV017151023780\",\"consigneeMobile\":\"***********\",\"virtualMobile\":\"2\",\"virtualMobileTime\":\"2024-10-18 14:48:01\"}\n";
        //String jsonMsg = JsonUtil.toJson(testData());
        classToTest.onMessage(jsonMsg);
    }

    @Test
    public void testCainiaoJp() throws Exception {
        when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder().code(ResponseConstant.SUCCESS_CODE).build());
        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(mockModel());
        when(cainiaoJpUtils.isCainiaoJpNewFlow(any())).thenReturn(true);
        String jsonMsg = "{\"cityName\":\"北京市\",\"consigneeAddress\":\"科创十******\",\"consigneeName\":\"王鑫\",\"countyName\":\"\",\"orderNo\":\"ESL00000021990071733\",\"productCode\":\"fr-m-0004\",\"provinceName\":\"北京市\",\"townName\":\"亦庄地区\",\"ts\":\"2024-08-29 12:14:10\",\"waybillNo\":\"JDV017151023780\",\"consigneeMobile\":\"***********\",\"virtualMobile\":\"2\",\"virtualMobileTime\":\"2024-10-18 14:48:01\"}\n";
        classToTest.onMessage(jsonMsg);
    }

    @Test
    public void testXhsJp() throws Exception {
        when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder().code(ResponseConstant.SUCCESS_CODE).build());
        OrderFulfillmentModel model = mockModel();
        model.getShipment().assignDeliveryPerformanceChannel(DeliveryPerformanceChannelEnum.XHS);
        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        when(xhsNewProcessUtil.isXhsNewProcess(any(), any())).thenReturn(true);
        String jsonMsg = "{\"cityName\":\"北京市\",\"consigneeAddress\":\"科创十******\",\"consigneeName\":\"王鑫\",\"countyName\":\"\",\"orderNo\":\"ESL00000021990071733\",\"productCode\":\"fr-m-0004\",\"provinceName\":\"北京市\",\"townName\":\"亦庄地区\",\"ts\":\"2024-08-29 12:14:10\",\"waybillNo\":\"JDV017151023780\",\"consigneeMobile\":\"***********\",\"virtualMobile\":\"2\",\"virtualMobileTime\":\"2024-10-18 14:48:01\"}\n";
        classToTest.onMessage(jsonMsg);
    }

    private OrderFulfillmentModel mockModel() {
        return OrderFulfillmentModel.builder()
                .batrixInfo(BatrixInfo.builder().businessUnit("unit").build())
                .customer(Customer.builder().accountNo("EBU0001").build())
                .orderStatus(OrderStatusEnum.INIT)
                .fulfillment(Fulfillment.builder().build())
                .shipment(Shipment.builder().shipperType(ShipperTypeEnum.JDPS).deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.CAI_NIAO).build())
                .build();
    }

    private DecryptMessage testData() {
        return DecryptMessage.builder()
                .orderNo("orderNo")
                .waybillNo("waybillNo")
                .consigneeName("consigneeName")
                .consigneePhone("phone")
                .consigneeMobile("mobile")
                .provinceName("provinceName")
                .cityName("cityName")
                .countyName("countyName")
                .townName("townName")
                .consigneeAddress("consigneeAddress")
                .addedProducts(new ArrayList<AddProduct>(){{
                    add(AddProduct.builder()
                            .productCode("SVC-WBHOMEDELIVERY")
                            .build());
                }})
                .ts("2024-08-21 19:04:01")
                .build();
    }

    @Test
    public void validMessageTest() throws Exception {

        //单号校验
        DecryptMessage decryptMessage = testData();
        try {
            classToTest.onMessage(JsonUtil.toJsonSafe(decryptMessage));
        }catch (Exception e){
            Assert.assertTrue(e instanceof IllegalArgumentException);
        }

        when(switchKeyUtils.isDecryptMessageValidOrderNoSwitch()).thenReturn(true);
        when(orderCenterAcl.checkOrderExist(any())).thenReturn(true);

        ReflectionUtils.setFieldValue(DecryptMessage.class, decryptMessage, "orderNo", "ESL000000111");
        classToTest.onMessage(JsonUtil.toJsonSafe(decryptMessage));

        OrderFulfillmentModel model = mockModel();
        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        when(cainiaoJpUtils.isCainiaoJpNewFlow(any())).thenReturn(true);
        //内容校验
        ReflectionUtils.setFieldValue(DecryptMessage.class, decryptMessage, "consigneePhone", null);
        ReflectionUtils.setFieldValue(DecryptMessage.class, decryptMessage, "consigneeMobile", null);
        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(mockModel());
        try {
            classToTest.onMessage(JsonUtil.toJsonSafe(decryptMessage));
        }catch (Exception e){
            Assert.assertTrue(e instanceof RuntimeException);
        }

    }


    @Test
    public void testTiktockDecrypt() throws Exception {

        DecryptMessage message = testData();

        OrderFulfillmentModel model = mockModel();
        ReflectionUtils.setFieldValue(Shipment.class, model.getShipment(), "deliveryPerformanceChannel", DeliveryPerformanceChannelEnum.TIK_TOK);

        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder()
                .code(ResponseConstant.SUCCESS_CODE)
                .build());
        classToTest.onMessage(JsonUtil.toJsonSafe(message));
    }

    @Test
    public void testTiktokJpInstant_CndDelivery_Success() throws Exception {
        when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder().code(ResponseConstant.SUCCESS_CODE).build());

        OrderFulfillmentModel model = mockModel();
        ReflectionUtils.setFieldValue(Shipment.class, model.getShipment(), "deliveryPerformanceChannel", DeliveryPerformanceChannelEnum.TIK_TOK);

        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        when(tiktokJpUtils.isJpInstant(any())).thenReturn(true);

        String jsonMsg = "{\"cityName\":\"北京市\",\"consigneeAddress\":\"科创十******\",\"consigneeName\":\"王鑫\",\"countyName\":\"\",\"orderNo\":\"ESL00000021990071733\",\"productCode\":\"fr-m-0004\",\"provinceName\":\"北京市\",\"townName\":\"亦庄地区\",\"ts\":\"2024-08-29 12:14:10\",\"waybillNo\":\"JDV017151023780\",\"consigneeMobile\":\"***********\",\"virtualMobile\":\"2\",\"virtualMobileTime\":\"2024-10-18 14:48:01\",\"customExtends\":[{\"key\":\"jslsScene\",\"value\":[\"ThirdHalfPartlyLogistics\"]},{\"key\":\"LatestReceiptTime\",\"value\":[\"2025-05-19\"]}]}";

        classToTest.onMessage(jsonMsg);
    }

    @Test
    public void testTiktokJpInstant_jslsOrder() throws Exception {
        when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder().code(ResponseConstant.SUCCESS_CODE).build());

        OrderFulfillmentModel model = mockModel();
        ReflectionUtils.setFieldValue(Shipment.class, model.getShipment(), "deliveryPerformanceChannel", DeliveryPerformanceChannelEnum.TIK_TOK);

        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        when(tiktokJpUtils.isJpInstant(any())).thenReturn(true);

        String jsonMsg = "{\"cityName\":\"北京市\",\"consigneeAddress\":\"科创十******\",\"consigneeName\":\"王鑫\",\"countyName\":\"\",\"orderNo\":\"ESL00000021990071733\",\"productCode\":\"fr-m-0004\",\"provinceName\":\"北京市\",\"townName\":\"亦庄地区\",\"ts\":\"2024-08-29 12:14:10\",\"waybillNo\":\"JDV017151023780\",\"consigneeMobile\":\"***********\",\"virtualMobile\":\"2\",\"virtualMobileTime\":\"2024-10-18 14:48:01\",\"customExtends\":[{\"key\":\"jslsScene\",\"value\":[\"jslsOrder\"]},{\"key\":\"LatestReceiptTime\",\"value\":[\"2025-05-20\"]}]}";

        classToTest.onMessage(jsonMsg);
    }
}