package com.jdl.sc.ofw.out.app.service;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.search.dto.Order;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.ability.PersistenceAbility;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.vo.*;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashSet;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OutboundUtilServiceImplTest {

    @Mock
    private PersistenceAbility mockPersistenceAbility;

    @InjectMocks
    private OutboundUtilServiceImpl outboundUtilServiceImplUnderTest;
    @Mock
    private PersistenceAbility persistenceAbility;

    @Mock
    private OrderCenterAcl orderCenterAcl;



    @Test
    public void test_repair() {
        outboundUtilServiceImplUnderTest.repair("ESL", "$.batrixInfo", "1");
    }
    @Test
    public void test_addKey() {
        when(persistenceAbility.getModelByOrderNo(anyString())).thenReturn(OrderFulfillmentModel.
                builder()
                .orderNo("ESL00000010005612242")
                .ediRemark(EdiRemarkInfo.builder().sourceOrderCode("2222").build())
                .build());
        outboundUtilServiceImplUnderTest.addKey("ESL00000010005612242", "$.ediRemark","tictokDaifaShopId", "11810741");
    }
}
