package com.jdl.sc.ofw.route;

import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OutboundRouteServiceImplTest {


    @InjectMocks
    private OutboundRouteServiceImpl outboundRouteService;

    @Mock
    private OutboundService outboundService;

    @Test
    public void modifyTest() throws Exception {
        OutboundModifyRequest request = OutboundModifyRequest.builder().orderNo("123").build();
        when(outboundService.modify(any())).thenReturn(OutboundModifyResponse.builder().build());
        OutboundModifyResponse result = outboundRouteService.modify(request);
        assertNotNull(result);
    }

    @Test
    public void cancelTest() throws Exception {
        OutboundCancelRequest request = OutboundCancelRequest.builder().orderNo("123").build();
        when(outboundService.cancel(any())).thenReturn(OutboundCancelResponse.builder().build());
        OutboundCancelResponse result = outboundRouteService.cancel(request);
        assertNotNull(result);
    }
}