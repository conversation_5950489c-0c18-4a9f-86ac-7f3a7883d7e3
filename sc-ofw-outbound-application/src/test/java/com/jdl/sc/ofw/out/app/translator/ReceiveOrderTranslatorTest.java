package com.jdl.sc.ofw.out.app.translator;

import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.QuantityInfo;
import cn.jdl.oms.core.model.SmartPatternInfo;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.CreateOutboundOrderMessage;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.EcpOrderInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentFlag;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.PrintExtendInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.PrintInfo;
import com.jdl.sc.ofw.out.domain.rpc.EclpJimkvLoadServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.dto.vo.Cargo;
import com.jdl.sc.ofw.outbound.dto.vo.Consignee;
import com.jdl.sc.ofw.outbound.dto.vo.Fulfillment;
import com.jdl.sc.ofw.outbound.dto.vo.Shipment;
import com.jdl.sc.ofw.outbound.dto.vo.Solution;
import com.jdl.sc.ofw.outbound.spec.enums.WaybillNoFetchTypeEnum;
import com.jdl.sc.test.TestFileLoader;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReceiveOrderTranslatorTest {
    @InjectMocks
    private ReceiveOrderTranslator classToTest = new ReceiveOrderTranslator();
    @Mock
    private EclpJimkvLoadServiceAcl eclpJimkvLoadServiceAcl;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Test
    public void testPop() throws Exception {
        String requestStr = "{\n" +
                "    \"data\":{\n" +
                "        \"businessIdentity\":{\n" +
                "            \"businessScene\":\"receive\",\n" +
                "            \"businessType\":\"outbound_sale\",\n" +
                "            \"businessUnit\":\"cn_jdl_sc-pop\"\n" +
                "        },\n" +
                "        \"businessSolutionInfo\":{\n" +
                "            \"businessSolutionName\":\"中小件商务仓配\",\n" +
                "            \"businessSolutionNo\":\"sc-s-0013\",\n" +
                "            \"productAttrs\":{\n" +
                "                \"shouldPayMoney\":\"159.0\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"cargoInfos\":[\n" +
                "            {\n" +
                "                \"cargoLevel\":\"100\",\n" +
                "                \"cargoName\":\"麦富迪狗粮营养森林10kg全价小型犬幼犬成犬泰迪雪纳瑞比熊贵宾通用型20斤\",\n" +
                "                \"cargoNo\":\"EMG4418202145017\",\n" +
                "                \"cargoOccupyQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"cargoQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"extendProps\":{\n" +
                "                    \"uniqueCodeCollectionWay\":\"0\",\n" +
                "                    \"sequenceNo\":\"2061324770600001\",\n" +
                "                    \"uniqueLineFlag\":\"EMG4418202145017-100\",\n" +
                "                    \"sellerGoodsSign\":\"POP10031546100898\"\n" +
                "                },\n" +
                "                \"isvCargoNo\":\"10032839718863\",\n" +
                "                \"productInfos\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"refGoodsNo\":\"ESG4418370618301\",\n" +
                "                \"refGoodsType\":\"1\",\n" +
                "                \"serialInfos\":[\n" +
                "\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"channelInfo\":{\n" +
                "            \"channelName\":\"京东商城\",\n" +
                "            \"channelNo\":\"1\",\n" +
                "            \"channelOperateTime\":1680599118000,\n" +
                "            \"channelOrderNo\":\"************\",\n" +
                "            \"channelShopNo\":\"10039727\",\n" +
                "            \"customerOrderNo\":\"************\",\n" +
                "            \"extendProps\":{\n" +
                "                \"espId\":\"20000042953\"\n" +
                "            },\n" +
                "            \"secondLevelChannel\":\"ESP0020000042953\",\n" +
                "            \"secondLevelChannelName\":\"味粒宠物用品专营店\",\n" +
                "            \"systemCaller\":\"POP\"\n" +
                "        },\n" +
                "        \"consigneeInfo\":{\n" +
                "            \"addressInfo\":{\n" +
                "                \"address\":\"辽宁鞍山市海城市经济开发区海城市铁西芭东海城二期B区N9-1011\",\n" +
                "                \"addressGis\":\"芭东海城二期B区N91011\",\n" +
                "                \"cityNameGis\":\"鞍山市\",\n" +
                "                \"cityNo\":\"579\",\n" +
                "                \"cityNoGis\":\"579\",\n" +
                "                \"countyNameGis\":\"海城市\",\n" +
                "                \"countyNo\":\"581\",\n" +
                "                \"countyNoGis\":\"581\",\n" +
                "                \"provinceNameGis\":\"辽宁\",\n" +
                "                \"provinceNo\":\"8\",\n" +
                "                \"provinceNoGis\":\"8\",\n" +
                "                \"townNameGis\":\"经济开发区\",\n" +
                "                \"townNo\":\"40013\",\n" +
                "                \"townNoGis\":\"40013\"\n" +
                "            },\n" +
                "            \"consigneeEmail\":\"<EMAIL>\",\n" +
                "            \"consigneeMobile\":\"13130153777\",\n" +
                "            \"consigneeName\":\"王贤\",\n" +
                "            \"consigneePhone\":\"04123203777\"\n" +
                "        },\n" +
                "        \"consignorInfo\":{\n" +
                "            \"addressInfo\":{\n" +
                "                \"address\":\"辽宁省沈阳市浑南新区文溯街京东商城亚洲一号\",\n" +
                "                \"cityName\":\"沈阳市\",\n" +
                "                \"cityNo\":\"560\",\n" +
                "                \"countyName\":\"浑南新区\",\n" +
                "                \"countyNo\":\"50826\",\n" +
                "                \"provinceName\":\"辽宁\",\n" +
                "                \"provinceNo\":\"8\",\n" +
                "                \"townName\":\"城区\",\n" +
                "                \"townNo\":\"52074\"\n" +
                "            },\n" +
                "            \"customerWarehouse\":{\n" +
                "                \"actualWarehouseName\":\"沈阳公共平台仓3号库\",\n" +
                "                \"actualWarehouseNo\":\"*********\",\n" +
                "                \"actualWarehouseType\":1,\n" +
                "                \"warehouseName\":\"沈阳公共平台仓3号库\",\n" +
                "                \"warehouseNo\":\"*********\",\n" +
                "                \"warehouseType\":1\n" +
                "            }\n" +
                "        },\n" +
                "        \"customerInfo\":{\n" +
                "            \"account2No\":\"027K281456\",\n" +
                "            \"accountName\":\"武汉默优宠物用品有限责任公司\",\n" +
                "            \"accountNo\":\"EBU*************\"\n" +
                "        },\n" +
                "        \"extendProps\":{\n" +
                "            \"settlementType\":\"1\",\n" +
                "            \"sendpay\":\"20000000200000000000000002001200034100100000000001600000000001030000000000000000000010000000000000001040000000090000000000000000000000000100000000000000000000000000010000002000000000000103020000000000000000000000000000000000000010000000402000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\",\n" +
                "            \"soSource\":\"2\",\n" +
                "            \"sellerName\":\"武汉默优宠物用品有限责任公司\",\n" +
                "            \"sellerNo\":\"ECP0000000001785\",\n" +
                "            \"tradeInfo\":\"{\\\"orderItemQuantityType\\\":\\\"1\\\"}\",\n" +
                "            \"warehouseNo\":\"*********\",\n" +
                "            \"orderSellModel\":\"1\",\n" +
                "            \"agentSales\":\"1\",\n" +
                "            \"ecpOrderInfo\":\"{\\\"buyerNo\\\":\\\"hanzhi999\\\",\\\"orderGiftRelationList\\\":[],\\\"paymentType\\\":\\\"0\\\",\\\"jdlMark\\\":\\\"1\\\",\\\"ecpOrderNo\\\":\\\"*********\\\",\\\"ecpOrderSalesStrategy\\\":\\\"1\\\"}\",\n" +
                "            \"requestId\":\"8796113252612_4333720\",\n" +
                "            \"accountNo\":\"EBU*************\",\n" +
                "            \"outboundOrderSyncEclp\":\"1\",\n" +
                "            \"account2No\":\"027K281456\",\n" +
                "            \"fulfillmentInfo\":\"{\\\"flashSaleGoodsType\\\":\\\"0\\\",\\\"logisticsOriginalPackage\\\":\\\"1\\\",\\\"extendedField\\\":\\\"https://fulfillment-extend-field-info.s3-internal.cn-north-1.jdcloud-oss.com/online/2023-04-04/5f300a0666e92487eb2f0a1156224e9b?AWSAccessKeyId=55BDCC1A9094F50A9E5397734261AACE&amp;Expires=**********&amp;Signature=Nkv9XiqYRch%2BsF2u9F10agurT3c%3D\\\",\\\"deductibleCompensation\\\":\\\"0\\\",\\\"extendedFieldKey\\\":\\\"online/2023-04-04/5f300a0666e92487eb2f0a1156224e9b\\\"}\",\n" +
                "            \"customerOrderNo\":\"************\",\n" +
                "            \"shopNo\":\"ESP0020000042953\",\n" +
                "            \"orderMark\":\"00001100000000000000120000000000000000000000000000000000000000000000000000000000000000000000000000000000000000090000000000000000000000000000000000000000010000000000000000000000000000000000000000000000\"\n" +
                "        },\n" +
                "        \"financeInfo\":{\n" +
                "            \"settlementType\":1\n" +
                "        },\n" +
                "        \"goodsInfos\":[\n" +
                "            {\n" +
                "                \"channelGoodsNo\":\"10032839718863\",\n" +
                "                \"extendProps\":{\n" +
                "                    \"goodsSequenceNo\":\"20613247706200001\"\n" +
                "                },\n" +
                "                \"goodsAmount\":{\n" +
                "                    \"currencyCode\":\"CNY\"\n" +
                "                },\n" +
                "                \"goodsName\":\"麦富迪（Myfoodie）麦富迪狗粮营养森林10kg小型犬成幼犬泰迪比熊通用型20斤 小型犬粮10kg\",\n" +
                "                \"goodsNo\":\"ESG4418370618301\",\n" +
                "                \"goodsPrice\":{\n" +
                "                    \"amount\":189,\n" +
                "                    \"currencyCode\":\"CNY\"\n" +
                "                },\n" +
                "                \"goodsQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"goodsType\":\"1\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"operator\":\"POP-MQ\",\n" +
                "        \"orderCreateTime\":1680601339000,\n" +
                "        \"orderNo\":\"ESL00000020613247706\",\n" +
                "        \"orderType\":\"100\",\n" +
                "        \"productInfos\":[\n" +
                "            {\n" +
                "                \"extendProps\":{\n" +
                "\n" +
                "                },\n" +
                "                \"productAttrs\":{\n" +
                "\n" +
                "                },\n" +
                "                \"productName\":\"中小件商务仓\",\n" +
                "                \"productNo\":\"sc-m-0001\",\n" +
                "                \"productType\":1\n" +
                "            },\n" +
                "            {\n" +
                "                \"extendProps\":{\n" +
                "\n" +
                "                },\n" +
                "                \"parentNo\":\"sc-m-0001\",\n" +
                "                \"productAttrs\":{\n" +
                "\n" +
                "                },\n" +
                "                \"productName\":\"耗材计费\",\n" +
                "                \"productNo\":\"sc-a-0028\",\n" +
                "                \"productType\":2\n" +
                "            }\n" +
                "        ],\n" +
                "        \"refOrderInfo\":{\n" +
                "\n" +
                "        },\n" +
                "        \"shipmentInfo\":{\n" +
                "            \"deliveryType\":1,\n" +
                "            \"endStationName\":\"*鞍山市-海城新东营业部\",\n" +
                "            \"endStationNo\":\"3551\",\n" +
                "            \"expectDeliveryEndTime\":1680601338000,\n" +
                "            \"expectDeliveryStartTime\":1680601338000,\n" +
                "            \"extendProps\":{\n" +
                "                \"selfPickupType\":\"0\",\n" +
                "                \"nextDayAgingPromotion\":\"0\",\n" +
                "                \"marineContraband\":\"1\"\n" +
                "            },\n" +
                "            \"shipperName\":\"京东配送\",\n" +
                "            \"shipperNo\":\"CYS0000010\",\n" +
                "            \"shipperType\":1,\n" +
                "            \"signandHalfReturn\":2\n" +
                "        },\n" +
                "        \"smartPatternInfo\":{\n" +
                "            \"extendProps\":{\n" +
                "                \"expectOutboundTime\":\"2023-04-04 17:42:18\"\n" +
                "            },\n" +
                "            \"stockOperationRule\":{\n" +
                "                \"occupyResult\":1,\n" +
                "                \"occupyWay\":1,\n" +
                "                \"stockType\":1\n" +
                "            },\n" +
                "            \"warehouseRule\":{\n" +
                "                \"cargoOutboundWay\":1\n" +
                "            }\n" +
                "        }\n" +
                "    },\n" +
                "    \"profile\":{\n" +
                "        \"ext\":{\n" +
                "\n" +
                "        },\n" +
                "        \"locale\":\"zh_CN\",\n" +
                "        \"tenantId\":\"1000\",\n" +
                "        \"timeZone\":\"GMT+8\",\n" +
                "        \"traceId\":\"10804997137950707\"\n" +
                "    }\n" +
                "}";
        CreateOutboundOrderMessage createOutboundOrderMessage = JsonUtil.readValue(requestStr, CreateOutboundOrderMessage.class);
        Solution solution = classToTest.getSolutionByOmsInput(createOutboundOrderMessage.getData());
        assertNotNull(solution.getAttributes());
        assertTrue(solution.getAttributes().containsKey("shouldPayMoney"));
    }
    @Test
    public void testIsv() throws Exception {
        String requestStr = "{\n" +
                "    \"data\":{\n" +
                "        \"businessIdentity\":{\n" +
                "            \"businessScene\":\"receive\",\n" +
                "            \"businessStrategy\":\"BOutboundSaleISVSupplyChain\",\n" +
                "            \"businessType\":\"outbound_sale\",\n" +
                "            \"businessUnit\":\"cn_jdl_sc-isv\",\n" +
                "            \"fulfillmentUnit\":\"BOutboundSaleISVSupplyChain\"\n" +
                "        },\n" +
                "        \"businessSolutionInfo\":{\n" +
                "            \"businessSolutionName\":\"中小件商务仓配\",\n" +
                "            \"businessSolutionNo\":\"sc-s-0013\",\n" +
                "            \"productAttrs\":{\n" +
                "\n" +
                "            }\n" +
                "        },\n" +
                "        \"cargoInfos\":[\n" +
                "            {\n" +
                "                \"cargoLevel\":\"100\",\n" +
                "                \"cargoName\":\"[广东移动][附加商品][告知书]\",\n" +
                "                \"cargoNo\":\"EMG4418278468598\",\n" +
                "                \"cargoOccupyQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"cargoQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"cargoSign\":{\n" +
                "                    \"virtualGoods\":\"0\"\n" +
                "                },\n" +
                "                \"extendProps\":{\n" +
                "                    \"cargoRefNo\":\"R1\",\n" +
                "                    \"uniqueCodeCollectionWay\":\"0\",\n" +
                "                    \"sequenceNo\":\"2059938936900001\",\n" +
                "                    \"uniqueLineFlag\":\"EMG4418278468598-100\",\n" +
                "                    \"cargoExtendProps\":\"{}\",\n" +
                "                    \"sellerGoodsSign\":\"EMG4418278468598\"\n" +
                "                },\n" +
                "                \"isvCargoNo\":\"CMCCGDATTACHMENTA06\",\n" +
                "                \"productInfos\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"refGoodsNo\":\"ESG4418423306414\",\n" +
                "                \"refGoodsType\":\"1\",\n" +
                "                \"serialInfos\":[\n" +
                "\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"cargoLevel\":\"100\",\n" +
                "                \"cargoName\":\"[广东移动][白卡][全国]\",\n" +
                "                \"cargoNo\":\"EMG4418197839466\",\n" +
                "                \"cargoOccupyQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"cargoQuantity\":{\n" +
                "                    \"unit\":\"件\",\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"cargoSign\":{\n" +
                "                    \"virtualGoods\":\"0\"\n" +
                "                },\n" +
                "                \"extendProps\":{\n" +
                "                    \"cargoRefNo\":\"R2\",\n" +
                "                    \"uniqueCodeCollectionWay\":\"0\",\n" +
                "                    \"sequenceNo\":\"2059938936900002\",\n" +
                "                    \"uniqueLineFlag\":\"EMG4418197839466-100\",\n" +
                "                    \"cargoExtendProps\":\"{}\",\n" +
                "                    \"sellerGoodsSign\":\"EMG4418197839466\"\n" +
                "                },\n" +
                "                \"isvCargoNo\":\"CMCCGDBK001\",\n" +
                "                \"productInfos\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"refGoodsNo\":\"ESG4418331756601\",\n" +
                "                \"refGoodsType\":\"1\",\n" +
                "                \"serialInfos\":[\n" +
                "\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"channelInfo\":{\n" +
                "            \"channelName\":\"其他\",\n" +
                "            \"channelNo\":\"6\",\n" +
                "            \"channelOperateTime\":1680601336000,\n" +
                "            \"channelShopNo\":\"9908765\",\n" +
                "            \"customerOrderNo\":\"T20230404224906\",\n" +
                "            \"extendProps\":{\n" +
                "                \"espId\":\"20000051678\"\n" +
                "            },\n" +
                "            \"secondLevelChannel\":\"ESP0020000051678\",\n" +
                "            \"secondLevelChannelName\":\"京云仓-便利电华南移动卡\",\n" +
                "            \"systemCaller\":\"JOS\",\n" +
                "            \"systemSubCaller\":\"ISV0020000000629\"\n" +
                "        },\n" +
                "        \"consigneeInfo\":{\n" +
                "            \"addressInfo\":{\n" +
                "                \"address\":\"北京北京市朝阳区六里屯街道丽水嘉园1-105\",\n" +
                "                \"addressGis\":\"北京丽水嘉园1105\",\n" +
                "                \"cityNameGis\":\"朝阳区\",\n" +
                "                \"cityNoGis\":\"72\",\n" +
                "                \"countyNameGis\":\"六里屯街道\",\n" +
                "                \"countyNoGis\":\"55673\",\n" +
                "                \"provinceNameGis\":\"北京\",\n" +
                "                \"provinceNoGis\":\"1\"\n" +
                "            },\n" +
                "            \"consigneeMobile\":\"15941889995\",\n" +
                "            \"consigneeName\":\"王建\",\n" +
                "            \"extendProps\":{\n" +
                "                \"consigneeExtendProps\":\"{}\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"consignorInfo\":{\n" +
                "            \"addressInfo\":{\n" +
                "                \"address\":\"河北省廊坊市固安县北开发区电子产业园锦绣大道与江山路交叉口京东二期3库\",\n" +
                "                \"cityName\":\"廊坊市\",\n" +
                "                \"cityNo\":\"274\",\n" +
                "                \"countyName\":\"固安县\",\n" +
                "                \"countyNo\":\"284\",\n" +
                "                \"provinceName\":\"河北\",\n" +
                "                \"provinceNo\":\"5\"\n" +
                "            },\n" +
                "            \"customerWarehouse\":{\n" +
                "                \"actualWarehouseName\":\"北京公共平台仓7号库\",\n" +
                "                \"actualWarehouseNo\":\"*********\",\n" +
                "                \"actualWarehouseType\":1,\n" +
                "                \"warehouseName\":\"北京公共平台仓7号库\",\n" +
                "                \"warehouseNo\":\"*********\",\n" +
                "                \"warehouseType\":1\n" +
                "            }\n" +
                "        },\n" +
                "        \"customerInfo\":{\n" +
                "            \"account2No\":\"020K940382\",\n" +
                "            \"accountName\":\"辽宁便利电科技有限公司\",\n" +
                "            \"accountNo\":\"EBU4418046574815\"\n" +
                "        },\n" +
                "        \"extendProps\":{\n" +
                "            \"professionType\":\"00\",\n" +
                "            \"reReceiveMode\":\"0\",\n" +
                "            \"soSource\":\"1\",\n" +
                "            \"sellerName\":\"辽宁便利电科技有限公司-移动卡\",\n" +
                "            \"sellerNo\":\"ECP0020000034066\",\n" +
                "            \"tradeInfo\":\"{\\\"orderItemQuantityType\\\":\\\"2\\\"}\",\n" +
                "            \"orderSellModel\":\"1\",\n" +
                "            \"agentSales\":\"1\",\n" +
                "            \"ecpOrderInfo\":\"{\\\"buyerRemark\\\":\\\"【广东移动】拒收需退回入仓，严禁站点报废,\\\",\\\"invoiceInfo\\\":{\\\"invoiceBiz\\\":\\\"0\\\",\\\"invoiceSource\\\":2,\\\"invoiceDetailDTOList\\\":[{}]},\\\"sellerChannelInfo\\\":{},\\\"buyerNo\\\":\\\"bld365365\\\",\\\"afterSaleInfo\\\":{},\\\"paymentType\\\":\\\"0\\\"}\",\n" +
                "            \"fulfillmentInfo\":\"{\\\"logisticsOriginalPackage\\\":\\\"0\\\",\\\"goodWithInvoiceFlag\\\":\\\"0\\\",\\\"trustJDMetrics\\\":\\\"0\\\",\\\"printInfo\\\":\\\"{\\\\\\\"extendPrintInfo\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"CNTemplate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"0\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"thirdPayment\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"null\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"receiveMode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"null\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"sendMode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"null\\\\\\\\\\\\\\\"}\\\\\\\"}\\\",\\\"extendedField\\\":\\\"https://fulfillment-extend-field-info.s3-internal.cn-north-1.jdcloud-oss.com/online/2023-04-04/132669026e1e5115de3da16d0b1eac2b?AWSAccessKeyId=55BDCC1A9094F50A9E5397734261AACE&amp;Expires=1689241336&amp;Signature=36gX948SMYRFIH%2F1Kr1Ofrng51Q%3D\\\",\\\"customField\\\":\\\"{}\\\",\\\"bdSellerNo\\\":\\\"0030001\\\",\\\"extendedFieldKey\\\":\\\"online/2023-04-04/132669026e1e5115de3da16d0b1eac2b\\\"}\",\n" +
                "            \"orderMark\":\"00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\"\n" +
                "        },\n" +
                "        \"financeInfo\":{\n" +
                "            \"settlementType\":1\n" +
                "        },\n" +
                "        \"goodsInfos\":[\n" +
                "            {\n" +
                "                \"channelGoodsNo\":\"ESG4418331756601\",\n" +
                "                \"extendProps\":{\n" +
                "                    \"goodsRefNo\":\"R2\",\n" +
                "                    \"goodsSequenceNo\":\"20599389369200001\"\n" +
                "                },\n" +
                "                \"goodsAmount\":{\n" +
                "                    \"currencyCode\":\"CNY\"\n" +
                "                },\n" +
                "                \"goodsName\":\"[广东移动][白卡][全国]\",\n" +
                "                \"goodsNo\":\"ESG4418331756601\",\n" +
                "                \"goodsPrice\":{\n" +
                "                    \"currencyCode\":\"CNY\"\n" +
                "                },\n" +
                "                \"goodsQuantity\":{\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"goodsType\":\"1\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"channelGoodsNo\":\"ESG4418423306414\",\n" +
                "                \"extendProps\":{\n" +
                "                    \"goodsRefNo\":\"R1\",\n" +
                "                    \"goodsSequenceNo\":\"20599389369200002\"\n" +
                "                },\n" +
                "                \"goodsAmount\":{\n" +
                "                    \"currencyCode\":\"CNY\"\n" +
                "                },\n" +
                "                \"goodsName\":\"[广东移动][附加商品][告知书]\",\n" +
                "                \"goodsNo\":\"ESG4418423306414\",\n" +
                "                \"goodsPrice\":{\n" +
                "                    \"currencyCode\":\"CNY\"\n" +
                "                },\n" +
                "                \"goodsQuantity\":{\n" +
                "                    \"value\":1\n" +
                "                },\n" +
                "                \"goodsType\":\"1\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"operator\":\"bld365365\",\n" +
                "        \"orderCreateTime\":1680601336000,\n" +
                "        \"orderNo\":\"ESL00000020599389369\",\n" +
                "        \"orderType\":\"100\",\n" +
                "        \"productInfos\":[\n" +
                "            {\n" +
                "                \"extendProps\":{\n" +
                "\n" +
                "                },\n" +
                "                \"productAttrs\":{\n" +
                "\n" +
                "                },\n" +
                "                \"productName\":\"中小件商务仓\",\n" +
                "                \"productNo\":\"sc-m-0001\",\n" +
                "                \"productType\":1\n" +
                "            }\n" +
                "        ],\n" +
                "        \"refOrderInfo\":{\n" +
                "            \"extendProps\":{\n" +
                "\n" +
                "            }\n" +
                "        },\n" +
                "        \"shipmentInfo\":{\n" +
                "            \"deliveryType\":1,\n" +
                "            \"expectDeliveryEndTime\":1680601336000,\n" +
                "            \"expectDeliveryStartTime\":1680601336000,\n" +
                "            \"extendProps\":{\n" +
                "                \"activeCityDelivery\":\"1\",\n" +
                "                \"shipmentExtendProps\":\"{\\\"reReceiveInfo\\\":{},\\\"partialPayment\\\":\\\"0\\\",\\\"siteShipType\\\":\\\"0\\\",\\\"needExternalWaybillOnly\\\":\\\"0\\\"}\",\n" +
                "                \"expectCityDelivery\":\"1\",\n" +
                "                \"transportMode\":\"0\",\n" +
                "                \"lastDeliveryType\":\"2\",\n" +
                "                \"marineContraband\":\"1\"\n" +
                "            },\n" +
                "            \"serviceRequirements\":{\n" +
                "                \"unpackingFlag\":\"0\",\n" +
                "                \"heavyGoodsUpstairsFlag\":\"0\",\n" +
                "                \"insured\":\"false\",\n" +
                "                \"hidePrivacyType\":\"0\",\n" +
                "                \"firstWaybillIssue\":\"0\",\n" +
                "                \"specialSignRequirements\":\"{\\\"activationService\\\":\\\"1\\\",\\\"signType\\\":\\\"0\\\"}\",\n" +
                "                \"packageProduceMode\":\"0\"\n" +
                "            },\n" +
                "            \"shipperName\":\"京东配送\",\n" +
                "            \"shipperNo\":\"CYS0000010\",\n" +
                "            \"shipperType\":1,\n" +
                "            \"signandHalfReturn\":1,\n" +
                "            \"waybillNoFetchType\":1\n" +
                "        },\n" +
                "        \"smartPatternInfo\":{\n" +
                "            \"extendProps\":{\n" +
                "                \"expectOutboundTime\":\"2023-04-04 17:42:16\",\n" +
                "                \"smartPatternExtendProps\":\"{\\\"clpsCainiaoElecFlag\\\":\\\"0\\\",\\\"tmDeliveryByJgFlag\\\":\\\"0\\\",\\\"packageSignFlag\\\":\\\"0\\\",\\\"tmOrderFlag\\\":\\\"0\\\"}\",\n" +
                "                \"orderTemporaryStorage\":\"1\"\n" +
                "            },\n" +
                "            \"stockOperationRule\":{\n" +
                "                \"occupyResult\":1,\n" +
                "                \"occupyResultProcessWay\":1,\n" +
                "                \"occupyWay\":1,\n" +
                "                \"stockType\":1\n" +
                "            },\n" +
                "            \"warehouseRule\":{\n" +
                "                \"cargoOutboundWay\":2\n" +
                "            }\n" +
                "        }\n" +
                "    },\n" +
                "    \"profile\":{\n" +
                "        \"ext\":{\n" +
                "\n" +
                "        },\n" +
                "        \"locale\":\"zh_CN\",\n" +
                "        \"tenantId\":\"1000\",\n" +
                "        \"timeZone\":\"GMT+8\",\n" +
                "        \"traceId\":\"10804994638034675\"\n" +
                "    }\n" +
                "}";
        CreateOutboundOrderMessage createOutboundOrderMessage = JsonUtil.readValue(requestStr, CreateOutboundOrderMessage.class);
        Solution solution = classToTest.getSolutionByOmsInput(createOutboundOrderMessage.getData());
        assertNotNull(solution.getAttributes());
        assertTrue(solution.getAttributes().containsKey("insured"));
        Shipment shipment = classToTest.getShipmentByOmsInput(createOutboundOrderMessage.getData(), FulfillmentInfo.builder().build(), EcpOrderInfo.builder().build(), null);
        assertEquals(shipment.getWaybillNoFetchType().intValue(), WaybillNoFetchTypeEnum.NO_WAYBILL.getCode());
    }
    @Test
    public void testAppraisal() throws Exception {
        String requestStr = "{\"data\":{\"businessIdentity\":{\"businessScene\":\"receive\",\"businessType\":\"integrated_services\",\"businessUnit\":\"cn_jdl_ka-jdr-appraisal\"},\"cargoInfos\":[{\"cargoLevel\":\"100\",\"cargoName\":\"蜡笔小新正版\",\"cargoNo\":\"EMG4398060931279\",\"cargoOccupyQuantity\":{\"unit\":\"件\",\"value\":0},\"cargoQuantity\":{\"unit\":\"件\",\"value\":1.0},\"extendProps\":{\"sequenceNo\":\"1000084537600001\",\"uniqueLineFlag\":\"EMG4398060931279-100\",\"sellerGoodsSign\":\"************\"},\"isvCargoNo\":\"************\",\"productInfos\":[],\"refGoodsNo\":\"ESG4398060770896\",\"refGoodsType\":\"1\",\"serialInfos\":[]}],\"channelInfo\":{\"channelName\":\"京东商城\",\"channelNo\":\"1\",\"channelOperateTime\":1679402171723,\"channelOrderNo\":\"***********\",\"channelShopNo\":\"126157\",\"customerOrderNo\":\"***********\",\"extendProps\":{\"espId\":\"26157\"},\"secondLevelChannel\":\"ESP0000000026157\",\"secondLevelChannelName\":\"正品鉴定\",\"systemCaller\":\"POP\"},\"consigneeInfo\":{\"addressInfo\":{},\"receiveWarehouse\":{\"actualWarehouseName\":\"55测试仓\",\"actualWarehouseNo\":\"*********\",\"actualWarehouseType\":1,\"warehouseName\":\"55测试仓\",\"warehouseNo\":\"*********\",\"warehouseType\":1}},\"consignorInfo\":{\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区京东总部大厦\"},\"consignorMobile\":\"***********\",\"consignorName\":\"发件人丫丫\"},\"customerInfo\":{\"accountName\":\"正品鉴定测试事业部\",\"accountNo\":\"EBU4398046538211\"},\"extendProps\":{\"orderSellModel\":\"1\",\"ecpOrderInfo\":\"{\\\"jdlMark\\\":\\\"0\\\",\\\"paymentType\\\":\\\"0\\\"}\",\"soSource\":\"2\",\"sellerName\":\"正品鉴定\",\"sellerNo\":\"ECP0000000024807\",\"tradeInfo\":\"{\\\"orderItemQuantityType\\\":\\\"1\\\"}\",\"appraisalInstitutionName\":\"鉴定服务商名称\",\"appraisalInstitutionNo\":\"8916\",\"appraiseAddress\":\"北京市亦庄区京东总部4号楼一楼\",\"fulfillmentInfo\":\"{}\"},\"financeInfo\":{\"settlementType\":1},\"goodsInfos\":[{\"channelGoodsNo\":\"************\",\"extendProps\":{\"goodsSequenceNo\":\"*****************\"},\"goodsAmount\":{\"currencyCode\":\"CNY\"},\"goodsName\":\"蜡笔小新正版\",\"goodsNo\":\"ESG4398060770896\",\"goodsPrice\":{\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"件\",\"value\":1.0},\"goodsType\":\"1\"}],\"operator\":\"system\",\"orderCreateTime\":*************,\"orderNo\":\"ITS00000010000845376\",\"orderType\":\"800\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productNo\":\"d-a-0003\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"JDV000900213184\"},\"remark\":\"\",\"shipmentInfo\":{\"pickupType\":1,\"shipperNo\":\"CYS0000010\"},\"smartPatternInfo\":{}},\"profile\":{\"ext\":{},\"locale\":\"zh_CN\",\"tenantId\":\"1000\",\"timeZone\":\"GMT+8\",\"traceId\":\"zpjdOrder5125232883376\"}}";
        CreateOutboundOrderMessage createOutboundOrderMessage = JsonUtil.readValue(requestStr, CreateOutboundOrderMessage.class);
        Solution solution = classToTest.getSolutionByOmsInput(createOutboundOrderMessage.getData());
        assertNull(solution);
    }

    @Test
    public void testWarehouseFlag() throws Exception {
        String requestStr = "{\n" +
                "    \"data\": {\n" +
                "        \"businessIdentity\": {\n" +
                "            \"businessScene\": \"receive\",\n" +
                "            \"businessType\": \"integrated_services\",\n" +
                "            \"businessUnit\": \"cn_jdl_ka-jdr-appraisal\"\n" +
                "        },\n" +
                "        \"cargoInfos\": [\n" +
                "            {\n" +
                "                \"cargoLevel\": \"100\",\n" +
                "                \"cargoName\": \"蜡笔小新正版\",\n" +
                "                \"cargoNo\": \"EMG4398060931279\",\n" +
                "                \"cargoOccupyQuantity\": {\n" +
                "                    \"unit\": \"件\",\n" +
                "                    \"value\": 0\n" +
                "                },\n" +
                "                \"cargoQuantity\": {\n" +
                "                    \"unit\": \"件\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                \"extendProps\": {\n" +
                "                    \"sequenceNo\": \"1000084537600001\",\n" +
                "                    \"uniqueLineFlag\": \"EMG4398060931279-100\",\n" +
                "                    \"sellerGoodsSign\": \"************\"\n" +
                "                },\n" +
                "                \"isvCargoNo\": \"************\",\n" +
                "                \"productInfos\": [],\n" +
                "                \"refGoodsNo\": \"ESG4398060770896\",\n" +
                "                \"refGoodsType\": \"1\",\n" +
                "                \"serialInfos\": []\n" +
                "            }\n" +
                "        ],\n" +
                "        \"channelInfo\": {\n" +
                "            \"channelName\": \"京东商城\",\n" +
                "            \"channelNo\": \"1\",\n" +
                "            \"channelOperateTime\": 1679402171723,\n" +
                "            \"channelOrderNo\": \"***********\",\n" +
                "            \"channelShopNo\": \"126157\",\n" +
                "            \"customerOrderNo\": \"***********\",\n" +
                "            \"extendProps\": {\n" +
                "                \"espId\": \"26157\"\n" +
                "            },\n" +
                "            \"secondLevelChannel\": \"ESP0000000026157\",\n" +
                "            \"secondLevelChannelName\": \"正品鉴定\",\n" +
                "            \"systemCaller\": \"POP\"\n" +
                "        },\n" +
                "        \"consigneeInfo\": {\n" +
                "            \"addressInfo\": {},\n" +
                "            \"receiveWarehouse\": {\n" +
                "                \"actualWarehouseName\": \"55测试仓\",\n" +
                "                \"actualWarehouseNo\": \"*********\",\n" +
                "                \"actualWarehouseType\": 1,\n" +
                "                \"warehouseName\": \"55测试仓\",\n" +
                "                \"warehouseNo\": \"*********\",\n" +
                "                \"warehouseType\": 1,\n" +
                "                \"extendProps\": {\n" +
                "                    \"warehouseFlag\": \"1\"\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "        \"consignorInfo\": {\n" +
                "            \"addressInfo\": {\n" +
                "                \"address\": \"北京市大兴区亦庄经济开发区京东总部大厦\"\n" +
                "            },\n" +
                "            \"consignorMobile\": \"***********\",\n" +
                "            \"consignorName\": \"发件人丫丫\"\n" +
                "        },\n" +
                "        \"customerInfo\": {\n" +
                "            \"accountName\": \"正品鉴定测试事业部\",\n" +
                "            \"accountNo\": \"EBU4398046538211\"\n" +
                "        },\n" +
                "        \"extendProps\": {\n" +
                "            \"orderSellModel\": \"1\",\n" +
                "            \"ecpOrderInfo\": \"{\\\"jdlMark\\\":\\\"0\\\",\\\"paymentType\\\":\\\"0\\\"}\",\n" +
                "            \"soSource\": \"2\",\n" +
                "            \"sellerName\": \"正品鉴定\",\n" +
                "            \"sellerNo\": \"ECP0000000024807\",\n" +
                "            \"tradeInfo\": \"{\\\"orderItemQuantityType\\\":\\\"1\\\"}\",\n" +
                "            \"appraisalInstitutionName\": \"鉴定服务商名称\",\n" +
                "            \"appraisalInstitutionNo\": \"8916\",\n" +
                "            \"appraiseAddress\": \"北京市亦庄区京东总部4号楼一楼\",\n" +
                "            \"fulfillmentInfo\": \"{}\"\n" +
                "        },\n" +
                "        \"financeInfo\": {\n" +
                "            \"settlementType\": 1\n" +
                "        },\n" +
                "        \"goodsInfos\": [\n" +
                "            {\n" +
                "                \"channelGoodsNo\": \"************\",\n" +
                "                \"extendProps\": {\n" +
                "                    \"goodsSequenceNo\": \"*****************\"\n" +
                "                },\n" +
                "                \"goodsAmount\": {\n" +
                "                    \"currencyCode\": \"CNY\"\n" +
                "                },\n" +
                "                \"goodsName\": \"蜡笔小新正版\",\n" +
                "                \"goodsNo\": \"ESG4398060770896\",\n" +
                "                \"goodsPrice\": {\n" +
                "                    \"currencyCode\": \"CNY\"\n" +
                "                },\n" +
                "                \"goodsQuantity\": {\n" +
                "                    \"unit\": \"件\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                \"goodsType\": \"1\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"operator\": \"system\",\n" +
                "        \"orderCreateTime\": *************,\n" +
                "        \"orderNo\": \"ITS00000010000845376\",\n" +
                "        \"orderType\": \"800\",\n" +
                "        \"productInfos\": [\n" +
                "            {\n" +
                "                \"productNo\": \"ed-m-0001\",\n" +
                "                \"productType\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"productNo\": \"d-a-0003\",\n" +
                "                \"productType\": 2\n" +
                "            }\n" +
                "        ],\n" +
                "        \"refOrderInfo\": {\n" +
                "            \"waybillNo\": \"JDV000900213184\"\n" +
                "        },\n" +
                "        \"remark\": \"\",\n" +
                "        \"shipmentInfo\": {\n" +
                "            \"pickupType\": 1,\n" +
                "            \"shipperNo\": \"CYS0000010\"\n" +
                "        },\n" +
                "        \"smartPatternInfo\": {}\n" +
                "    },\n" +
                "    \"profile\": {\n" +
                "        \"ext\": {},\n" +
                "        \"locale\": \"zh_CN\",\n" +
                "        \"tenantId\": \"1000\",\n" +
                "        \"timeZone\": \"GMT+8\",\n" +
                "        \"traceId\": \"zpjdOrder5125232883376\"\n" +
                "    }\n" +
                "}";
        CreateOutboundOrderMessage createOutboundOrderMessage = JsonUtil.readValue(requestStr, CreateOutboundOrderMessage.class);
        Consignee consignee = classToTest.getConsigneeByOmsInput(createOutboundOrderMessage.getData());
    }



    @Test
    public void getCargosByOmsInputTest() throws Exception {

        final Class<ReceiveOrderTranslator> aClass = ReceiveOrderTranslator.class;
        final ReceiveOrderTranslator instance = aClass.newInstance();
        final Method getCargosByOmsInput = aClass.getDeclaredMethod("getCargosByOmsInput", boolean.class, List.class);
        getCargosByOmsInput.setAccessible(true);

        @SuppressWarnings("unchecked")
        final List<Cargo> invoke = (List<Cargo>) getCargosByOmsInput.invoke(instance, true, new ArrayList<CargoInfo>() {{
            add(new CargoInfo() {{
                setCargoOccupyQuantity(new QuantityInfo());
                setCargoQuantity(new QuantityInfo());
                setExtendProps(new HashMap<String, String>() {{
                    put("sellerCustomPictureUrl", "sellerCustomPictureUrl");
                    put("cargoBrand", "cargoBrand");
                }});
                setProductInfos(Collections.emptyList());
            }});
        }});
        Assert.assertTrue(CollectionUtils.isNotEmpty(invoke));
    }

    @Test
    public void testGetFulfillment() throws Exception {
        final Class<ReceiveOrderTranslator> aClass = ReceiveOrderTranslator.class;
        final ReceiveOrderTranslator instance = aClass.newInstance();
        ReflectionTestUtils.setField(instance, "eclpJimkvLoadServiceAcl", eclpJimkvLoadServiceAcl);
        final Method getFulfillment = aClass.getDeclaredMethod("getFulfillment",
                OrderData.class, FulfillmentInfo.class, PrintInfo.class, PrintExtendInfo.class, FulfillmentFlag.class, EcpOrderInfo.class);
        getFulfillment.setAccessible(true);

        when(eclpJimkvLoadServiceAcl.load(anyString())).thenReturn("{}");
        final Fulfillment invoke = (Fulfillment) getFulfillment.invoke(instance,  OrderData.builder()
                        .fulfillmentInfo(new cn.jdl.oms.core.model.FulfillmentInfo(){{
                            setExtendProps(new HashMap<String, String>(){{
                                put("sellerCustomPictureUrl", "sellerCustomPictureUrl");
                                put("adventSafeDays", "adventSafeDays");
                            }});
                        }})
                        .smartPatternInfo(new SmartPatternInfo(){{
                            setWarehouseRule(new WarehouseRule(){{
                                setCargoOutboundWay(3);
                            }});
                            setStockOperationRule(new StockOperationRule());
                        }})

                        .build(),
                FulfillmentInfo.builder()
                        .customField("jfs/kveclp/0/1695191247900/4790d1c214e855efb5d5424ecb8f37ff")
                        .build(),
                PrintInfo.builder().build(),
                PrintExtendInfo.builder().build(),
                FulfillmentFlag.builder().build(),
                EcpOrderInfo.builder().build());
        Assert.assertNotNull(invoke.getSellerCustomPictureUrl());
        Assert.assertNotNull(invoke.getAdventSafeDays());
        Assert.assertNull( invoke.getKaExtensionPoint());
    }

    @Test
    public void testShipmentTpWaybill() throws Exception {
        String orderData = TestFileLoader.loadTestFile("data/outbound/model/orderData.json");
        CreateOutboundOrderMessage message = JsonUtil.readValue(orderData, CreateOutboundOrderMessage.class);
        Shipment shipment = classToTest.getShipmentByOmsInput(message.getData(), FulfillmentInfo.builder().build(), EcpOrderInfo.builder().build(), null);
        assertEquals(message.getData().getRefOrderInfo().getExtendProps().get("tpWaybillNo"), shipment.getTpWaybill());
    }

    @Test
    public void getChannelByOmsInputTest() throws Exception {
        String orderData = TestFileLoader.loadTestFile("data/outbound/model/orderData.json");
        CreateOutboundOrderMessage message = JsonUtil.readValue(orderData, CreateOutboundOrderMessage.class);
        classToTest.getChannelByOmsInput(message.getData(), EcpOrderInfo.builder().build(), FulfillmentInfo.builder().build());
    }

    @Test
    public void getOrderClobTest(){
        Integer orderClob = classToTest.getOrderClob(new OrderData());
        Assert.assertNull(orderClob);
    }
}
