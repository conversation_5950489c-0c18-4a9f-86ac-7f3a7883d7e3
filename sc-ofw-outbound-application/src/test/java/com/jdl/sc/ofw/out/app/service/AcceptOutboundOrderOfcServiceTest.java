package com.jdl.sc.ofw.out.app.service;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.AcceptOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.AcceptOutboundOrderOfcResponse;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcRequest;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.SmartPatternInfo;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.app.service.oms.AcceptOutboundOrderOfcServiceImpl;
import com.jdl.sc.ofw.out.app.service.oms.CancelOutboundOrderOfcServiceImpl;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class AcceptOutboundOrderOfcServiceTest {

    @InjectMocks
    AcceptOutboundOrderOfcServiceImpl acceptOutboundOrderOfcService;

    @Mock
    private WorkflowControlService workflowControlService;

    @Mock
    private OutboundService outboundService;

    @Mock
    private ReceiveOrderTranslator receiveOrder;

    RequestProfile requestProfile = new RequestProfile();

    AcceptOutboundOrderOfcRequest acceptOutboundOrderOfcRequest;

    @Before
    public void setup() {
        String requestJson = "{\"businessIdentity\":{\"businessScene\":\"modify\",\"businessType\":\"outbound_sale\",\"businessUnit\":\"cn_jdl_sc-isv\",\"fulfillmentUnit\":\"JDL.ORDER.SC_ISV\"},\"cargoInfos\": [{\"cargoName\": \"移动硬盘盒\",\"cargoNo\": \"EMG4418049432484\",\"cargoType\": null,\"cargoLevel\": \"100\",\"cargoLevelName\": null,\"cargoVolume\": {\"value\": 475.2,\"unit\": \"CM3\"},\"cargoWeight\": {\"value\": 0.14,\"unit\": \"KG\"},\"cargoQuantity\": {\"value\": 1,\"unit\": \"件\"},\"cargoInnerQuantity\": null,\"cargoOccupyQuantity\": {\"value\": 1,\"unit\": \"件\"},\"cargoActualQuantity\": null,\"cargoDimension\": {\"length\": null,\"width\": null,\"height\": null,\"unit\": null},\"cargoRemark\": null,\"polluteSign\": null,\"cargoAttachmentInfos\": null,\"batchInfos\": null,\"refGoodsType\": \"1\",\"refGoodsNo\": \"ESG4418078057439\",\"isvCargoNo\": \"***********\",\"extendProps\": {\"preSale\": \"false\",\"uniqueCodeCollectionWay\": \"0\",\"sequenceNo\": \"2063733264600001\",\"uniqueLineFlag\": \"EMG4418049432484-100\",\"sellerGoodsSign\": \"30848\"},\"serialInfos\": null,\"productInfos\": null,\"cargoSource\": null,\"cargoPrice\": null,\"cargoLineNo\": null,\"cargoVulnerable\": null,\"cargoSign\": null}],\"consignorInfo\": {\"customerWarehouse\": { \"warehouseNo\": \"110007625\", \"warehouseType\": 1, \"warehouseName\": \"广州公共平台仓1号库\", \"actualWarehouseNo\": \"110007625\", \"actualWarehouseType\": 1, \"actualWarehouseName\": \"广州公共平台仓1号库\", \"planWarehouseNo\": null, \"planWarehouseType\": null, \"planWarehouseName\": null, \"warehouseSource\": \"1\", \"distributionNo\": null, \"warehouseAttrs\": null }},\"operator\":\"SYSTEM\",\"orderNo\":\"ESL00000010000210218\"}";
        acceptOutboundOrderOfcRequest = JsonUtil.readValueSafe(requestJson, AcceptOutboundOrderOfcRequest.class);
    }

    @Test
    public void testAccept() {
        OutboundModifyResponse modifyResponse = OutboundModifyResponse.builder()
                .code(ResponseConstant.SUCCESS_CODE)
                .message("")
                .build();
        when(outboundService.modify(any())).thenReturn(modifyResponse);
        SmartPatternInfo smartPatternInfo = new SmartPatternInfo();
        SmartPatternInfo.CrossDockRule crossDockRule = new SmartPatternInfo.CrossDockRule();
        crossDockRule.setCrossDockType(0);
        smartPatternInfo.setCrossDockRule(crossDockRule);
        acceptOutboundOrderOfcRequest.setSmartPatternInfo(smartPatternInfo);
        acceptOutboundOrderOfcService.acceptOrder(requestProfile, acceptOutboundOrderOfcRequest);
        acceptOutboundOrderOfcRequest.setSmartPatternInfo(null);
        acceptOutboundOrderOfcService.acceptOrder(requestProfile, acceptOutboundOrderOfcRequest);


    }

}
