package com.jdl.sc.ofw.out.app.listener.decrypt;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.LasDecryptMessage;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.pojo.OrderPausePojo;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.function.Supplier;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class LasDecryptMessageListenerTest {

	@Mock
	private OutboundModelRepository outboundModelRepository;

	@InjectMocks
	private LasDecryptMessageListener lasDecryptMessageListener;

	@Mock
	private OutboundModelService modelService;

    @Mock
    private OrderHoldHandler orderHoldHandler;

    @Before
    public void before(){
        when(orderHoldHandler.queryOne(any(),any(),any())).thenReturn(OrderPausePojo.builder().build());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testValidIllegalArgumentAllBlank() {
        // generated by JoyCoder taskId d4c8361fb3cd
        LasDecryptMessageListener lasDecryptMessageListener = new LasDecryptMessageListener();
        Supplier<String> supplier1 = () -> "";
        Supplier<String> supplier2 = () -> "";

        lasDecryptMessageListener.validIllegalArgument("Warning Message", supplier1, supplier2);
        
    }



    @Test
    public void testValidIllegalArgumentNotAllBlank() {
        // generated by JoyCoder taskId d4c8361fb3cd
        LasDecryptMessageListener lasDecryptMessageListener = new LasDecryptMessageListener();
        Supplier<String> supplier1 = () -> "Not Blank";
        Supplier<String> supplier2 = () -> "";
        
        lasDecryptMessageListener.validIllegalArgument("Warning Message", supplier1, supplier2);
        
        // No exception should be thrown
        assertTrue(true);
    }

    @Test
    public void testValidIllegalArgumentMixedBlankAndNonBlank() {
        // generated by JoyCoder taskId d4c8361fb3cd
        LasDecryptMessageListener lasDecryptMessageListener = new LasDecryptMessageListener();
        Supplier<String> supplier1 = () -> "Not Blank";
        Supplier<String> supplier2 = () -> "";
        
        lasDecryptMessageListener.validIllegalArgument("Warning Message", supplier1, supplier2);
        
        // No exception should be thrown
        assertTrue(true);
    }

    @Test(expected = RuntimeException.class)
    public void testInvalidMessage() throws Exception {
        lasDecryptMessageListener.onMessage("JsonUtil.toJson(decryptMessage)");
    }

    @Test(expected = RuntimeException.class)
    public void testInvalidMessage1() throws Exception {
        LasDecryptMessage decryptMessage = LasDecryptMessage.builder()
                .salesPlatformOrderNo("123456").orderNo("ESL123456").receiverInfo(LasDecryptMessage.PersonInfo.builder()
                        .phone("111").mobile("111").detailAddress("1q2w3e4r").build()).build();
        lasDecryptMessageListener.onMessage(JsonUtil.toJson(decryptMessage));
    }

    @Test
    public void testOnMessageSuccess() throws Exception {
        // generated by JoyCoder taskId d4c8361fb3cd
        LasDecryptMessage decryptMessage = LasDecryptMessage.builder()
                .salesPlatformOrderNo("123456").orderNo("ESL123456").receiverInfo(LasDecryptMessage.PersonInfo.builder()
                .phone("111").name("lisi").mobile("111").detailAddress("1q2w3e4r").build()).build();
        
        OrderFulfillmentModel model = mockModel();

        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        doNothing().when(modelService).modify(any(),any());

        lasDecryptMessageListener.onMessage(JsonUtil.toJson(decryptMessage));
    }

    @Test
    public void testOnMessageOrderNotFound() throws Exception {
        // generated by JoyCoder taskId d4c8361fb3cd
        LasDecryptMessage decryptMessage = LasDecryptMessage.builder()
                .salesPlatformOrderNo("123456").orderNo("ESL123456").receiverInfo(LasDecryptMessage.PersonInfo.builder()
                        .phone("111").name("lisi").mobile("111").detailAddress("1q2w3e4r").build()).build();

        lasDecryptMessageListener.onMessage(JsonUtil.toJsonSafe(decryptMessage));
    }

    @Test
    public void testOnMessageModelNotFound() throws Exception {
        // generated by JoyCoder taskId d4c8361fb3cd
        LasDecryptMessage decryptMessage = LasDecryptMessage.builder()
                .salesPlatformOrderNo("123456").orderNo("ESL123456").receiverInfo(LasDecryptMessage.PersonInfo.builder()
                        .phone("111").name("lisi").mobile("111").detailAddress("1q2w3e4r").build()).build();
        when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(null);

        lasDecryptMessageListener.onMessage(JsonUtil.toJsonSafe(decryptMessage));
    }

    private OrderFulfillmentModel mockModel() {
        return OrderFulfillmentModel.builder()
                .batrixInfo(BatrixInfo.builder().businessUnit("unit").build())
                .customer(Customer.builder().accountNo("EBU0001").build())
                .orderStatus(OrderStatusEnum.INIT)
                .shipment(Shipment.builder().shipperType(ShipperTypeEnum.JDPS).deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.CAI_NIAO).build())
                .build();
    }
}