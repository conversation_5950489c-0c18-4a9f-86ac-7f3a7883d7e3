package com.jdl.sc.ofw.out.app.listener.ka;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MujiOrderCenterListenerTest {
    @Mock
    private OutboundService mockOutboundService;

    @Mock
    private ReceiveOrderTranslator mockReceiveOrderTranslator;

    @InjectMocks
    private MujiOrderCenterListener mujiOrderCenterListener;

    @Before
    public void setUp() {
        mockReceiveOrderTranslator = new ReceiveOrderTranslator();
        mujiOrderCenterListener = new MujiOrderCenterListener(mockOutboundService, mockReceiveOrderTranslator, "businessType");
    }

    @Test
    public void testConsumer() {
        mujiOrderCenterListener.onMessage("{\"data\":{\"businessIdentity\":{\"businessScene\":\"receive\",\"businessStrategy\":\"JDRAppraisalSupplyChain\",\"businessType\":\"outbound_sale\",\"businessUnit\":\"cn_jdl_ka-jdr-appraisal-sale\",\"fulfillmentUnit\":\"JDRAppraisalSupplyChain\"},\"businessSolutionInfo\":{\"businessSolutionName\":\"经济仓配2.0\",\"businessSolutionNo\":\"sc-s-0016\",\"productAttrs\":{}},\"cargoInfos\":[{\"cargoLevel\":\"100\",\"cargoName\":\"富士（FUJIFILM） 拍立得相机 Instax mini90一次成像复古款迷你胶片相机 双重曝光\",\"cargoNo\":\"EMG4418366920544\",\"cargoOccupyQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"extendProps\":{\"sequenceNo\":\"2059314986400001\",\"uniqueLineFlag\":\"EMG4418366920544-100\",\"sellerGoodsSign\":\"**************\",\"serialNos\":\"[{\\\"serialNo\\\":\\\"3954500000491642\\\"}]\"},\"isvCargoNo\":\"**************\",\"productInfos\":[],\"refGoodsNo\":\"ESG4418473289072\",\"refGoodsType\":\"1\",\"serialInfos\":[{\"serialNo\":\"3954500000491642\"}]}],\"channelInfo\":{\"channelName\":\"京东商城\",\"channelNo\":\"1\",\"channelOperateTime\":1679736906000,\"channelOrderNo\":\"************\",\"channelShopNo\":\"12254471\",\"customerOrderNo\":\"************\",\"extendProps\":{\"espId\":\"***********\"},\"secondLevelChannel\":\"ESP00***********\",\"secondLevelChannelName\":\"京严数码买手店\",\"systemCaller\":\"POP\"},\"consigneeInfo\":{\"addressInfo\":{\"address\":\"山东青岛市市南区香港中路街道漳州二路19号中环国际广场A座2805号众乐教育\",\"addressGis\":\"漳州二路19号中环国际广场A座2805号众乐教育\",\"cityNameGis\":\"青岛市\",\"cityNo\":\"1007\",\"cityNoGis\":\"1007\",\"countyNameGis\":\"市南区\",\"countyNo\":\"3521\",\"countyNoGis\":\"3521\",\"provinceNameGis\":\"山东\",\"provinceNo\":\"13\",\"provinceNoGis\":\"13\",\"townNameGis\":\"香港中路街道\",\"townNo\":\"59753\",\"townNoGis\":\"59753\"},\"consigneeMobile\":\"13953220199\",\"consigneeName\":\"刘先生\",\"consigneePhone\":\"13953220199\"},\"consignorInfo\":{\"addressInfo\":{\"address\":\"上海市青浦区重固镇天瑞路88号门\",\"cityName\":\"青浦区\",\"cityNo\":\"2833\",\"countyName\":\"重固镇\",\"countyNo\":\"51955\",\"provinceName\":\"上海\",\"provinceNo\":\"2\"},\"customerWarehouse\":{\"actualWarehouseName\":\"上海综合KA仓11号库\",\"actualWarehouseNo\":\"*********\",\"actualWarehouseType\":1,\"warehouseName\":\"上海综合KA仓11号库\",\"warehouseNo\":\"*********\",\"warehouseType\":1}},\"customerInfo\":{\"accountName\":\"金乡县京选电子产品经营部\",\"accountNo\":\"EBU4418054951127\"},\"extendProps\":{\"settlementType\":\"1\",\"identificationFlag\":\"2\",\"soSource\":\"2\",\"sellerName\":\"金乡县京选电子产品经营部\",\"tradeInfo\":\"{\\\"orderItemQuantityType\\\":\\\"1\\\"}\",\"sellerNo\":\"ECP0020008343169\",\"appraisalInstitutionName\":\"玄豹检测服务商\",\"warehouseNo\":\"*********\",\"ecpOrder\":\"{\\\"jdlMark\\\":\\\"0\\\",\\\"paymentType\\\":\\\"0\\\"}\",\"orderSellModel\":\"1\",\"businessTranObject\":\"1\",\"ecpOrderInfo\":\"{\\\"buyerNo\\\":\\\"system\\\",\\\"paymentType\\\":\\\"0\\\",\\\"jdlMark\\\":\\\"0\\\"}\",\"accountNo\":\"EBU4418054951127\",\"outboundOrderSyncEclp\":\"1\",\"appraisalInstitutionNo\":\"********\",\"fulfillmentInfo\":\"{\\\"logisticsOriginalPackage\\\":\\\"0\\\"}\",\"customerOrderNo\":\"************\",\"shopNo\":\"ESP00***********\"},\"financeInfo\":{\"settlementType\":1},\"goodsInfos\":[{\"channelGoodsNo\":\"**************\",\"extendProps\":{\"goodsSequenceNo\":\"*****************\"},\"goodsAmount\":{\"currencyCode\":\"CNY\"},\"goodsName\":\"富士（FUJIFILM） 拍立得相机 Instax mini90一次成像复古款迷你胶片相机 双重曝光\",\"goodsNo\":\"ESG4418473289072\",\"goodsPrice\":{\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"件\",\"value\":1},\"goodsType\":\"1\"}],\"operator\":\"system\",\"orderCreateTime\":*************,\"orderNo\":\"ESL00000020593149864\",\"orderType\":\"100\",\"productInfos\":[{\"extendProps\":{},\"productAttrs\":{},\"productName\":\"经济仓产品 v2.0\",\"productNo\":\"sc-m-0047\",\"productType\":1},{\"extendProps\":{},\"parentNo\":\"sc-m-0047\",\"productAttrs\":{},\"productName\":\"耗材计费\",\"productNo\":\"sc-a-0028\",\"productType\":2}],\"refOrderInfo\":{\"extendProps\":{\"appraisalOrderNo\":\"ITS00000020587578116\"}},\"remark\":\"\",\"shipmentInfo\":{\"serviceRequirements\":{},\"extendProps\":{\"shipmentExtInfo\":\"{\\\"returnInfo\\\":{\\\"returnConsigneeName\\\":\\\"莉莉\\\",\\\"returnConsigneeMobile\\\":\\\"15054820637\\\",\\\"returnConsigneeAddress\\\":\\\"广东深圳市龙华区民治街道万家灯火125电商\\\"}}\"},\"shipperName\":\"京东配送\",\"shipperNo\":\"CYS0000010\",\"shipperType\":1},\"smartPatternInfo\":{\"stockOperationRule\":{\"occupyResult\":1,\"occupyWay\":1,\"stockType\":1},\"warehouseRule\":{\"cargoOutboundWay\":1}}},\"profile\":{\"ext\":{},\"locale\":\"zh_CN\",\"tenantId\":\"1000\",\"timeZone\":\"GMT+8\",\"traceId\":\"1680065866991\"}}");

    }
}
