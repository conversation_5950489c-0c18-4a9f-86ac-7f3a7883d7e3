package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.oms.relation.model.QueryOrderRelationData;
import cn.jdl.oms.relation.model.QueryOrderRelationRequest;
import cn.jdl.oms.relation.model.QueryOrderRelationResponse;
import cn.jdl.oms.relation.service.QueryOrderRelationService;
import cn.jdl.oms.search.dto.BaseInfo;
import cn.jdl.oms.search.dto.Order;
import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.OrderCenterRpc;
import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.OrderHoldRpc;
import com.jdl.sc.order.hold.api.OrderHoldQueryService;
import com.jdl.sc.order.hold.api.OrderHoldService;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import com.jdl.sc.order.hold.common.Response;
import com.jdl.sc.test.TestFileLoader;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * This class is used for unit testing the classToTest class.(Generated by JoyCoder)
 * @author: fanchangliang
 * @date: 2025-04-14
 */
@RunWith(MockitoJUnitRunner.class)
public class JdFreshFoodSplitOrderListenerTest {

	@Mock
	private MessageReceive messageReceive;

	@Mock
	private OutboundModelRepository outboundModelRepository;

	@Mock
	private OrderWorkflowLockAbility orderWorkflowLockAbility;

	@Mock
	private OrderHoldAcl orderHoldAcl;

	private OrderCenterAcl orderCenterAcl;

    @Mock
    private OrderHoldService orderHoldService;

    @Mock
    private OrderHoldQueryService orderHoldQueryService;

    @Mock
    private QueryOrderRelationService queryOrderRelationService;

	@InjectMocks
	private JdFreshFoodSplitOrderListener classToTest;

    @Before
    public void setUp() {
        classToTest = new JdFreshFoodSplitOrderListener(messageReceive);
        orderCenterAcl = new OrderCenterRpc();
        orderHoldAcl = new OrderHoldRpc(orderHoldService, orderHoldQueryService);

        ReflectionTestUtils.setField(classToTest, "orderCenterAcl", orderCenterAcl);
        ReflectionTestUtils.setField(orderCenterAcl, "queryOrderRelationService", queryOrderRelationService);
        ReflectionTestUtils.setField(classToTest, "outboundModelRepository", outboundModelRepository);
        ReflectionTestUtils.setField(classToTest, "orderWorkflowLockAbility", orderWorkflowLockAbility);
        ReflectionTestUtils.setField(classToTest, "orderHoldAcl", orderHoldAcl);
    }

    @Test
    public void testOnMessageSuccess() throws Exception {
        String orderData = TestFileLoader.loadTestFile("data/outbound/model/syn_tms_unlock_delivery_order.json");
        // generated by JoyCoder taskId 5d91feb04df7
        QueryOrderRelationResponse response = getQueryOrderRelationResponse();
        Mockito.when(queryOrderRelationService.queryOrderRelation(any(), any())).thenReturn(response);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().orderNo("ESL347543929").fulfillmentCommand(FulfillmentCommand.builder().build()).build();
        Mockito.when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        ReentrantLock lock = null;
        Mockito.when(orderWorkflowLockAbility.acquireLock(any(), any())).thenReturn(lock);
        OrderHoldRecord orderHoldRecord = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> orderHoldRecordResponse = new Response<>(0, null, orderHoldRecord);
        Mockito.when(orderHoldQueryService.queryOne(any())).thenReturn(orderHoldRecordResponse);
        Response<Void> voidResponse = new Response<>(0, null, null);
        Mockito.when(orderHoldService.orderHoldResume(any())).thenReturn(voidResponse);
        classToTest.onMessage(orderData);
    }


    @Test(expected = InfrastructureException.class)
    public void testOnMessageException() throws Exception {
        String orderData = TestFileLoader.loadTestFile("data/outbound/model/syn_tms_unlock_delivery_order.json");
        // generated by JoyCoder taskId 5d91feb04df7
        QueryOrderRelationResponse response = getQueryOrderRelationResponse();
        Mockito.when(queryOrderRelationService.queryOrderRelation(any(), any())).thenReturn(response);
        Mockito.when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(null);
        ReentrantLock lock = null;
        Mockito.when(orderWorkflowLockAbility.acquireLock(any(), any())).thenReturn(lock);
        OrderHoldRecord orderHoldRecord = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> orderHoldRecordResponse = new Response<>(0, null, orderHoldRecord);
        Mockito.when(orderHoldQueryService.queryOne(any())).thenReturn(orderHoldRecordResponse);
        Response<Void> voidResponse = new Response<>(0, null, null);
        Mockito.when(orderHoldService.orderHoldResume(any())).thenReturn(voidResponse);
        classToTest.onMessage(orderData);
    }

    @Test
    public void testOnMessageExceptionOrderIsNull() throws Exception {
        String orderData = TestFileLoader.loadTestFile("data/outbound/model/syn_tms_unlock_delivery_order.json");
        // generated by JoyCoder taskId 5d91feb04df7
        QueryOrderRelationResponse response = getQueryOrderRelationResponse();
        response.setData(null);
        Mockito.when(queryOrderRelationService.queryOrderRelation(any(), any())).thenReturn(response);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().orderNo("ESL347543929").fulfillmentCommand(FulfillmentCommand.builder().build()).build();
        Mockito.when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        ReentrantLock lock = null;
        Mockito.when(orderWorkflowLockAbility.acquireLock(any(), any())).thenReturn(lock);
        OrderHoldRecord orderHoldRecord = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> orderHoldRecordResponse = new Response<>(0, null, orderHoldRecord);
        Mockito.when(orderHoldQueryService.queryOne(any())).thenReturn(orderHoldRecordResponse);
        Response<Void> voidResponse = new Response<>(0, null, null);
        Mockito.when(orderHoldService.orderHoldResume(any())).thenReturn(voidResponse);
        classToTest.onMessage(orderData);
    }

    @Test
    public void testOnMessageExceptionResumeIsNull() throws Exception {
        String orderData = TestFileLoader.loadTestFile("data/outbound/model/syn_tms_unlock_delivery_order.json");
        // generated by JoyCoder taskId 5d91feb04df7
        QueryOrderRelationResponse response = getQueryOrderRelationResponse();
        Mockito.when(queryOrderRelationService.queryOrderRelation(any(), any())).thenReturn(response);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().orderNo("ESL347543929").fulfillmentCommand(FulfillmentCommand.builder().build()).build();
        Mockito.when(outboundModelRepository.getModelByOrderNo(any())).thenReturn(model);
        ReentrantLock lock = null;
        Mockito.when(orderWorkflowLockAbility.acquireLock(any(), any())).thenReturn(lock);
        OrderHoldRecord orderHoldRecord = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> orderHoldRecordResponse = new Response<>(0, null, null);
        Mockito.when(orderHoldQueryService.queryOne(any())).thenReturn(orderHoldRecordResponse);
        Response<Void> voidResponse = new Response<>(0, null, null);
        Mockito.when(orderHoldService.orderHoldResume(any())).thenReturn(voidResponse);
        classToTest.onMessage(orderData);
    }

    private static QueryOrderRelationResponse getQueryOrderRelationResponse() {
        Order order = new Order();
        BaseInfo baseInfo = new BaseInfo();
        baseInfo.setOrderNo("ESL347543929");
        order.setBaseInfo(baseInfo);
        List<Order> orders = new ArrayList<>();
        orders.add(order);
        QueryOrderRelationData orderRelationData = new QueryOrderRelationData();
        orderRelationData.setOrders(orders);
        QueryOrderRelationResponse response = new QueryOrderRelationResponse();
        response.setCode("1");  // 设置成功码
        response.setMessage("success");  // 设置成功消息
        response.setData(orderRelationData);  // 设置数据对象
        return response;
    }

}