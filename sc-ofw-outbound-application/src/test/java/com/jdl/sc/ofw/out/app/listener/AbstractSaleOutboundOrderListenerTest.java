package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import com.jd.eclp.core.AssertUtil;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ReflectionUtils;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderClobData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.oss.OSSClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AbstractSaleOutboundOrderListenerTest {

    private AbstractSaleOutboundOrderListener classToTest;


    @Mock
    private  OutboundService outboundService;
    @Mock
    private  ReceiveOrderTranslator receiveOrderTranslator;

    private  String businessType;

    @Mock(name = "omsOssClient")
    private OSSClient omsOssClient;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Test
    public void parseOrderClobTest() throws InvocationTargetException, IllegalAccessException {

        final Class<AbstractSaleOutboundOrderListener> aClass = AbstractSaleOutboundOrderListener.class;
        classToTest = new AbstractSaleOutboundOrderListener(outboundService, receiveOrderTranslator, businessType) {
            @Override
            public void onMessage(String message) {
                super.onMessage(message);
            }
            @Override
            protected OutboundCreateRequest buildReceiveRequestByOmsMsg(OrderData input) {
                return super.buildReceiveRequestByOmsMsg(input);
            }
        };
        ReflectionUtils.setFieldValue(aClass, classToTest, "omsOssClient", omsOssClient);
        ReflectionUtils.setFieldValue(aClass, classToTest, "switchKeyUtils", switchKeyUtils);
        when(omsOssClient.get(anyString())).thenReturn(JsonUtil.toJsonSafe(
                OrderClobData.builder()
                        .cargoInfos(new ArrayList<CargoInfo>(){{
                            add(new CargoInfo());
                        }})
                        .goodsInfos(new ArrayList<GoodsInfo>(){{
                            add(new GoodsInfo());
                        }})
                        .build()));


        final Method parseOrderClob = ReflectionUtils.getDeclaredMethod(aClass, "parseOrderClob", OrderData.class);
        parseOrderClob.setAccessible(true);

        final OrderData orderData = OrderData.builder()
                .extendProps(new HashMap<String, String>() {{
                    put("orderClobIssueOssKey", "anyOssKey");
                }})
                .build();
         parseOrderClob.invoke(classToTest, orderData);
         AssertUtil.notNull(orderData.getCargoInfos());
         AssertUtil.notNull(orderData.getGoodsInfos());


        parseOrderClob.invoke(classToTest, orderData);
        AssertUtil.notNull(orderData.getCargoInfos());
        AssertUtil.notNull(orderData.getGoodsInfos());
    }
}