package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ServicePlusAcl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * @author: xuxurui
 * @date: 2025-04-13
 */
@RunWith(MockitoJUnitRunner.class)
public class OmsOrderStatusListenerTest {

    @Mock
    private OutboundModelRepository outboundModelRepository;

    @Mock
    private ServicePlusAcl servicePlusAcl;

    @InjectMocks
    private OmsOrderStatusListener omsOrderStatusListener;

    @Test
    public void testOnMessageExceptionDuringSerialization() {

        try {
            omsOrderStatusListener.onMessage("invalid_message");
        } catch (Exception e) {
            // Add assertions or verify mocks if needed
        }
    }

    @Test
    public void testOnMessageValidationException() {
        try {
            omsOrderStatusListener.onMessage("{\"orderInfo\": {\"orderNo\": null}}");
        } catch (Exception e) {
            // Add assertions or verify mocks if needed
        }
    }

    @Test
    public void testOnMessageNoModelFound() throws Exception {

        // generated by JoyCoder taskId 37f3e6c9026e
        // Mocking
        when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(null);

        omsOrderStatusListener.onMessage("{\n" +
                "    \"requestProfile\": {\n" +
                "        \"locale\": \"zh_CN\",\n" +
                "        \"tenantId\": \"1000\",\n" +
                "        \"timeZone\": \"GMT+8\",\n" +
                "        \"traceId\": \"11111\"\n" +
                "    },\n" +
                "    \"orderInfo\": {\n" +
                "        \"businessIdentity\": {\n" +
                "            \"businessScene\": \"cancel\",\n" +
                "            \"businessType\": \"xxxxx\",\n" +
                "            \"businessUnit\": \"xxxxx\"\n" +
                "        },\n" +
                "        \"channelInfo\": {\n" +
                "            \"channelOperateTime\": 1645164825409,\n" +
                "            \"systemCaller\": \"OFC\"\n" +
                "        },\n" +
                "        \"orderNo\": \"ESL1200000100000032350\",\n" +
                "        \"orderStatus\": 10035\n" +
                "    }\n" +
                "}");
        // Add assertions or verify mocks if needed
    }

    @Test
    public void testOnMessageCancelServicePlusOrder() throws Exception {
        // generated by JoyCoder taskId 37f3e6c9026e
        // Mocking
        final OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("ESL1200000100000032350")
                .fulfillment(
                        Fulfillment.builder().build()
                )
                .build();
        when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(model);

        omsOrderStatusListener.onMessage("{\n" +
                "    \"requestProfile\": {\n" +
                "        \"locale\": \"zh_CN\",\n" +
                "        \"tenantId\": \"1000\",\n" +
                "        \"timeZone\": \"GMT+8\",\n" +
                "        \"traceId\": \"11111\"\n" +
                "    },\n" +
                "    \"orderInfo\": {\n" +
                "        \"businessIdentity\": {\n" +
                "            \"businessScene\": \"cancel\",\n" +
                "            \"businessType\": \"xxxxx\",\n" +
                "            \"businessUnit\": \"xxxxx\"\n" +
                "        },\n" +
                "        \"channelInfo\": {\n" +
                "            \"channelOperateTime\": 1645164825409,\n" +
                "            \"systemCaller\": \"OFC\"\n" +
                "        },\n" +
                "        \"orderNo\": \"ESL1200000100000032350\",\n" +
                "        \"orderStatus\": 10035\n" +
                "    }\n" +
                "}");
        // Add assertions or verify mocks if needed
    }

    @Test
    public void testOnMessageCancelServicePlusOrderException() {
        // generated by JoyCoder taskId 37f3e6c9026e
        // Mocking
        final OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("ESL1200000100000032350")
                .fulfillment(
                        Fulfillment.builder().build()
                )
                .build();
        when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(model);
//        when(servicePlusAcl.cancel(any(OFCCancelOrderRequest.class))).thenThrow(new RuntimeException("ServicePlusAcl Exception"));

        try {
            omsOrderStatusListener.onMessage("{\n" +
                    "    \"requestProfile\": {\n" +
                    "        \"locale\": \"zh_CN\",\n" +
                    "        \"tenantId\": \"1000\",\n" +
                    "        \"timeZone\": \"GMT+8\",\n" +
                    "        \"traceId\": \"11111\"\n" +
                    "    },\n" +
                    "    \"orderInfo\": {\n" +
                    "        \"businessIdentity\": {\n" +
                    "            \"businessScene\": \"cancel\",\n" +
                    "            \"businessType\": \"xxxxx\",\n" +
                    "            \"businessUnit\": \"xxxxx\"\n" +
                    "        },\n" +
                    "        \"channelInfo\": {\n" +
                    "            \"channelOperateTime\": 1645164825409,\n" +
                    "            \"systemCaller\": \"OFC\"\n" +
                    "        },\n" +
                    "        \"orderNo\": \"ESL1200000100000032350\",\n" +
                    "        \"orderStatus\": 10035\n" +
                    "    }\n" +
                    "}");
        } catch (Exception e) {
            // Add assertions or verify mocks if needed
        }
    }

    @Test
    public void testOnMessageCancelServicePlusOrderNoModel() throws Exception {
        // generated by JoyCoder taskId 37f3e6c9026e
        // Mocking
        when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(null);

        omsOrderStatusListener.onMessage("{\n" +
                "    \"requestProfile\": {\n" +
                "        \"locale\": \"zh_CN\",\n" +
                "        \"tenantId\": \"1000\",\n" +
                "        \"timeZone\": \"GMT+8\",\n" +
                "        \"traceId\": \"11111\"\n" +
                "    },\n" +
                "    \"orderInfo\": {\n" +
                "        \"businessIdentity\": {\n" +
                "            \"businessScene\": \"cancel\",\n" +
                "            \"businessType\": \"xxxxx\",\n" +
                "            \"businessUnit\": \"xxxxx\"\n" +
                "        },\n" +
                "        \"channelInfo\": {\n" +
                "            \"channelOperateTime\": 1645164825409,\n" +
                "            \"systemCaller\": \"OFC\"\n" +
                "        },\n" +
                "        \"orderNo\": \"ESL1200000100000032350\",\n" +
                "        \"orderStatus\": 10035\n" +
                "    }\n" +
                "}");
        // Add assertions or verify mocks if needed
    }


}