package com.jdl.sc.ofw.out.app.service;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcResponse;
import cn.jdl.oms.core.model.ChannelInfo;
import com.jdl.sc.ofw.out.app.service.oms.CancelOutboundOrderOfcServiceImpl;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelResponse;
import com.jdl.sc.ofw.outbound.exception.AcquireConcurrentLockException;
import com.jdl.sc.ofw.outbound.spec.enums.CancelResultStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.HashMap;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CancelOutboundOrderOfcServiceTest {

    @InjectMocks
    CancelOutboundOrderOfcServiceImpl cancelOutboundOrderOfcService;
    @Mock
    private OutboundService outboundService;

    RequestProfile requestProfile = new RequestProfile();
    CancelOutboundOrderOfcRequest cancelOutboundOrderOfcRequest = new CancelOutboundOrderOfcRequest();

    @Before
    public void setup() {
        requestProfile.setExt(new HashMap<>());
        requestProfile.setLocale("local");
        requestProfile.setTenantId("tenantid");
        requestProfile.setTraceId("trace");
        requestProfile.setTimeZone("timezone");

        BusinessIdentity businessIdentity = new BusinessIdentity();
        ChannelInfo channelInfo = new ChannelInfo();
        cancelOutboundOrderOfcRequest.setBusinessIdentity(businessIdentity);
        cancelOutboundOrderOfcRequest.setChannelInfo(channelInfo);
        businessIdentity.setBusinessUnit("cn_jdl_sc-pop");
        cancelOutboundOrderOfcRequest.setOrderNo("ESL00100000032");
        cancelOutboundOrderOfcRequest.setRequestId("3444255954944");
        cancelOutboundOrderOfcRequest.setCancelType(1);
        cancelOutboundOrderOfcRequest.setOperator("popTest");
        cancelOutboundOrderOfcRequest.setRemark("");
        channelInfo.setSystemCaller("POP");
        channelInfo.setChannelOperateTime(new Date());
    }

    @Test
    public void testCancel() {
        OutboundCancelResponse response = OutboundCancelResponse.builder()
                .code(CancelResultStateEnum.SUCCESS.getCode())
                .message(CancelResultStateEnum.SUCCESS.getName())
                .build();
        Mockito.when(outboundService.cancel(Mockito.any())).
                thenReturn(response);

        CancelOutboundOrderOfcResponse res = cancelOutboundOrderOfcService.cancelOrder(requestProfile, cancelOutboundOrderOfcRequest);
        Assert.assertTrue(CancelResultStateEnum.SUCCESS.getCode()==res.getData().getCancelResult());
    }

    @Test
    public void testCancelFail() {
        OutboundCancelResponse response = OutboundCancelResponse.builder()
                .code(CancelResultStateEnum.FAIL.getCode())
                .message(CancelResultStateEnum.FAIL.getName())
                .build();
        Mockito.when(outboundService.cancel(Mockito.any())).thenReturn(response);

        CancelOutboundOrderOfcResponse res = cancelOutboundOrderOfcService.cancelOrder(requestProfile, cancelOutboundOrderOfcRequest);
        Assert.assertTrue(CancelResultStateEnum.FAIL.getCode()==res.getData().getCancelResult());
    }

    @Test
    public void testCancelLockException() {
        OutboundCancelResponse response = OutboundCancelResponse.builder()
                .code(CancelResultStateEnum.FAIL.getCode())
                .message(CancelResultStateEnum.FAIL.getName())
                .build();
        Mockito.when(outboundService.cancel(Mockito.any())).thenThrow(new AcquireConcurrentLockException());

        CancelOutboundOrderOfcResponse res = cancelOutboundOrderOfcService.cancelOrder(requestProfile, cancelOutboundOrderOfcRequest);
        Assert.assertTrue(CancelResultStateEnum.CANCELING.getCode()==res.getData().getCancelResult());
    }


    /**
     * 测试取消DTC失败重试
     */
    @Test
    public void testCancelDTCFailRetry(){
        String assertMsg = "系统交互异常,请重试!";
        Mockito.when(outboundService.cancel(Mockito.any())).thenThrow(new InfrastructureException(UnifiedErrorSpec.ACL.RETURN_EXCEPTION)
                .withCustom(assertMsg));
        CancelOutboundOrderOfcResponse res = cancelOutboundOrderOfcService.cancelOrder(requestProfile, cancelOutboundOrderOfcRequest);
        Assert.assertEquals(assertMsg, res.getMessage());
    }

}
