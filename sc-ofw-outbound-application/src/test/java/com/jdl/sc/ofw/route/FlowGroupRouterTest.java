package com.jdl.sc.ofw.route;

import com.jd.jsf.gd.config.ConsumerGroupConfig;
import com.jd.jsf.gd.msg.Invocation;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Cancel;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.service.adapter.GrayscaleAdapter;
import com.jdl.sc.ofw.outbound.dto.FlowRestartRequest;
import com.jdl.sc.ofw.outbound.dto.FlowResumeRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FlowGroupRouterTest {

    @Mock
    private OutboundModelRepository mockOutboundModelRepository;

    @InjectMocks
    private FlowGroupRouter flowGroupRouterUnderTest;

    @Mock
    private GrayscaleAdapter grayscaleAdapter;

    @Test
    public void testRouter() {
        // Setup
        final Invocation invocation = new Invocation();
        invocation.setClazzName("clazzName");
        invocation.setMethodName("methodName");
        invocation.setAlias("alias");
        invocation.setArgs(new Object[]{FlowResumeRequest.builder().orderNo("orderNo").build()});
        invocation.setArgsType(new Class[]{Object.class});
        invocation.setIfaceId("ifaceId");


        final ConsumerGroupConfig config = new ConsumerGroupConfig<>();
        config.setGroupRouter(null);
        config.setDstParam(0);
        config.setConsumerConfigs(new HashMap<>());
        config.setAliasAdaptive(false);

        // Configure OutboundModelRepository.getModelByOrderNo(...).
        final NonstandardProducts nonstandardProducts = new NonstandardProducts();
        final OrderFulfillmentModel model = new OrderFulfillmentModel(
                new BatrixInfo(
                        "businessUnit",
                        "businessType",
                        "businessScene",
                        "yId",
                        new HashSet<>(Arrays.asList("value")),
                        new HashSet<>(Arrays.asList("value")),
                        "businessStrategy"
                ),
                "orderNo",
                "parentOrderNo",
                OrderTypeEnum.DEFAULT,
                OrderBusinessTypeEnum.B2C,
                OrderStatusEnum.INIT,
                BasicInfo.builder().build(),
                Customer.builder().build(),
                Arrays.asList(Cargo.builder().build()),
                Arrays.asList(Goods.builder().build()),
                Consignee.builder().build(),
                Consignor.builder().build(),
                Shipment.builder().build(),
                Warehouse.builder().build(),
                Finance.builder().build(),
                RefOrderInfo.builder().build(),
                nonstandardProducts,
                Channel.builder().build(),
                new ProductCollection(),
                "remark",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                "buyerRemark",
                "sellerRemark",
                Cancel.builder().build(),
                Intercept.builder().build(),
                Solution.builder().build(),
                Audit.builder().build(),
                Fulfillment.builder().build(),
                "orderMark",
                Arrays.asList(PauseReasonEnum.FLOW_NODE_SYSTEM_EXCEPTION),
                Invoice.builder().build(),
                "originalCustomerOrderType", "originalCustomerOrderTypeName",
                EdiRemarkInfo.builder().build(),
                OrderSign.builder().build(),
                "jfs/0001",
                "2",
                FulfillmentCommand.builder().build()
        );
        when(mockOutboundModelRepository.getModelByOrderNo("orderNo")).thenReturn(model);

        // Run the test
        final String result = flowGroupRouterUnderTest.router(invocation, config);

        // Verify the results
        assertThat(result).isEqualTo(null);
    }

    @Test
    public void testAfterPropertiesSet_ThrowsException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> flowGroupRouterUnderTest.afterPropertiesSet()).isInstanceOf(Exception.class);
    }


    @Test
    public void testModifyRouter() {
        // Setup
        final Invocation invocation = new Invocation();
        invocation.setClazzName("com.jdl.sc.ofw.outbound.api.route.OutboundRouteService");
        invocation.setMethodName("modify");
        invocation.setAlias("alias");
        invocation.setArgs(new Object[]{OutboundModifyRequest.builder().orderNo("orderNo").build()});
        invocation.setArgsType(new Class[]{Object.class});
        invocation.setIfaceId("ifaceId");

        ReflectionTestUtils.setField(flowGroupRouterUnderTest, "aliasMap", new HashMap<String, String>() {{
            put("cn_jdl_cc-isv", "sc-cc-ofw-test");
        }});


        final ConsumerGroupConfig config = new ConsumerGroupConfig<>();
        config.setGroupRouter(null);
        config.setDstParam(0);
        config.setConsumerConfigs(new HashMap<>());
        config.setAliasAdaptive(false);

        final OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(BatrixInfo.builder()
                        .businessUnit("cn_jdl_cc-isv")
                        .build())
                .build();
        when(mockOutboundModelRepository.getModelByOrderNo("orderNo")).thenReturn(model);

        final String result = flowGroupRouterUnderTest.router(invocation, config);
        assertThat(result).isEqualTo("sc-cc-ofw-test");
    }

    @Test
    public void testRestartRouter() {
        // Setup
        final Invocation invocation = new Invocation();
        invocation.setClazzName("com.jdl.sc.ofw.outbound.api.route.OutboundRouteService");
        invocation.setMethodName("modify");
        invocation.setAlias("alias");
        invocation.setArgs(new Object[]{FlowRestartRequest.builder().orderNo("orderNo").build()});
        invocation.setArgsType(new Class[]{Object.class});
        invocation.setIfaceId("ifaceId");

        ReflectionTestUtils.setField(flowGroupRouterUnderTest, "aliasMap", new HashMap<String, String>() {{
            put("cn_jdl_cc-isv", "sc-cc-ofw-test");
        }});


        final ConsumerGroupConfig config = new ConsumerGroupConfig<>();
        config.setGroupRouter(null);
        config.setDstParam(0);
        config.setConsumerConfigs(new HashMap<>());
        config.setAliasAdaptive(false);

        final OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(BatrixInfo.builder()
                        .businessUnit("cn_jdl_cc-isv")
                        .build())
                .build();
        when(mockOutboundModelRepository.getModelByOrderNo("orderNo")).thenReturn(model);

        final String result = flowGroupRouterUnderTest.router(invocation, config);
        assertThat(result).isEqualTo("sc-cc-ofw-test");
    }
}
