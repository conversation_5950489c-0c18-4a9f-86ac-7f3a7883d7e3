package com.jdl.sc.ofw.out.app.service;

import cn.jdl.ofc.api.oms.common.model.ModifyOutboundOrderOfcRequest;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import com.jdl.sc.core.utils.ReflectionUtils;
import com.jdl.sc.ofw.out.app.service.oms.ModifyOutboundOrderOfcServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class ModifyOutboundOrderOfcServiceTest {
    @InjectMocks
    private ModifyOutboundOrderOfcServiceImpl modifyOutboundOrderOfcService;

    @Test
    public void buildShipmentTest() {
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setShipperType(1);

        Map<String, String> extendProps = new HashMap<>();
        modifyOutboundOrderOfcService.buildShipment(shipmentInfo, extendProps);
    }

    @Test
    public void buildSolutionTest() {
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setServiceRequirements(new HashMap<>());
        modifyOutboundOrderOfcService.buildSolution(shipmentInfo);
    }

    @Test
    public void testOrderSign() throws InvocationTargetException, IllegalAccessException {
        Method buildOrderSignRequest = ReflectionUtils.getDeclaredMethod(ModifyOutboundOrderOfcServiceImpl.class,
                "buildOrderSign",
                Map.class);
        buildOrderSignRequest.setAccessible(true);
        Map<String, String> orderSign = new HashMap<>();
        buildOrderSignRequest.invoke(modifyOutboundOrderOfcService, orderSign);

        orderSign.put("businessSubsidy","11xx");

        buildOrderSignRequest.invoke(modifyOutboundOrderOfcService, orderSign);
    }
    @Test
    public void testChannel() throws InvocationTargetException, IllegalAccessException {
        Method buildChannelRequest = ReflectionUtils.getDeclaredMethod(ModifyOutboundOrderOfcServiceImpl.class,
                "buildChannel",
                ChannelInfo.class);
        buildChannelRequest.setAccessible(true);
        ChannelInfo channelInfo = null;
        buildChannelRequest.invoke(modifyOutboundOrderOfcService, channelInfo);

        channelInfo = new ChannelInfo();
        channelInfo.setExtendProps(new HashMap(){{put("channelTaskCode","11xx");}});

        buildChannelRequest.invoke(modifyOutboundOrderOfcService, channelInfo);
    }
}
