package com.jdl.sc.ofw.out.deprecated.rpc.impl;

import com.jd.eclp.bbp.so.dto.CustomExtend;
import com.jd.eclp.bbp.so.service.SoPddWaybillApi;
import com.jd.eclp.bbp.so.service.dto.response.QuerySoPddWaybillActiveResponse;
import com.jd.eclp.core.ApiResponse;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetPlatform;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class ElectronicSheetConfigRpcTest {
    @InjectMocks
    private ElectronicSheetConfigRpc electronicSheetConfigRpc;
    @Mock
    private SoPddWaybillApi soPddWaybillApi;

    /**
     * 测试查询运单信息时抛出PauseException异常的场景。
     */
    @Test(expected = PauseException.class)
    public void queryWaybillInfoTest() {
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo(ShipperConstants.JD_SHIPPER_NO)
                .platform(ElectronicSheetPlatform.DY)
                .build();
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(null);
        when(electronicSheetConfigRpc.queryConfig(electronicSheetRequest)).thenReturn(null);
    }

    @Test
    public void testQueryConfigInfo_Not_Cainiao_JP() {
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(apiResponse());
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo(ShipperConstants.JD_SHIPPER_NO)
                .platform(ElectronicSheetPlatform.PDD)
                .build();
        electronicSheetConfigRpc.queryConfigInfo(electronicSheetRequest);
    }

    @Test
    public void testQueryConfigInfo_Cainiao_Not_JP() {
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(apiResponse());
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo("CYS00001")
                .platform(ElectronicSheetPlatform.CN)
                .build();
        electronicSheetConfigRpc.queryConfigInfo(electronicSheetRequest);
    }

    @Test
    public void testQueryConfigInfo_Cainiao_JP() {
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(apiResponse());
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo(ShipperConstants.JD_SHIPPER_NO)
                .platform(ElectronicSheetPlatform.CN)
                .build();
        electronicSheetConfigRpc.queryConfigInfo(electronicSheetRequest);
    }

    @Test
    public void testQueryConfigInfo_Cainiao_JP_KY() {
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(apiResponse());
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo(ShipperConstants.JD_SHIPPER_NO)
                .platform(ElectronicSheetPlatform.CN)
                .existFreightProduct(true)
                .build();
        electronicSheetConfigRpc.queryConfigInfo(electronicSheetRequest);
    }

    @Test
    public void testQueryConfigInfo_XHS_JP() {
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(apiResponse());
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo(ShipperConstants.JD_SHIPPER_NO)
                .platform(ElectronicSheetPlatform.XHS)
                .existFreightProduct(true)
                .build();
        electronicSheetConfigRpc.queryConfigInfo(electronicSheetRequest);
    }

    @Test
    public void testQueryConfigInfo_Dw() {
        QuerySoPddWaybillActiveResponse response = new QuerySoPddWaybillActiveResponse();
        response.setCustomExtend(new CustomExtend());
        ApiResponse<QuerySoPddWaybillActiveResponse> success = ApiResponse.ofSuccess("success", response);
        when(soPddWaybillApi.querySoPddWaybillActive(any())).thenReturn(success);
        ElectronicSheetRequest electronicSheetRequest = ElectronicSheetRequest.builder()
                .shipperNo(ShipperConstants.JD_SHIPPER_NO)
                .platform(ElectronicSheetPlatform.DW)
                .existFreightProduct(true)
                .build();
        electronicSheetConfigRpc.queryConfigInfo(electronicSheetRequest);
    }

    private ApiResponse<QuerySoPddWaybillActiveResponse> apiResponse() {
        QuerySoPddWaybillActiveResponse response = new QuerySoPddWaybillActiveResponse();
        return ApiResponse.ofSuccess("success", response);
    }
}