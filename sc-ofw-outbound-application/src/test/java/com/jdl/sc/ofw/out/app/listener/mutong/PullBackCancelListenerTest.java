package com.jdl.sc.ofw.out.app.listener.mutong;

import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.horiz.infra.facade.mutong.MuTongFaced;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.any;

/**
 * This class is used for unit testing the PullBackCancelListener class.(Generated by JoyCoder)
 *
 * @author: jiaxiangdong3
 * @date: 2024-12-23
 */
@RunWith(MockitoJUnitRunner.class)
public class PullBackCancelListenerTest {

    @InjectMocks
    private PullBackCancelListener pullBackCancelListener;

    @Mock
    private OutboundModelRepository outboundModelRepository;

    @Mock
    private MuTongFaced muTongFaced;

    @Mock
    private OrderConvertServiceAcl orderConvertServiceAcl;

    @Mock
    private OrderCenterAcl orderCenterAcl;

    @Test
    public void testOnMessageOrderNoBlank() throws Exception {
        String message = "{\n" +
                "    \"orderNo\": \"ESL111111111111\",\n" +
                "    \"status\": \"12312\",\n" +
                "    \"operator\": \"system\",\n" +
                "    \"operateTime\": \"2024-12-23 00:00:00\"\n" +
                "}";
        Mockito.when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("");
        pullBackCancelListener.onMessage(message);
    }

    @Test
    public void testOnMessageOrderNotExist() throws Exception {
        String message = "{\n" +
                "    \"orderNo\": \"ESL111111111111\",\n" +
                "    \"status\": \"12312\",\n" +
                "    \"operator\": \"system\",\n" +
                "    \"operateTime\": \"2024-12-23 00:00:00\"\n" +
                "}";
        Mockito.when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("orderNo");
        Mockito.when(orderCenterAcl.checkOrderExist("orderNo")).thenReturn(false);
        pullBackCancelListener.onMessage(message);
    }

    @Test
    public void testOnMessageWmsPullBackStatusCodeNotEqual() throws Exception {
        String message = "{\n" +
                "    \"orderNo\": \"ESL111111111111\",\n" +
                "    \"status\": \"12312\",\n" +
                "    \"operator\": \"system\",\n" +
                "    \"operateTime\": \"2024-12-23 00:00:00\"\n" +
                "}";
        Mockito.when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("orderNo");
        Mockito.when(orderCenterAcl.checkOrderExist("orderNo")).thenReturn(true);
        pullBackCancelListener.onMessage(message);
    }

    @Test
    public void testOnMessageIsBipOrderFalse() throws Exception {
        String message = "{\n" +
                "    \"orderNo\": \"ESL111111111111\",\n" +
                "    \"status\": \"10031\",\n" +
                "    \"operator\": \"system\",\n" +
                "    \"operateTime\": \"2024-12-23 00:00:00\"\n" +
                "}";
        Mockito.when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("orderNo");
        Mockito.when(orderCenterAcl.checkOrderExist("orderNo")).thenReturn(true);
        Mockito.when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(OrderFulfillmentModel.builder()
                .orderNo("123")
                .batrixInfo(BatrixInfo.builder()
                        .businessUnit("unit")
                        .businessType("type")
                        .build())
                .shipment(Shipment.builder().build())
                .channel(Channel.builder().build())
                .build());
        pullBackCancelListener.onMessage(message);
    }

    @Test
    public void testOnMessageSuccess() throws Exception {
        String message = "{\n" +
                "    \"orderNo\": \"ESL111111111111\",\n" +
                "    \"status\": \"10031\",\n" +
                "    \"operator\": \"system\",\n" +
                "    \"operateTime\": \"2024-12-23 00:00:00\"\n" +
                "}";
        Mockito.when(orderConvertServiceAcl.convert(any(String.class))).thenReturn("orderNo");
        Mockito.when(orderCenterAcl.checkOrderExist("orderNo")).thenReturn(true);
        Mockito.when(outboundModelRepository.getModelByOrderNo(any(String.class))).thenReturn(OrderFulfillmentModel.builder()
                .orderNo("123")
                .batrixInfo(BatrixInfo.builder()
                        .businessUnit("unit")
                        .businessType("type")
                        .build())
                .shipment(Shipment.builder()
                        .shipperType(ShipperTypeEnum.TC)
                        .build())
                .channel(Channel.builder()
                        .systemCaller("BIP")
                        .build())
                .build());
        pullBackCancelListener.onMessage(message);
    }

}