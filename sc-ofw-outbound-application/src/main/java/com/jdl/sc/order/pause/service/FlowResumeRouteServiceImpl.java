package com.jdl.sc.order.pause.service;

import com.jdl.sc.ofw.outbound.api.FlowResumeService;
import com.jdl.sc.ofw.outbound.dto.FlowResponse;
import com.jdl.sc.ofw.outbound.dto.FlowRestartRequest;
import com.jdl.sc.ofw.outbound.dto.FlowResumeRequest;
import com.jdl.sc.order.hold.common.Response;
import com.jdl.sc.order.hold.spi.OrderResumeService;
import com.jdl.sc.order.hold.spi.dto.OrderRestartRequest;
import com.jdl.sc.order.hold.spi.dto.OrderResumeRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@AllArgsConstructor
public class FlowResumeRouteServiceImpl implements OrderResumeService {

    @Resource(name = "consumerFlowResumeService")
    private FlowResumeService flowResumeService;


    @Override
    public Response<Void> resume(OrderResumeRequest orderResumeRequest) {
        try {
            FlowResumeRequest flowResumeRequest = translateResumeRequest(orderResumeRequest);
            FlowResponse flowResponse = flowResumeService.resume(flowResumeRequest);
            if(flowResponse.success()) {
                return Response.ofSuccess();
            }
            return Response.failed(flowResponse.getMessage());
        } catch (Exception e) {
            log.error("流程恢复分流层调用业务分组异常,订单号：{}", orderResumeRequest.getOrderNo(), e);
            return Response.failed("调用业务分组异常");
        }
    }

    @Override
    public Response<Void> restart(OrderRestartRequest orderRestartRequest) {

        try {
            FlowRestartRequest flowRestartRequest = translateRestartRequest(orderRestartRequest);
            FlowResponse flowResponse = flowResumeService.restart(flowRestartRequest);
            if(flowResponse.success()) {
                return Response.ofSuccess();
            }
            return Response.failed(flowResponse.getMessage());
        } catch (Exception e) {
            log.error("流程重启分流层调用业务分组异常,订单号：{}", orderRestartRequest.getOrderNo(), e);
            return Response.failed("调用业务分组异常");
        }
    }

    private FlowResumeRequest translateResumeRequest(OrderResumeRequest orderResumeRequest) {
        return FlowResumeRequest.builder()
                .orderNo(orderResumeRequest.getOrderNo())
                .code(orderResumeRequest.getCode())
                .id(orderResumeRequest.getId())
                .operator(orderResumeRequest.getOperator())
                .customizeSnapshot(orderResumeRequest.getCustomizeSnapshot())
                .orderType(orderResumeRequest.getOrderType())
                .traceId(orderResumeRequest.getTraceId())
                .resumeInstructions(orderResumeRequest.getResumeInstructions())
                .build();
    }

    private FlowRestartRequest translateRestartRequest(OrderRestartRequest orderRestartRequest) {
        return FlowRestartRequest.builder()
                .orderNo(orderRestartRequest.getOrderNo())
                .code(orderRestartRequest.getCode())
                .id(orderRestartRequest.getId())
                .operator(orderRestartRequest.getOrderNo())
                .orderType(orderRestartRequest.getOrderType())
                .traceId(orderRestartRequest.getTraceId())
                .build();
    }
}
