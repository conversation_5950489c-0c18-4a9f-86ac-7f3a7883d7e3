package com.jdl.sc.order.pause.service;

import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.pojo.OrderPausePojo;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseStatusEnum;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import com.jdl.sc.order.pause.api.OrderPauseService;
import com.jdl.sc.order.pause.dto.OrderPauseInfo;
import com.jdl.sc.order.pause.dto.OrderPauseModifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component("orderPauseServiceImpl")
public class OrderPauseServiceImpl implements OrderPauseService {

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private OrderHoldAcl orderHoldAcl;

    /**
     * 恢复成功返回true
     */
    @Override
    public boolean resumeById(Long id, String operator) {
        throw new RuntimeException("接口已下线");
    }

    @Override
    public boolean close(Long id, String operator) {
        throw new RuntimeException("接口已下线");
    }

    @Override
    public OrderPauseInfo getByOrderNo(String orderNo, PauseReasonEnum pauseReason) {
        throw new RuntimeException("接口已下线");
    }

    @Override
    public OrderPauseInfo getLastByOrderNo(String orderNo) {
        //FIXME 因管理端调用，入口暂时不变。直接改造。
        try {
            return this.getLastByOrderNoNew(orderNo);
        } catch (Exception e) {
            log.error("订单暂停新流程错误,orderNo:{}", orderNo, e);
            throw new RuntimeException("订单暂停新流程错误");
        }
    }


    private OrderPauseInfo getLastByOrderNoNew(String orderNo) {
        QueryRequest queryRequest = QueryRequest.builder()
                .orderNo(orderNo)
                .build();
        OrderHoldRecord orderHoldRecord = orderHoldAcl.queryOneHoldRecord(queryRequest);
        if (orderHoldRecord == null) {
            return null;
        }
        return OrderPauseInfo.builder()
                .id(orderHoldRecord.getId())
                .orderNo(orderHoldRecord.getOrderNo())
                .pauseReason(PauseReasonEnum.fromCode(orderHoldRecord.getCode()))
                .errorMessage(orderHoldRecord.getHoldMessage())
                .build();
    }

    /**
     * 重启成功返回true
     */
    @Override
    public boolean restart(Long id, String operator) {
        throw new RuntimeException("接口已下线");
    }

    @Override
    public void modify(OrderPauseModifyRequest request) {
        throw new RuntimeException("接口已下线");
    }

    @Override
    public boolean pause(OrderPauseInfo pauseInfo) {
        throw new RuntimeException("接口已下线");
    }

    /**
     * 重启流程
     */
    @Override
    public void restartByOrderNo(String orderNo) throws Exception {
        throw new RuntimeException("接口已下线");
    }
}
