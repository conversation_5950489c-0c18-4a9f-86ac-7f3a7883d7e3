package com.jdl.sc.ofw.out.app.service;

import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.ofw.out.common.dict.OperateType;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.api.FlowResumeService;
import com.jdl.sc.ofw.outbound.dto.FlowResponse;
import com.jdl.sc.ofw.outbound.dto.FlowRestartRequest;
import com.jdl.sc.ofw.outbound.dto.FlowResumeRequest;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.common.ForceResumeInstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * FileName: FlowRouteServiceImpl
 * Description:
 * [@author]:   quhuafeng
 * [@date]:     2023/4/264:25 PM
 */
@Slf4j
@Service("flowResumeService")
public class FlowResumeServiceImpl implements FlowResumeService {

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private OrderWorkflowLockAbility orderWorkflowLockAbility;

    @Override
    public FlowResponse<Void> resume(FlowResumeRequest orderResumeRequest) {
        ReentrantLock lock;
        try {
            lock = orderWorkflowLockAbility.acquireLock(orderResumeRequest.getOrderNo(),"FlowResumeServiceImpl.resume");
        } catch (AcquireLockException e) {
            log.warn("订单恢复:{},获取锁失败！", orderResumeRequest.getOrderNo());
            throw e;
        }
        try {
            OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderResumeRequest.getOrderNo());
            if (model == null) {
                log.error("订单不存在,恢复失败");
                return FlowResponse.failed("订单不存在,恢复失败");
            }
            Intercept intercept = Optional.ofNullable(model)
                    .map(OrderFulfillmentModel::getIntercept)
                    .orElse(null);

            if (intercept != null && intercept.isIntercepted()) {
                log.info("订单{}已经被拦截:{}, 不允许恢复", orderResumeRequest.getOrderNo(), intercept.getState());
                return FlowResponse.failed("已经被拦截,不允许恢复");
            }

            PauseReasonEnum orderPause = PauseReasonEnum.fromCode(orderResumeRequest.getCode());
            if (orderPause.couldIgnore(orderResumeRequest.getOperator())) {
                model.addPauseToIgnore(orderPause, Audit.builder()
                        .operateType(OperateType.MODIFY)
                        .operateTime(new Date())
                        .operator(orderResumeRequest.getOperator())
                        .build());
                outboundModelRepository.persist(model);
            }

            //履约指令信息
            final FulfillmentCommand fulfillmentCommand = Optional.ofNullable(model.getFulfillmentCommand())
                    .orElse(FulfillmentCommand.builder().build());
            //强制恢复异常码
            List<PauseReasonEnum> forceResumes = Optional.ofNullable(fulfillmentCommand.getForceResumes())
                    .orElse(new ArrayList<>());
            if (ForceResumeInstruct.FORCE.getCode().equals(orderResumeRequest.getResumeInstructions())){
                forceResumes.add(orderPause);

                fulfillmentCommand.assignResumeInstructions(orderResumeRequest.getResumeInstructions());
                fulfillmentCommand.assignForceResumes(forceResumes);
                model.assignFulfillmentCommand(fulfillmentCommand);
                outboundModelRepository.persist(model);
            }

            log.info("Resume paused order: {}, pause code: {}", orderResumeRequest.getOrderNo(), orderResumeRequest.getCode());

            switch (orderPause.getResumePhaseEnum()) {
                case FLOW_RESUME:
                    workflowControlService.resume(model, orderResumeRequest.getCustomizeSnapshot(), false);
                    break;
                default:
                    workflowControlService.resume(model, orderResumeRequest.getCustomizeSnapshot(), true);
                    break;
            }
            return FlowResponse.ofSuccess();

        } catch (Exception e) {
            log.error("流程恢复失败,orderNo:{}", orderResumeRequest.getOrderNo(), e);
            return FlowResponse.failed();
        } finally {
            lock.unlock();
        }
    }
    @Override
    public FlowResponse<Void> restart(FlowRestartRequest orderRestartRequest) {
        try {
            workflowControlService.restart(orderRestartRequest.getOrderNo());
            return FlowResponse.ofSuccess();
        } catch (Exception e) {
            log.error("重启流程失败,orderNo:{}", orderRestartRequest.getOrderNo(), e);
            return FlowResponse.failed();
        }
    }
}
