package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Builder
@Getter
@Jacksonized
public class PurchaseReturnDto {
    /**
     * EPL采购单号
     */
    private String poNo;
    /**
     * 商家单号
     */
    private String outPoNo;
    /**
     * 事业部
     */
    private String deptNo;
    /**
     * 商家关联单号(调拨单号)
     */
    private String referenceOrderNo;

    /**
     * 采购单状态
     */
    private Integer poStatus;
}
