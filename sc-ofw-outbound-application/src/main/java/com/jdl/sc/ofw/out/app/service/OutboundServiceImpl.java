package com.jdl.sc.ofw.out.app.service;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.core.cache.jimdb.JimClient;
import com.jdl.sc.core.jsf.JsfConstants;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.profiler.Watcher;
import com.jdl.sc.core.ump.Ump;
import com.jdl.sc.core.utils.CollectionUtils;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.app.translator.OutboundModelTranslator;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.dict.OperateType;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.AbstractException;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.common.exception.DomainServiceException;
import com.jdl.sc.ofw.out.common.mq.MessageProducer;
import com.jdl.sc.ofw.out.common.specification.JSR303Specification;
import com.jdl.sc.ofw.out.common.specification.Notification;
import com.jdl.sc.ofw.out.domain.ability.PersistenceAbility;
import com.jdl.sc.ofw.out.domain.cache.KeyGenerator;
import com.jdl.sc.ofw.out.domain.dto.CancelCommand;
import com.jdl.sc.ofw.out.domain.dto.CancelResult;
import com.jdl.sc.ofw.out.domain.dto.CloseCommand;
import com.jdl.sc.ofw.out.domain.dto.CloseResult;
import com.jdl.sc.ofw.out.domain.dto.InterceptCommand;
import com.jdl.sc.ofw.out.domain.dto.InterceptResult;
import com.jdl.sc.ofw.out.domain.dto.ModifyCommand;
import com.jdl.sc.ofw.out.domain.dto.ModifyStatusCommand;
import com.jdl.sc.ofw.out.domain.dto.OutboundModifyWaybillCommand;
import com.jdl.sc.ofw.out.domain.dto.PassCommand;
import com.jdl.sc.ofw.out.domain.dto.PassResult;
import com.jdl.sc.ofw.out.domain.dto.PullbackCommand;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.monitor.MonitorLogger;
import com.jdl.sc.ofw.out.domain.service.OutboundCancelService;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.out.domain.service.OutboundModelServiceV2;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundCloseRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCloseResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundInterceptRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundInterceptResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyWaybillRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundPullbackRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundRetransportRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundRetransportResponse;
import com.jdl.sc.ofw.outbound.dto.callback.OutboundStatusRequest;
import com.jdl.sc.ofw.outbound.dto.callback.OutboundStatusResponse;
import com.jdl.sc.ofw.outbound.exception.AcquireConcurrentLockException;
import com.jdl.sc.ofw.outbound.spec.enums.CancelModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.CancelResultStateEnum;
import com.jdl.sc.ofw.outbound.spec.enums.CancelTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.InterceptOperationTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.InterceptStateEnum;
import com.jdl.sc.ofw.outbound.spec.utils.MessageIdUtils;
import com.jdl.sc.ofw.outbound.spec.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Component("outboundServiceImpl")
@Slf4j
public class OutboundServiceImpl implements OutboundService {
    /**
     * 基本信息校验
     */
    @Resource
    private JSR303Specification jsr303Specification;

    @Resource
    private OutboundModelService modelService;

    @Resource
    private OutboundModelServiceV2 newModelService;

    @Resource
    private OutboundCancelService cancelService;

    @Resource
    private OutboundModelTranslator outboundModelTranslator;

    @Resource
    private WorkflowControlService workflowControlService;


    @Autowired
    private PersistenceAbility persistenceAbility;


    @Value("${ofw.standard.businessUnit}")
    private String businessUnit;

    @Value("${ofw.standard.businessType}")
    private String businessType;

    @Resource
    private OutboundCreateAgent outboundCreateAgent;

    @Resource
    private JimClient jimClient;

    @Resource(name = "jmqMessageProducer")
    private MessageProducer messageProducer;

    @Value("${mq.producer.cancel.result.topic}")
    private String topic;

    @Override
    public OutboundCreateResponse create(OutboundCreateRequest request) {
        throw new RuntimeException("此功能已下线");
    }

    @Override
    public OutboundCreateResponse redrive(OutboundCreateRequest request) {
        throw new RuntimeException("此功能已下线");
    }

    @Override
    public OutboundModifyResponse modify(OutboundModifyRequest request) {

//        if (Objects.isNull(request) || StringUtils.isBlank(request.getOrderNo())) {
//            throw new RuntimeException("订单号为空");
//        }
        try {
            if (request == null) {
                throw new IllegalArgumentException("请求参数为空");
            }
            before(request);
            OrderFulfillmentModel persistenceModel = persistenceAbility.getModelByOrderNo(request.getOrderNo());
            if (persistenceModel == null) {
                log.warn("修改订单时，入参订单号在OFW中不存在，orderNo:{}", request.getOrderNo());
                throw new IllegalArgumentException("订单不存在");
            }
            ModifyCommand modifyCommand = ModifyCommand.builder()
                    .orderNo(request.getOrderNo())
                    .operator(request.getOperator())
                    .operateTime(request.getOperateTime())
                    .basicInfo(outboundModelTranslator.toBasicInfo(request.getExtendProps()))
                    .cargos(outboundModelTranslator.toCargoModel(request.getCargos()))
                    .goods(convertGoods(request.getGoods()))
                    .consignee(outboundModelTranslator.toConsigneeModel(request.getConsignee()))
                    .shipment(outboundModelTranslator.toShipment(request.getShipment()))
                    .warehouse(outboundModelTranslator.toWarehouseInfo(request.getWarehouse()))
                    .solution(outboundModelTranslator.toSolution(request))
                    .fulfillment(outboundModelTranslator.toFulfillment(request))
                    .fulfillmentCommand(outboundModelTranslator.toFulfillmentCommand(request.getFulfillmentCommand()))
                    //暂时保留
                    .heavyGoodsUpstairs(request.getHeavyGoodsUpstairs())
                    .occupyResult(request.getOccupyResult())
                    //废弃 TODO 完全切换后删除
                    .extendProps(request.getExtendProps())
                    .outboundUrgency(request.getOutboundUrgency())
                    .shouldPayMoney(request.getShouldPayMoney())
                    .purchaseOrderNo(request.getPurchaseOrderNo())
                    .products(buildProducts(request.getProducts()))
                    .deliveryIntoWarehouse(request.getDeliveryIntoWarehouse())
                    .enterHouseAppointment(request.getEnterHouseAppointment())
                    .reReceiveMode(request.getReReceiveMode())
                    .waybillNo(request.getWaybillNo())
                    .deliveryOrderNo(request.getDeliveryOrderNo())
                    .fulfillmentThirdWaybillNo(request.getFulfillmentThirdWaybillNo())
                    .systemCaller(request.getSystemCaller())
                    .deleteShipperInfo(request.getDeleteShipperInfo())
                    .orderSign(outboundModelTranslator.toOrderSign(request.getOrderSign()))
                    .channel(outboundModelTranslator.toChannel(request.getChannel()))
                    .subOrderNos(request.getSubOrderNos())
                    .build();
            OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                    .businessIdentity(
                            new BusinessIdentity(persistenceModel.getBatrixInfo().getBusinessUnit(), StringUtils.defaultIfBlank(request.getBusinessType(), businessType), BusinessScene.MODIFY))
                    .command(modifyCommand)
                    .build();
            modelService.modify(modifyCommand, context);
            log.info("OFW修改成功，orderNo:{}", request.getOrderNo());
        } catch (IllegalArgumentException illegalArgumentException) {
            log.error("修改失败,请求参数异常，orderNo:{}", request != null ? request.getOrderNo() : "null", illegalArgumentException);
            OutboundModifyResponse errorResponse = OutboundModifyResponse.builder()
                    .code(ResponseConstant.FAILED_CODE)
                    .message(illegalArgumentException.getMessage())
                    .build();
            return errorResponse;
        } catch (AcquireLockException acquireLockException) {
            log.error("修改失败,并发锁加锁异常，orderNo:{}", request.getOrderNo());
            OutboundModifyResponse errorResponse = OutboundModifyResponse.builder()
                    .code(ResponseConstant.FAILED_CODE)
                    .message(acquireLockException.getMessage())
                    .build();
            return errorResponse;
        } catch (Exception e) {
            log.error("修改失败,orderNo:{}", request.getOrderNo(), e);
            OutboundModifyResponse errorResponse = OutboundModifyResponse.builder()
                    .code(ResponseConstant.FAILED_CODE)
                    .message(e.getMessage())
                    .build();
            Watcher.fault(e);
            return errorResponse;
        }
        return OutboundModifyResponse.builder()
                .code(ResponseConstant.SUCCESS_CODE)
                .build();
    }

    private List<Product> buildProducts(List<com.jdl.sc.ofw.outbound.dto.vo.Product> products) {
        if (products == null) {
            return new ArrayList<>();
        }
        return products.stream().map(product -> Product.builder()
                        .no(product.getNo())
                        .name(product.getName())
                        .type(ObjectUtils.nullIfNull(product.getType(), ProductTypeEnum::fromCode))
                        .attributes(ObjectUtils.defaultIfNull(product.getAttributes(), new HashMap<String, String>()))
                        .operateType(product.getOperateType())
                        .build())
                .collect(Collectors.toList());
    }

    private void before(Object request) {
        //修改前置校验
        Notification notification = Notification.create();
        if (!jsr303Specification.isSatisfiedBy(request, notification)) {
            throw new IllegalArgumentException(notification.first().getKey() + ":" + notification.first().getValue());
        }
    }

    @Override
    public OutboundCancelResponse cancel(OutboundCancelRequest request) {
        before(request);
        OrderFulfillmentContext context = null;
        try {
            CancelCommand cancelCommand = CancelCommand.builder()
                    .channelSource(ObjectUtils.nullIfNull(request.getChannelSource(), ChannelSourceEnum::fromCode))
                    .cancelType(CancelTypeEnum.fromCode(request.getCancelType()))
                    .requestId(request.getRequestId())
                    .remark(request.getRemark())
                    .orderNo(request.getOrderNo())
                    .childOrders(request.getChildOrderNos())
                    .cancelMode(CancelModeEnum.fromCode(request.getCancelMode()))
                    .operator(request.getOperator())
                    .operateTime(request.getChannelOperateTime())
                    .deliveryIntercept(getDeliveryIntercept(request))
                    .wmsCancelResult(request.getWmsCancelResult())
                    .build();
            context = OrderFulfillmentContext.builder()
                    .businessIdentity(
                            new BusinessIdentity(
                                    StringUtils.defaultIfBlank(request.getBusinessUnit(), businessUnit),
                                    StringUtils.defaultIfBlank(request.getBusinessType(), businessType), BusinessScene.CANCEL))
                    .build();

            CancelResult cancelResult;

            if (CancelModeEnum.SPLIT_CANCEL.equals(cancelCommand.getCancelMode())) {
                cancelResult = cancelService.sourceSplitCancel(cancelCommand, context);
            } else {
                cancelResult = cancelService.cancel(cancelCommand, context);
            }

            MonitorLogger.cancel(cancelCommand, context, cancelResult);

            //TODO 取消结果通知，是不是抽成能力点？
            try {
                messageProducer.send(topic, JsonUtil.toJsonSafe(cancelResult), MessageIdUtils.getMessageIdByOrderNo(cancelCommand.getOrderNo()));
            } catch (Exception ignore) {
                log.error("cancel send mq exception ignore", ignore);
            }

            OutboundCancelResponse response = OutboundCancelResponse.builder()
                    .code(cancelResult.getState().getCode())
                    .message(cancelResult.getMessage())
                    .requestId(cancelResult.getRequestId())
                    .build();
            if (log.isInfoEnabled()) {
                log.info("cancel order,response:{}", JsonUtil.toJsonSafe(response));
            }
            return response;
        } catch (AcquireLockException e) {
            log.warn("cancel order acquireLock error,orderNo:{}", request.getOrderNo());
            throw new AcquireConcurrentLockException(e.getMessage());
        } catch (Exception e) {
            MonitorLogger.cancel(request.getOrderNo(), context, true, CancelResultStateEnum.FAIL.getCode(), e.getMessage());
            log.error("cancel order error,orderNo:{}", request.getOrderNo(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private String getDeliveryIntercept(OutboundCancelRequest request) {
        return Optional.ofNullable(request.getWmsCancelResult())
                .map(cancelResult -> jimClient.get(KeyGenerator.getCancelInterceptTmsKey(request.getOrderNo())))
                .orElse(Optional.ofNullable(request.getExtendProps())
                        .map(props -> props.get("deliveryIntercept"))
                        .orElse(null)
                );
    }

    @Override
    public OutboundCloseResponse close(OutboundCloseRequest request) {
        before(request);
        try {
            CloseCommand closeCommand = CloseCommand.builder()
                    .requestId(request.getRequestId())
                    .operateTime(request.getOperateTime())
                    .operator(request.getOperator())
                    .orderNo(request.getOrderNo())
                    .build();
            CloseResult closeResult = cancelService.close(closeCommand);
            return OutboundCloseResponse.builder()
                    .code(closeResult.getCode())
                    .message(closeResult.getMessage())
                    .requestId(closeResult.getRequestId())
                    .build();
        } catch (Exception e) {
            log.error("关单请求执行异常，orderNo:" + request.getOrderNo(), e);
            return OutboundCloseResponse.builder()
                    .code(ResponseConstant.FAILED_CODE)
                    .message(e.getMessage())
                    .requestId(request.getRequestId())
                    .build();
        }
    }

    @Override
    public OutboundInterceptResponse intercept(OutboundInterceptRequest request) {
        before(request);
        try {
            /**
             * 目前sc-request-router的拦截流程与sc-callback的wms回传流程都会调用intercept方法
             * 其中orderNo、operationType、operateTime、operator为必传属性
             * remark 只有sc-request-router的拦截流程会传
             * state、source sc-callback的wms回传流程会传，且必有值
             */
            if (StringUtils.isNotBlank(request.getSource())
                    && ChannelSourceEnum.WMS.equals(ChannelSourceEnum.fromCode(request.getSource()))) {
                return doFromWMS(request);
            }

            OutboundInterceptResponse response;
            InterceptOperationTypeEnum operationType =
                    InterceptOperationTypeEnum.fromCode(request.getOperationType());
            if (InterceptOperationTypeEnum.INTERCEPT.equals(operationType)) {
                InterceptResult interceptResult = cancelService.intercept(buildInterceptCommand(request));
                response = OutboundInterceptResponse.builder()
                        .code(interceptResult.getState().getCode())
                        .message(interceptResult.getMessage())
                        .build();
            } else {
                PassResult passResult = cancelService.pass(buildPassCommand(request));
                response = OutboundInterceptResponse.builder()
                        .code(passResult.getState().getCode())
                        .message(passResult.getMessage())
                        .build();
            }
            if (log.isInfoEnabled()) {
                log.info("intercept order,response:{}", JsonUtil.toJsonSafe(response));
            }
            return response;
        } catch (AcquireLockException e) {
            log.warn("intercept order acquireLock error,orderNo:{}", request.getOrderNo());
            throw new AcquireConcurrentLockException(e.getMessage());
        } catch (Exception e) {
            log.error("intercept order error,orderNo:{}", request.getOrderNo(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public OutboundStatusResponse modifyStatus(OutboundStatusRequest request) {
        before(request);
        if (Objects.isNull(request) || StringUtils.isBlank(request.getOrderNo())) {
            throw new RuntimeException("订单号为空");
        }
        try {
            ModifyStatusCommand modifyStatusCommand = ModifyStatusCommand.builder()
                    .orderNo(request.getOrderNo())
                    .operator(request.getOperateUser())
                    .operateTime(request.getOperateTime())
                    .status(request.getStatus())
                    .build();
            modelService.modifyStatus(modifyStatusCommand);
        } catch (AcquireLockException e) {
            log.warn("Modify status acquireLock error,orderNo:{}", request.getOrderNo());
            throw new AcquireConcurrentLockException(e.getMessage());
        } catch (DomainServiceException e) {
            log.error("Modify status error，订单号不存在,orderNo:{}", request.getOrderNo(), e);
            throw e;
        } catch (Exception e) {
            log.error("Modify status error,orderNo:{}", request.getOrderNo(), e);
            throw new RuntimeException(e.getMessage());
        }
        return OutboundStatusResponse.builder().build();
    }

    @Override
    public void pullback(OutboundPullbackRequest request) {
        before(request);
        try {
            PullbackCommand pullbackcommand = PullbackCommand.builder()
                    .operateTime(request.getOperateTime())
                    .operator(request.getOperator())
                    .orderNo(request.getOrderNo())
                    .skipPause(request.skipPause())
                    .build();
            modelService.pullback(pullbackcommand);
        } catch (AcquireLockException e) {
            log.warn("Pullback acquireLock error,orderNo:{}", request.getOrderNo());
            throw new AcquireConcurrentLockException(e.getMessage());
        } catch (Exception e) {
            log.error("Pullback error,orderNo:{}", request.getOrderNo(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void modifyWaybill(OutboundModifyWaybillRequest request) {
        before(request);
        try {
            OutboundModifyWaybillCommand byHandCommand = OutboundModifyWaybillCommand.builder()
                    .orderNo(request.getOrderNo())
                    .receiveSmileType(request.getReceiveSmileType())
                    .deliveryType(request.getDeliveryType())
                    .roadArea(request.getRoadArea())
                    .siteId(request.getSiteId())
                    .siteName(request.getSiteName())
                    .siteType(request.getSiteType())
                    .aoiCode(request.getAoiCode())
                    .tmsDeliveryType(request.getTmsDeliveryType())
                    .pauseReasonEnum(request.getPauseReasonEnum())
                    .skipPipeTms(request.getSkipPipeTms())
                    .build();
            modelService.modifyWaybill(byHandCommand);
        } catch (AcquireLockException e) {
            log.warn("modifyWaybill acquireLock error,orderNo:{}", request.getOrderNo());
            throw new AcquireConcurrentLockException(e.getMessage());
        } catch (Exception e) {
            log.error("modifyWaybill error,orderNo:{}", request.getOrderNo(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private OutboundInterceptResponse doFromWMS(OutboundInterceptRequest request) throws Exception {
        log.info("orderNo:{},收到wms回传拦截结果为:{}", request.getOrderNo(), request.getInterceptResultStatus());
        // 判断订单是否存在
        OrderFulfillmentModel persistenceModel = persistenceAbility.getModelByOrderNo(request.getOrderNo());
        if (persistenceModel == null) {
            return OutboundInterceptResponse.builder()
                    .code(InterceptStateEnum.SUCCESS.getCode())
                    .message("订单不存在")
                    .build();
        }
        InterceptCommand interceptCommand = buildInterceptCommand(request);
        updateIntercept(interceptCommand, persistenceModel, interceptCommand.getState());
        return OutboundInterceptResponse.builder()
                .code(interceptCommand.getState().getCode())
                .message(interceptCommand.getState().getName())
                .build();
    }

    private InterceptCommand buildInterceptCommand(OutboundInterceptRequest request) {
        return InterceptCommand.builder()
                .orderNo(request.getOrderNo())
                .operationType(request.getOperationType())
                .operateTime(request.getOperateTime())
                .operator(request.getOperator())
                .remark(request.getRemark())
                .state(request.getInterceptResultStatus() != null ? InterceptStateEnum.fromCode(request.getInterceptResultStatus()) : null)
                .source(request.getSource() != null ? ChannelSourceEnum.fromCode(request.getSource()) : null)
                .build();
    }

    private PassCommand buildPassCommand(OutboundInterceptRequest request) {
        return PassCommand.builder()
                .orderNo(request.getOrderNo())
                .operationType(request.getOperationType())
                .operateTime(request.getOperateTime())
                .operator(request.getOperator())
                .remark(request.getRemark())
                .state(request.getInterceptResultStatus() != null ? InterceptStateEnum.fromCode(request.getInterceptResultStatus()) : null)
                .source(request.getSource() != null ? ChannelSourceEnum.fromCode(request.getSource()) : null)
                .build();
    }

    /**
     * 为model增加审计信息及拦截信息，并落库
     * remark取值逻辑，如果库中拦截信息remark有值，则用库中的（防止被wms回传的空值覆盖）。如果传入的拦截信息remark有值，则取最新的
     *
     * @param interceptCommand 拦截指令
     * @param persistenceModel 订单信息
     * @param state            拦截最终状态
     */
    protected void updateIntercept(
            InterceptCommand interceptCommand,
            OrderFulfillmentModel persistenceModel,
            InterceptStateEnum state) {
        String remarkValue = ObjectUtils.defaultIfNull(
                interceptCommand.getRemark(), remark -> remark,
                ObjectUtils.nullIfNull(persistenceModel.getIntercept(), Intercept::getRemark));
        Audit audit = Audit.builder()
                .operateType(OperateType.INTERCEPT)
                .operateTime(interceptCommand.getOperateTime())
                .operator(interceptCommand.getOperator())
                .build();
        persistenceModel.assignIntercept(
                Intercept.builder()
                        .operationType(interceptCommand.getOperationType())
                        .remark(remarkValue)
                        .state(interceptCommand.getState() != null ? interceptCommand.getState() : state)
                        .source(interceptCommand.getSource())
                        .build(), audit
        );
        persistenceAbility.persist(persistenceModel);
    }


    @Override
    @Ump
    public OutboundCreateResponse createByAgent(OutboundCreateRequest request) {
        before(request);
        return outboundCreateAgent.create(request, false);
    }

    @Override
    public OutboundRetransportResponse retransport(OutboundRetransportRequest request) {
        return modelService.retransport(request);
    }

    public boolean isNewFlow(String businessType) {
        return this.businessType.equals(businessType);
    }

    private List<Goods> convertGoods(List<com.jdl.sc.ofw.outbound.dto.vo.Goods> goods) {
        if (CollectionUtils.isEmpty(goods)) {
            return null;
        } else {
            return goods.stream()
                    .map(good -> outboundModelTranslator.goodsBuilderCreate(good).build())
                    .collect(Collectors.toList());
        }
    }
}
