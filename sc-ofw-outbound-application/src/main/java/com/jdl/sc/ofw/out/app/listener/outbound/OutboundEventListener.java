package com.jdl.sc.ofw.out.app.listener.outbound;

import cn.jdl.oms.search.dto.Order;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.OutboundEvent;
import com.jdl.sc.ofw.out.app.listener.outbound.process.OutboundEventProcessor;
import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.app.service.constants.BusinessIdentityEnums;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractFilterEnvMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
public class OutboundEventListener extends AbstractFilterEnvMessageTemplate {

    @Resource
    private OrderConvertServiceAcl orderConvertServiceAcl;

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private OutboundEventProcessor outboundEventProcessor;

    public OutboundEventListener(@Qualifier("eclpBdPredicate") MessageReceive messageReceive) {
        super(messageReceive);
    }

    @Override
    protected void onMessage(String message) throws Exception {
        if (StringUtils.isBlank(message)) {
            return;
        }

        OutboundEvent outboundEvent = JsonUtil.readValue(message, OutboundEvent.class);
        String orderNo = orderConvertServiceAcl.convert(outboundEvent.getOrderNo());
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        if (!orderCenterAcl.checkOrderExist(orderNo)) {
            return;
        }
        Order order = orderCenterAcl.queryOrderByOrderNo(orderNo, Arrays.asList("Product", "Identity"));
        if (order == null) {
            log.error("根据订单号查询订单详情失败，orderNo:{}", orderNo);
            throw new RuntimeException("根据订单号查询订单详情失败");
        }
        if (!isLocalOnDemandDeliveryOrder(order)) {
            return;
        }

        if (BusinessIdentityEnums.CN_JDL_SC_INSTANT_FULFILLMENT.getCode().equals(order.getBusinessIdentity().getBusinessUnit())) {
            return;
        }

        outboundEventProcessor.process(orderNo);
    }

    /**
     * 根据订单中心订单中的产品判断是否同城速配订单
     */
    private boolean isLocalOnDemandDeliveryOrder(Order order) {
        if (CollectionUtils.isEmpty(order.getProductInfos())) {
            return false;
        }
        return order.getProductInfos().stream()
                .anyMatch(productInfo -> ProductEnum.TCSP.getCode().equals(productInfo.getProductNo()));
    }

}