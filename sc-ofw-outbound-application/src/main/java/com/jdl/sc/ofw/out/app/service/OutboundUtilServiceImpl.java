package com.jdl.sc.ofw.out.app.service;

import cn.jdl.batrix.core.base.BIDomainAbilityExtension;
import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.search.dto.Order;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.WriteContext;
import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.ldop.center.api.ResponseDTO;
import com.jd.ldop.center.api.update.WaybillUpdateApi;
import com.jd.ldop.center.api.update.dto.WaybillUpdateBeforePickupDTO;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.lfc.trans.pipe.api.wms.TransPipeJDWmsService;
import com.jdl.sc.lfc.trans.pipe.dto.wms.JDWmsReceiveRequest;
import com.jdl.sc.ofw.out.domain.ability.PersistenceAbility;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.rpc.WarehouseServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.util.SpringContextUtil;
import com.jdl.sc.ofw.outbound.api.OutboundUtilService;
import com.jdl.sc.ofw.outbound.dto.OutboundOldFlowResponse;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * FileName: OutboundUtilServiceImpl
 * Description:
 * [@author]:   quhuafeng
 * [@date]:     2023/7/55:35 PM
 */

@Component("outboundUtilServiceImpl")
@Slf4j
public class OutboundUtilServiceImpl implements OutboundUtilService {

    @Autowired
    private PersistenceAbility persistenceAbility;

    @Autowired
    private SpringContextUtil springContextUtil;

    private static final String OLD_BUSINESS_UNIT = "cn_jdl_sc";

    private static final String NEW_BUSINESS_TYPE="sc_ofc_outbound_sale";

    private static final String NEW_BUSINESS_SCENE="fulfillmentGenerate";

    private static List<String> OFW_BUSINESS_UNIT_LIST = new ArrayList<>();
    static {
        OFW_BUSINESS_UNIT_LIST.add("cn_jdl_sc-isv");
        OFW_BUSINESS_UNIT_LIST.add("cn_jdl_sc-pop");
        OFW_BUSINESS_UNIT_LIST.add("cn_jdl_ka-jdr-appraisal-sale");
        OFW_BUSINESS_UNIT_LIST.add("cn_jdl_ka-jdr-appraisal");
    }

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private WaybillUpdateApi waybillUpdateApi;
    @Resource
    TransPipeJDWmsService transPipeJDWmsService;

    @Autowired
    private WarehouseServiceAcl warehouseServiceAcl;

    @Override
    public void repair(String orderNo, String key, String value) {
        log.info("model数据修正开始：orderNo:{}, key:{}, 修改值为：{}", orderNo, key, value);
        if(StringUtils.isBlank(orderNo) || StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("请求参数不正确");
        }
        OrderFulfillmentModel model = persistenceAbility.getModelByOrderNo(orderNo);
        if(model == null ) {
            log.info("该单号不存在。orderNo:{}", orderNo);
            return;
        }
        log.info("数据修复开始,原数据：{}", JsonUtil.toJsonSafe(model));
        WriteContext ctx = JsonPath.parse(JsonUtil.toJsonSafe(model));
        String newModelJson = ctx.set(key, value).jsonString();
        OrderFulfillmentModel newModel = JsonUtil.readValueSafe(newModelJson, OrderFulfillmentModel.class);
        // orderStatus和Audit使用了JsonIgnore，需要重新赋值回来。
        newModel.assignOrderStatus(model.getOrderStatus(), model.getAudit());
        if(newModel != null) {
            persistenceAbility.persist(newModel);
        }
        log.info("数据修复完成，修改后数据：{}", JsonUtil.toJsonSafe(newModel));
    }

    @Override
    public void addKey(String orderNo, String path, String key, String value) {
        log.info("model数据修正开始：orderNo:{}, key:{}, 新增值为：{}", orderNo, key, value);
        if (StringUtils.isBlank(orderNo) || StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("请求参数不正确");
        }
        OrderFulfillmentModel model = persistenceAbility.getModelByOrderNo(orderNo);
        if (model == null) {
            log.info("该单号不存在。orderNo:{}", orderNo);
            return;
        }
        log.info("数据修复开始,原数据：{}", JsonUtil.toJsonSafe(model));
        WriteContext ctx = JsonPath.parse(JsonUtil.toJsonSafe(model));
        String newModelJson = ctx.put(path, key, value).jsonString();
        OrderFulfillmentModel newModel = JsonUtil.readValueSafe(newModelJson, OrderFulfillmentModel.class);
        // orderStatus和Audit使用了JsonIgnore，需要重新赋值回来。
        newModel.assignOrderStatus(model.getOrderStatus(), model.getAudit());
        if (newModel != null) {
            persistenceAbility.persist(newModel);
        }
        log.info("数据修复完成，修改后数据：{}", JsonUtil.toJsonSafe(newModel));
    }

    @Override
    public void modelRepair(RepairRequest request) {
        if (log.isInfoEnabled()) {
            log.info("model数据修正开始：request:{}", JsonUtil.toJsonSafe(request));
        }
        if (StringUtils.isBlank(request.getOrderNo())) {
            throw new IllegalArgumentException("订单号为空");
        }
        if (StringUtils.isBlank(request.getMethod())) {
            request.setMethod("set");
        }
        OrderFulfillmentModel model = persistenceAbility.getModelByOrderNo(request.getOrderNo());
        if (model == null) {
            log.info("该单号不存在。orderNo:{}", request.getOrderNo());
            return;
        }

        log.info("数据修复开始,原数据：{}", JsonUtil.toJsonSafe(model));
        WriteContext ctx = JsonPath.parse(JsonUtil.toJsonSafe(model));
        if ("set".equals(request.getMethod())) {
            ctx.set(request.getPath(), request.getValue());
        } else if ("put".equals(request.getMethod())) {
            ctx.put(request.getPath(), request.getKey(), request.getValue());
        } else if ("renameKey".equals(request.getMethod())) {
            ctx.renameKey(request.getPath(), request.getKey(), request.getNewKeyName());
        } else if ("delete".equals(request.getMethod())) {
            ctx.delete(request.getPath());
        }
        String newModelJson = ctx.jsonString();

        OrderFulfillmentModel newModel = JsonUtil.readValueSafe(newModelJson, OrderFulfillmentModel.class);
        // orderStatus和Audit使用了JsonIgnore，需要重新赋值回来。
        newModel.assignOrderStatus(model.getOrderStatus(), model.getAudit());
        if (newModel != null) {
            persistenceAbility.persist(newModel);
        }
        log.info("数据修复完成，修改后数据：{}", JsonUtil.toJsonSafe(newModel));
    }

    @Override
    public void repaidStatus(String orderNo, Integer orderStatus) {

        log.info("model订单状态修正开始：orderNo:{}, orderStatus:{}", orderNo, orderStatus);
        if(StringUtils.isBlank(orderNo) || orderStatus == null) {
            throw new IllegalArgumentException("请求参数不正确");
        }

        OrderFulfillmentModel model = persistenceAbility.getModelByOrderNo(orderNo);
        if(model == null ) {
            log.info("该单号不存在。orderNo:{}", orderNo);
            return;
        }
        model.assignOrderStatus(OrderStatusEnum.fromCode(orderStatus));
        persistenceAbility.persist(model);
        log.info("model订单状态修正完成：orderNo:{}, orderStatus:{}", orderNo, orderStatus);
    }


    @Override
    public void executeExt(String orderNo, String extName, String className) {
        try {
            BIDomainAbilityExtension ext = springContextUtil.getExtBean(extName);
            Class<?> clazz = Class.forName(className);
            Method method = clazz.getDeclaredMethod("execute", OrderFulfillmentContext.class);
            OrderFulfillmentModel model = persistenceAbility.getModelByOrderNo(orderNo);
            BusinessIdentity businessIdentity = new BusinessIdentity(" "," "," ");
            OrderFulfillmentContext cxt = OrderFulfillmentContext.builder().model(model).businessIdentity(businessIdentity).build();
            method.invoke(ext, cxt);
            persistenceAbility.persist(model);
            log.info("执行完成,单号：{}", orderNo);
        } catch (Exception e) {
            log.error("调用ext发生异常", e);
        }
    }
}
