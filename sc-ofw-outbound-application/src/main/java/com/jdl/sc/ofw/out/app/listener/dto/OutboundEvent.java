package com.jdl.sc.ofw.out.app.listener.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class OutboundEvent implements Serializable {

    /**
     * 单号
     */
    @JsonProperty(required = true)
    private String orderNo;

    /**
     * 产品/解决方案
     */
    @JsonProperty(required = true)
    private String product;

}