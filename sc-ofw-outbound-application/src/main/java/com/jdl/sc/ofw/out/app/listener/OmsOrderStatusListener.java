package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jd.serviceplus.order.dto.ofc.OFCCancelOrderRequest;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OmsOrderStatusData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OmsOrderStatusMessage;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ServicePlusAcl;
import com.jdl.sc.ofw.out.horiz.infra.util.BusinessIdentityUtil;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.util.Objects;

/**
 * 监听 outbound_order_info 消息
 */
@Slf4j
public class OmsOrderStatusListener extends AbstractMessageTemplate {
    private static final String CLPS_BUSINESS_TYPE = "cn_jdl_clps";

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private ServicePlusAcl servicePlusAcl;

    @Override
    protected void onMessage(String message) throws Exception {
        if (message == null) {
            if (log.isInfoEnabled()) {
                log.info("OmsOrderStatusListener.message为空消息无需处理");
            }
            return;
        }
        OmsOrderStatusMessage omsOrderStatusMessage;
        try {
            omsOrderStatusMessage = JsonUtil.readValue(message, OmsOrderStatusMessage.class);
        } catch (Exception e) {
            log.error("OmsOrderStatusListener.onMessage 序列化异常", e);
            throw new RuntimeException("OmsOrderStatusListener.onMessage 序列化异常:" + e.getMessage());
        }

        try {
            validate(omsOrderStatusMessage);

            // 状态过滤 只处理取消和拒收
            if (!needHandle(omsOrderStatusMessage)) {
                return;
            }
            String orderNo = omsOrderStatusMessage.getOrderInfo().getOrderNo();

            OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);
            if (Objects.isNull(model)) {
                log.info("{}未查询到订单信息，忽略", orderNo);
                return;
            }
            cancelServicePlusOrder(model, omsOrderStatusMessage);
        } catch (ValidationException e) {
            log.warn("OmsOrderStatusListener忽略消息:{} text: {}", e.getMessage(), message);
        } catch (Exception e) {
            log.error("OmsOrderStatusListener处理异常", e);
            throw e;
        }
    }

    /**
     * 取消服务家订单
     */
    private void cancelServicePlusOrder(OrderFulfillmentModel model, OmsOrderStatusMessage omsOrderStatusMessage) {
        if (!model.isHaveServicePlusOrder()) {
            return;
        }
        if (OrderStatusEnum.REJECT.getOmsCode().equals(omsOrderStatusMessage.getOrderInfo().getOrderStatus())) {
            try {
                log.info("{}订单已拒收，取消送装服务", model.orderNo());
                servicePlusAcl.cancel(
                        OFCCancelOrderRequest.builder()
                                .orderNumber(model.orderNo())
                                .companyCode(model.getCustomer().getAccountNo())
                                .cancelReason("订单已拒收，不再需要送装服务")
                                .build()
                );
            } catch (Exception e) {
                log.error("{}取消送装服务失败{}", model.orderNo(), e.getMessage());
            }
        }
    }

    /**
     * 状态过滤 目前只处理
     * 拒收
     */
    private static boolean needHandle(OmsOrderStatusMessage omsOrderStatusMessage) {
        if (OrderStatusEnum.REJECT.getOmsCode().equals(omsOrderStatusMessage.getOrderInfo().getOrderStatus())) {
            return true;
        }
        return false;
    }

    /**
     * 订单广播消息校验
     */
    private void validate(OmsOrderStatusMessage omsOrderStatusMessage) {
        if (omsOrderStatusMessage == null) {
            throw new ValidationException("订单广播消息解析结果为空无需处理");
        }
        if (omsOrderStatusMessage.getOrderInfo() == null) {
            throw new ValidationException("订单广播消息解析结果OrderInfo为空无需处理");
        }
        OmsOrderStatusData orderInfo = omsOrderStatusMessage.getOrderInfo();
        // 身份校验 除VMI业务身份（cn_jdl_sc-jdr-vmi）之外的所有消息处理
        if (isVmiBusinessIdentity(orderInfo.getBusinessIdentity())) {
            throw new ValidationException("订单广播消息解析结果BusinessIdentity是VMI业务身份无需处理");
        }
    }

    /**
     * 判断是vmi订单
     *
     * @param businessIdentity 身份信息
     * @return true=是vmi false=非vmi
     */
    private boolean isVmiBusinessIdentity(BusinessIdentity businessIdentity) {
        return businessIdentity != null
                && ((BusinessIdentityUtil.OmsBusinessEnum.VMI_ORDER.getUnit().equals(businessIdentity.getBusinessUnit())
                && BusinessIdentityUtil.OmsBusinessEnum.VMI_ORDER.getType().equals(businessIdentity.getBusinessType())
                || (StringUtils.isNotBlank(businessIdentity.getBusinessUnit()) && businessIdentity.getBusinessUnit().startsWith(CLPS_BUSINESS_TYPE))));
    }

}
