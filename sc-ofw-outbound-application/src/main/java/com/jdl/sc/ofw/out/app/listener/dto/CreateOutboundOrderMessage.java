package com.jdl.sc.ofw.out.app.listener.dto;


import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.Profile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * CreateOutboundOrderMessage简介
 * 订单中心对接销售出库接单
 *
 * <AUTHOR>
 * @date 2021-04-12 15:23
 */
//对接订单中心对象需要通过jackson反序列化，不能使用builder
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOutboundOrderMessage {

    private Profile profile;

    private OrderData data;

}

