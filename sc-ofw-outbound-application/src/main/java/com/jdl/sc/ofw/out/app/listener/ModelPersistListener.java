package com.jdl.sc.ofw.out.app.listener;


import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.ump.Ump;
import com.jdl.sc.ofw.out.domain.dto.HbasePersistMqMessage;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ModelPersistListener extends AbstractMessageTemplate {

    @Resource
    private OutboundModelRepository repository;

    @Override
    @Ump
    public void onMessage(String message) throws Exception {
        HbasePersistMqMessage result = JsonUtil.readValue(message, HbasePersistMqMessage.class);
        if (result == null) {
            log.error("message 序列化为空, 丢弃不处理");
            return;
        }
        if (result.getModel() != null) {
            repository.persist(result.getModel());
            log.info("ModelPersistListener，异步持久化完成，orderNo:{}", result.getModel().getOrderNo());
        }

    }
}
