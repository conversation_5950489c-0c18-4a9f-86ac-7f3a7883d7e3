package com.jdl.sc.ofw.out.app.service.oms;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.ModifyOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.ModifyOutboundOrderOfcResponse;
import cn.jdl.ofc.supplychain.api.oms.ModifyOutboundOrderOfcService;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.search.dto.Order;
import com.google.common.collect.Lists;
import com.jd.ldop.center.api.update.dto.WaybillUpdateBeforePickupDTO;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.horiz.infra.rpc.WaybillIntegrateReceiveAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.OrderCenterRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("modifyAppraisalOrderOfcService")
public class ModifyAppraisalOrderOfcServiceImpl implements ModifyOutboundOrderOfcService {

    private static final String ERROR_CODE = "2";

    @Resource
    private OrderCenterRpc orderCenterRpc;

    @Resource
    private WaybillIntegrateReceiveAcl waybillIntegrateReceiveAcl;

    @Override
    public ModifyOutboundOrderOfcResponse modifyOrder(RequestProfile requestProfile, ModifyOutboundOrderOfcRequest request) {
        ModifyOutboundOrderOfcResponse response = new ModifyOutboundOrderOfcResponse();
        try {
            String orderNo = request.getOrderNo();
            //1.校验
            if (request.getConsignorInfo() == null) {
                log.error("发货人信息不能为空，request:{}", JsonUtil.toJsonSafe(request));
                response.setCode(ERROR_CODE);
                response.setMessage(orderNo + "发货人信息不能为空");
                return response;
            }
            //2.根据出库单号查运单号
            Order order = orderCenterRpc.queryOrderByOrderNo(orderNo, Lists.newArrayList("RefOrder"));
            if (order == null || order.getRefOrderInfo() == null || CollectionUtils.isEmpty(order.getRefOrderInfo().getWaybillNos())) {
                log.error("出库单号{}未查到运单号，order:{}", orderNo, JsonUtil.toJsonSafe(order));
                response.setCode(ERROR_CODE);
                response.setMessage(orderNo + "订单中心未查到对应的运单号");
                return response;
            }
            //3.根据运单号修改运单地址
            waybillIntegrateReceiveAcl.modifyAppraisal(createWaybillUpdateBeforePickupDTO(request, order.getRefOrderInfo().getWaybillNos().get(0)));
            response.setCode(String.valueOf(ResponseConstant.SUCCESS_CODE));
            response.setMessage("修改成功");
            return response;
        } catch (Exception e) {
            log.error("调用青龙接口修改鉴定单一段物流地址接口异常：", e);
            response.setCode(ERROR_CODE);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    private WaybillUpdateBeforePickupDTO createWaybillUpdateBeforePickupDTO(ModifyOutboundOrderOfcRequest request, String waybillNo) {
        ConsignorInfo consignorInfo = request.getConsignorInfo();
        WaybillUpdateBeforePickupDTO updateDto = new WaybillUpdateBeforePickupDTO();
        updateDto.setWaybillCode(waybillNo);
        if (consignorInfo.getAddressInfo() != null) {
            updateDto.setSenderAddress(consignorInfo.getAddressInfo().getAddress());
        }
        updateDto.setSenderCompany(consignorInfo.getConsignorCompany());
        updateDto.setSenderIdNumber(consignorInfo.getConsignorIdNo());
        updateDto.setSenderMobile(consignorInfo.getConsignorMobile());
        updateDto.setSenderName(consignorInfo.getConsignorName());
        updateDto.setSenderTel(consignorInfo.getConsignorPhone());
        if (request.getConsigneeInfo() != null && request.getConsigneeInfo().getReceiveWarehouse() != null) {
            updateDto.setWarehouseCode(request.getConsigneeInfo().getReceiveWarehouse().getWarehouseNo());
        }
        return updateDto;
    }
}
