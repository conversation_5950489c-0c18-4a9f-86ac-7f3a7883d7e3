package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.core.model.CustomerInfo;
import com.jd.jmq.common.message.Message;
import com.jd.traceholder.TraceHolder;
import com.jdl.sc.core.json.GsonUtil;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.ump.Ump;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.app.listener.dto.CreateOutboundOrderMessage;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderClobData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.CustomerAddressInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.EcpOrderInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.ExtendedField;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentFlag;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.PrintExtendInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.PrintInfo;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.out.common.mq.MessageProducer;
import com.jdl.sc.ofw.out.domain.rpc.EclpJimkvLoadServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import com.jdl.sc.oss.OSSClient;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 销售出订单抽象处理类
 */
@Slf4j
public abstract class AbstractSaleOutboundOrderListener extends AbstractMessageTemplate {

    private final OutboundService outboundService;

    private final ReceiveOrderTranslator receiveOrderTranslator;

    @Setter
    private final String businessType;

    @Resource
    private EclpJimkvLoadServiceAcl eclpJimkvLoadServiceAcl;

    @Value("${jmq4.producer.sync.ofw.es.topic}")
    private String syncTopic;

    @Resource(name = "jmq4MessageProducer")
    private MessageProducer messageProducer;

    @Resource(name = "omsOssClient")
    private OSSClient omsOssClient;

    private static final String ECLP_JIMKV_PREFIX = "jfs/kveclp";


    public AbstractSaleOutboundOrderListener(OutboundService outboundService, ReceiveOrderTranslator receiveOrderTranslator, String businessType) {
        this.outboundService = outboundService;
        this.receiveOrderTranslator = receiveOrderTranslator;
        this.businessType = businessType;
    }

    /**
     * 灰度处理
     * @param orderMessage
     * @param message
     * @return
     */
    private boolean isGrayscale(CreateOutboundOrderMessage orderMessage, String message) {
        String topic = TraceHolder.get(TRACE_HOLDER_TOPIC_KEY);
        if (StringUtils.isBlank(topic)) {
            return false;
        }
        String topicStarts = GRAYSCALE_TOPIC_PREFIX;
        if (TraceHolder.isForcebot()) {
            topicStarts = "shadow_" + topicStarts;
        }
        if (topic.startsWith(topicStarts)) {
            return false;
        }
        String orderNo = orderMessage.getData().getOrderNo();

        String accountNo = Optional.ofNullable(orderMessage)
                .map(CreateOutboundOrderMessage::getData)
                .map(OrderData::getCustomerInfo)
                .map(CustomerInfo::getAccountNo)
                .orElse(null);
        String businessUnit = Optional.ofNullable(orderMessage)
                .map(CreateOutboundOrderMessage::getData)
                .map(OrderData::getBusinessIdentity)
                .map(BusinessIdentity::getBusinessUnit)
                .orElse(null);

        //灰度环境处理
        try {
            boolean couldGrayscale = grayscaleAdapter.couldGrayscale(orderNo, accountNo, businessUnit,
                    StringUtils.join(".", this.getClass().getName(), "isGrayscale"));
            if (couldGrayscale) {
                topic = GRAYSCALE_TOPIC_PREFIX + topic;
                messageProducer.send(topic, message, orderNo);
                log.info("onMessage 灰度环境转发消息成功,topic:{},orderNo:{},accountNo:{},businessUnit:{}",
                        topic, orderNo, accountNo, businessUnit);
                return true;
            }
        } catch (Throwable e) {
            log.warn("onMessage 灰度环境处理异常,orderNo:{},accountNo:{},businessUnit:{}", orderNo, accountNo, businessUnit);
        }
        return false;
    }
    @Ump
    @Override
    public void onMessage(String message) {
        CreateOutboundOrderMessage createOutboundOrderMessage;
        try {
            createOutboundOrderMessage = JsonUtil.readValue(message, CreateOutboundOrderMessage.class);
        } catch (Exception e) {
            log.error("message 序列化异常", e);
            throw new RuntimeException("message 序列化异常:" + e.getMessage());
        }

        if (isGrayscale(createOutboundOrderMessage, message)) {
            return;
        }

        OutboundCreateRequest request;
        try {
            final OrderData inputData = createOutboundOrderMessage.getData();
            this.parseOrderClob(inputData);
            request = this.buildReceiveRequestByOmsMsg(inputData);
        } catch (RuntimeException e) {
            log.error("request转化异常", e);
            throw e;
        }
        try {
            if(TraceHolder.isForcebot()) {
                log.info("当前消息为压测流量，订单号:{}", createOutboundOrderMessage.getData().getOrderNo());
            }
        } catch (Exception e) {
            log.warn("压测标识读取失败", e);
        }
        try {
            log.info("create入参:{}", JsonUtil.toJsonSafe(request));
            this.outboundService.createByAgent(request);
        } catch (Throwable t) {
            log.error("接单失败, failInfo:", t);
            throw t;
        }
        try {
            String orderNo = createOutboundOrderMessage.getData().getOrderNo();
            //同步OFW-ES运维端
            if(!TraceHolder.isForcebot()) {
                messageProducer.send(syncTopic, message, orderNo);
            }
        } catch (Exception e) {
            log.error("接单ES同步消息发送失败, message:{}", message, e);
        }
    }


    private void parseOrderClob(OrderData inputData) {

        final String orderClobOssKey = MapUtils.getString(inputData.getExtendProps(), ReceiveOrderTranslator.ExtendPropsEnum.ORDER_CLOB_OSS_KEY.getKey());
        if (StringUtils.isBlank(orderClobOssKey) && (CollectionUtils.isEmpty(inputData.getCargoInfos()) || CollectionUtils.isEmpty(inputData.getGoodsInfos()))){
            log.error("接单数据校验, 订单中心下发货品数据为空 orderNo:{}", inputData.getOrderNo());
            throw new RuntimeException("接单数据校验, 订单中心下发货品数据为空 orderNo:" + inputData.getOrderNo());
        }
        if (StringUtils.isBlank(orderClobOssKey)){
            return;
        }
        log.info("接单大报文数据解析 orderNo:{}", inputData.getOrderNo());
        final OrderClobData orderClobData = Optional.of(orderClobOssKey)
                .map(ossKey -> omsOssClient.get(ossKey))
                .map(orderClobOssDate -> GsonUtil.fromJson(orderClobOssDate, OrderClobData.class))
                .orElseThrow(() -> new RuntimeException("接单大报文数据序列化异常"));
        inputData.assignCargoInfos(orderClobData.getCargoInfos());
        inputData.assignGoodsInfos(orderClobData.getGoodsInfos());
    }

    protected OutboundCreateRequest buildReceiveRequestByOmsMsg(OrderData input) {
        EcpOrderInfo ecpOrderInfo = EcpOrderInfo.builder().build();
        if (input.getExtendProps() != null &&
                input.getExtendProps().containsKey(ReceiveOrderTranslator.ExtendPropsEnum.ECP_ORDER_INFO.getKey())) {
            try {
                ecpOrderInfo = JsonUtil.readValue(
                        ReceiveOrderTranslator.ExtendPropsEnum.ECP_ORDER_INFO.
                                getValue(input.getExtendProps()), EcpOrderInfo.class);
            } catch (Exception e) {
                log.error("ecpOrderInfo 序列化异常", e);
                throw new RuntimeException("ecpOrderInfo 序列化异常:" + e.getMessage());
            }
        }
        FulfillmentInfo fulfillmentInfo = FulfillmentInfo.builder().build();
        if (input.getExtendProps() != null &&
                input.getExtendProps().containsKey(ReceiveOrderTranslator.ExtendPropsEnum.FULFILLMENT_INFO.getKey())) {
            try {
                fulfillmentInfo = JsonUtil.readValue(
                        ReceiveOrderTranslator.ExtendPropsEnum.FULFILLMENT_INFO.getValue(input.getExtendProps()), FulfillmentInfo.class);
            } catch (Exception e) {
                log.error("fulfillmentInfo 序列化异常", e);
                throw new RuntimeException("fulfillmentInfo 序列化异常:" + e.getMessage());
            }
        }
        PrintInfo printInfo = parsePrintInfo(fulfillmentInfo);
        PrintExtendInfo printExtendInfo = null;
        if (printInfo != null && StringUtils.isNotBlank(printInfo.getExtendPrintInfo())) {
            try {
                printExtendInfo = JsonUtil.readValue(printInfo.getExtendPrintInfo(), PrintExtendInfo.class);
            } catch (Exception e) {
                log.error("printExtendInfo 序列化异常", e);
                throw new RuntimeException("printExtendInfo 序列化异常:" + e.getMessage());
            }
        }
        CustomerAddressInfo customerAddressInfo = null;
        if (StringUtils.isNotBlank(fulfillmentInfo.getCustomerAddressInfo())) {
            try {
                customerAddressInfo = JsonUtil.readValue(fulfillmentInfo.getCustomerAddressInfo(), CustomerAddressInfo.class);
            } catch (Exception e) {
                log.error("fulfillmentInfo.customerAddressInfo  序列化异常", e);
                throw new RuntimeException("fulfillmentInfo.customerAddressInfo 序列化异常:" + e.getMessage());
            }
        }
        FulfillmentFlag fulfillmentFlag = null;
        if (StringUtils.isNotBlank(fulfillmentInfo.getFulfillmentFlag())) {
            try {
                fulfillmentFlag = JsonUtil.readValue(fulfillmentInfo.getFulfillmentFlag(), FulfillmentFlag.class);
            } catch (Exception e) {
                log.error("fulfillmentInfo.fulfillmentFlag  序列化异常", e);
                throw new RuntimeException("fulfillmentInfo.fulfillmentFlag 序列化异常:" + e.getMessage());
            }
        }
        // todo,后期拼多多接入再加入开关
        ExtendedField extendedField = null;
         /*if (StringUtils.isNotBlank(fulfillmentInfo.getExtendedField())) {
            try {
                extendedField = JsonUtil.readValue(fulfillmentInfo.getExtendedField(), ExtendedField.class);
            } catch (Exception e) {
                log.error("fulfillmentInfo.ExtendedField  序列化异常", e);
                throw new RuntimeException("fulfillmentInfo.ExtendedField 序列化异常:" + e.getMessage());
            }
        }*/
        if(input.getBusinessIdentity() == null || StringUtils.isBlank(input.getBusinessIdentity().getBusinessUnit())) {
            log.error("业务身份统一，订单中心传下的业务身份为空,单号：{}", input.getOrderNo());
            throw new RuntimeException("业务身份统一，订单中心传下的业务身份为空");
        }
        //TODO:上线期间向下兼容，全量上线后下单该逻辑
        String mergeStatus = null;
        if (input.getExtendProps() != null &&
                input.getExtendProps().containsKey(ReceiveOrderTranslator.ExtendPropsEnum.MERGE_STATUS.getKey())) {
            mergeStatus = input.getExtendProps().get(ReceiveOrderTranslator.ExtendPropsEnum.MERGE_STATUS.getKey());
        }
        return OutboundCreateRequest.builder()
                .orderNo(input.getOrderNo())
                .orderType(input.getOrderType())
                .businessUnit(input.getBusinessIdentity().getBusinessUnit())
                .businessType(businessType)
                .businessStrategy(input.getBusinessIdentity().getBusinessStrategy())
                .orderBusinessType(ReceiveOrderTranslator.ExtendPropsEnum.ORDER_SELL_MODEL.getValue(input.getExtendProps()))
                .basicInfo(receiveOrderTranslator.getBasicInfoByOmsInput(input))
                .customer(receiveOrderTranslator.getCustomerByOmsInput(input))
                .cargos(receiveOrderTranslator.getCargosByOmsInput(input))
                .goods(receiveOrderTranslator.getGoodsByOmsInput(input))
                .solution(receiveOrderTranslator.getSolutionByOmsInput(input))
                .products(receiveOrderTranslator.getProductsByOmsInput(input))
                .nonstandardProducts(receiveOrderTranslator.getNonstandardProductsByOmsInput(input, fulfillmentInfo, extendedField))
                .consignee(receiveOrderTranslator.getConsigneeByOmsInput(input, customerAddressInfo))
                .shipment(receiveOrderTranslator.getShipmentByOmsInput(input, fulfillmentInfo, ecpOrderInfo, printExtendInfo))
                .warehouse(receiveOrderTranslator.getWarehouseByOmsInput(input))
                .finance(receiveOrderTranslator.getFinanceOmsByInput(input, ecpOrderInfo))
                .refOrderInfo(receiveOrderTranslator.getRefOrderInfoByOmsInput(input, ecpOrderInfo))
                .channel(receiveOrderTranslator.getChannelByOmsInput(input, ecpOrderInfo, fulfillmentInfo))
                .remark(input.getRemark())
                .buyerRemark(ecpOrderInfo.getBuyerRemark())
                .sellerRemark(ecpOrderInfo.getSellerRemark())
                .extendProps(receiveOrderTranslator.getPropsForScm(input, fulfillmentInfo))
                .createTime(input.getOrderCreateTime())
                .fulfillment(receiveOrderTranslator.getFulfillment(input, fulfillmentInfo, printInfo, printExtendInfo, fulfillmentFlag, ecpOrderInfo))
                .afterSaleInfo(ecpOrderInfo.getAfterSaleInfo())
                .invoice(receiveOrderTranslator.buildInvoice(ecpOrderInfo.getInvoiceInfo()))
                .orderSubType(input.getOrderSubType())
                .ediRemark(ecpOrderInfo.getEdiRemark())
                .orderSign(input.getOrderSign())
                .parentOrderNo(input.getParentOrderNo())
                .mergeStatus(ObjectUtils.defaultIfNull(ObjectUtils.nullIfNull(input.getMergeStatus(), String::valueOf), mergeStatus))
                .orderClob(receiveOrderTranslator.getOrderClob(input))
                .build();
    }

    private PrintInfo parsePrintInfo(FulfillmentInfo fulfillmentInfo) {
        String printInfo = fulfillmentInfo.getPrintInfo();
        if (StringUtils.isBlank(printInfo)) {
            return null;
        }
        try {
            if (printInfo.startsWith(ECLP_JIMKV_PREFIX)) {
                printInfo = eclpJimkvLoadServiceAcl.load(printInfo);
            }
            return JsonUtil.readValue(printInfo, PrintInfo.class);
        } catch (Exception e) {
            log.error("printInfo 序列化异常", e);
            throw new RuntimeException("printInfo 序列化异常:" + e.getMessage());
        }
    }

}
