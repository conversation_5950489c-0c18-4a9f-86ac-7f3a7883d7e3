package com.jdl.sc.ofw.out.app.translator;

import com.fasterxml.jackson.databind.JsonNode;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.dict.CheckGoodsTypeEnum;
import com.jdl.sc.ofw.out.common.dict.ContactlessReceiveTypeEnum;
import com.jdl.sc.ofw.out.common.dict.DeliveryTypeEnum;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.out.common.dict.FrozenDamageEnum;
import com.jdl.sc.ofw.out.common.dict.GoodsTypeEnum;
import com.jdl.sc.ofw.out.common.dict.HalfReceiveEnum;
import com.jdl.sc.ofw.out.common.dict.OperateType;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import com.jdl.sc.ofw.out.common.dict.PaymentTypeEnum;
import com.jdl.sc.ofw.out.common.dict.PreSaleStageEnum;
import com.jdl.sc.ofw.out.common.dict.TmsDeliveryTypeEnum;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.orderclob.ClobTypeEnums;
import com.jdl.sc.ofw.out.domain.model.product.CityDeliveryTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.CrossDockTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.product.PrintComponentTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.TmOrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.AgentRefCargoDetails;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.CargoBatchRangeDetail;
import com.jdl.sc.ofw.out.domain.model.vo.CargoBatchRangeInfo;
import com.jdl.sc.ofw.out.domain.model.vo.CargoServiceInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderExtendProps;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jdl.sc.ofw.out.domain.model.vo.GoodsAgentSalesDetail;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import com.jdl.sc.ofw.out.domain.model.vo.InvoiceDetail;
import com.jdl.sc.ofw.out.domain.model.vo.JdAddress;
import com.jdl.sc.ofw.out.domain.model.vo.Money;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.OrderGiftRelation;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import com.jdl.sc.ofw.out.domain.model.vo.PresortInfo;
import com.jdl.sc.ofw.out.domain.model.vo.PrintExtendInfo;
import com.jdl.sc.ofw.out.domain.model.vo.ProcessingInfo;
import com.jdl.sc.ofw.out.domain.model.vo.ProcessingOption;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.model.vo.Quantity;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.SerialInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import com.jdl.sc.ofw.out.domain.model.vo.StockDetail;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.out.domain.rpc.EclpJimkvLoadServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.jimkv.DtcJimKvAgent;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.vo.AttributeInfo;
import com.jdl.sc.ofw.outbound.dto.vo.BatchInfo;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PlatformBusinessModelEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ProfessionChannelTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ReusedHistoryWaybillTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.SettlementTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WarehouseTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WaybillNoFetchTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.jdl.sc.ofw.out.domain.model.product.SolutionAttributeEnum.SHOULD_PAY_MONEY;

@Component
public class OutboundModelTranslator {


    @Resource
    private DtcJimKvAgent dtcJimKvAgent;

    @Resource
    private EclpJimkvLoadServiceAcl eclpJimkvLoadServiceAcl;

    @Resource
    private ProductCenterServiceAcl productCenterServiceAcl;

    private static final String PREFIX = "jfs/kveclp";
    // 开启配运分key 0-否（默认），1-是；
    private static final String ASSIGN_CARRIERS_AND_PRODUCTS = "AssignCarriersAndProducts";
    private static final String OPEN_ASSIGN_CARRIERS_AND_PRODUCTS = "1";

    private String jimKvLoadProcess(String value) {
        if (StringUtils.isNotBlank(value) && value.startsWith(PREFIX)) {
            return eclpJimkvLoadServiceAcl.load(value);
        }
        return value;
    }

    private Boolean isPddJg(String customField) {
        String customFieldJson = jimKvLoadProcess(customField);
        return customFieldJson != null && customFieldJson.contains("isPddJdMj");
    }

    /**
     * batrix构造器
     * @param request
     * @return
     */
    protected BatrixInfo.BatrixInfoBuilder batrixInfoBuilderCreate(OutboundCreateRequest request) {
        return BatrixInfo.builder()
                .businessUnit(request.getBusinessUnit())
                .businessType(request.getBusinessType())
                .businessScene(BusinessScene.GENERATE)
                .businessStrategy(request.getBusinessStrategy());

    }

    protected Customer.CustomerBuilder customerBuilderCreate(OutboundCreateRequest request) {
        return Customer.builder()
                .customerNo(request.getCustomer().getCustomerNo())
                .accountNo(request.getCustomer().getAccountNo())
                .accountName(request.getCustomer().getAccountName())
                .customerLevel(request.getCustomer().getCustomerLevel());
    }

    protected BasicInfo.BasicInfoBuilder basicInfoBuilderCreate(OutboundCreateRequest request) {
        return BasicInfo.builder()
                .agentSales(request.getBasicInfo().isAgentSales())
                .preSaleStage(PreSaleStageEnum.fromCode(request.getBasicInfo().getPreSaleStage()));
    }

    protected Cargo.CargoBuilder buildCargoCreate(com.jdl.sc.ofw.outbound.dto.vo.Cargo cargoVo) {
        Cargo.CargoBuilder cargoBuilder = Cargo.builder()
                .name(cargoVo.getName())
                .no(cargoVo.getNo())
                .level(cargoVo.getLevel())
                .levelName(cargoVo.getLevelName())
                .quantity(cargoVo.getQuantity() == null ? null : Quantity.builder()
                        .value(cargoVo.getQuantity().getValue())
                        .unit(cargoVo.getQuantity().getUnit()).build())
                .occupyQuantity(cargoVo.getOccupyQuantity() == null ? null : Quantity.builder()
                        .value(cargoVo.getOccupyQuantity().getValue())
                        .unit(cargoVo.getOccupyQuantity().getUnit())
                        .build())
                .refGoodsNo(cargoVo.getRefGoodsNo())
                .refGoodsType(cargoVo.getRefGoodsType())
                .isvCargoNo(cargoVo.getIsvCargoNo())
                .uniqueCode(cargoVo.getUniqueCode())
                .serialInfos(buildSerialInfos(cargoVo.getSerialInfos()))
                .batchInfoList(buildBatchInfos(cargoVo.getBatchInfos()))
                .cargoLineNo(cargoVo.getCargoLineNo())
                .virtualType(cargoVo.getVirtualType())
                .checkUniSnCode(cargoVo.getCheckUniSnCode())
                .processing(cargoVo.getProcessing())
                .printName(cargoVo.getPrintName())
                .cargoBatchRangeList(buildCargoBatchRangeList(cargoVo.getCargoBatchRangeList()))
                .sellerCustomPictureUrl(cargoVo.getSellerCustomPictureUrl())
                .products(createProductCollection(cargoVo))
                .cargoBrand(cargoVo.getCargoBrand())
                .brandNo(cargoVo.getBrandNo())
                .insurePrice(cargoVo.getInsurePrice())
                .price(cargoVo.getPrice() == null ? null : Money.builder()
                        .amount(cargoVo.getPrice().getAmount())
                        .currencyCode(cargoVo.getPrice().getCurrencyCode())
                        .build())
                .actualReceivableQuantity(cargoVo.getActualReceivableQuantity())
                .packagingDetails(cargoVo.getPackagingDetails())
                .cargoRemark(cargoVo.getCargoRemark())
                .consigneeWarehouseName(cargoVo.getConsigneeWarehouseName())
                .consigneeLocName(cargoVo.getConsigneeLocName())
                .cargoCustomField(cargoVo.getCargoCustomField())
                .sellerGoodsRemark(cargoVo.getSellerGoodsRemark())
                .aviationRegCod(cargoVo.getAviationRegCod())
                .cargoWeakBatchRangeInfo(cargoVo.getCargoWeakBatchRangeInfo())
                .frozenProducts(cargoVo.getFrozenProducts())
                .traceabilityCodeManage(cargoVo.getTraceabilityCodeManage())
                .outboundByBoxRule(cargoVo.getOutboundByBoxRule())
                .extCollectionUniqCode(cargoVo.getExtCollectionUniqCode())
                .wholeBoxOut(cargoVo.getWholeBoxOut())
                .standardWeight(cargoVo.getStandardWeight())
                .productionWay(cargoVo.getProductionWay())
                .weighPieceworkQuantity(cargoVo.getWeighPieceworkQuantity())
                .processingServicesType(cargoVo.getProcessingServicesType())
                .customerProcessingInfo(cargoVo.getCustomerProcessingInfo())
                ;
        // 半加工订单信息字段
        if (cargoVo.getProcessingInfo() != null) {
            cargoBuilder.processingInfo(ProcessingInfo.builder()
                    .optionCode(cargoVo.getProcessingInfo().getOptionCode())
                    .processingOptions(CollectionUtils.emptyIfNull(cargoVo.getProcessingInfo().getProcessingOptions())
                            .stream().map(po -> ProcessingOption.builder()
                                    .rawMaterialNo(po.getRawMaterialNo())
                                    .rawMaterialName(po.getRawMaterialName())
                                    .rawMaterialQuantity(Quantity.builder()
                                            .unit(po.getRawMaterialQuantity().getUnit())
                                            .value(po.getRawMaterialQuantity().getValue())
                                            .build())
                                    .rawMaterialLevel(po.getRawMaterialLevel())
                                    .extendProps(po.getExtendProps())
                                    .build())
                            .collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(cargoVo.getSubCargos())) {
            cargoBuilder.subCargos(cargoVo.getSubCargos().stream().map(subCargo -> Cargo.builder()
                    .no(subCargo.getNo())
                    .level(subCargo.getLevel())
                    .quantity(Quantity.builder()
                            .unit(subCargo.getQuantity().getUnit())
                            .value(subCargo.getQuantity().getValue())
                            .build())
                    .build()).collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(cargoVo.getCargoServiceInfo())) {
            cargoBuilder.cargoServiceInfo(cargoVo.getCargoServiceInfo().stream()
                    .map(serviceInfo -> CargoServiceInfo.builder()
                            .serviceCode(serviceInfo.getServiceCode())
                            .serviceName(serviceInfo.getServiceName())
                            .businessLine(serviceInfo.getBusinessLine())
                            .serviceRequirement(serviceInfo.getServiceRequirement())
                            .operateType(serviceInfo.getOperateType())
                            .serviceSign(serviceInfo.getServiceSign())
                            .remark(serviceInfo.getRemark())
                            .build())
                    .collect(Collectors.toList()));
        }
        return cargoBuilder;
    }

    public Goods.GoodsBuilder goodsBuilderCreate(com.jdl.sc.ofw.outbound.dto.vo.Goods good) {
        return Goods.builder()
                .type(GoodsTypeEnum.fromValue(good.getType()))
                .no(good.getNo())
                .name(good.getName())
                .price(good.getPrice() == null ? null : Money.builder()
                        .amount(good.getPrice().getAmount())
                        .currencyCode(good.getPrice().getCurrencyCode())
                        .build())
                .amount(good.getAmount() == null ? null : Money.builder()
                        .amount(good.getAmount().getAmount())
                        .currencyCode(good.getAmount().getCurrencyCode())
                        .build())
                .quantity(good.getQuantity() == null ? null : Quantity.builder()
                        .value(good.getQuantity().getValue())
                        .unit(good.getQuantity().getUnit())
                        .build())
                .combinationVersion(good.getCombinationVersion())
                .channelGoodsNo(good.getChannelGoodsNo())
                .agentSaleGoodsNo(good.getAgentSaleGoodsNo())
                .refCargoNo(good.getRefCargoNo())
                .goodsAgentSalesDetailList(buildAgentSalesDetail(good.getGoodsAgentSalesDetailList()))
                .salesAttribute(good.getSalesAttribute())
                .goodsEdiRemark(good.getGoodsEdiRemark())
                ;
    }

    public Consignee toConsigneeModel(com.jdl.sc.ofw.outbound.dto.vo.Consignee consignee) {
        if (Objects.isNull(consignee)) {
            return null;
        }
        return this.consigneeBuilderCreate(consignee).build();
    }

    protected Consignee.ConsigneeBuilder consigneeBuilderCreate(com.jdl.sc.ofw.outbound.dto.vo.Consignee consignee) {
        return Consignee.builder()
                .address(consignee.getAddress() == null
                        ? JdAddress.builder().build()
                        : JdAddress.builder()
                        .provinceNo(consignee.getAddress().getProvinceNo())
                        .provinceName(consignee.getAddress().getProvinceName())
                        .cityName(consignee.getAddress().getCityName())
                        .cityNo(consignee.getAddress().getCityNo())
                        .countyName(consignee.getAddress().getCountyName())
                        .countyNo(consignee.getAddress().getCountyNo())
                        .townName(consignee.getAddress().getTownName())
                        .townNo(consignee.getAddress().getTownNo())
                        .detailAddress(consignee.getAddress().getDetailAddress())
                        .provinceNoGis(consignee.getAddress().getProvinceNoGis())
                        .provinceNameGis(consignee.getAddress().getProvinceNameGis())
                        .cityNoGis(consignee.getAddress().getCityNoGis())
                        .cityNameGis(consignee.getAddress().getCityNameGis())
                        .countyNoGis(consignee.getAddress().getCountyNoGis())
                        .countyNameGis(consignee.getAddress().getCountyNameGis())
                        .townNoGis(consignee.getAddress().getTownNoGis())
                        .townNameGis(consignee.getAddress().getTownNameGis())
                        .regionNo(consignee.getAddress().getRegionNo())
                        .regionName(consignee.getAddress().getRegionName())
                        .enAddress(consignee.getAddress().getEnAddress())
                        .build())
                .warehouse(buildWarehouse(consignee.getWarehouse()))
                .name(consignee.getName())
                .phone(consignee.getPhone())
                .mobile(consignee.getMobile())
                .email(consignee.getEmail())
                .zipCode(consignee.getZipCode())
                .consigneeAddrEnc(consignee.getConsigneeAddrEnc())
                .consigneeNameEnc(consignee.getConsigneeNameEnc())
                .consigneeMobileEnc(consignee.getConsigneeMobileEnc())
                .consigneePhoneEnc(consignee.getConsigneePhoneEnc())
                .originProvinceName(consignee.getOriginProvinceName())
                .originCityName(consignee.getOriginCityName())
                .originCountyName(consignee.getOriginCountyName())
                .originTownName(consignee.getOriginTownName())
                .consigneeSiteName(consignee.getConsigneeSiteName())
                .consigneeCustomerName(consignee.getConsigneeCustomerName())
                .virtualNumberExpirationDate(consignee.getVirtualNumberExpirationDate())
                .encryptMode(consignee.getEncryptMode())
                .consigneeCompany(consignee.getConsigneeCompany())
                ;
    }

    /**
     * 发货信息
     */
    public Consignor toConsignorModel(com.jdl.sc.ofw.outbound.dto.vo.Consignor consignor) {
        if (consignor == null) {
            return null;
        }
        return this.consignorBuilderCreate(consignor).build();
    }

    protected Consignor.ConsignorBuilder consignorBuilderCreate(com.jdl.sc.ofw.outbound.dto.vo.Consignor consignor) {
        JdAddress address = null;
        if (consignor.getAddress() != null) {
            address = JdAddress.builder()
                    .provinceNo(consignor.getAddress().getProvinceNo())
                    .provinceName(consignor.getAddress().getProvinceName())
                    .cityName(consignor.getAddress().getCityName())
                    .cityNo(consignor.getAddress().getCityNo())
                    .countyName(consignor.getAddress().getCountyName())
                    .countyNo(consignor.getAddress().getCountyNo())
                    .townName(consignor.getAddress().getTownName())
                    .townNo(consignor.getAddress().getTownNo())
                    .detailAddress(consignor.getAddress().getDetailAddress())
                    .build();
        }
        return Consignor.builder()
                .address(address)
                .name(consignor.getName())
                .phone(consignor.getPhone())
                .mobile(consignor.getMobile())
                .zipCode(consignor.getZipCode());
    }

    protected Shipment.ShipmentBuilder shipmentBuilder(OutboundCreateRequest request) {
        return Shipment.builder()
                .presortInfo(PresortInfo.builder().build())
                .bdOwnerNo(request.getShipment().getBdOwnerNo())
                .shipperNo(request.getShipment().getShipperNo())
                .designatedShipper(StringUtils.isNotEmpty(request.getShipment().getShipperNo()) ? 1 : 0)
                .shipperType(ObjectUtils.nullIfNull(request.getShipment().getShipperType(), ShipperTypeEnum::fromCode))
                .shipperName(request.getShipment().getShipperName())
                .expectDeliveryStartTime(request.getShipment().getExpectDeliveryStartTime())
                .expectDeliveryEndTime(request.getShipment().getExpectDeliveryEndTime())
                .endStationNo(request.getShipment().getEndStationNo())
                .endStationName(request.getShipment().getEndStationName())
                .contactlessReceiveType(ContactlessReceiveTypeEnum.fromCode(request.getShipment().getContactlessReceiveType()))
                .halfReceive(HalfReceiveEnum.fromCode(request.getShipment().getHalfReceive()))
                .assignedAddress(request.getShipment().getAssignedAddress())
                .transportationType(request.getShipment().getTransportType() == null ?
                        TransportationTypeEnum.LAND : TransportationTypeEnum.fromOmsCode(request.getShipment().getTransportType()))
                .sellerExpectTransport(request.getShipment().getExpectTransportType() == null ?
                        null : TransportationTypeEnum.fromOmsCode(request.getShipment().getExpectTransportType()))
                .deliveryType(DeliveryTypeEnum.fromCode(request.getShipment().getDeliveryType()))
                .billingType(request.getShipment().getBillingType())
                .logisticsOriginalPackage(request.getShipment().getLogisticsOriginalPackage())
                .deductibleCompensation(request.getShipment().getDeductibleCompensation())
                .cityDeliveryType(request.getShipment().isCityDeliveryType())
                .initialCityDeliveryType(request.getShipment().isCityDeliveryType())
                .expectCityDeliveryType(request.getShipment().isExpectCityDeliveryType())
                .unloadingDock(request.getShipment().getUnloadingDock())
                .tmsDeliveryType(TmsDeliveryTypeEnum.fromOmsCode(request.getShipment().getTmsDeliveryType()))
                .checkGoodsTypeEnum(CheckGoodsTypeEnum.fromCode(request.getShipment().getCheckGoodsType()))
                .contactPerson(request.getShipment().getContactPerson())
                .contactPhone(request.getShipment().getContactPhone())
                .contactAddress(request.getShipment().getContactAddress())
                .heavyGoodsUpstairs(request.getShipment().isHeavyGoodsUpstairs())
                .hidePrivacyType(request.getShipment().isHidePrivacyType())
                .transportModeEnum(TransportModeEnum.fromCode(request.getShipment().getTransportMode()))
                // 城配时效
                .expectDeliveryStartDate(request.getShipment().getExpectDeliveryStartDate())
                .cityDeliveryStartDate(request.getShipment().getCityDeliveryStartDate())
                .cityDeliveryEndDate(request.getShipment().getCityDeliveryEndDate())
                .cityDeliveryDeadline(request.getShipment().getCityDeliveryDeadline())
                .cityDeadlineType(CityDeliveryTypeEnum.parseValue(request.getShipment().getCityDeadlineType()))
                .agingName(request.getShipment().getAgingName())
                .wayBill(request.getShipment().getWaybillNo())
                .thirdWayBill(request.getShipment().getThirdWaybill())
                .thirdPartExpressType(request.getShipment().getThirdExpressType())
                .CNTemplate(Integer.valueOf(1).equals(request.getShipment().getCNTemplate()))
                .needExternalWaybillOnly(Integer.valueOf(1).equals(request.getShipment().getNeedExternalWaybillOnly()))
                .printExtendInfo(this.buildPrintExtendInfo(request.getShipment().getPrintExtendInfo()))
                .signBillTypeEnum(request.getShipment().getSignBillTypeEnum())
                .reReceivePhone(request.getShipment().getReReceivePhone())
                .reReceiveMobile(request.getShipment().getReReceiveMobile())
                .reReceiveName(request.getShipment().getReReceiveName())
                .reReceiveAdress(request.getShipment().getReReceiveAdress())
                .deliveryPerformanceChannel(isPddJg(request.getFulfillment().getCustomField()) ?
                        DeliveryPerformanceChannelEnum.PDD_JG :
                        DeliveryPerformanceChannelEnum.fromCode(request.getShipment().getDeliveryPerformanceChannel()))
                .thirdExpressProduct(request.getShipment().getThirdExpressProduct())
                .vehicleType(request.getShipment().getVehicleType())
                .express(DeliveryCollectionEnum.fromCode(request.getShipment().getExpress()))
                .activationCardService(request.getShipment().getActivationCardService())
                .signIDCode(request.getShipment().getSignIDCode())
                .signType(request.getShipment().getSignType())
                .packageProduceMode(request.getShipment().getPackageProduceMode())
                .firstWaybillIssue(request.getShipment().getFirstWaybillIssue())
                .warmLayer(request.getShipment().getWarmLayer())
                .goodsIsolateType(request.getShipment().getGoodsIsolateType())
                .planDeliveryTime(request.getShipment().getPlanDeliveryTime())
                .sellerShipperNo(request.getShipment().getSellerShipperNo())
                .sellerShipperName(request.getShipment().getSellerShipperName())
                .expectDeliveryTimeFlag(request.getShipment().getExpectDeliveryTimeFlag())
                .luxurySecurity(request.getShipment().getLuxurySecurity())
                .deliveryForcePhoto(request.getShipment().getDeliveryForcePhoto())
                .forceContact(request.getShipment().getForceContact())
                .deliveryIntoWarehouse(request.getShipment().getDeliveryIntoWarehouse())
                .warehousingAppointmentCode(request.getShipment().getWarehousingAppointmentCode())
                .warehousingAppointmentStartTime(request.getShipment().getWarehousingAppointmentStartTime())
                .warehousingAppointmentEndTime(request.getShipment().getWarehousingAppointmentEndTime())
                .warehousingAppointmentRemark(request.getShipment().getWarehousingAppointmentRemark())
                .refusalAllowed(request.getShipment().getRefusalAllowed())
                .expectDeliveryResource(ExpectDeliveryResourceEnum.fromCode(request.getShipment().getExpectDeliveryResource()))
                .waybillNoFetchType(WaybillNoFetchTypeEnum.fromCode(request.getShipment().getWaybillNoFetchType()))
                .deliveryInBatches(request.getShipment().getDeliveryInBatches())
                .deliveryService(request.getShipment().getDeliveryService())
                .nonSelfSignCall(request.getShipment().getNonSelfSignCall())
                .consignmentControlRules(request.getShipment().getConsignmentControlRules())
                .packagingStandards(request.getShipment().getPackagingStandards())
                .dataTransferRequirements(request.getShipment().getDataTransferRequirements())
                .consignmentType(request.getShipment().getConsignmentType())
                .pickupType(request.getShipment().getPickupType())
                .tcExpectPickupStartTime(request.getShipment().getTcExpectPickupStartTime())
                .tcExpectPickupEndTime(request.getShipment().getTcExpectPickupEndTime())
                .customerTransportType(request.getShipment().getCustomerTransportType())
                .productSplitCargoQuantityType(request.getShipment().getProductSplitCargoQuantityType())
                .customerTransportName(request.getShipment().getCustomerTransportName())
                .serviceRequirements(request.getShipment().getServiceRequirements())
                .flowType(request.getShipment().getFlowType())
                .customerContactlessType(request.getShipment().getCustomerContactlessType())
                .instantDeliveryType(request.getShipment().getInstantDeliveryType())
                .gisFenceId(request.getShipment().getGisFenceId())
                ;
    }

    protected Finance.FinanceBuilder financeBuilderCreate(OutboundCreateRequest request) {
        return Finance.builder()
                .orderAmount(request.getFinance().getOrderAmount() == null ? null :
                        Money.builder().amount(request.getFinance().getOrderAmount().getAmount())
                                .currencyCode(request.getFinance().getOrderAmount().getCurrencyCode()).build())
                .paymentType(PaymentTypeEnum.fromCode(request.getFinance().getPaymentType()))
                .settlementType(SettlementTypeEnum.fromCode(request.getFinance().getSettlementType()))
                .jdlMark(request.getFinance().getJdlMark())
                .payTime(request.getFinance().getPayTime());
    }


    protected RefOrderInfo.RefOrderInfoBuilder refOrderInfoBuilderCreate(OutboundCreateRequest request) {
        return RefOrderInfo.builder()
                .waybillNo(request.getRefOrderInfo().getWaybillNo())
                .giftType(request.getRefOrderInfo().getGiftType())
                .orderGiftRelationList(ObjectUtils.nullIfNull(request.getRefOrderInfo().getOrderGiftRelationList(),
                        orderGiftRelationList -> orderGiftRelationList.stream().map(orderGiftRelation -> OrderGiftRelation.builder()
                                .ecpOrderNo(orderGiftRelation.getOrderNo())
                                .orderGiftRelationType(orderGiftRelation.getOrderGiftRelationType())
                                .build()
                        ).collect(Collectors.toList())))
                .serviceType(request.getRefOrderInfo().getServiceType())
                .serviceTypeName(request.getRefOrderInfo().getServiceTypeName())
                .sellerPurchaseOrderNo(request.getRefOrderInfo().getSellerPurchaseOrderNo())
                .groupOrderNo(request.getRefOrderInfo().getGroupOrderNo())
                .relationNo(request.getRefOrderInfo().getRelationNo())
                .allocationOrderNo(request.getRefOrderInfo().getAllocationOrderNo())
                .purchaseOrderNo(request.getRefOrderInfo().getPurchaseOrderNo())
                .tcPurchaseOrderNo(request.getRefOrderInfo().getTcPurchaseOrderNo())
                .mainOrderNos(request.getRefOrderInfo().getMainOrderNos())
                .subOrderNos(request.getRefOrderInfo().getSubOrderNos())
                ;
    }

    protected Channel.ChannelBuilder channelBuilderCreate(OutboundCreateRequest request) {
        return Channel.builder()
                .pin(request.getChannel().getPin())
                .channelNo(request.getChannel().getChannelNo())
                .channelName(request.getChannel().getChannelName())
                .channelShopNo(request.getChannel().getChannelShopNo())
                .channelSource(request.getChannel().getChannelSource())
                .channelOrderNo(request.getChannel().getChannelOrderNo())
                .channelOrderCreateTime(request.getChannel().getChannelOrderCreateTime())
                .shopNo(request.getChannel().getShopNo())
                .shopName(request.getChannel().getShopName())
                .customerOrderNo(request.getChannel().getCustomerOrderNo())
                .bdSellerNo(request.getChannel().getBdSellerNo())
                .isvSource(request.getChannel().getIsvSource())
                .systemCaller(request.getChannel().getSystemCaller())
                .sellerChannelNo(request.getChannel().getSellerChannelNo())
                .professionChannelType(ProfessionChannelTypeEnum.fromCode(request.getExtendProps().get("carBusinessLine")))
                .childChannelOrderNos(request.getChannel().getChildChannelOrderNos())
                .childCustomerOrderNos(request.getChannel().getChildCustomerOrderNos())
                .customerShopName(request.getChannel().getCustomerShopName())
                ;
    }

    protected Fulfillment.FulfillmentBuilder fulfillmentBuilderCreate(OutboundCreateRequest request) {
        return Fulfillment.builder()
                .occupyResult(request.getFulfillment().occupyResult)
                .stockDetailList(toStockDetailModel(request.getFulfillment().stockDetailList))
                .cargoFulfillmentWay(request.getFulfillment().isCargoFulfillmentWay())
                .outboundStrategy(request.getFulfillment().getOutboundStrategy())
                .partialFulfillment(request.getFulfillment().isPartialFulfillment())
                .sellerPackTypeNo(request.getFulfillment().getSellerPackTypeNo())
                .sellerPackTypeName(request.getFulfillment().getSellerPackTypeName())
                .sellerAssignPackType(request.getFulfillment().getSellerAssignPackType())
                .sellerIndividuationPrintInfo(request.getFulfillment().getSellerIndividuationPrintInfo())
                .thirdInsured(request.getFulfillment().getThirdInsured())
                .thirdGuaranteeMoney(request.getFulfillment().getThirdGuaranteeMoney())
                .invoiceEnumFlag(request.getFulfillment().getInvoiceEnumFlag())
                .professionType(request.getExtendProps().get(ReceiveOrderTranslator.ExtendPropsEnum.PROFESSION_TYPE.getKey()))
                .orderPriority(request.getFulfillment().getOrderPriority())
                .orderUrgencyType(OrderUrgencyType.fromOmsCode(request.getFulfillment().getOrderUrgencyType()))
                .partialPayment(request.getShipment().isPartialPayment())
                .skipMobileDecrypt(request.getFulfillment().isSkipMobileDecrypt())
                .quarantineCert(request.getFulfillment().isQuarantineCert())
                .trustJDMetrics(request.getFulfillment().isTrustJDMetrics())
                .printComponentType(PrintComponentTypeEnum.fromCode(request.getFulfillment().getPrintComponentType()))
                .customField(request.getFulfillment().getCustomField())
                .packageSignFlag(request.getFulfillment().isPackageSignFlag())
                .tmOrderFlag(TmOrderTypeEnum.fromCode(request.getFulfillment().getTmOrderFlag()))
                .clpsCainiaoElecFlag(request.getFulfillment().isClpsCainiaoElecFlag())
                .tmDeliveryByJgFlag(request.getFulfillment().isTmDeliveryByJgFlag())
                .supervisionCode(request.getFulfillment().getSupervisionCode())
                .invoiceChecker(request.getFulfillment().getInvoiceChecker())
                .paymentType(request.getFulfillment().getPaymentType())
                .saleType(request.getFulfillment().getSaleType())
                .warmLayer(request.getFulfillment().getWarmLayer())
                .batchNo(request.getFulfillment().getBatchNo())
                .batchQuantity(request.getFulfillment().getBatchQuantity())
                .stationCode(request.getFulfillment().getStationCode())
                .requireConsigneeTimeTo(request.getFulfillment().getRequireConsigneeTimeTo())
                .sellerCustomPictureUrl(request.getFulfillment().getSellerCustomPictureUrl())
                .crossDockType(CrossDockTypeEnum.fromCode(request.getFulfillment().getCrossDockType()))
                .adventSafeDays(request.getFulfillment().getAdventSafeDays())
                .printInfoFromSeller(request.getFulfillment().isPrintInfoFromSeller())
                .cainiaoPlatformWaybill(request.getShipment().getTpWaybill())
                .kaExtensionPoint(request.getFulfillment().getKaExtensionPoint())
                .mjCompanyCode(request.getFulfillment().getMjCompanyCode())
                .logicParam(request.getFulfillment().getLogicParam())
                .packDetailsCollect(request.getFulfillment().getPackDetailsCollect())
                .salesmanAttribute(request.getFulfillment().getSalesmanAttribute())
                .customerSignforAttributes(request.getFulfillment().getCustomerSignforAttributes())
                .platformBusinessModel(PlatformBusinessModelEnum.fromCode(request.getFulfillment().getPlatformBusinessModel()))
                .kaExtensionPoint(request.getFulfillment().getKaExtensionPoint())
                .boxInfos(request.getFulfillment().getBoxInfos())
                .skyWorthWaybill(request.getFulfillment().getSkyWorthWaybill())
                .occupyResultType(request.getFulfillment().getOccupyResultType())
                .platformExpectWmsDeliveryTime(request.getFulfillment().getPlatformExpectWmsDeliveryTime())
                .platformExpectPickupTime(request.getFulfillment().getPlatformExpectPickupTime())
                .packageRestrictions(request.getFulfillment().getPackageRestrictions())
                .ecpFirstOrderNo(request.getFulfillment().getEcpFirstOrderNo())
                .ecpOrderSalesStrategy(request.getFulfillment().getEcpOrderSalesStrategy())
                .ecpOrderDeliveryStrategy(request.getFulfillment().getEcpOrderDeliveryStrategy())
                .frozenDamageMethod(FrozenDamageEnum.fromCode(request.getFulfillment().getFrozenDamageMethod()))
                .reusedHistoryWaybillType(ReusedHistoryWaybillTypeEnum.fromCode(request.getFulfillment().getReusedHistoryWaybillType()))
                .isPrintMedicinePriceBalance(request.getFulfillment().getIsPrintMedicinePriceBalance())
                .serialNumberCheckChannel(request.getFulfillment().getSerialNumberCheckChannel())
                .memberCardId(request.getFulfillment().getMemberCardId())
                .memberName(request.getFulfillment().getMemberName())
                .customizeOrderPrintInfo(request.getFulfillment().getCustomizeOrderPrintInfo())
                .warehouseShipmentMode(request.getFulfillment().getWarehouseShipmentMode())
                .ecpOrderType(request.getFulfillment().getEcpOrderType())
                .cargoGiftRelationInfo(request.getFulfillment().getCargoGiftRelationInfo())
                .jitPromise(request.getFulfillment().getJitPromise())
                .productionPromiseType(request.getFulfillment().getProductionPromiseType())
                .stockShortsProcessWay(request.getFulfillment().getStockShortsProcessWay())
                .sevenFreshStoreName(request.getFulfillment().getSevenFreshStoreName())
                .estimatedDeliveryTimePeriod(request.getFulfillment().getEstimatedDeliveryTimePeriod())
                .packagingFee(request.getFulfillment().getPackagingFee())
                .processingWay(request.getFulfillment().getProcessingWay())
                .customerBoxInfos(request.getFulfillment().getCustomerBoxInfos())
                .sellerExternalOrderNo(request.getFulfillment().getSellerExternalOrderNo())
                .handoverPickupRequirements(request.getFulfillment().getHandoverPickupRequirements())
                .orderMainSubFlag(request.getFulfillment().getOrderMainSubFlag())
                ;
    }

    public OrderFulfillmentModel toModel(OutboundCreateRequest request) {
        return OrderFulfillmentModel.builder()
                .batrixInfo(this.batrixInfoBuilderCreate(request).build())
                .orderNo(request.getOrderNo())
                .parentOrderNo(request.getParentOrderNo())
                .mergeStatus(request.getMergeStatus())
                .orderType(OrderTypeEnum.fromCode(request.getOrderType()))
                .orderBusinessType(OrderBusinessTypeEnum.fromCode(request.getOrderBusinessType()))
                .orderStatus(OrderStatusEnum.INIT)
                .customer(this.customerBuilderCreate(request).build())
                .basicInfo(this.basicInfoBuilderCreate(request).build())
                .cargos(OrderClobEnum.isOrderClob(request.getOrderClob()) ? null : toCargoModel(request.getCargos()))
                .goods(OrderClobEnum.isOrderClob(request.getOrderClob()) ? null : request.getGoods().stream().map(good -> this.goodsBuilderCreate(good).build()).collect(Collectors.toList()))
                .consignee(toConsigneeModel(request.getConsignee()))
                .consignor(toConsignorModel(request.getConsignor()))
                .shipment(this.shipmentBuilder(request).build())
                .warehouse(buildWarehouse(request.getWarehouse()))
                .finance(this.financeBuilderCreate(request).build())
                .refOrderInfo(this.refOrderInfoBuilderCreate(request).build())
                .nonstandardProducts(createNonstandardProducts(request))
                .channel(this.channelBuilderCreate(request).build())
                .solution(buildSolution(request.getSolution()))
                .products(createProductCollection(request))
                .remark(request.getRemark())
                .buyerRemark(request.getBuyerRemark())
                .sellerRemark(request.getSellerRemark())
                .createTime(request.getCreateTime())
                .orderMark(request.getExtendProps() == null ? null : request.getExtendProps().get("orderMark"))
                .audit(Audit.builder()
                        .operateType(OperateType.CREATE)
                        .operateTime(new Date())
                        .operator(request.getChannel().getPin())
                        .build())
                .fulfillment(this.fulfillmentBuilderCreate(request).build())
                .invoice(buildInvoice(request.getInvoice()))
                .originalCustomerOrderType(request.getOrderSubType())
                .originalCustomerOrderTypeName(request.getShipment().getPrintExtendInfo() == null ? null : request.getShipment().getPrintExtendInfo().getIsvSoTypeName())
                .sellerRemark(request.getSellerRemark())
                .ediRemark(buildEdiRemarkInfo(request.getEdiRemark()))
                .orderSign(request.getOrderSign() == null ? null : OrderSign.builder()
                        .controlOrder(request.getOrderSign().get("controlOrder"))
                        .batchOrder(request.getOrderSign().get("batchOrder"))
                        .lettering(request.getOrderSign().get("lettering"))
                        .processing(request.getOrderSign().get("processing"))
                        .sourcingSplit(request.getOrderSign().get("sourcingSplit"))
                        .splitOrder(request.getOrderSign().get("splitOrder"))
                        .mergeOrder(request.getOrderSign().get("mergeOrder"))
                        .businessSubsidy(request.getOrderSign().get("businessSubsidy"))
                        .consumableOrder(request.getOrderSign().get("consumableOrder"))
                        .globalDirectDelivery(request.getOrderSign().get("globalDirectDelivery"))
                        .build())
                .ediRemarkAddress(request.getEdiRemark())
                .fulfillmentCommand(fulfillmentCommandCreate(request))
                .build();
    }

    private FulfillmentCommand fulfillmentCommandCreate(OutboundCreateRequest request) {
        FulfillmentCommand.FulfillmentCommandBuilder builder = FulfillmentCommand.builder();
        builder.shipmentAndProductSplitFlag(OPEN_ASSIGN_CARRIERS_AND_PRODUCTS.equals(productCenterServiceAcl.readParamValue(request.getCustomer().getAccountNo(), ASSIGN_CARRIERS_AND_PRODUCTS)));
        builder.productSplitResultSource(request.getFulfillment().getProductSplitResultSource());

        //大报文
        if (OrderClobEnum.isOrderClob(request.getOrderClob())){
            //将cargo、goods列表信息转存 Jmkv
            final List<Cargo> cargoList = toCargoModel(request.getCargos());
            final List<Goods> goodsList = request.getGoods()
                    .stream()
                    .map(good -> this.goodsBuilderCreate(good).build())
                    .collect(Collectors.toList());
            builder.orderClob(request.getOrderClob())
                    .cargosJimKvs(FulfillmentCommand.getParser(ClobTypeEnums.CARGO).set(request.getOrderNo(), cargoList))
                    .goodsJimKvs(FulfillmentCommand.getParser(ClobTypeEnums.GOODS).set(request.getOrderNo(), goodsList))
                    .build();
        }
        return builder.build();
    }

    private PrintExtendInfo buildPrintExtendInfo(com.jdl.sc.ofw.outbound.dto.vo.PrintExtendInfo printExtendInfo) {
        if (printExtendInfo == null) {
            return PrintExtendInfo.builder().build();
        }
        return PrintExtendInfo.builder()
                .businessType(printExtendInfo.getBusinessType())
                .appointDeliveryTime(printExtendInfo.getAppointDeliveryTime())
                .destinationCode(printExtendInfo.getDestinationCode())
                .destinationName(printExtendInfo.getDestinationName())
                .receiveMode(printExtendInfo.getReceiveMode())
                .gatherCenterName(printExtendInfo.getGatherCenterName())
                .monthlyAccount(printExtendInfo.getMonthlyAccount())
                .sellerRemark(printExtendInfo.getSellerRemark())
                .packageMark(printExtendInfo.getPackageMark())
                .sendMode(printExtendInfo.getSendMode())
                .sendWebsiteCode(printExtendInfo.getSendWebsiteCode())
                .shipment(printExtendInfo.getShipment())
                .sendWebsiteName(printExtendInfo.getSendWebsiteName())
                .thirdPayment(printExtendInfo.getThirdPayment())
                .thirdSite(printExtendInfo.getThirdSite())
                .customPrintInfo(printExtendInfo.getCustomPrintInfo())
                .matchWmsPrintTemplateFlag(printExtendInfo.getMatchWmsPrintTemplateFlag())
                .build();
    }

    public List<Cargo> toCargoModel(List<com.jdl.sc.ofw.outbound.dto.vo.Cargo> cargos) {
        if (CollectionUtils.isEmpty(cargos)) {
            return null;
        }
        return cargos.stream().map(c -> this.buildCargoCreate(c).build()).collect(Collectors.toList());
    }

    private List<List<CargoBatchRangeInfo>> buildCargoBatchRangeList(List<List<com.jdl.sc.ofw.outbound.dto.vo.CargoBatchRangeInfo>> cargoBatchRangeList) {
        if (CollectionUtils.isEmpty(cargoBatchRangeList)) {
            return null;
        }
        return cargoBatchRangeList.stream()
                .map(innerList ->
                        innerList.stream()
                                .map(cargoBatchRangeInfo -> CargoBatchRangeInfo.builder()
                                        .batchKey(cargoBatchRangeInfo.getBatchKey())
                                        .cargoBatchRangeDetailList(buildCargoBatchRangeDetailList(cargoBatchRangeInfo.getCargoBatchRangeDetailList()))
                                        .build())
                                .collect(Collectors.toList())
                ).collect(Collectors.toList());
    }

    private List<CargoBatchRangeDetail> buildCargoBatchRangeDetailList(List<com.jdl.sc.ofw.outbound.dto.vo.CargoBatchRangeDetail> cargoBatchRangeDetailList) {
        return cargoBatchRangeDetailList.stream().map(cargoBatchRangeDetail -> CargoBatchRangeDetail.builder()
                .batchValue(cargoBatchRangeDetail.getBatchValue())
                .batchOperator(cargoBatchRangeDetail.getBatchOperator())
                .build()).collect(Collectors.toList());
    }

    private NonstandardProducts createNonstandardProducts(OutboundCreateRequest request) {
        NonstandardProducts nonstandardProducts = new NonstandardProducts();
        if (request.getNonstandardProducts() == null) {
            return nonstandardProducts;
        }
        Iterator<Map.Entry<String, String>> iterator = request.getNonstandardProducts().entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> next = iterator.next();
            nonstandardProducts.enroll(next.getKey(), next.getValue());
        }
        return nonstandardProducts;
    }

    private ProductCollection createProductCollection(OutboundCreateRequest request) {
        ProductCollection collection = new ProductCollection();
        for (com.jdl.sc.ofw.outbound.dto.vo.Product product : request.getProducts()) {
            collection.enroll(Product.builder()
                    .no(product.getNo())
                    .name(product.getName())
                    .type(ObjectUtils.nullIfNull(product.getType(), ProductTypeEnum::fromCode))
                    .attributes(ObjectUtils.defaultIfNull(product.getAttributes(), new HashMap<String, String>()))
                    .build());
        }
        return collection;
    }

    private ProductCollection createProductCollection(com.jdl.sc.ofw.outbound.dto.vo.Cargo cargo) {
        ProductCollection collection = new ProductCollection();
        for (com.jdl.sc.ofw.outbound.dto.vo.Product product : cargo.getProductList()) {
            collection.enroll(Product.builder()
                    .no(product.getNo())
                    .name(product.getName())
                    .type(ObjectUtils.nullIfNull(product.getType(), ProductTypeEnum::fromCode))
                    .attributes(ObjectUtils.defaultIfNull(product.getAttributes(), new HashMap<>()))
                    .build());
        }
        return collection;
    }

    private Warehouse buildWarehouse(com.jdl.sc.ofw.outbound.dto.vo.Warehouse warehouse) {
        return Objects.isNull(warehouse)
                ? null
                : Warehouse.builder()
                .warehouseNo(warehouse.getWarehouseNo())
                .originalWarehouseNo(warehouse.getOriginalWarehouseNo())
                .warehouseName(warehouse.getWarehouseName())
                .warehouseType(Objects.isNull(warehouse.getWarehouseType())
                        ? null
                        : WarehouseTypeEnum.fromCode(warehouse.getWarehouseType()))
                // 下仓时效
                .transportWmsStartDate(warehouse.getTransportWmsStartDate())
                .transportWmsEndDate(warehouse.getTransportWmsEndDate())
                .wmsPlanDeliveryTime(warehouse.getWmsPlanDeliveryTime())
                .wmsDeadlineType(warehouse.getWmsDeadlineType())
                .endErpWarehouseNo(warehouse.getEndErpWarehouseNo())
                .endDistributionId(warehouse.getEndDistributionId())
                .shipmentWarehouseType(warehouse.getShipmentWarehouseType())
                .externalWarehouseNo(warehouse.getExternalWarehouseNo())
                .build();
    }


    private List<StockDetail> toStockDetailModel(List<com.jdl.sc.ofw.outbound.dto.vo.StockDetail> stockDetailList) {
        if (CollectionUtils.isEmpty(stockDetailList)) {
            return null;
        }

        return stockDetailList.stream().map(stockDetail -> StockDetail.builder()
                .cargoNo(stockDetail.getCargoNo())
                .isvCargoNo(stockDetail.getIsvCargoNo())
                .warehouseNo(stockDetail.getWarehouseNo())
                .realShortNum(stockDetail.getRealShortNum())
                .useShortNum(stockDetail.getUseShortNum())
                .useStockNum(stockDetail.getUseStockNum())
                .partialFulfillment(stockDetail.isPartialFulfillment())
                .build()).collect(Collectors.toList());

    }

    private Solution buildSolution(com.jdl.sc.ofw.outbound.dto.vo.Solution solution) {
        if (solution == null) {
            return null;
        }
        return Solution.builder()
                .no(solution.getNo())
                .name(solution.getName())
                .attributes(ObjectUtils.defaultIfNull(solution.getAttributes(), new HashMap<String, String>()))
                .build();
    }

    private List<SerialInfo> buildSerialInfos(List<com.jdl.sc.ofw.outbound.dto.vo.SerialInfo> serialInfos) {
        if (serialInfos == null || serialInfos.isEmpty()) {
            return null;
        }
        return serialInfos.stream()
                .map(serialInfo -> SerialInfo.builder().serialNo(serialInfo.getSerialNo()).serialType(serialInfo.getSerialType()).build())
                .collect(Collectors.toList());
    }

    private List<com.jdl.sc.ofw.out.domain.model.vo.BatchInfo> buildBatchInfos(List<BatchInfo> batchInfos) {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return null;
        }
        return batchInfos.stream().map(batchInfo -> com.jdl.sc.ofw.out.domain.model.vo.BatchInfo.builder()
                .quantity(batchInfo.getQuantity() == null ? null : Quantity.builder()
                        .unit(batchInfo.getQuantity().getUnit())
                        .value(batchInfo.getQuantity().getValue())
                        .build())
                .batchSource(batchInfo.getBatchSource())
                .attributeInfos(buildBatchAttributeInfos(batchInfo.getAttributeInfoList()))
                .batchAttrList(batchInfo.getBatchAttrList())
                .batchAttr(batchInfo.getBatchAttr())
                .build()).collect(Collectors.toList());
    }

    private List<com.jdl.sc.ofw.out.domain.model.vo.AttributeInfo> buildBatchAttributeInfos(List<AttributeInfo> attributeInfoList) {
        return attributeInfoList.stream().map(attributeInfo -> com.jdl.sc.ofw.out.domain.model.vo.AttributeInfo.builder()
                .key(attributeInfo.getKey())
                .value(attributeInfo.getValue())
                .build()).collect(Collectors.toList());
    }

    public Warehouse toWarehouseInfo(com.jdl.sc.ofw.outbound.dto.vo.Warehouse warehouse) {
        return Objects.isNull(warehouse)
                ? null
                : Warehouse.builder()
                .warehouseNo(warehouse.getWarehouseNo())
                .warehouseName(warehouse.getWarehouseName())
                .warehouseType(Objects.isNull(warehouse.getWarehouseType())
                        ? null
                        : WarehouseTypeEnum.fromCode(warehouse.getWarehouseType()))
                .build();
    }

    private Invoice buildInvoice(com.jdl.sc.ofw.outbound.dto.vo.Invoice invoice) {
        List<InvoiceDetail> invoiceDetailList = null;
        if (!Objects.isNull(invoice) && CollectionUtils.isNotEmpty(invoice.getInvoiceDetailList())) {
            invoiceDetailList = invoice.getInvoiceDetailList().stream().map(invoiceDetailDTO -> InvoiceDetail.builder()
                    .goodsName(invoiceDetailDTO.getGoodsName())
                    .amount(invoiceDetailDTO.getAmount())
                    .rate(invoiceDetailDTO.getRate())
                    .price(invoiceDetailDTO.getPrice())
                    .quantity(invoiceDetailDTO.getQuantity())
                    .remark(invoiceDetailDTO.getRemark())
                    .type(invoiceDetailDTO.getType())
                    .unit(invoiceDetailDTO.getUnit())
                    .build()
            ).collect(Collectors.toList());
        }
        return Objects.isNull(invoice)
                ? null
                : Invoice.builder()
                .invoiceTax(invoice.getInvoiceTax())
                .elecInvoiceUrl(invoice.getElecInvoiceUrl())
                .invoiceContent(invoice.getInvoiceContent())
                .invoiceTitle(invoice.getInvoiceTitle())
                .invoiceSource(invoice.getInvoiceSource())
                .bankAccount(invoice.getBankAccount())
                .printModel(invoice.getPrintModel())
                .invoiceTotalAmount(invoice.getInvoiceTotalAmount())
                .address(invoice.getAddress())
                .phoneNumber(invoice.getPhoneNumber())
                .address(invoice.getAddress())
                .invoiceDetailList(invoiceDetailList)
                .invoiceBiz(invoice.getInvoiceBiz())
                .build();
    }

    public Shipment toShipment(com.jdl.sc.ofw.outbound.dto.vo.Shipment shipment) {
        if (shipment == null) {
            return null;
        }
        PresortInfo presortInfo = PresortInfo.builder()
                .siteNo(shipment.getPreSiteNo())
                .siteName(shipment.getPreSiteName())
                .roadArea(shipment.getRoad())
                .build();
        return Shipment.builder()
                .presortInfo(presortInfo)
                .dispatchNo(shipment.getDispatchNo())
                .transportCount(shipment.getTransportCount())
                .loadSequence(shipment.getLoadSequence())
                .cardNo(shipment.getCardNo())
                .planBeginEnterTime(shipment.getPlanBeginEnterTime())
                .transportModeEnum(TransportModeEnum.fromExternalCode(ObjectUtils.nullIfNull(shipment.getTransportMode(), Integer::parseInt)))
                .bunchQty(shipment.getBunchQty())
                .kilometers(shipment.getKilometers())
                .shipperNo(shipment.getShipperNo())
                .shipperName(shipment.getShipperName())
                .shipperType(Optional.ofNullable(shipment.getShipperType()).map(ShipperTypeEnum::fromCode).orElse(null))
                .deliveryStaffName(shipment.getDeliveryStaffName())
                .vehicleNumber(shipment.getVehicleNumber())
                .operateTime(shipment.getOperateTime())
                .halfReceive(Optional.ofNullable(shipment.getHalfReceive()).map(HalfReceiveEnum::fromCode).orElse(null))
                .signType(shipment.getSignType())
                .signBillTypeEnum(shipment.getSignBillTypeEnum())
                .platformLogisticsService(shipment.getPlatformLogisticsService())
                .expectDeliveryStartTime(shipment.getExpectDeliveryStartTime())
                .expectDeliveryEndTime(shipment.getExpectDeliveryEndTime())
                .serviceRequirements(shipment.getServiceRequirements())
                .build();
    }

    private EdiRemarkInfo buildEdiRemarkInfo(String ediRemark) {
        if (StringUtils.isBlank(ediRemark)) {
            return null;
        }
        String ediRemarkContent;
        try {
            ediRemarkContent = dtcJimKvAgent.load(ediRemark);
        } catch (Exception e) {
            throw new RuntimeException("ediRemark解析失败,ediRemark:" + ediRemark);
        }
        JsonNode ediRemarkInfoNode = JsonUtil.toJsonNodeSafe(ediRemarkContent);
        if (ediRemarkInfoNode == null) {
            return null;
        }

        EdiRemarkInfo.EdiRemarkInfoBuilder builder = EdiRemarkInfo.builder();
        // deliveryOrder
        JsonNode deliveryOrderNode = ediRemarkInfoNode.get("deliveryOrder");
        if (deliveryOrderNode != null) {
            JsonNode receiverInfoNode = deliveryOrderNode.get("receiverInfo");
            String oaid = receiverInfoNode == null ? PlatformConstants.INVALID_VALUE : receiverInfoNode.path("oaid").textValue();
            final JsonNode oaidOrderSourceCodeNode = deliveryOrderNode.path("oaidOrderSourceCode");
            String oaidOrderSourceCode = oaidOrderSourceCodeNode == null ? null : oaidOrderSourceCodeNode.textValue();
            final JsonNode extendPropsNode = deliveryOrderNode.path("extendProps");
            builder.deliveryOrder(DeliveryOrderInfo.builder()
                    .sourcePlatformCode(deliveryOrderNode.path("sourcePlatformCode").textValue())
                    .buyerMessage(deliveryOrderNode.path("buyerMessage").textValue())
                    .logisticsCode(deliveryOrderNode.path("logisticsCode").textValue())
                    .oaid(oaid)
                    .oaidOrderSourceCode(oaidOrderSourceCode)
                    .extendProps(extendPropsNode != null ? DeliveryOrderExtendProps.builder().userId(extendPropsNode.path("userId").textValue()).build() : null)
                    .build());
        }
        // orderLines
        JsonNode orderLines = ediRemarkInfoNode.get("orderLines");
        String sourceOrderCode = PlatformConstants.INVALID_VALUE;
        if (orderLines != null) {
            JsonNode orderLine = orderLines.get("orderLine");
            sourceOrderCode = orderLine == null ? PlatformConstants.INVALID_VALUE : orderLine.get(0).path("sourceOrderCode").textValue();
        }
        builder.sourceOrderCode(sourceOrderCode);
        builder.comment(ediRemarkInfoNode.path("COMMENT").textValue());
        JsonNode tictokDaifaShopIdNode = ediRemarkInfoNode.get("tictokDaifaShopId");
        if (tictokDaifaShopIdNode != null && StringUtils.isNotBlank(tictokDaifaShopIdNode.textValue())) {
            builder.tictokDaifaShopId(tictokDaifaShopIdNode.textValue());
        }
        return builder.build();
    }

    public BasicInfo toBasicInfo(Map<String, String> extendProps) {
        if (extendProps == null) {
            return null;
        }
        PreSaleStageEnum preSaleStageEnum = getPreSaleStageEnum(extendProps);

        return BasicInfo.builder()
                .preSaleStage(preSaleStageEnum)
                .build();
    }

    private PreSaleStageEnum getPreSaleStageEnum(Map<String, String> extendProps) {
        PreSaleStageEnum preSaleStageEnum = null;
        //订单中心重处理结果,受理不成功，则修改为非预售单，已废弃
        if (extendProps.containsKey("acceptResult")) {
            final String acceptSuccess = "1";
            preSaleStageEnum = !acceptSuccess.equals(extendProps.get("acceptResult")) ? PreSaleStageEnum.DEFAULT : null;
        }
        //订单中心仓拉回恢复修改为非预售单
        if (extendProps.containsKey("preSale")) {
            preSaleStageEnum = "false".equals(extendProps.get("preSale")) ? PreSaleStageEnum.DEFAULT : null;
        }
        //定金阶段修改
        if (extendProps.containsKey("preSaleStage")) {
            preSaleStageEnum = PreSaleStageEnum.fromCode(Integer.valueOf(extendProps.get("preSaleStage")));
        }
        return preSaleStageEnum;
    }

    public Solution toSolution(OutboundModifyRequest request) {
        Map<String, String> attributes = null;
        if (request.getSolution() != null && request.getSolution().getAttributes() != null) {
            attributes = request.getSolution().getAttributes();
        }
        if (request.getShouldPayMoney() != null) {
            if (attributes == null) {
                attributes = new HashMap<>();
            }
            attributes.put(SHOULD_PAY_MONEY.getValue(), request.getShouldPayMoney());
        }
        return attributes == null ? null : Solution.builder()
                .attributes(attributes)
                .build();
    }

    public Fulfillment toFulfillment(OutboundModifyRequest request) {
        if (request.getOutboundUrgency() == null && request.getFulfillment() == null) {
            return null;
        }
        return Fulfillment.builder()
                .orderUrgencyType(ObjectUtils.nullIfNull(request.getOutboundUrgency(), OrderUrgencyType::fromOmsCode))
                .crossDockType(
                        ObjectUtils.nullIfNull(
                                Optional.ofNullable(request.getFulfillment())
                                        .map(com.jdl.sc.ofw.outbound.dto.vo.Fulfillment::getCrossDockType)
                                        .orElse(null),
                                CrossDockTypeEnum::fromCode
                        )
                )
                .customerSignforAttributes(Optional.ofNullable(request.getFulfillment())
                        .map(com.jdl.sc.ofw.outbound.dto.vo.Fulfillment::getCustomerSignforAttributes).orElse(null))
                .build();
    }

    public OrderSign toOrderSign(com.jdl.sc.ofw.outbound.dto.vo.OrderSign orderSign) {
        if (orderSign == null) {
            return null;
        }
        return OrderSign.builder()
                .businessSubsidy(orderSign.getBusinessSubsidy())
                .build();
    }

    public Channel toChannel(com.jdl.sc.ofw.outbound.dto.vo.Channel channel) {
        if (channel == null) {
            return null;
        }
        return Channel.builder()
                .channelTaskCode(channel.getExtendProps() == null ? null : channel.getExtendProps().get("channelTaskCode"))
                .build();
    }

    private List<GoodsAgentSalesDetail> buildAgentSalesDetail(List<com.jdl.sc.ofw.outbound.dto.vo.GoodsAgentSalesDetail> goodsAgentSalesDetailList) {
        if (CollectionUtils.isEmpty(goodsAgentSalesDetailList)) {
            return null;
        }

        List<GoodsAgentSalesDetail> goodsAgentSalesDetails = new ArrayList<>();
        for (com.jdl.sc.ofw.outbound.dto.vo.GoodsAgentSalesDetail agentSalesDetail : goodsAgentSalesDetailList) {
            GoodsAgentSalesDetail goodsAgentSalesDetail = GoodsAgentSalesDetail.builder()
                    .agentSaleGoodsNo(agentSalesDetail.getAgentSaleGoodsNo())
                    .agentSaleGoodsQuantity(agentSalesDetail.getAgentSaleGoodsQuantity())
                    .agentSaleCargoInfos(toAgentSaleInfos(agentSalesDetail.getAgentSaleCargoInfos()))
                    .build();
            goodsAgentSalesDetails.add(goodsAgentSalesDetail);
        }
        return goodsAgentSalesDetails;
    }

    private List<AgentRefCargoDetails> toAgentSaleInfos(List<com.jdl.sc.ofw.outbound.dto.vo.AgentRefCargoDetails> agentSaleInfos) {
        if (CollectionUtils.isEmpty(agentSaleInfos)) {
            return null;
        }

        List<AgentRefCargoDetails> agentRefCargoDetails = new ArrayList<>();
        for (com.jdl.sc.ofw.outbound.dto.vo.AgentRefCargoDetails agentSaleInfo : agentSaleInfos) {
            AgentRefCargoDetails agentRefCargo = AgentRefCargoDetails.builder()
                    .cargoNo(agentSaleInfo.getCargoNo())
                    .cargoQuantity(Quantity.builder()
                            .unit(agentSaleInfo.getCargoQuantity().getUnit())
                            .value(agentSaleInfo.getCargoQuantity().getValue())
                            .build())
                    .agentSign(agentSaleInfo.getAgentSign())
                    .build();
            agentRefCargoDetails.add(agentRefCargo);
        }
        return agentRefCargoDetails;
    }

    public FulfillmentCommand toFulfillmentCommand(com.jdl.sc.ofw.outbound.dto.vo.FulfillmentCommand fulfillmentCommand) {
        if (Objects.isNull(fulfillmentCommand) || Objects.isNull(fulfillmentCommand.getDecryptFlag())) {
            return null;
        }
        return FulfillmentCommand.builder()
                .decryptFlag(fulfillmentCommand.getDecryptFlag())
                .build();
    }
}
