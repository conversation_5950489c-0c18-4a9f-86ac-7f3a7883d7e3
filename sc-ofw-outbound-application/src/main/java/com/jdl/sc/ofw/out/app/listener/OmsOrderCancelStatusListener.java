package com.jdl.sc.ofw.out.app.listener;

import com.jd.serviceplus.order.dto.ofc.OFCCancelOrderRequest;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OmsOrderCancelStatusMessage;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ServicePlusAcl;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.util.Objects;

/**
 * 监听订单中心 outbound_order_cancel_status 消息
 * <a href="https://joyspace.jd.com/pages/QccV6zW6qvhE059NeBc5">仓配订单取消状态广播-MQ</a>
 */
@Slf4j
public class OmsOrderCancelStatusListener extends AbstractMessageTemplate {

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private ServicePlusAcl servicePlusAcl;

    @Override
    protected void onMessage(String message) throws Exception {
        if (message == null) {
            if (log.isInfoEnabled()) {
                log.info("OmsOrderCancelStatusListener.message为空消息无需处理");
            }
            return;
        }

        OmsOrderCancelStatusMessage cancelStatusMessage;
        try {
            cancelStatusMessage = JsonUtil.readValue(message, OmsOrderCancelStatusMessage.class);
        } catch (Exception e) {
            log.error("OmsOrderCancelStatusListener.onMessage 序列化异常", e);
            throw new RuntimeException("OmsOrderCancelStatusListener.onMessage 序列化异常:" + e.getMessage());
        }

        try {
            validate(cancelStatusMessage);

            // 状态过滤 只处理取消和取消拦截成功
            if (!needHandle(cancelStatusMessage)) {
                return;
            }

            String orderNo = cancelStatusMessage.getOrderInfo().getOrderNo();
            OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);
            if (Objects.isNull(model)) {
                log.info("{}未查询到订单信息，忽略", orderNo);
                return;
            }

            // 取消送装服务
            cancelServicePlusOrder(model, cancelStatusMessage);
        } catch (ValidationException e) {
            log.warn("OmsOrderCancelStatusListener忽略消息:{} ", e.getMessage());
        } catch (Exception e) {
            log.error("OmsOrderCancelStatusListener处理异常", e);
            throw e;
        }
    }

    /**
     * 状态过滤 目前只处理
     * 取消成功
     * 取消拦截成功
     */
    private static boolean needHandle(OmsOrderCancelStatusMessage cancelStatusMessage) {
        /**
         * {@link cn.jdl.oms.supplychain.domain.spec.dict.CancelStatusEnum}
         */
        // 取消成功
        if (cancelStatusMessage.getOrderInfo().getCancelStatus() == 1) {
            return true;
        }
        // 取消拦截成功
        if (cancelStatusMessage.getOrderInfo().getCancelStatus() == 5) {
            return true;
        }
        return false;
    }

    /**
     * 取消服务家订单
     */
    private void cancelServicePlusOrder(OrderFulfillmentModel model, OmsOrderCancelStatusMessage cancelStatusMessage) {
        if (!model.isHaveServicePlusOrder()) {
            return;
        }

        try {
            /**
             * {@link cn.jdl.oms.supplychain.domain.spec.dict.CancelStatusEnum}
             */
            if (cancelStatusMessage.getOrderInfo().getCancelStatus() == 1) {
                log.info("{}订单已取消，取消送装服务", model.orderNo());
                servicePlusAcl.cancel(
                        OFCCancelOrderRequest.builder()
                                .orderNumber(model.orderNo())
                                .companyCode(model.getCustomer().getAccountNo())
                                .cancelReason("订单已取消，不再需要送装服务")
                                .build()
                );
            } else if (cancelStatusMessage.getOrderInfo().getCancelStatus() == 5) {
                log.info("{}订单已取消拦截成功，取消送装服务", model.orderNo());
                servicePlusAcl.cancel(
                        OFCCancelOrderRequest.builder()
                                .orderNumber(model.orderNo())
                                .companyCode(model.getCustomer().getAccountNo())
                                .cancelReason("订单已取消拦截成功，不再需要送装服务")
                                .build()
                );
            }
        } catch (Exception e) {
            log.error("{}取消送装服务失败{}", model.orderNo(), e.getMessage());
        }
    }

    /**
     * 消息校验
     */
    private void validate(OmsOrderCancelStatusMessage message) {
        if (message == null) {
            throw new ValidationException("取消状态消息为空无需处理");
        }
        if (message.getOrderInfo().getOrderNo() == null) {
            throw new ValidationException("取消状态消息订单号为空无需处理");
        }
    }
}
