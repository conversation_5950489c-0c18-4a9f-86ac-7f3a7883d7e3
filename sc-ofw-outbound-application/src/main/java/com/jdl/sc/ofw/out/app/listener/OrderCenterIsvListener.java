package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.core.ump.Ump;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.service.OutboundCreateAgent;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 销售出订单->ToB
 */
@Component
public class OrderCenterIsvListener extends AbstractSaleOutboundOrderListener {


    public OrderCenterIsvListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                  ReceiveOrderTranslator receiveOrderTranslator,
                                  @Value("${ofw.standard.businessType}") String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }


    @Ump
    @Override
    public void onMessage(String message) {
        super.onMessage(message);
    }
}
