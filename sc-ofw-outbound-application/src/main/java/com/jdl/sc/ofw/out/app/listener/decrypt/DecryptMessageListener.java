package com.jdl.sc.ofw.out.app.listener.decrypt;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.app.listener.dto.AddProduct;
import com.jdl.sc.ofw.out.app.listener.dto.CustomExtend;
import com.jdl.sc.ofw.out.app.listener.dto.DecryptMessage;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.common.constant.OvasConstants;
import com.jdl.sc.ofw.out.common.dict.DecryptCommandEnum;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.util.CainiaoJpUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.TiktokJpUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.WaybillAddressUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.XhsNewProcessUtil;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.dto.vo.Consignee;
import com.jdl.sc.ofw.outbound.dto.vo.FulfillmentCommand;
import com.jdl.sc.ofw.outbound.dto.vo.JdAddress;
import com.jdl.sc.ofw.outbound.dto.vo.Shipment;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 监听EDI 明文消息
 * 场景: 快手、小红书
 */
@Slf4j
@SuppressWarnings("all")
public class DecryptMessageListener extends AbstractMessageTemplate {

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource(name = "consumerGroupOutboundService")
    private OutboundService outboundService;

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    // TODO
    @Resource
    private CainiaoJpUtils cainiaoJpUtils;

    @Resource
    private XhsNewProcessUtil xhsNewProcessUtil;

    @Resource
    private OrderCenterAcl orderCenterAcl;
    @Resource
    private TiktokJpUtils tiktokJpUtils;

    private static final String BAICHUAN_ORDER_NO_PRE = "ESL";

    /**
     * 抖音音需达 增值服务
     */
    private static final String SVC_WBHOMEDELIVERY = "SVC-WBHOMEDELIVERY";

    /**
     * 抖音即时零售-增值服务
     */
    private static final String DY_INSTANT_JSLS_SCENE_KEY = "jslsScene";
    private static final String DY_INSTANT_THIRDHALFPARTLYLOGISTICS_VALUE = "ThirdHalfPartlyLogistics";
    private static final String DY_INSTANT_CND_DELIVERY = "CND_DELIVERY";
    private static final String DY_INSTANT_LATEST_RECEIPT_TIME_KEY = "LatestReceiptTime";
    private static final String SERVICE_TYPE = "serviceType";
    private static final String APPOINTMENT_DELIVERY_TIME = "appointmentDeliveryTime";
    private static final String DY_INSTANT_JSLS_ORDER_VALUE = "jslsOrder";
    private static final String DY_INSTANT_ADDED_SERVICE_CODE_KEY = "DY_INSTANT_ADDED_SERVICE_CODE";

    private static final List<DeliveryPerformanceChannelEnum> ENCRYPT_JMQ_DECRYPT = new ArrayList<DeliveryPerformanceChannelEnum>() {{
        // TODO 增加枚举
        add(DeliveryPerformanceChannelEnum.CAI_NIAO);
        add(DeliveryPerformanceChannelEnum.AKC);
        add(DeliveryPerformanceChannelEnum.KS);
        add(DeliveryPerformanceChannelEnum.XHS);
        add(DeliveryPerformanceChannelEnum.TIK_TOK);
    }};

    @Override
    protected void onMessage(String message) throws Exception {
        DecryptMessage decryptMessage = null;
        try {
            decryptMessage = JsonUtil.parse(message, DecryptMessage.class);
        } catch (Exception e) {
            log.error("4PL明文信息监听,报文信息解析异常", e);
            throw e;
        }
        //单号校验
        if(switchKeyUtils.isDecryptMessageValidOrderNoSwitch()){
            if(!validOrderNo(decryptMessage.getOrderNo())){
                log.warn("4PL明文信息监听,单号校验未通过 忽略 decryptMessage:{}", JsonUtil.toJsonSafe(decryptMessage));
                return;
            }
        }
        final String orderNo = decryptMessage.getOrderNo();
        OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);
        if (Objects.isNull(model) || !OrderStatusEnum.INIT.equals(model.getOrderStatus())) {
            log.info("4PL明文信息监听,{}未查询到订单信息 或 订单状态非初始化，忽略", orderNo);
            return;
        }

        if (!support(model)) {
            return;
        }
        //报文内容校验
        try {
            validDecryptMessage(decryptMessage);
        } catch (Exception e) {
            log.error("4PL明文信息监听,报文信息校验未通过 decryptMessage:{}", JsonUtil.toJsonSafe(decryptMessage), e);
            throw new RuntimeException("4PL明文信息监听,报文信息校验未通过");
        }
        if (DeliveryPerformanceChannelEnum.CAI_NIAO.equals(model.getShipment().getDeliveryPerformanceChannel())
            || DeliveryPerformanceChannelEnum.AKC.equals(model.getShipment().getDeliveryPerformanceChannel())
            || DeliveryPerformanceChannelEnum.XHS == model.getShipment().getDeliveryPerformanceChannel()
            || DeliveryPerformanceChannelEnum.TIK_TOK == model.getShipment().getDeliveryPerformanceChannel()
            || (DeliveryPerformanceChannelEnum.KS.equals(model.getShipment().getDeliveryPerformanceChannel())
                && Objects.nonNull(FreightProductEnum.getKsFreightProduct(model.getProducts().getProductCodes())))) {
            log.debug("没有历史链路，不需要使用链路切换工具");
        } else {
            if (switchKeyUtils.isJmqDecrypt(model)) {
                log.debug("4PL明文信息监听,业务身份:{} 事业部:{}开启", model.getBatrixInfo().getBusinessUnit(), model.getCustomer().getAccountNo());
            } else {
                log.info("4PL明文信息监听,业务身份:{} 事业部:{}未切, 忽略", model.getBatrixInfo().getBusinessUnit(), model.getCustomer().getAccountNo());
                return;
            }
        }

        OutboundModifyResponse modify = outboundService.modify(this.buildModifyRequest(decryptMessage, model));
        if (ResponseConstant.FAILED_CODE.equals(modify.getCode())) {
            log.error("4PL明文信息监听-修改订单信息失败,orderNo:{}", orderNo);
            throw new RuntimeException("修改订单信息失败");
        }
    }

    private boolean validOrderNo(String orderNo) {
        return StringUtils.isNotBlank(orderNo)
                && orderNo.startsWith(BAICHUAN_ORDER_NO_PRE)
                && orderCenterAcl.checkOrderExist(orderNo) ;
    }

    private void validDecryptMessage(DecryptMessage decryptMessage) {
        String preMsg = "4PL明文信息监听-校验-";
        if (Objects.isNull(decryptMessage)) {
            log.error(preMsg + "消息内容为空");
            throw new IllegalArgumentException(preMsg + "消息内容为空");
        }
        validIllegalArgument(preMsg + "运单号为空", decryptMessage::getWaybillNo);
        validIllegalArgument(preMsg + "收货人姓名为空", decryptMessage::getConsigneeName);
        validIllegalArgument(preMsg + "收件人详细地址为空", decryptMessage::getConsigneeAddress);
        validIllegalArgument(preMsg + "手机号和电话都为空", decryptMessage::getConsigneePhone, decryptMessage::getConsigneeMobile);
    }


    public void validIllegalArgument(String warnMsg, Supplier<String>... supplier) {
        boolean allBlack = Arrays.stream(supplier).allMatch(s -> StringUtils.isBlank(s.get()));
        if (allBlack) {
            throw new IllegalArgumentException(warnMsg);
        }
    }

    private OutboundModifyRequest buildModifyRequest(DecryptMessage decryptMessage, OrderFulfillmentModel model) {

        return OutboundModifyRequest.builder()
                .orderNo(decryptMessage.getOrderNo())
                .operator("order_consigneeInfo_plaintext")
                .operateTime(new Date())
                .consignee(Consignee.builder()
                        .name(decryptMessage.getConsigneeName())
                        .phone(decryptMessage.getConsigneePhone())
                        .mobile(decryptMessage.getConsigneeMobile())
                        .address(this.buildConsigneeAddress(decryptMessage))
                        .encryptMode(decryptMessage.getEncryptMode())
                        .virtualNumberExpirationDate(decryptMessage.getVirtualMobileTime())
                        .build())
                .shipment(this.buildShipment(decryptMessage, model))
                .fulfillmentCommand(FulfillmentCommand.builder()
                        .decryptFlag(DecryptCommandEnum.DECRYPTED.getCode())
                        .build())
                .build();
    }

    private Shipment buildShipment(DecryptMessage decryptMessage, OrderFulfillmentModel model) {
        String dyYxdAddedService = getDyYxdAddedService(decryptMessage);
        Shipment.ShipmentBuilder shipmentBuilder = Shipment.builder().platformLogisticsService(dyYxdAddedService);
        if (tiktokJpUtils.isJpInstant(model)) {
            String dyInstantAddedService = getDyInstantAddedService(decryptMessage);
            if (StringUtils.isNotBlank(dyInstantAddedService)) {
                Map<String, String> serviceRequirements = ObjectUtils.defaultIfNull(model.getShipment().getServiceRequirements(), new HashMap<>());
                serviceRequirements.put(OvasConstants.CHOOSE_TIME_DELIVERY_SERVICE, dyInstantAddedService);
                shipmentBuilder.serviceRequirements(serviceRequirements);
            }
        }
        return shipmentBuilder.build();
    }

    private JdAddress buildConsigneeAddress(DecryptMessage decryptMessage) {
        final JdAddress address = JdAddress.builder().build();
        WaybillAddressUtils.assignAddress(decryptMessage.getProvinceName(), address::setProvinceNo, address::setProvinceName);
        WaybillAddressUtils.assignAddress(decryptMessage.getCityName(), address::setCityNo, address::setCityName);
        WaybillAddressUtils.assignAddress(decryptMessage.getCountyName(), address::setCountyNo, address::setCountyName);
        WaybillAddressUtils.assignAddress(decryptMessage.getTownName(), address::setTownNo, address::setTownName);
        address.setDetailAddress(decryptMessage.getConsigneeAddress());
        return address;
    }

    private boolean support(OrderFulfillmentModel model) {
        // 非京配订单不处理
        if (!ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())) {
            log.info("4PL明文信息监听 非京配订单, 不处理 orderNo:{}", model.getOrderNo());
            return false;
        }
        // 加密场景判断
        DeliveryPerformanceChannelEnum deliveryPerformanceChannel = model.getShipment().getDeliveryPerformanceChannel();
        if (deliveryPerformanceChannel == null || !ENCRYPT_JMQ_DECRYPT.contains(deliveryPerformanceChannel)) {
            log.info("4PL明文信息监听,{} 此加密场景{} 不支持，忽略", model.getOrderNo(), deliveryPerformanceChannel);
            return false;
        }
        // 菜鸟
        if (DeliveryPerformanceChannelEnum.CAI_NIAO.equals(deliveryPerformanceChannel)) {
            return cainiaoJpUtils.isCainiaoJpNewFlow(model);
        }
        // 小红书
        if (DeliveryPerformanceChannelEnum.XHS == deliveryPerformanceChannel) {
            return xhsNewProcessUtil.isXhsNewProcess(model.getCustomer().getAccountNo(), model.getFulfillment().getCustomField());
        }
        return true;
    }

    /**
     * 抖音音需达 增值服务
     */
    private String getDyYxdAddedService(DecryptMessage decryptMessage) {
        if (CollectionUtils.isEmpty(decryptMessage.getAddedProducts())) {
            return null;
        }
        List<String> productCodes = decryptMessage.getAddedProducts()
                .stream()
                .filter(addProduct -> SVC_WBHOMEDELIVERY.equals(addProduct.getProductCode()))
                .map(AddProduct::getProductCode)
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(productCodes) ? null : productCodes.get(0);
    }

    /**
     * 抖音即时零售 增值服务
     */
    private String getDyInstantAddedService(DecryptMessage decryptMessage) {
        if (CollectionUtils.isEmpty(decryptMessage.getCustomExtends())) {
            return null;
        }

        // 即时配-ThirdHalfPartlyLogistics
        String dyInstantCndDeliveryAddedService = getDyInstantThirdHalfPartlyLogisticsAddedService(decryptMessage);
        if (StringUtils.isNotBlank(dyInstantCndDeliveryAddedService)) {
            return dyInstantCndDeliveryAddedService;
        }

        // 即时配-jslsOrder
        return getDyInstantJslsOrderAddedService(decryptMessage);
    }

    private String getDyInstantJslsOrderAddedService(DecryptMessage decryptMessage) {
        CustomExtend jslsOrderAddedService = decryptMessage.getCustomExtends()
                .stream()
                .filter(customExtend -> DY_INSTANT_JSLS_SCENE_KEY.equals(customExtend.getKey())
                        && CollectionUtils.isNotEmpty(customExtend.getValue())
                        && customExtend.getValue().contains(DY_INSTANT_JSLS_ORDER_VALUE))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(jslsOrderAddedService)) {
            return null;
        }
        Map<String, String> service = new HashMap<>();
        service.put(APPOINTMENT_DELIVERY_TIME, getAppointmentDeliveryTime(decryptMessage, DY_INSTANT_JSLS_ORDER_VALUE));
        service.put(DY_INSTANT_ADDED_SERVICE_CODE_KEY, DY_INSTANT_JSLS_ORDER_VALUE);
        return JsonUtil.stringify(service);
    }

    private String getDyInstantThirdHalfPartlyLogisticsAddedService(DecryptMessage decryptMessage) {
        CustomExtend cndDeliveryAddedService = decryptMessage.getCustomExtends()
                .stream()
                .filter(customExtend -> DY_INSTANT_JSLS_SCENE_KEY.equals(customExtend.getKey())
                        && CollectionUtils.isNotEmpty(customExtend.getValue())
                        && customExtend.getValue().contains(DY_INSTANT_THIRDHALFPARTLYLOGISTICS_VALUE))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(cndDeliveryAddedService)) {
            return null;
        }
        Map<String, String> service = new HashMap<>();
        service.put(SERVICE_TYPE, DY_INSTANT_CND_DELIVERY);
        service.put(APPOINTMENT_DELIVERY_TIME, getAppointmentDeliveryTime(decryptMessage, DY_INSTANT_CND_DELIVERY));
        service.put(DY_INSTANT_ADDED_SERVICE_CODE_KEY, DY_INSTANT_THIRDHALFPARTLYLOGISTICS_VALUE);
        return JsonUtil.stringify(service);
    }

    /**
     * 获取抖音即时零售预约时间
     * <p>
     * 即时配：
     * <li>jslsOrder: appointmentDeliveryTime 取第二天最晚时间</li>
     * <li>ThirdHalfPartlyLogistics: 用抖音返回的承诺送达日期LatestReceiptTime+23:59:59的时间戳（秒），未返回则取当前时间</li>
     * </p>
     */
    private String getAppointmentDeliveryTime(DecryptMessage decryptMessage, String addedService) {
        if (DY_INSTANT_JSLS_ORDER_VALUE.equals(addedService)){
            Instant instant = LocalDate.now().plusDays(1).atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
            return String.valueOf(instant.getEpochSecond());
        }
        CustomExtend latestReceiptTimeAddedService = decryptMessage.getCustomExtends()
                .stream()
                .filter(customExtend -> DY_INSTANT_LATEST_RECEIPT_TIME_KEY.equals(customExtend.getKey())
                        && CollectionUtils.isNotEmpty(customExtend.getValue()))
                .findFirst()
                .orElse(null);
        String latestReceiptTime = (Objects.nonNull(latestReceiptTimeAddedService) && CollectionUtils.isNotEmpty(latestReceiptTimeAddedService.getValue()))
                ? latestReceiptTimeAddedService.getValue().get(0)
                : null;
        LocalDate dateToUse = StringUtils.isNotBlank(latestReceiptTime)
                ? LocalDate.parse(latestReceiptTime)
                : LocalDate.now();
        Instant instant = dateToUse.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
        return String.valueOf(instant.getEpochSecond());
    }
}
