package com.jdl.sc.ofw.preheat;

import com.jdl.sc.core.preheat.PreheatPlugin;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

public class HbasePreheat extends PreheatPlugin<Object> {
    private static final List<String> ORDER_NOS = Arrays.asList("ESL00000021221921835",
            "ESL00000021221921836", "ESL00000021221921837", "ESL00000021221921838", "ESL00000021221921839",
            "ESL00000021221921810", "ESL00000021221921811", "ESL00000021221921812", "ESL00000021221921813",
            "ESL00000021221921814");
    @Resource
    private OutboundModelRepository repository;

    public HbasePreheat() {
        super("");
    }

    @Override
    public void execute(Object bean, int index) {
        repository.getModelByOrderNo(ORDER_NOS.get(index));
    }

    @Override
    public String name() {
        return "Hbase 预热";
    }

    @Override
    public int getTimes() {
        return ORDER_NOS.size();
    }
}
