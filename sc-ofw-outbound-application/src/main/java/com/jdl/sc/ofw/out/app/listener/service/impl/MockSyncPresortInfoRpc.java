package com.jdl.sc.ofw.out.app.listener.service.impl;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.SyncPreSortInfoDto;
import com.jdl.sc.ofw.out.app.listener.service.SyncPresortInfoAcl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Profile({"ab"})
public class MockSyncPresortInfoRpc implements SyncPresortInfoAcl {
    @Override
    public void sendPresortInfo(SyncPreSortInfoDto syncPreSortInfoDto) {
        if (log.isInfoEnabled()) {
            log.info("MockSyncPresortInfoRpc 完成,message:{}", JsonUtil.toJsonSafe(syncPreSortInfoDto));
        }
    }
}
