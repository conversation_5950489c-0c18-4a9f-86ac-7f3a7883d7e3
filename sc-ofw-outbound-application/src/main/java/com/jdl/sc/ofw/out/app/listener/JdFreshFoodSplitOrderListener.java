package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.relation.dict.RelationFieldKeyEnum;
import cn.jdl.oms.relation.model.OrderElement;
import cn.jdl.oms.relation.model.QueryOrderRelationRequest;
import cn.jdl.oms.search.dto.Order;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.ofw.out.app.listener.dto.JdFreshFoodSplitOrderMessage;
import com.jdl.sc.ofw.out.common.constant.OmsConstant;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractFilterEnvMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.dto.request.ResumeRequest;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 京东买菜拆单结果消息
 */
@Slf4j
public class JdFreshFoodSplitOrderListener extends AbstractFilterEnvMessageTemplate {

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private OrderWorkflowLockAbility orderWorkflowLockAbility;

    @Resource
    private OrderHoldAcl orderHoldAcl;

    private static final String BUSINESS_IDENTITY_SUPPLY_CHAIN = "cn_jdl_supplychain";
    private static final String BUSINESS_TYPE_OUTBOUND_UNION = "outbound_union";

    private static final String BUSINESS_IDENTITY_SC_ISV = "cn_jdl_sc-isv";
    private static final String BUSINESS_TYPE_OUTBOUND_SALE = "outbound_sale";

    private static final String ORDER_TYPE = "100";

    public JdFreshFoodSplitOrderListener(@Qualifier("jdFreshFoodUatPredicate") MessageReceive messageReceive) {
        super(messageReceive);
    }

    @Override
    protected void onMessage(String message) throws Exception {
        JdFreshFoodSplitOrderMessage jdFreshFoodSplitOrderMessage = null;
        try {
            jdFreshFoodSplitOrderMessage = JsonUtil.readValue(message, JdFreshFoodSplitOrderMessage.class);
        } catch (Exception e) {
            log.error("message 序列化异常", e);
            throw new RuntimeException("message 序列化异常:" + e.getMessage(), e);
        }

        if (jdFreshFoodSplitOrderMessage == null || StringUtils.isEmpty(jdFreshFoodSplitOrderMessage.getDeptNo())) {
            return;
        }

        // 根据orderId获取百川订单
        List<Order> orders = orderCenterAcl.queryOrderRelation(buildRequest(jdFreshFoodSplitOrderMessage));
        if (CollectionUtils.isEmpty(orders)) {
            log.error("京东买菜拆单结果处理，根据商家单号[{}]与事业部[{}]查询订单中心，返回数据为空", jdFreshFoodSplitOrderMessage.getOrderId(), jdFreshFoodSplitOrderMessage.getDeptNo());
            return;
        }

        String orderNo = orders.get(0).getBaseInfo().getOrderNo();
        OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);
        if (model == null) {
            log.error("京东买菜拆单结果处理，根据百川单号[{}]查询OFC，返回数据为空", orderNo);
            throw new InfrastructureException(UnifiedErrorSpec.ACL.RETURN_EXCEPTION).withCustom("查询订单信息异常:" + orderNo);
        }

        ReentrantLock lock = null;
        try {
            lock = orderWorkflowLockAbility.acquireLock(orderNo, "JdFreshFoodSplitOrderListener.onMessage");
        } catch (AcquireLockException e) {
            log.warn("前置仓订单:{}, 京东买菜拆单结果获取锁失败！", orderNo);
            throw e;
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

        try {
            //更新为已拆单完成
            model.getFulfillmentCommand().assignInstantOrderSplit(Boolean.TRUE);
            outboundModelRepository.persist(model);
        } catch (Exception e) {
            log.error("京东买菜拆单结果处理，更新拆单标识异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.Business.MODIFY_FAILED, e).withCustom("京东买菜拆单结果处理，更新拆单标识异常:" + orderNo);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

        OrderHoldRecord orderHoldRecord = orderHoldAcl.queryOneHoldRecord(QueryRequest.builder()
                .orderNo(orderNo)
                .code(PauseReasonEnum.WAIT_INSTANT_SPLIT_RESULT_PAUSE_EXCEPTION.code())
                .build());
        if (orderHoldRecord == null) {
            log.info("未查询到同城速配订单等待拆单结果暂停信息，不执行恢复流程，orderNo:{}", orderNo);
            return;
        }

        final ResumeRequest resumeRequest = ResumeRequest.builder()
                .orderNo(orderNo)
                .operator("JdFreshFoodSplitOrderListener")
                .code(PauseReasonEnum.WAIT_INSTANT_SPLIT_RESULT_PAUSE_EXCEPTION.code())
                .build();
        orderHoldAcl.orderHoldResume(resumeRequest);
    }


    private QueryOrderRelationRequest buildRequest(JdFreshFoodSplitOrderMessage jdFreshFoodSplitOrderMessage) {
        QueryOrderRelationRequest queryOrderRelationRequest = new QueryOrderRelationRequest();
        queryOrderRelationRequest.setSource(buildSource(jdFreshFoodSplitOrderMessage));
        queryOrderRelationRequest.setTarget(buildTarget());
        queryOrderRelationRequest.setAttributes(buildAttrs(jdFreshFoodSplitOrderMessage));
        return queryOrderRelationRequest;
    }

    private OrderElement buildSource(JdFreshFoodSplitOrderMessage jdFreshFoodSplitOrderMessage) {
        OrderElement source = new OrderElement();
        source.setBusinessIdentity(buildBusinessIdentity(BUSINESS_IDENTITY_SUPPLY_CHAIN, BUSINESS_TYPE_OUTBOUND_UNION, null));
        source.setOrderNo(jdFreshFoodSplitOrderMessage.getOrderId());
        return source;
    }

    private OrderElement buildTarget() {
        OrderElement target = new OrderElement();
        target.setBusinessIdentity(buildBusinessIdentity(BUSINESS_IDENTITY_SC_ISV, BUSINESS_TYPE_OUTBOUND_SALE, null));
        return target;
    }

    private BusinessIdentity buildBusinessIdentity(String businessUnit, String businessType, String businessScene) {
        return new BusinessIdentity(businessUnit, businessType, businessScene);
    }

    private Map<String, Object> buildAttrs(JdFreshFoodSplitOrderMessage jdFreshFoodSplitOrderMessage) {
        Map<String, Object> attributes = new HashMap<>(8);
        attributes.put(RelationFieldKeyEnum.ACCOUNT_NO.getCode(), jdFreshFoodSplitOrderMessage.getDeptNo());
        attributes.put(RelationFieldKeyEnum.BUSINESS_TYPE.getCode(), BUSINESS_TYPE_OUTBOUND_SALE);
        attributes.put(RelationFieldKeyEnum.ORDER_TYPE.getCode(), ORDER_TYPE);
        attributes.put(RelationFieldKeyEnum.SYSTEM_CALLER.getCode(), OmsConstant.SYSTEM_CALLER);
        return attributes;
    }
}
