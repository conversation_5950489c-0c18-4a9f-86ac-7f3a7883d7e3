package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: tangjinlei
 * @version: v1.0.0
 * @date: 2024-03-01
 * @description
 **/
@Component
public class ColdChainKaOrderCenterListener extends AbstractSaleOutboundOrderListener{

    public ColdChainKaOrderCenterListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                          ReceiveOrderTranslator receiveOrderTranslator,
                                          @Value("${ofw.standard.businessType}")String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }
}
