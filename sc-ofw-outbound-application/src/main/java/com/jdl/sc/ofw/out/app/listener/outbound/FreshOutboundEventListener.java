package com.jdl.sc.ofw.out.app.listener.outbound;

import cn.jdl.oms.search.dto.Order;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.OutboundEvent;
import com.jdl.sc.ofw.out.app.listener.outbound.process.OutboundEventProcessor;
import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;

@Slf4j
public class FreshOutboundEventListener extends AbstractMessageTemplate {

    @Resource
    private OrderConvertServiceAcl orderConvertServiceAcl;

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private OrderWorkflowLockAbility orderWorkflowLockAbility;

    @Resource
    private OutboundModelService outboundModelService;

    @Resource
    private OrderHoldAcl orderHoldAcl;

    @Resource
    private OutboundEventProcessor outboundEventProcessor;

    @Override
    protected void onMessage(String message) throws Exception {
        if (StringUtils.isBlank(message)) {
            return;
        }

        OutboundEvent outboundEvent = JsonUtil.readValue(message, OutboundEvent.class);
        String orderNo = orderConvertServiceAcl.convert(outboundEvent.getOrderNo());
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        if (!orderCenterAcl.checkOrderExist(orderNo)) {
            return;
        }
        Order order = orderCenterAcl.queryOrderByOrderNo(orderNo, Collections.singletonList("Product"));
        if (order == null) {
            log.error("根据订单号查询订单详情失败，orderNo:{}", orderNo);
            throw new RuntimeException("根据订单号查询订单详情失败");
        }
        if (!isLocalOnDemandDeliveryOrder(order)) {
            return;
        }

        outboundEventProcessor.process(orderNo);

    }

    /**
     * 根据订单中心订单中的产品判断是否同城速配订单
     */
    private boolean isLocalOnDemandDeliveryOrder(Order order) {
        if (CollectionUtils.isEmpty(order.getProductInfos())) {
            return false;
        }
        return order.getProductInfos().stream()
                .anyMatch(productInfo -> ProductEnum.TCSP.getCode().equals(productInfo.getProductNo()));
    }
}