package com.jdl.sc.ofw.out.app.service.oms;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcData;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcResponse;
import cn.jdl.ofc.supplychain.api.oms.CancelOutboundOrderOfcService;
import com.jdl.sc.ofw.out.app.service.constants.BusinessIdentityEnums;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelResponse;
import com.jdl.sc.ofw.outbound.exception.AcquireConcurrentLockException;
import com.jdl.sc.ofw.outbound.exception.RetryException;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("cancelOutboundOrderOfcService")
public class CancelOutboundOrderOfcServiceImpl implements CancelOutboundOrderOfcService {

    @Value("${ofw.standard.businessType}")
    private String businessType;
    @Resource(name = "outboundServiceImpl")
    private OutboundService outboundService;

    @Override
    public CancelOutboundOrderOfcResponse cancelOrder(RequestProfile requestProfile, CancelOutboundOrderOfcRequest cancelOutboundOrderOfcRequest) {
        CancelOutboundOrderOfcResponse cancelOutboundOrderOfcResponse = new CancelOutboundOrderOfcResponse();
        CancelOutboundOrderOfcData cancelOutboundOrderOfcData = new CancelOutboundOrderOfcData();
        cancelOutboundOrderOfcData.setExtendProps(cancelOutboundOrderOfcRequest.getExtendProps());
        cancelOutboundOrderOfcResponse.setData(cancelOutboundOrderOfcData);
        if (isVmiOrderCancel(cancelOutboundOrderOfcRequest)) {
            buildVmiCancelResult(cancelOutboundOrderOfcResponse);
            return cancelOutboundOrderOfcResponse;
        }
        try {
            OutboundCancelRequest outboundCancelRequest = buildCancelRequestByOmsMsg(cancelOutboundOrderOfcRequest);
            OutboundCancelResponse response = outboundService.cancel(outboundCancelRequest);
            buildCancelResult(cancelOutboundOrderOfcResponse, response);
        } catch (AcquireConcurrentLockException e) {
            log.info("接收订单中心取消指令异常，获取锁失败：", e);
            //加锁失败，上游需要重试
            buildCancelErrorResult(cancelOutboundOrderOfcResponse, ResponseConstant.DENIED_CODE, e.getMessage());
        } catch (RetryException e) {
            log.info("接收订单中心取消指令异常：", e);
            //上游需要重试
            buildCancelErrorResult(cancelOutboundOrderOfcResponse, ResponseConstant.DENIED_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("接收订单中心取消指令异常：", e);
            buildCancelErrorResult(cancelOutboundOrderOfcResponse, ResponseConstant.FAILED_CODE, e.getMessage());
        }
        return cancelOutboundOrderOfcResponse;
    }

    private void buildVmiCancelResult(CancelOutboundOrderOfcResponse cancelOutboundOrderOfcResponse) {
        CancelOutboundOrderOfcData cancelOutboundOrderOfcData = cancelOutboundOrderOfcResponse.getData();
        cancelOutboundOrderOfcResponse.setCode("1");
        cancelOutboundOrderOfcResponse.setMessage("成功");
        cancelOutboundOrderOfcData.setCancelResult(1);
        cancelOutboundOrderOfcData.setCancelMessage("可以取消");
    }

    private boolean isVmiOrderCancel(CancelOutboundOrderOfcRequest cancelOutboundOrderOfcRequest) {
        return cancelOutboundOrderOfcRequest.getBusinessIdentity() != null &&
                BusinessIdentityEnums.VMI_ORDER.getCode().equals(cancelOutboundOrderOfcRequest.getBusinessIdentity().getBusinessUnit());
    }

    private OutboundCancelRequest buildCancelRequestByOmsMsg(CancelOutboundOrderOfcRequest cancelOutboundOrderOfcRequest) {
        return OutboundCancelRequest.builder()
                .businessUnit(cancelOutboundOrderOfcRequest.getBusinessIdentity().getBusinessUnit())
                .businessType(businessType)
                .orderNo(cancelOutboundOrderOfcRequest.getOrderNo())
                .childOrderNos(cancelOutboundOrderOfcRequest.getChildrenOrderNos())
                .channelOperateTime(cancelOutboundOrderOfcRequest.getChannelInfo().getChannelOperateTime())
                .channelSource(ChannelSourceEnum.OMS.getCode())
                .operator(cancelOutboundOrderOfcRequest.getOperator())
                .remark(cancelOutboundOrderOfcRequest.getRemark())
                .requestId(cancelOutboundOrderOfcRequest.getRequestId())
                .cancelType(cancelOutboundOrderOfcRequest.getCancelType())
                .cancelMode(cancelOutboundOrderOfcRequest.getCancelMode())
                .extendProps(cancelOutboundOrderOfcRequest.getExtendProps())
                .build();
    }

    private void buildCancelErrorResult(CancelOutboundOrderOfcResponse cancelOutboundOrderOfcResponse, Integer code, String message) {
        CancelOutboundOrderOfcData cancelOutboundOrderOfcData = cancelOutboundOrderOfcResponse.getData();
        cancelOutboundOrderOfcResponse.setCode(String.valueOf(code));
        cancelOutboundOrderOfcResponse.setMessage(message);
        cancelOutboundOrderOfcData.setCancelResult(code);
    }

    private void buildCancelResult(CancelOutboundOrderOfcResponse cancelOutboundOrderOfcResponse, OutboundCancelResponse responseApiResult) {
        CancelOutboundOrderOfcData cancelOutboundOrderOfcData = cancelOutboundOrderOfcResponse.getData();
        cancelOutboundOrderOfcResponse.setCode("1");
        cancelOutboundOrderOfcResponse.setMessage("成功");
        cancelOutboundOrderOfcData.setCancelResult(responseApiResult.getCode());
        cancelOutboundOrderOfcData.setCancelMessage(responseApiResult.getMessage());
    }

}
