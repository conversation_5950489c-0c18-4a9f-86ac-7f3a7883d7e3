package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.core.ump.Ump;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 腰部KA  ISV接单
 */
@Component
public class MediumOrderCenterIsvListener extends AbstractSaleOutboundOrderListener {


    public MediumOrderCenterIsvListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                        ReceiveOrderTranslator receiveOrderTranslator,
                                        @Value("${ofw.standard.businessType}") String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }


    @Ump
    @Override
    public void onMessage(String message) {
        super.onMessage(message);
    }
}
