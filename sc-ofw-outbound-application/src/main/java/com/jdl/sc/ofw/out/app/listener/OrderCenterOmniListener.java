package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 销售出订单->全渠道
 */
@Component
public class OrderCenterOmniListener extends AbstractSaleOutboundOrderListener {

    public OrderCenterOmniListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                   ReceiveOrderTranslator receiveOrderTranslator) {
        super(outboundService, receiveOrderTranslator,null);
    }
}
