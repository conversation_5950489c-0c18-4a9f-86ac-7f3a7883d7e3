package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-08-30
 * @description 大件-销售出-POP
 */
@Component
public class LasOrderCenterPopListener extends AbstractSaleOutboundOrderListener {
    public LasOrderCenterPopListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                           ReceiveOrderTranslator receiveOrderTranslator,
                                           @Value("${ofw.standard.businessType}")String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }
}
