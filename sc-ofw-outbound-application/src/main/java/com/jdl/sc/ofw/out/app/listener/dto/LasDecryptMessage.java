package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

@Getter
@Builder
@Jacksonized
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("all")
public class LasDecryptMessage implements Serializable {

    /**
     * 商家专用的pin
     */
    private String pin;
    /**
     * 销售平台订单号
     */
    private String salesPlatformOrderNo;
    /**
     * 京东运单号
     */
    private String waybillNo;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 寄件人信息
     */
    private PersonInfo senderInfo;
    /**
     * 收件人信息
     */
    private PersonInfo receiverInfo;

    @Getter
    @Builder
    @Jacksonized
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonInfo {
        private String name;
        private String mobile;
        private String phone;
        private String detailAddress;
        private String province;
        private String city;
        private String district;
    }
}
