package com.jdl.sc.ofw.preheat;

import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.preheat.PreheatPlugin;
import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.WmsServiceRpc;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WmsWebServicePreheat extends PreheatPlugin<WmsServiceRpc> {

    public WmsWebServicePreheat() {
        super(WmsServiceRpc.class);
    }

    @Override
    public void execute(WmsServiceRpc bean, int index) {

        try {
            Warehouse warehouse = JsonUtil.readValue(
                    "{\"id\":8066435,\"distributionId\":***********,\"distributionNo\":\"711\",\"orgId\":***********,\"orgNo\":\"958\",\"orgName\":\"温州腾立贸易\",\"distributionName\":\"温州\",\"erpWarehouseNo\":\"153\",\"warehouseNo\":\"118066435\",\"warehouseName\":\"温州电商仓******\",\"status\":2,\"source\":2,\"type\":1,\"sellerName\":\"京东商城\",\"businessModel\":2,\"contacts\":\"张保军\",\"phone\":\"185****6868\",\"provinceId\":15,\"cityId\":1233,\"countyId\":42321,\"townId\":42429,\"province\":\"浙江\",\"city\":\"温州市\",\"county\":\"瑞安市\",\"town\":\"塘下镇\",\"address\":\"浙江省温州市瑞安市塘下镇******\",\"yn\":0,\"coverScope\":\"1\",\"wareType\":\"65\",\"region\":\"华东\",\"airMark\":1,\"storeType\":0,\"storeBusinessModel\":0,\"useCache\":true,\"storeSystem\":0,\"wareProductCategory\":0,\"wareProvinceId\":15,\"wareCityId\":1233,\"wareCountyId\":42321,\"wareTownId\":42429,\"wareProvinceName\":\"浙江\",\"wareCityName\":\"温州市\",\"wareCountyName\":\"瑞安市\",\"wareTownName\":\"塘下镇\",\"businessType\":1,\"runningStatus\":0,\"warehouseDeliveryCollect\":1,\"belongProvince\":\"330000\",\"cloudDeliveryAgency\":\"2000002886\"}",
                    Warehouse.class
            );
            bean.cancel(warehouse, "ESL00000021765400592", true);
        } catch (Exception e) {
            log.warn("Wms取消预热失败", e);
        }
    }

    @Override
    public String name() {
        return "WMS Webservice 预热";
    }
}
