package com.jdl.sc.ofw.out.app.listener.dto;

import com.jdl.sc.ofw.outbound.spec.dto.OrderStatusCallback;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

@Builder
@Jacksonized
@Getter
public class SyncPreSortInfoDto implements Serializable {

    private String orderNo;

    private String waybillNo;

    private OrderStatusCallback.PresortInfo presortInfo;

}
