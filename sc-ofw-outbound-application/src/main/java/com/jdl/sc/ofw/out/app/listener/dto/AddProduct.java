package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

@Getter
@Builder
@Jacksonized
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("all")
public class AddProduct implements Serializable {


    /**
     * 产品编码
     */
    private String productCode;
}

