package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-02-21
 * @description 前置仓-销售出-Isv
 */
@Component
public class InstantOrderCenterIsvListener extends AbstractSaleOutboundOrderListener {
    public InstantOrderCenterIsvListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                         ReceiveOrderTranslator receiveOrderTranslator,
                                         @Value("${ofw.standard.businessType}")String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }
}
