package com.jdl.sc.ofw.out.app.listener.service.impl;

import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.common.message.Message;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.SyncPreSortInfoDto;
import com.jdl.sc.ofw.out.app.listener.service.SyncPresortInfoAcl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@Profile({"!ab"})
public class SyncPresortInfoRpc implements SyncPresortInfoAcl {
    @Resource(name = "producer4")
    private MessageProducer producer4;

    @Value("${jmq4.producer.presort.info.sync.topic:}")
    private String syncPresortInfoTopic;

    @Override
    public void sendPresortInfo(SyncPreSortInfoDto syncPreSortInfoDto) {
        try {
            Message messagePresort = new Message(syncPresortInfoTopic, JsonUtil.toJsonSafe(syncPreSortInfoDto), syncPreSortInfoDto.getWaybillNo());
            producer4.send(messagePresort);
            if (log.isInfoEnabled()) {
                log.info("同步预分拣信息 发送成功 syncPreSortInfoDto:{}", JsonUtil.toJsonSafe(syncPreSortInfoDto));
            }
        } catch (Exception e) {
            log.error("同步预分拣信息 发送失败 syncPreSortInfoDto:{}", JsonUtil.toJsonSafe(syncPreSortInfoDto), e);
        }
    }

}
