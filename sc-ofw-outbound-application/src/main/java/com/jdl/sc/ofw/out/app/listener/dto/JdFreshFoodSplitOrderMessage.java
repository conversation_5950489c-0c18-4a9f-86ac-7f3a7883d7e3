package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Builder
@Getter
@Jacksonized
public class JdFreshFoodSplitOrderMessage {

    /** 运单号 */
    private String deliveryOrderId;

    /** 积理单号 */
    private String orderId;

    /** 京东买菜订单发生了缺件调整 59 */
    private Integer saleType;

    /** 积理门店id */
    private Long storeId;

    /** 事业部 */
    private String deptNo;

}
