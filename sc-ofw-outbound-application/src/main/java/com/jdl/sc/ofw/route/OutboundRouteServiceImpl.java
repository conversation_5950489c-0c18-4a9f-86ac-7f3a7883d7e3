package com.jdl.sc.ofw.route;

import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.api.route.OutboundRouteService;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyWaybillRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundRetransportRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundRetransportResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@AllArgsConstructor
public class OutboundRouteServiceImpl implements OutboundRouteService {

    @Resource(name = "consumerGroupOutboundService")
    private OutboundService outboundService;

    @Resource(name = "consumerGroupRetransOutboundService")
    private OutboundService retransOutboundService;

    @Override
    public OutboundModifyResponse modify(OutboundModifyRequest request) {
        return outboundService.modify(request);
    }

    @Override
    public OutboundCancelResponse cancel(OutboundCancelRequest request) {
        return outboundService.cancel(request);
    }

    @Override
    public OutboundRetransportResponse retransport(OutboundRetransportRequest request) {
        return retransOutboundService.retransport(request);
    }

    @Override
    public void modifyWaybill(OutboundModifyWaybillRequest request) {
        outboundService.modifyWaybill(request);
    }

}
