package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.relation.dict.RelationFieldKeyEnum;
import cn.jdl.oms.relation.model.OrderElement;
import cn.jdl.oms.relation.model.QueryOrderRelationRequest;
import cn.jdl.oms.supplychain.model.CancelCallBackOutboundOrderRequest;
import com.google.common.collect.Maps;
import com.jd.ldop.alpha.waybill.api.dto.request.WaybillInterceptQueryDTO;
import com.jd.ldop.alpha.waybill.api.dto.response.WaybillInterceptResponse;
import com.jdl.sc.core.cache.jimdb.JimClient;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.AlphaWaybillInterceptResult;
import com.jdl.sc.ofw.out.domain.cache.KeyGenerator;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractFilterEnvMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import com.jdl.sc.ofw.out.horiz.infra.rpc.WaybillInterceptAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.enums.AlphaWaybillInterceptStateEnum;
import com.jdl.sc.ofw.out.horiz.infra.util.BusinessIdentityUtil;
import com.jdl.sc.ofw.outbound.spec.enums.CancelResultStateEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jdl.sc.ofw.out.horiz.infra.util.BusinessIdentityUtil.OmsBusinessEnum.SUPPLY_CHAIN_UNION_QUERY;

/**
 * <a href="https://cf.jd.com/pages/viewpage.action?pageId=459736050">三方拦截接口和异步通知</a>
 * topic:ldop_waybill_intercept_reslut
 */
@Slf4j
public class AlphaWaybillInterceptResultListener extends AbstractFilterEnvMessageTemplate {

    public AlphaWaybillInterceptResultListener(@Qualifier("eclpBdPredicate") MessageReceive messageReceive) {
        super(messageReceive);
    }

    private static final String ALPHA_SOURCE = "supplyChainOFC";

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private JimClient jimClient;

    @Resource
    private WaybillInterceptAcl waybillInterceptAcl;

    @Override
    protected void onMessage(String message) throws Exception {
        AlphaWaybillInterceptResult alphaWaybillInterceptResult;
        try {
            alphaWaybillInterceptResult = JsonUtil.readValue(message, AlphaWaybillInterceptResult.class);
        } catch (Exception e) {
            log.error("三方配-alpha拦截结果回传, 报文解析序列化失败", e);
            throw e;
        }
        if (!this.validate(alphaWaybillInterceptResult)) {
            return;
        }
        final String orderNo = orderCenterAcl.getOrderNoByThirdWaybillNo(alphaWaybillInterceptResult.getWaybillCode());
        final Set<String> waybillNoSet = getWaybillNoSet(orderNo);
        final List<Integer> stateList = getInterceptStateList(waybillNoSet, alphaWaybillInterceptResult);
        final boolean anyIntercepting = stateList.stream()
                .anyMatch(state -> AlphaWaybillInterceptStateEnum.INTERCEPTTING.getCode() == state);
        //存在拦截中的运单
        if (anyIntercepting) {
            log.info("alpha拦截结果回传，反向查询的运单结果，存在拦截中的运单，忽略本次回传");
            return;
        }
        Integer interceptResult = calInterceptResult(waybillNoSet, stateList);
        orderCenterAcl.cancelCallback(this.buildCancelCallBackOutboundOrderRequest(interceptResult, orderNo));
    }

    private List<Integer> getInterceptStateList(Set<String> waybillNoSet, AlphaWaybillInterceptResult alphaWaybillInterceptResult) {
        final List<WaybillInterceptQueryDTO> queryDTOList = waybillNoSet.stream()
                .map(waybillNo -> {
                    WaybillInterceptQueryDTO interceptQueryDTO = new WaybillInterceptQueryDTO();
                    interceptQueryDTO.setProviderCode(alphaWaybillInterceptResult.getProviderCode());
                    interceptQueryDTO.setProviderId(alphaWaybillInterceptResult.getProviderId());
                    interceptQueryDTO.setWaybillCode(waybillNo);
                    return interceptQueryDTO;
                }).collect(Collectors.toList());

        final List<WaybillInterceptResponse> responseList = waybillInterceptAcl.batchQueryWaybillIntercept(queryDTOList);
        final List<Integer> stateList = responseList.stream()
                .filter(Objects::nonNull)
                .map(WaybillInterceptResponse::getState)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(stateList)) {
            log.info("alpha拦截结果回传 反向查询的运单结果 无任何状态结果数据， waybillNoSet:{}, responseList:{}",
                    JsonUtil.toJsonSafe(waybillNoSet), JsonUtil.toJsonSafe(responseList));
            throw new RuntimeException("alpha拦截结果回传 反向查询的运单结果 无任何状态结果数据");
        }
        return stateList;
    }

    protected CancelCallBackOutboundOrderRequest buildCancelCallBackOutboundOrderRequest(Integer interceptResult, String orderNo) {
        CancelCallBackOutboundOrderRequest request = new CancelCallBackOutboundOrderRequest();
        request.setBusinessIdentity(BusinessIdentityUtil.createIdentity(BusinessIdentityUtil.OmsBusinessEnum.SALE_ORDER));
        request.setOrderNo(orderNo);
        request.setRequestId(String.valueOf(System.nanoTime()));
        request.setOperator("SupplyOFC");
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(new Date());
        channelInfo.setSystemCaller(ChannelSourceEnum.TMS.getOmsCode());
        request.setChannelInfo(channelInfo);
        request.setCancelResult(interceptResult);
        return request;
    }

    protected Integer calInterceptResult(Set<String> waybillNoSet, List<Integer> stateList) {

        //是否所有的运单都发起过拦截
        boolean allIntercepted = waybillNoSet.size() == stateList.size();
        //都拦截成功
        final boolean allSuccessMatch = stateList.stream()
                .allMatch(state -> AlphaWaybillInterceptStateEnum.INTERCEPT_SUCCESS.getCode() == state);
        //都拦截失败
        final boolean allFailMatch = stateList.stream()
                .allMatch(state -> AlphaWaybillInterceptStateEnum.INTERCEPT_FAIL.getCode() == state
                        || AlphaWaybillInterceptStateEnum.INTERCEPT_TIMEOUT.getCode() == state);
        if (allIntercepted) {
            if (allSuccessMatch) {
                log.info("全部运单都发起了拦截。拦截全部成功。状态：拦截成功");
                return CancelResultStateEnum.INTERCEPT_SUCCESS.getCode();
            } else if (allFailMatch) {
                log.info("全部运单都发起了拦截，拦截全部失败。状态：拦截失败");
                return CancelResultStateEnum.INTERCEPT_FAIL.getCode();
            } else {
                log.info("全部运单都发起了拦截，拦截不分失败。状态：部分拦截");
                return CancelResultStateEnum.PART_INTERCEPT.getCode();
            }
        } else {
            if (allSuccessMatch) {
                log.info("部分运单发起了拦截（未发起拦截的运单，认为是已取消成功），拦截全部成功。状态：拦截成功");
                return CancelResultStateEnum.INTERCEPT_SUCCESS.getCode();
            } else {
                log.info("部分运单发起了拦截（未发起拦截的运单，认为是已取消成功），拦截非全部成功。状态：部分拦截");
                return CancelResultStateEnum.PART_INTERCEPT.getCode();
            }
        }

    }

    private Set<String> getWaybillNoSet(String orderNo) {
        final String waybillNoInterceptViaAlpha = jimClient.get(KeyGenerator.createWaybillNoInterceptViaAlphaCacheKey(orderNo));
        if (StringUtils.isBlank(waybillNoInterceptViaAlpha)) {
            log.info("三方配-alpha拦截结果回传，根据订单号未查询到包裹信息orderNo:{}", orderNo);
            throw new RuntimeException("三方配-alpha拦截结果回传，根据订单号未查询到包裹信息");
        }
        return Arrays.stream(waybillNoInterceptViaAlpha.split(","))
                .collect(Collectors.toSet());
    }

    private QueryOrderRelationRequest buildRequest(String waybillCode) {
        QueryOrderRelationRequest queryOrderRelationRequest = new QueryOrderRelationRequest();
        queryOrderRelationRequest.setSource(buildSource(waybillCode));
        queryOrderRelationRequest.setAttributes(buildAttrs());
        return queryOrderRelationRequest;
    }

    private OrderElement buildSource(String waybillCode) {
        OrderElement source = new OrderElement();
        source.setBusinessIdentity(BusinessIdentityUtil.createIdentity(SUPPLY_CHAIN_UNION_QUERY));
        source.setOrderNo(waybillCode);
        return source;
    }

    private Map<String, Object> buildAttrs() {
        Map<String, Object> attributes = Maps.newHashMapWithExpectedSize(3);
        attributes.put(RelationFieldKeyEnum.SYSTEM_CALLER.getCode(), "SupplyOFC");
        attributes.put(RelationFieldKeyEnum.BUSINESS_TYPE.getCode(), "outbound_sale");
        //销售出订单类型
        attributes.put(RelationFieldKeyEnum.ORDER_TYPE.getCode(), "100");
        return attributes;
    }

    private boolean validate(AlphaWaybillInterceptResult interceptResult) {

        final String source = interceptResult.getSource();
        if (StringUtils.isBlank(source) || !ALPHA_SOURCE.equals(source)) {
            log.info("alpha拦截结果回传 拦截来源为空或非供应链OFC 忽略, source:{}", source);
            return false;
        }
        if (StringUtils.isBlank(interceptResult.getWaybillCode())) {
            log.info("alpha拦截结果回传 运单号为空 忽略");
            return false;
        }
        if (Objects.isNull(interceptResult.getState())) {
            log.info("alpha拦截结果回传 状态为空 忽略");
            return false;
        }
        if (Objects.isNull(interceptResult.getProviderId()) && StringUtils.isBlank(interceptResult.getProviderCode())) {
            log.info("alpha拦截结果回传 承运商ID和承运商编码都为空 忽略");
            return false;
        }
        return true;
    }
}
