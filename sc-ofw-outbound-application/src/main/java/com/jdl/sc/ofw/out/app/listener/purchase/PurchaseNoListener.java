
package com.jdl.sc.ofw.out.app.listener.purchase;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.relation.dict.RelationFieldKeyEnum;
import cn.jdl.oms.relation.model.OrderElement;
import cn.jdl.oms.relation.model.QueryOrderRelationRequest;
import cn.jdl.oms.search.dto.Order;
import com.google.common.collect.Lists;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.PurchaseReturnDto;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.pojo.OrderPausePojo;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractFilterEnvMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ECLPPoServiceAcl;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class PurchaseNoListener extends AbstractFilterEnvMessageTemplate {

    private static final String SOURCE_BUSINESS_UNIT = "cn_jdl_supplychain";
    private static final String SOURCE_BUSINESS_TYPE = "outbound_union";
    private static final String ATTR_BUSINESS_TYPE = "outbound_sale";
    private static final String ATTR_ORDER_TYPE = "100";
    @Resource(name = "consumerGroupOutboundService")
    private OutboundService outboundService;
    @Resource
    private OrderCenterAcl orderCenterAcl;
    @Resource
    private ECLPPoServiceAcl eclpPoServiceAcl;
    @Resource
    private OrderHoldHandler orderHoldHandler;

    /**
     * 只消费新建状态
     */
    private static final Integer STATUS_CREATE = 10;

    public PurchaseNoListener(@Qualifier("eclpBdPredicate") MessageReceive messageReceive) {
        super(messageReceive);
    }

    @Override
    protected void onMessage(String message) throws Exception {
        PurchaseReturnDto purchaseReturnDto = this.parseMessage(message);
        if (purchaseReturnDto == null) {
            return;
        }
        if (StringUtils.isBlank(purchaseReturnDto.getReferenceOrderNo())) {
            log.info("采购单消息不含referenceOrderNo，丢弃消息，po:{}", purchaseReturnDto.getPoNo());
            return;
        }
        if (!STATUS_CREATE.equals(purchaseReturnDto.getPoStatus())) {
            log.info("采购单消息状态不是新建，丢弃消息，po:{}", purchaseReturnDto.getPoNo());
            return;
        }
        //只返回单号
        List<Order> orders = orderCenterAcl.queryOrderRelationIgnoreEmptyData(this.buildRequest(purchaseReturnDto));
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Order orderNotCancelEd = null;
        //循环查询订单，取第一个未取消的订单
        for (Order tempOrder: orders) {
            Order order = orderCenterAcl.queryOrderByOrderNo(tempOrder.getBaseInfo().getOrderNo(), Lists.newArrayList("Base"));
            if (order != null && !OrderStatusEnum.CANCELLED.getOmsCode().equals(order.getBaseInfo().getOrderStatus())) {
                orderNotCancelEd = order;
                break;
            }
        }
        if (orderNotCancelEd == null) {
            return;
        }
        String orderNo = orderNotCancelEd.getBaseInfo().getOrderNo();

        OrderPausePojo orderPausePojo = orderHoldHandler.queryOne(orderNo, PauseReasonEnum.TRANSFER_WAIT_EPL, PauseStatusEnum.INITIALIZATION);
        if (orderPausePojo == null) {
            log.info("未查询到等待采购单暂停信息，不执行恢复流程，orderNo:{}", orderNo);
            return;
        }
        OutboundModifyResponse modify = outboundService.modify(this.getRequest(orderNotCancelEd, purchaseReturnDto));
        if (ResponseConstant.FAILED_CODE.equals(modify.getCode())) {
            log.error("修改订单信息失败,orderNo:{}", orderNotCancelEd.getBaseInfo().getOrderNo());
            throw new RuntimeException("修改订单信息失败");
        }
        orderHoldHandler.resume(orderNo, PauseReasonEnum.TRANSFER_WAIT_EPL.code(), "eclp_po_msg_return");
    }

    private QueryOrderRelationRequest buildRequest(PurchaseReturnDto purchaseReturnDto) {
        QueryOrderRelationRequest queryOrderRelationRequest = new QueryOrderRelationRequest();
        queryOrderRelationRequest.setSource(buildSource(purchaseReturnDto));
        queryOrderRelationRequest.setAttributes(buildAttrs(purchaseReturnDto.getDeptNo()));
        return queryOrderRelationRequest;
    }

    private OrderElement buildSource(PurchaseReturnDto purchaseReturnDto) {
        OrderElement source = new OrderElement();
        source.setBusinessIdentity(new BusinessIdentity(SOURCE_BUSINESS_UNIT, SOURCE_BUSINESS_TYPE, null));
        source.setOrderNo(purchaseReturnDto.getReferenceOrderNo());
        return source;
    }

    private Map<String, Object> buildAttrs(String deptNo) {
        Map<String, Object> attributes = new HashMap<>(8);
        attributes.put(RelationFieldKeyEnum.ACCOUNT_NO.getCode(), deptNo);
        attributes.put(RelationFieldKeyEnum.BUSINESS_TYPE.getCode(), ATTR_BUSINESS_TYPE);
        attributes.put(RelationFieldKeyEnum.ORDER_TYPE.getCode(), ATTR_ORDER_TYPE);
        return attributes;
    }

    private PurchaseReturnDto parseMessage(String message) {
        try {
            return JsonUtil.parse(message, PurchaseReturnDto.class);
        } catch (Exception e) {
            log.error("接收调度信息返序列化失败", e);
            throw e;
        }
    }

    private OutboundModifyRequest getRequest(Order orderInfo, PurchaseReturnDto purchaseReturnDto) {
        return OutboundModifyRequest.builder()
                .orderNo(orderInfo.getBaseInfo().getOrderNo())
                .operateTime(new Date())
                .operator("eclp_po_msg_return")
                .purchaseOrderNo(purchaseReturnDto.getPoNo())
                .build();
    }
}
