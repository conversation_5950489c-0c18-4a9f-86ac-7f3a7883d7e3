package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.util.List;

@Getter
@Builder
@Jacksonized
@NoArgsConstructor
@AllArgsConstructor
public class CustomExtend implements Serializable {
    private String key;
    private List<String> value;
}
