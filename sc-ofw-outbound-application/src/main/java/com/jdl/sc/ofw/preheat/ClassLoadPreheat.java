package com.jdl.sc.ofw.preheat;

import com.jdl.sc.core.preheat.PreheatPlugin;
import com.jdl.sc.ofw.out.horiz.infra.rpc.dto.wms.ReceiptTrack;
import com.jdl.sc.ofw.out.horiz.infra.util.WmsXStreamUtil;

/**
 * FileName: ClassLoadPreheat
 * Description: 类加载预热
 * [@author]:   quhua<PERSON>
 * [@date]:     2025/3/2616:45
 */
public class ClassLoadPreheat extends PreheatPlugin<Object> {


    public ClassLoadPreheat() {
        super("");
    }

    @Override
    public void execute(Object bean, int index) {
        WmsXStreamUtil.toXML(new ReceiptTrack());
    }

    @Override
    public String name() {
        return "类加载预热";
    }
}
