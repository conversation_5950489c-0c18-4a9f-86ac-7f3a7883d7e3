package com.jdl.sc.ofw.out.app.service.oms;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.InterceptOutboundOrderOfcData;
import cn.jdl.ofc.api.oms.common.model.InterceptOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.InterceptOutboundOrderOfcResponse;
import cn.jdl.ofc.supplychain.api.oms.InterceptOutboundOrderOfcService;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundInterceptRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundInterceptResponse;
import com.jdl.sc.ofw.outbound.spec.enums.InterceptStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("interceptOutboundOrderOfcService")
public class InterceptOutboundOrderOfcServiceImpl implements InterceptOutboundOrderOfcService {

    @Resource(name = "outboundServiceImpl")
    private OutboundService outboundService;

    @Resource
    private OrderHoldHandler orderHoldHandler;

    @Override
    public InterceptOutboundOrderOfcResponse interceptOrder(RequestProfile requestProfile, InterceptOutboundOrderOfcRequest request) {
        InterceptOutboundOrderOfcResponse response = new InterceptOutboundOrderOfcResponse();
        try {
            OutboundInterceptRequest outboundInterceptRequest = null;
            try {
                outboundInterceptRequest = buildInterceptRequestByOmsMsg(request);
            } catch (Exception e) {
                log.error("request参数转化异常", e);
                throw new RuntimeException(e);
            }

            if (log.isInfoEnabled()) {
                log.info("接收订单中心拦截指令 执行拦截处理逻辑，入参：{}", JsonUtil.toJsonSafe(outboundInterceptRequest));
            }
            OutboundInterceptResponse result = outboundService.intercept(outboundInterceptRequest);
            if (log.isInfoEnabled()) {
                log.info("接收订单中心拦截指令 结束，结果：{}", JsonUtil.toJsonSafe(result));
            }

            if(InterceptStateEnum.PASS.getCode() == result.getCode()){
                try {
                    orderHoldHandler.restart(request.getOrderNo(), request.getOperator());
                } catch (Exception e) {
                    throw new RuntimeException("流程重启失败", e);
                }
            }

            InterceptOutboundOrderOfcData interceptOutboundOrderOfcData = new InterceptOutboundOrderOfcData();
            interceptOutboundOrderOfcData.setInterceptResult(result.getCode());
            response.setData(interceptOutboundOrderOfcData);
            response.setCode(String.valueOf(ResponseConstant.SUCCESS_CODE));

        } catch (Exception e) {
            log.error("接收订单中心拦截指令异常：", e);
            buildErrorResult(response, ResponseConstant.FAILED_CODE, e.getMessage());
        }

        return response;
    }

    private void buildErrorResult(InterceptOutboundOrderOfcResponse response, Integer code, String message) {
        response.setCode(String.valueOf(code));
        response.setMessage(message);
    }

    private OutboundInterceptRequest buildInterceptRequestByOmsMsg(InterceptOutboundOrderOfcRequest request) {
        return OutboundInterceptRequest.builder()
                .orderNo(request.getOrderNo())
                .operationType(request.getOperationType())
                .operateTime(request.getChannelInfo().getChannelOperateTime())
                .operator(request.getOperator())
                .build();
    }

}
