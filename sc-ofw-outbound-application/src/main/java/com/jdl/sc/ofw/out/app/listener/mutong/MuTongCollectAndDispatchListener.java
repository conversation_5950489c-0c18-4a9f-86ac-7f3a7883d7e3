package com.jdl.sc.ofw.out.app.listener.mutong;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.core.utils.StringUtils;
import com.jdl.sc.ofw.out.app.listener.dto.mutong.MuTongCollectAndDispatchResult;
import com.jdl.sc.ofw.out.app.listener.dto.mutong.MuTongDispatchStatus;
import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.cache.CacheKeyEnum;
import com.jdl.sc.ofw.out.domain.dto.ModifyCommand;
import com.jdl.sc.ofw.out.domain.dto.ModifyStatusCommand;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.pojo.OrderPausePojo;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

import static com.jdl.sc.ofw.out.horiz.infra.facade.mutong.MuTongFaced.buildCallbackRequest;

@Slf4j
@Component
public class MuTongCollectAndDispatchListener extends AbstractMessageTemplate {
    @Resource
    private OutboundModelService outboundModelService;

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private OrderHoldHandler orderHoldHandler;

    @Resource
    private OrderConvertServiceAcl orderConvertServiceAcl;

    @Resource
    private OrderCenterAcl orderCenterAcl;
    @Resource
    private OrderWorkflowLockAbility orderWorkflowLockAbility;
    private static final String OPERATOR = "send_ofc_schedule_result";

    @Override
    protected void onMessage(String message) throws Exception {
        MuTongCollectAndDispatchResult result;
        try {
            result = JsonUtil.parse(message, MuTongCollectAndDispatchResult.class);
        } catch (Exception e) {
            log.error("牧童系统集单调度结果监听，报文解析失败", e);
            throw e;
        }
        if (this.validateFalse(result)) {
            log.error("牧童系统集单调度结果监听，报文不合法,msg:{}", message);
            throw new RuntimeException();
        }
        String orderNo = orderConvertServiceAcl.convert(result.getOrderNo());
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        if (!orderCenterAcl.checkOrderExist(orderNo)) {
            return;
        }
        ReentrantLock lock;
        try {
            lock = orderWorkflowLockAbility.acquireUniqueLock(orderNo, CacheKeyEnum.MU_TONG_LISTENER_ORDER_REENTRANT_LOCK, "MuTongCollectAndDispatchListener.onMessage");
        } catch (AcquireLockException e) {
            log.warn("牧童回传状态消息处理, 获取锁失败！,orderNo:{}", orderNo);
            throw e;
        }
        try {
            //集单完成后，开始调度，牧童返回运输调度中消息，更新状态
            if (ObjectUtils.nullSafeEquals(MuTongDispatchStatus.TRANS_SCHEDULING.getName(), result.getDispatchStatus())) {
                // 查询订单履约信息
                OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);
                //非集单中状态,说明已提前接收到调度完成消息，不处理
                if (!ObjectUtils.nullSafeEquals(model.getOrderStatus(), OrderStatusEnum.COLLECTING)) {
                    return;
                }
                //状态变更
                ModifyStatusCommand modifyCommand = ModifyStatusCommand.builder()
                        .orderNo(orderNo)
                        .operator(OPERATOR)
                        .operateTime(new Date())
                        .status(OrderStatusEnum.TRANS_SCHEDULING.getCode())
                        .build();
                outboundModelService.modifyStatus(modifyCommand);
                //回传状态给订单中心
                model.assignOrderStatus(OrderStatusEnum.TRANS_SCHEDULING);
                orderCenterAcl.callBackOrder(buildCallbackRequest(model));
            }
            //调度完成，更新调度单号和分网结果，恢复履约流程
            if (ObjectUtils.nullSafeEquals(MuTongDispatchStatus.TRANS_SCHEDULING_COMPLETED.getName(), result.getDispatchStatus())) {
                if (result.getDeliveryNetworkResults() == null || StringUtils.isBlank(result.getDispatchNo())) {
                    log.error("运输调度完成,分网结果或调度单号为空,集单批次号:{}", result.getCollectBatchNo());
                    throw new RuntimeException("运输调度完成,分网结果或调度单号为空");
                }
                OrderPausePojo orderPausePojo = orderHoldHandler.queryOne(orderNo, PauseReasonEnum.MU_TONG_COLLECT_DISPATCH, PauseStatusEnum.INITIALIZATION);
                if (orderPausePojo == null) {
                    log.info("未查询到等待集单调度暂停，不执行恢复流程，orderNo:{}", orderNo);
                    return;
                }
                // 查询订单履约信息
                OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);
                // 修改订单履约信息
                ModifyCommand modifyCommand = buildModifyCommand(model.getOrderNo(), result);
                OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                        .businessIdentity(new BusinessIdentity(
                                model.getBatrixInfo().getBusinessUnit(),
                                model.getBatrixInfo().getBusinessType(),
                                BusinessScene.MODIFY))
                        .command(modifyCommand)
                        .build();
                outboundModelService.modify(modifyCommand, context);
                orderHoldHandler.resume(orderNo, PauseReasonEnum.MU_TONG_COLLECT_DISPATCH.code(), OPERATOR);
            }
        }finally {
            orderWorkflowLockAbility.unlock(lock);
        }
    }

    private boolean validateFalse(MuTongCollectAndDispatchResult parse) {
        return StringUtils.isBlank(parse.getOrderNo()) || StringUtils.isBlank(parse.getDispatchStatus());
    }

    private ModifyCommand buildModifyCommand(String orderNo, MuTongCollectAndDispatchResult parse) {
        return ModifyCommand.builder()
                .orderNo(orderNo)
                .operator(OPERATOR)
                .operateTime(new Date())
                .shipment(Shipment.builder()
                        .dispatchNo(parse.getDispatchNo())
                        .deliveryNetworkResults(parse.getDeliveryNetworkResults())
                        .consolidationPointCode(parse.getConsolidationPointCode())
                        .consolidationPointName(parse.getConsolidationPointName())
                        .build())
                .build();
    }
}
