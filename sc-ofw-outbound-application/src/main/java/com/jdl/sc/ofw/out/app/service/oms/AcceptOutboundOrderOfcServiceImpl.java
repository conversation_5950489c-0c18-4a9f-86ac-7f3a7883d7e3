package com.jdl.sc.ofw.out.app.service.oms;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.AcceptOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.AcceptOutboundOrderOfcResponse;
import cn.jdl.ofc.supplychain.api.oms.AcceptOutboundOrderOfcService;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.SmartPatternInfo;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.dto.vo.Cargo;
import com.jdl.sc.ofw.outbound.dto.vo.Fulfillment;
import com.jdl.sc.ofw.outbound.dto.vo.Warehouse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service("acceptOutboundOrderOfcService")
public class AcceptOutboundOrderOfcServiceImpl implements AcceptOutboundOrderOfcService {

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource(name = "outboundServiceImpl")
    private OutboundService outboundService;

    @Resource
    private ReceiveOrderTranslator receiveOrder;

    /**
     * 更新订单数据，重启履约流程
     * @param requestProfile
     * @param request
     * @return
     */
    @Override
    public AcceptOutboundOrderOfcResponse acceptOrder(RequestProfile requestProfile, AcceptOutboundOrderOfcRequest request) {
        AcceptOutboundOrderOfcResponse response = new AcceptOutboundOrderOfcResponse();
        response.setCode(String.valueOf(ResponseConstant.SUCCESS_CODE));
        try {
            OutboundModifyResponse modifyResponse = outboundService.modify(buildModifyRequestByOfwMsg(request));

            if (!Objects.equals(modifyResponse.getCode(), ResponseConstant.SUCCESS_CODE)) {
                response.setCode(String.valueOf(modifyResponse.getCode()));
                response.setMessage(modifyResponse.getMessage());
                return response;
            }

            workflowControlService.restart(request.getOrderNo());
        } catch (Exception e) {
            log.error("重受理下发处理失败, orderNo:" + request.getOrderNo(), e);
            response.setCode(String.valueOf(ResponseConstant.FAILED_CODE));
            response.setMessage("重受理下发处理失败");
        }
        return response;
    }

    private OutboundModifyRequest buildModifyRequestByOfwMsg(AcceptOutboundOrderOfcRequest omsRequest) {
        return OutboundModifyRequest.builder()
                .orderNo(omsRequest.getOrderNo())
                .operateTime(new Date())
                .operator(omsRequest.getOperator())
                .cargos(buildCargos(omsRequest.getBusinessIdentity(), omsRequest.getCargoInfos()))
                .warehouse(buildWarehouseInfo(omsRequest))
                .fulfillment(buildFulfillment(omsRequest))
                .extendProps(omsRequest.getExtendProps())
                .build();
    }

    private Fulfillment buildFulfillment(AcceptOutboundOrderOfcRequest omsRequest) {
        Integer crossDockType = Optional.ofNullable(omsRequest.getSmartPatternInfo())
                .map(SmartPatternInfo::getCrossDockRule)
                .map(SmartPatternInfo.CrossDockRule::getCrossDockType)
                .orElse(null);
        if (crossDockType == null) {
            return null;
        }
        return Fulfillment.builder().crossDockType(String.valueOf(crossDockType)).build();
    }

    private List<Cargo> buildCargos(BusinessIdentity businessIdentity, List<CargoInfo> cargoInfos) {
        if (CollectionUtils.isEmpty(cargoInfos)) {
            return null;
        }
        boolean isPOP = businessIdentity == null || "cn_jdl_sc-pop".equals(businessIdentity.getBusinessUnit());
        return receiveOrder.getCargosByOmsInput(isPOP, cargoInfos);
    }

    private Warehouse buildWarehouseInfo(AcceptOutboundOrderOfcRequest omsRequest) {
        if (omsRequest.getConsignorInfo() == null || omsRequest.getConsignorInfo().getCustomerWarehouse() == null) {
            return null;
        }
        return Warehouse.builder()
                .warehouseNo(omsRequest.getConsignorInfo().getCustomerWarehouse().getActualWarehouseNo())
                .warehouseName(omsRequest.getConsignorInfo().getCustomerWarehouse().getActualWarehouseName())
                .warehouseType(omsRequest.getConsignorInfo().getCustomerWarehouse().getActualWarehouseType())
                .build();
    }

}
