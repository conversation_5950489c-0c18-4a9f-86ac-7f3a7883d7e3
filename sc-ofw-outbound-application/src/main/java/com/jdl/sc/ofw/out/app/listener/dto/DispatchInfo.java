package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.util.Date;

@Builder
@Getter
@Jacksonized
public class DispatchInfo {
    /**
     * 运输单号
     */
    private String transbillCode;

    /**
     * 派车单号
     */
    private String scheduleBillCode;

    /**
     * 运输单数量
     */
    private Integer scheduleAmount;

    /**
     * 装车顺序
     */
    private String allocateSequence;

    /**
     * 卡尾号
     */
    private String truckSpot;

    /**
     * 销售平台订单号
     */
    private String orderId;

    /**
     * 承运商类型
     */
    private Integer carrierType;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 站点编号
     */
    private Integer preSiteId;

    /**
     * 站点名称
     */
    private String preSiteName;

    /**
     * 运输方式
     */
    private Integer requireTransMode;

    /**
     * 调度（众包）状态说明
     * 0或空-无业务含义
     * 1-不可众包
     * 2-众包已抢单（表示该单子已经被众包平台抢单）
     * 3-众包超时未抢单
     * 4-众包抢单后取消
     * 5-再投入站再调度（无论众包与否，发生再投后，再投入站后触发再调度）
     * 详情见com.jd.eclp.bbp.so.constant.enumImpl.CrowdStatusEnum
     */
    private Integer dispatchStatus;

    /**
     * 订单小号
     */
    private String orderSmallNo;

    /**
     * 配送员姓名
     */
    private String deliveryStaffName;

    /**
     * 配送员联系方式
     */
    private String deliveryStaffMobile;

    /**
     * 公里数
     */
    private Double deliveryKilometers;

    /**
     * 配送点数
     */
    private Integer deliveryPoints;

    /**
     * 路区
     */
    private String road;

    /**
     * 计划始发进场时间
     */
    private Date planBeginEnterTime;

    /**
     * 车牌号
     */
    private String vehicleNumber;

    /**
     * 操作时间
     */
    private String operateTime;
}
