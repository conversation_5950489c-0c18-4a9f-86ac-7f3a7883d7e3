package com.jdl.sc.ofw.out.app.translator;


import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AttributeInfo;
import cn.jdl.oms.core.model.BatchInfo;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.SmartPatternInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.CustomerAddressInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.EcpOrderInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.EncryptInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.ExtendedField;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentFlag;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.Invoice;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.PrintExtendInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.PrintInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.ReReceiveInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.SellerChannelInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.SmartPatternExtendProps;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.StockDetail;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.common.dict.SmileWaybillTypeEnum;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.ParameterInvalidException;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.EclpJimkvLoadServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.facade.enums.CustomFieldEnum;
import com.jdl.sc.ofw.out.horiz.infra.jimkv.DtcJimKvAgent;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.dto.vo.AfterSaleInfo;
import com.jdl.sc.ofw.outbound.dto.vo.BasicInfo;
import com.jdl.sc.ofw.outbound.dto.vo.Cargo;
import com.jdl.sc.ofw.outbound.dto.vo.CargoBatchRangeDetail;
import com.jdl.sc.ofw.outbound.dto.vo.CargoBatchRangeInfo;
import com.jdl.sc.ofw.outbound.dto.vo.CargoServiceInfo;
import com.jdl.sc.ofw.outbound.dto.vo.Channel;
import com.jdl.sc.ofw.outbound.dto.vo.Consignee;
import com.jdl.sc.ofw.outbound.dto.vo.Consignor;
import com.jdl.sc.ofw.outbound.dto.vo.Customer;
import com.jdl.sc.ofw.outbound.dto.vo.Finance;
import com.jdl.sc.ofw.outbound.dto.vo.Fulfillment;
import com.jdl.sc.ofw.outbound.dto.vo.Goods;
import com.jdl.sc.ofw.outbound.dto.vo.GoodsAgentSalesDetail;
import com.jdl.sc.ofw.outbound.dto.vo.JdAddress;
import com.jdl.sc.ofw.outbound.dto.vo.Money;
import com.jdl.sc.ofw.outbound.dto.vo.OrderGiftRelation;
import com.jdl.sc.ofw.outbound.dto.vo.ProcessingInfo;
import com.jdl.sc.ofw.outbound.dto.vo.ProcessingOption;
import com.jdl.sc.ofw.outbound.dto.vo.Product;
import com.jdl.sc.ofw.outbound.dto.vo.Quantity;
import com.jdl.sc.ofw.outbound.dto.vo.RefOrderInfo;
import com.jdl.sc.ofw.outbound.dto.vo.SerialInfo;
import com.jdl.sc.ofw.outbound.dto.vo.Shipment;
import com.jdl.sc.ofw.outbound.dto.vo.Solution;
import com.jdl.sc.ofw.outbound.dto.vo.Warehouse;
import com.jdl.sc.ofw.outbound.spec.enums.BillingTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.CustomerLevelEnum;
import com.jdl.sc.ofw.outbound.spec.enums.DeliveryServiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.GoodsIsolateTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.InvoiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackDetailsCollectEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackageProduceModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PickupTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PrintMedicinePriceBalanceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ProductSplitResultSourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipmentWarehouseTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.SignBillTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WarehouseShipmentModeEnum;
import com.jdl.sc.oss.OSSClient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ReceiveOrderTranslater简介
 *
 * <AUTHOR>
 * @date 2021-04-13 16:32
 */
@Slf4j
@Component
public class ReceiveOrderTranslator {

    /**
     * 商家下发个性化打印信息订单维度key
     */
    public static final String SELLER_INDIVIDUATION_ORDER_INFO_KEY = "orderExtendInfo";
    /**
     * 商家下发个性化打印信息明细维度key
     */
    public static final String SELLER_INDIVIDUATION_SKU_INFO_KEY = "skuPrintInfoList";
    private static final String ECLP_JIMKV_PREFIX = "jfs/kveclp";
    private static final String ORDER_SIGN_SOURCING_SPLIT_KEY = "sourcingSplit";
    private static final String ORDER_SIGN_SOURCING_SPLIT_VALUE = "1";
    private static final String PARENT_CHANNEL_ORDER_NO_KEY = "parentChannelOrderNo";
    private static final String PARENT_CUSTOMER_ORDER_NO_KEY = "parentCustomerOrderNo";
    private static final String CHILD_CHANNEL_ORDER_NOS_KEY = "childChannelOrderNos";
    private static final String CHILD_CUSTOMER_ORDER_NOS_KEY = "childCustomerOrderNos";
    private static final String CHILD_ORDER_NOS_LENGTH_UMP_KEY = "child.ordernos.length.alert";
    private static final String CHILD_ORDER_NOS_SPLIT = ",";
    private static final String ORDER_SIGN_SPLIT_ORDER_KEY = "splitOrder";
    private static final String ORDER_SIGN_SPLIT_ORDER_SUB_VALUE = "2";

    @Resource
    private EclpJimkvLoadServiceAcl eclpJimkvLoadServiceAcl;

    @Resource
    private DtcJimKvAgent dtcJimKvAgent;

    @Resource(name = "omsOssClient")
    private OSSClient omsOssClient;

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    public BasicInfo getBasicInfoByOmsInput(OrderData input) {
        return BasicInfo.builder()
                .agentSales(ExtendPropsEnum.AGENT_SALES.getValue(input.getExtendProps()))
                .preSaleStage(ExtendPropsEnum.PRE_SALE_STAGE.getValue(input.getExtendProps()))
                .build();
    }

    public Customer getCustomerByOmsInput(OrderData input) {
        String customerNo = null;
        if (input.getConsigneeInfo() != null && input.getConsigneeInfo().getExtendProps() != null) {
            customerNo = input.getConsigneeInfo().getExtendProps().get("consigneeSiteNo");
        }
        return Customer.builder()
                .customerNo(customerNo)
                .accountNo(input.getCustomerInfo().getAccountNo())
                .accountName(input.getCustomerInfo().getAccountName())
                .customerLevel(CustomerLevelEnum.fromOmsCode(input.getCustomerInfo().getCustomerLevel()))
                .build();
    }


    public List<Cargo> getCargosByOmsInput(OrderData input) {
        ChannelSourceEnum channelSourceEnum = parseChannelSource(input);
        return getCargosByOmsInput(ChannelSourceEnum.POP.equals(channelSourceEnum), input.getCargoInfos());
    }

    public List<Cargo> getCargosByOmsInput(boolean isPOP, List<CargoInfo> cargoInfos) {
        return cargoInfos.stream()
                .map(e -> convertToCargo(isPOP, e))
                .collect(Collectors.toList());
    }

    /**
     * oms CargoInfo -> ofc cargo
     *
     * @param isPOP 是否属于pop订单
     */
    private Cargo convertToCargo(boolean isPOP, CargoInfo cargoInfo) {
        String insurePriceStr = CargoExtendPropsEnum.INSURE_PRICE.getValue(cargoInfo.getExtendProps());
        BigDecimal insure_price = insurePriceStr == null ? new BigDecimal(0) : new BigDecimal(insurePriceStr);
        String packagingDetailsStr = CargoExtendPropsEnum.PACKAGEING_DETAILS.getValue(cargoInfo.getExtendProps());
        Integer packagingDetails = StringUtils.isBlank(packagingDetailsStr) ? null : Integer.parseInt(packagingDetailsStr);
        Cargo.CargoBuilder cargoBuilder = Cargo.builder()
                .no(cargoInfo.getCargoNo())
                .name(cargoInfo.getCargoName())
                .level(cargoInfo.getCargoLevel())
                .levelName(cargoInfo.getCargoLevelName())
                .occupyQuantity(Quantity.builder()
                        .unit(cargoInfo.getCargoOccupyQuantity().getUnit())
                        .value(cargoInfo.getCargoOccupyQuantity().getValue())
                        .build())
                .quantity(Quantity.builder()
                        .value(cargoInfo.getCargoQuantity().getValue())
                        .unit(cargoInfo.getCargoQuantity().getUnit())
                        .build())
                .isvCargoNo(cargoInfo.getIsvCargoNo())
                .refGoodsNo(isPOP ? cargoInfo.getRefGoodsNo() : cargoInfo.getExtendProps().get("cargoRefNo"))
                .refGoodsType(cargoInfo.getRefGoodsType())
                .uniqueCode(CargoExtendPropsEnum.UNIQUE_CODE.getValue(cargoInfo.getExtendProps()))
                .cargoLineNo(cargoInfo.getCargoLineNo())
                .serialInfos(getSerialInfoList(cargoInfo.getSerialInfos()))
                .batchInfos(buildBatchInfos(cargoInfo.getBatchInfos()))
                .virtualType(isPOP ? null : CargoSignEnum.VIRTUAL_GOODS.getValue(cargoInfo.getCargoSign()))
                .checkUniSnCode(CargoSignEnum.CHECK_UNIT_SN_CODE.getValue(cargoInfo.getCargoSign()))
                .processing(CargoSignEnum.PROCESSING.getValue(cargoInfo.getCargoSign()))
                .printName(CargoSubExtendPropsEnum.PRINT_NAME.getValue(cargoInfo.getExtendProps()))
                .cargoBatchRangeList(parseCargoBatchRange(CargoSubExtendPropsEnum.BATCH_RANGE.getValue(cargoInfo.getExtendProps())))
                .cargoRemark(cargoInfo.getCargoRemark())
                .productList(getProductsByOmsInput(cargoInfo))
                .sellerCustomPictureUrl(CargoExtendPropsEnum.SELLER_CUSTOM_PICTURE_URL.getValue(cargoInfo.getExtendProps()))
                .cargoBrand(CargoExtendPropsEnum.CARGO_BRAND.getValue(cargoInfo.getExtendProps()))
                .brandNo(CargoExtendPropsEnum.BRAND_NO.getValue(cargoInfo.getExtendProps()))
                .insurePrice(insure_price)
                .price(cargoInfo.getCargoPrice() == null ? null : Money.builder()
                        .amount(cargoInfo.getCargoPrice().getAmount())
                        .currencyCode(cargoInfo.getCargoPrice().getCurrencyCode())
                        .build())
                .actualReceivableQuantity(CargoExtendPropsEnum.ACTUAL_RECEIVABLE_QUANTITY.getValue(cargoInfo.getExtendProps()))
                .packagingDetails(packagingDetails)
                .consigneeWarehouseName(CargoExtendPropsEnum.CONSIGNEE_WAREHOUSE_NAME.getValue(cargoInfo.getExtendProps()))
                .consigneeLocName(CargoExtendPropsEnum.CONSIGNEE_LOC_NAME.getValue(cargoInfo.getExtendProps()))
                .cargoCustomField(CargoExtendPropsEnum.CARGO_CUSTOM_FIELD.getValue(cargoInfo.getExtendProps()))
                .sellerGoodsRemark(CargoExtendPropsEnum.SELLER_GOODS_REMARK.getValue(cargoInfo.getExtendProps()))
                .aviationRegCod(CargoExtendPropsEnum.AVIATION_REG_COD.getValue(cargoInfo.getExtendProps()))
                .cargoWeakBatchRangeInfo(CargoSubExtendPropsEnum.WEAK_BATCH_RANGE.getValue(cargoInfo.getExtendProps()))
                .frozenProducts(CargoSignEnum.FROZEN_PRODUCTS.getValue(cargoInfo.getCargoSign()))
                .traceabilityCodeManage(CargoExtendPropsEnum.TRACEABILITY_CODE_MANAGE.getValue(cargoInfo.getExtendProps()))
                .outboundByBoxRule(CargoSignEnum.OUTBOUND_BY_BOX_RULE.getValue(cargoInfo.getCargoSign()))
                .extCollectionUniqCode(CargoExtendPropsEnum.EXT_COLLECTION_UNIQ_CODE.getValue(cargoInfo.getExtendProps()))
                .wholeBoxOut(CargoExtendPropsEnum.WHOLE_BOX_OUT.getValue(cargoInfo.getExtendProps()))
                .standardWeight(CargoExtendPropsEnum.STANDARD_WEIGHT.getValue(cargoInfo.getExtendProps()))
                .productionWay(CargoExtendPropsEnum.PRODUCTION_WAY.getValue(cargoInfo.getExtendProps()))
                .weighPieceworkQuantity(CargoExtendPropsEnum.WEIGH_PIECE_WORK_QUANTITY.getValue(cargoInfo.getExtendProps()))
                .processingServicesType(CargoExtendPropsEnum.PROCESSING_SERVICES_TYPE.getValue(cargoInfo.getExtendProps()))
                .customerProcessingInfo(CargoExtendPropsEnum.CUSTOMER_PROCESSING_INFO.getValue(cargoInfo.getExtendProps()))
                ;

        // 半加工货品-加工信息
        if (cargoInfo.getProcessingInfo() != null) {
            // 方案信息
            List<ProcessingOption> processingOptionList = CollectionUtils.emptyIfNull(cargoInfo.getProcessingInfo().getProcessingOptions())
                    .stream().map(e -> ProcessingOption.builder()
                            .rawMaterialNo(e.getRawMaterialNo())
                            .rawMaterialName(e.getRawMaterialName())
                            .rawMaterialQuantity(Quantity.builder()
                                    .unit(e.getRawMaterialQuantity().getUnit())
                                    .value(e.getRawMaterialQuantity().getValue())
                                    .build())
                            .rawMaterialLevel(e.getRawMaterialLevel())
                            .extendProps(e.getExtendProps())
                            .build())
                    .collect(Collectors.toList());
            // 方案编码
            cargoBuilder.processingInfo(ProcessingInfo.builder()
                    .optionCode(cargoInfo.getProcessingInfo().getOptionCode())
                    .processingOptions(processingOptionList)
                    .build());
        }
        // 半加工货品-原料明细
        if (CollectionUtils.isNotEmpty(cargoInfo.getSubCargoInfos())) {
            cargoBuilder.subCargos(cargoInfo.getSubCargoInfos().stream()
                    .map(subCargoInfo -> Cargo.builder()
                            .no(subCargoInfo.getCargoNo())
                            .level(subCargoInfo.getCargoLevel())
                            .quantity(Quantity.builder()
                                    .value(subCargoInfo.getCargoQuantity().getValue())
                                    .unit(subCargoInfo.getCargoQuantity().getUnit())
                                    .build())
                            .build())
                    .collect(Collectors.toList()));
        }
        // 增值服务
        if (CollectionUtils.isNotEmpty(cargoInfo.getServiceInfos())) {
            cargoBuilder.cargoServiceInfo(cargoInfo.getServiceInfos().stream()
                    .map(serviceInfo -> CargoServiceInfo.builder()
                            .serviceCode(serviceInfo.getServiceCode())
                            .serviceName(serviceInfo.getServiceName())
                            .businessLine(serviceInfo.getBusinessLine())
                            .serviceRequirement(serviceInfo.getServiceRequirement())
                            .remark(serviceInfo.getRemark())
                            .operateType(MapUtils.isEmpty(serviceInfo.getExtendProps()) ? null : serviceInfo.getExtendProps().get("operateType"))
                            .serviceSign(serviceInfo.getServiceSign())
                            .build())
                    .collect(Collectors.toList()));
        }

        return cargoBuilder.build();
    }

    public List<Product> getProductsByOmsInput(CargoInfo cargoInfo) {
        List<Product> productList;
        productList = cargoInfo.getProductInfos().stream().map(e ->
                Product.builder()
                        .no(e.getProductNo())
                        .parentNo(e.getParentNo())
                        .name(e.getProductName())
                        .type(e.getProductType())
                        .attributes(e.getProductAttrs())
                        .build()
        ).collect(Collectors.toList());
        return productList;
    }

    /**
     * 批次范围信息解析
     * example：
     * [[{"batchKey":"production_date_lot","cargoBatchRangeDetailList":[{"batchValue":"2022-07-01","batchOperator":"&lt;="}]},{"batchKey":"production_date_lot",
     * "cargoBatchRangeDetailList":[{"batchValue":"2022-07-31","batchOperator":"&gt;="}]}],
     * [{"batchKey":"production_date_lot2","cargoBatchRangeDetailList":[{"batchValue":"2022-07-01","batchOperator":"&lt;="}]},
     * {"batchKey":"production_date_lot2","cargoBatchRangeDetailList":[{"batchValue":"2022-07-31","batchOperator":"&gt;="}]}]]
     * @param batchRange
     * @return List<List<CargoBatchRangeInfo>>
     */
    private List<List<CargoBatchRangeInfo>> parseCargoBatchRange(String batchRange) {
        if (StringUtils.isEmpty(batchRange)) {
            return null;
        }
        List<List<HashMap<String, Object>>> batchInfoList = null;
        try {
            batchInfoList = ((Map<String, List<List<HashMap<String, Object>>>>) JsonUtil.readValue(batchRange, Map.class)).get("soBatAttrRangeList");
        } catch (Exception e) {
            throw new RuntimeException("batchRange批属性范围json转换失败", e);
        }

        if (CollectionUtils.isEmpty(batchInfoList)) {
            return null;
        }

        List<List<CargoBatchRangeInfo>> cargoBatchRangeList = batchInfoList.stream()
                .map(batchRangeInfoList -> batchRangeInfoList.stream()
                        .map(batchRangeInfo -> {
                            CargoBatchRangeInfo cargoBatchRangeInfo = new CargoBatchRangeInfo();
                            cargoBatchRangeInfo.setBatchKey((String) batchRangeInfo.get("batchKey"));
                            List<HashMap<String, Object>> batchRangeDetailList = (List<HashMap<String, Object>>) batchRangeInfo.get("soBatAttrRangeDetailList");
                            List<CargoBatchRangeDetail> cargoBatchRangeDetailList = batchRangeDetailList.stream()
                                    .map(batchRangeDetail -> {
                                        CargoBatchRangeDetail cargoBatchRangeDetail = new CargoBatchRangeDetail();
                                        cargoBatchRangeDetail.setBatchValue((String) batchRangeDetail.get("batchValue"));
                                        cargoBatchRangeDetail.setBatchOperator((String) batchRangeDetail.get("batchOperator"));
                                        return cargoBatchRangeDetail;
                                    })
                                    .collect(Collectors.toList());
                            cargoBatchRangeInfo.setCargoBatchRangeDetailList(cargoBatchRangeDetailList);
                            return cargoBatchRangeInfo;
                        })
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
        return cargoBatchRangeList;
    }

    public List<SerialInfo> getSerialInfoList(List<cn.jdl.oms.core.model.SerialInfo> serialInfos) {
        if (CollectionUtils.isEmpty(serialInfos)) {
            return Collections.emptyList();
        }
        return serialInfos.stream()
                .map(serialInfo -> SerialInfo.builder()
                        .serialNo(serialInfo.getSerialNo())
                        .serialType(serialInfo.getSerialType())
                        .build()).collect(Collectors.toList());
    }


    public List<Goods> getGoodsByOmsInput(OrderData input) {
        ChannelSourceEnum channelSourceEnum = parseChannelSource(input);
        return getGoodsByOmsInput(ChannelSourceEnum.POP.equals(channelSourceEnum), input.getGoodsInfos());
    }

    public List<Goods> getGoodsByOmsInput(boolean isPOP, List<GoodsInfo> goodsInfos) {
        return goodsInfos.stream()
                .map(e -> convertToGoods(isPOP, e))
                .collect(Collectors.toList());
    }

    private Goods convertToGoods(boolean isPOP, GoodsInfo goodsInfo) {
        return Goods.builder()
                .type(goodsInfo.getGoodsType())
                .no(goodsInfo.getGoodsNo())
                .name(goodsInfo.getGoodsName())
                .price(goodsInfo.getGoodsPrice() == null ? null : Money.builder()
                        .amount(goodsInfo.getGoodsPrice().getAmount())
                        .currencyCode(goodsInfo.getGoodsPrice().getCurrencyCode())
                        .build())
                .amount(goodsInfo.getGoodsAmount() == null ? null : Money.builder()
                        .amount(goodsInfo.getGoodsAmount().getAmount())
                        .currencyCode(goodsInfo.getGoodsAmount().getCurrencyCode())
                        .build())
                .quantity(goodsInfo.getGoodsQuantity() == null ? null : Quantity.builder()
                        .unit(goodsInfo.getGoodsQuantity().getUnit())
                        .value(goodsInfo.getGoodsQuantity().getValue())
                        .build())
                .combinationVersion(goodsInfo.getCombinationGoodsVersion())
                .channelGoodsNo(goodsInfo.getChannelGoodsNo())
                .agentSaleGoodsNo(GoodsExtendPropsEnum.AGENT_SALE_GOODSNO.getValue(goodsInfo.getExtendProps()))
                .refCargoNo(isPOP ? goodsInfo.getGoodsNo() : goodsInfo.getExtendProps().get("goodsRefNo"))
                .goodsAgentSalesDetailList(parseAgentSalesDetail(GoodsExtendPropsEnum.AGENT_SALE_GOODS.getValue(goodsInfo.getExtendProps())))
                .salesAttribute(GoodsExtendPropsEnum.SALES_ATTRIBUTE.getValue(goodsInfo.getExtendProps()))
                .goodsEdiRemark(GoodsExtendPropsEnum.GOODS_EDI_REMARK.getValue(goodsInfo.getExtendProps()))
.build();
    }

    private List<GoodsAgentSalesDetail> parseAgentSalesDetail(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        List<GoodsAgentSalesDetail> agentSalesDetails = null;
        try {
            agentSalesDetails = JsonUtil.readList(value, GoodsAgentSalesDetail.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return agentSalesDetails;
    }

    public List<Product> getProductsByOmsInput(OrderData input) {
        List<Product> productList;
        productList = input.getProductInfos().stream().map(e ->
                Product.builder()
                        .no(e.getProductNo())
                        .parentNo(e.getParentNo())
                        .name(e.getProductName())
                        .type(e.getProductType())
                        .attributes(e.getProductAttrs())
                        .build()
        ).collect(Collectors.toList());
        buildSpecialProduct(productList, input);
        return productList;
    }

    private void buildSpecialProduct(List<Product> productList, OrderData input) {
        String smileType = mapAttrToStringType(input.getShipmentInfo().getServiceRequirements(), "smileType");
        if (StringUtils.isBlank(smileType)) {
            return;
        }
        SmileWaybillTypeEnum smileWaybillTypeEnum = SmileWaybillTypeEnum.fromCode(NumberUtils.toInt(smileType));
        Map<String, String> productAttrMap = new HashMap<>();
        productAttrMap.put(ProductEnum.SMILE_WAYBILL.getProductAttribute(), smileWaybillTypeEnum.toHiddenContent());
        Product smileProduct = Product.builder()
                .no(ProductEnum.SMILE_WAYBILL.getCode())
                .name(ProductEnum.SMILE_WAYBILL.getDesc())
                .attributes(productAttrMap)
                .build();
        productList.add(smileProduct);
    }

    public Solution getSolutionByOmsInput(OrderData input) {
        // 正品鉴定单
        if (input.getBusinessSolutionInfo() == null) {
            return null;
        }
        return Solution.builder()
                .name(input.getBusinessSolutionInfo().getBusinessSolutionName())
                .no(input.getBusinessSolutionInfo().getBusinessSolutionNo())
                .attributes(MapUtils.isEmpty(input.getShipmentInfo().getServiceRequirements())
                        ? input.getBusinessSolutionInfo().getProductAttrs()
                        : input.getShipmentInfo().getServiceRequirements())
                .build();
    }

    public Map<String, String> getNonstandardProductsByOmsInput(OrderData input, FulfillmentInfo fulfillmentInfo, ExtendedField extendedField) {
        Map<String, String> map = new HashMap<>();
        //海运违禁品
        String marineContraband = ShipmentPropsEnum.MARINE_CONTRABAND.getValue(input.getShipmentInfo().getExtendProps());
        if ("2".equals(marineContraband)) {
            map.put(ShipmentPropsEnum.MARINE_CONTRABAND.key, marineContraband);
        }
        //次日达时效提升
        String nextDayAgingPromotion = ShipmentPropsEnum.NEXT_DAY_AGING_PROMOTION.getValue(input.getShipmentInfo().getExtendProps());
        if (StringUtils.isNotBlank(nextDayAgingPromotion)) {
            map.put(ShipmentPropsEnum.NEXT_DAY_AGING_PROMOTION.key, nextDayAgingPromotion);
        }
        //sop自提：0 sop自提柜、7 sop自提店
        String selfPickupType = ShipmentPropsEnum.SELF_PICKUP_TYPE.getValue(input.getShipmentInfo().getExtendProps());
        if (StringUtils.isNotBlank(selfPickupType)) {
            map.put(ShipmentPropsEnum.SELF_PICKUP_TYPE.key, selfPickupType);
        }

        //闪购订单
        String flashSale = ExtendPropsEnum.FLASH_SALE.getValue(input.getExtendProps());
        if (StringUtils.isNotBlank(flashSale)) {
            map.put(ExtendPropsEnum.FLASH_SALE.key, flashSale);
        }

        //闪购订单商品类型
        if (StringUtils.isNotBlank(fulfillmentInfo.getFlashSaleGoodsType())) {
            map.put(FulfillmentInfoEnum.FLASH_SALE_GOODS_TYPE.key, fulfillmentInfo.getFlashSaleGoodsType());
        }


        //临时存储位置
        String orderTemporaryStorage = SmartPatternPropsEnum.ORDER_TEMPORARY_STORAGE.getValue(input.getSmartPatternInfo().getExtendProps());
        if (StringUtils.isNotBlank(orderTemporaryStorage)) {
            map.put(SmartPatternPropsEnum.ORDER_TEMPORARY_STORAGE.key, orderTemporaryStorage);
        }
        //期望送达时间
        String expectOutboundTime = SmartPatternPropsEnum.EXPECT_OUTBOUND_TIME.getValue(input.getSmartPatternInfo().getExtendProps());
        if (StringUtils.isNotBlank(expectOutboundTime)) {
            map.put(SmartPatternPropsEnum.EXPECT_OUTBOUND_TIME.key, expectOutboundTime);
        }

        // 拼多多开关
        if (Objects.nonNull(extendedField) && extendedField.getSwitchPddNewInterface()) {
            map.put(ExtendedFieldEnum.SWITCH_PDD_NEW_INTERFACE_KEY.key, ExtendedFieldEnum.SWITCH_PDD_NEW_INTERFACE_KEY.value);
        }
        return map;
    }

    public Consignee getConsigneeByOmsInput(OrderData input) {
        return getConsigneeByOmsInput(input, null);
    }

    public Consignee getConsigneeByOmsInput(OrderData input, CustomerAddressInfo customerAddressInfo) {
        ConsigneeInfo consigneeInfo = input.getConsigneeInfo() == null ? new ConsigneeInfo() : input.getConsigneeInfo();
        JdAddress address = null;
        if (consigneeInfo.getAddressInfo() != null) {
            address = buildAddress(input, consigneeInfo.getAddressInfo());
        }
        Warehouse warehouse = null;
        if (consigneeInfo.getReceiveWarehouse() != null) {
            warehouse = Warehouse.builder()
                    .warehouseNo(consigneeInfo.getReceiveWarehouse().getWarehouseNo())
                    .endErpWarehouseNo(mapAttrToStringType(consigneeInfo.getReceiveWarehouse().getExtendProps(), "endErpWarehouseNo"))
                    // 订单中心下发的 endDistributionId 其实是 distributionNo，字段名有歧义
                    .endDistributionId(mapAttrToStringType(consigneeInfo.getReceiveWarehouse().getExtendProps(), "endDistributionId"))
                    .shipmentWarehouseType(ShipmentWarehouseTypeEnum.fromCode(mapAttrToIntegerType(consigneeInfo.getReceiveWarehouse().getExtendProps(), "warehouseFlag")))
                    .externalWarehouseNo(mapAttrToStringType(consigneeInfo.getReceiveWarehouse().getExtendProps(), "externalWarehouseNo"))
                    .build();
        }
        EncryptInfo encryptInfo = EncryptInfo.builder().build();
        try {
            /**
             * 订单中心定义的收货信息说明
             * https://cf.jd.com/pages/viewpage.action?pageId=907600570
             */
            if (consigneeInfo.getExtendProps() != null && consigneeInfo.getExtendProps().get("consigneeExtendProps") != null) {
                encryptInfo = JsonUtil.readValue(consigneeInfo.getExtendProps().get("consigneeExtendProps"), EncryptInfo.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("consigneeExtendProps 序列化异常:" + e.getMessage());
        }

        return Consignee.builder()
                .warehouse(warehouse)
                .name(consigneeInfo.getConsigneeName())
                .phone(consigneeInfo.getConsigneePhone())
                .mobile(consigneeInfo.getConsigneeMobile())
                .zipCode(consigneeInfo.getConsigneeZipCode())
                .email(consigneeInfo.getConsigneeEmail())
                .address(address)
                .consigneeAddrEnc(encryptInfo.getConsigneeAddrEnc())
                .consigneeNameEnc(encryptInfo.getConsigneeNameEnc())
                .consigneeMobileEnc(encryptInfo.getConsigneeMobileEnc())
                .consigneePhoneEnc(encryptInfo.getConsigneePhoneEnc())
                .originProvinceName(ObjectUtils.nullIfNull(customerAddressInfo, CustomerAddressInfo::getProvinceName))
                .originCityName(ObjectUtils.nullIfNull(customerAddressInfo, CustomerAddressInfo::getCityName))
                .originCountyName(ObjectUtils.nullIfNull(customerAddressInfo, CustomerAddressInfo::getCountyName))
                .originTownName(ObjectUtils.nullIfNull(customerAddressInfo, CustomerAddressInfo::getTownName))
                .consigneeSiteName(MapUtils.isEmpty(consigneeInfo.getExtendProps()) ? null : consigneeInfo.getExtendProps().get("consigneeSiteName"))
                .consigneeCustomerName(MapUtils.isEmpty(consigneeInfo.getExtendProps()) ? null : consigneeInfo.getExtendProps().get("consigneeCustomerName"))
                .consigneeCompany(consigneeInfo.getConsigneeCompany())
                .build();
    }

    public Consignor getConsignorByOmsInput(OrderData input) {
        ConsignorInfo consignorInfo = input.getConsignorInfo();
        JdAddress jdAddress = null;
        if (consignorInfo.getAddressInfo() != null) {
            jdAddress = JdAddress.builder()
                    .provinceNo(consignorInfo.getAddressInfo().getProvinceNo())
                    .provinceName(consignorInfo.getAddressInfo().getProvinceName())
                    .cityNo(consignorInfo.getAddressInfo().getCityNo())
                    .cityName(consignorInfo.getAddressInfo().getCityName())
                    .countyNo(consignorInfo.getAddressInfo().getCountyNo())
                    .countyName(consignorInfo.getAddressInfo().getCountyName())
                    .townNo(consignorInfo.getAddressInfo().getTownNo())
                    .townName(consignorInfo.getAddressInfo().getTownName())
                    .detailAddress(consignorInfo.getAddressInfo().getAddress())
                    .build();
        }
        return Consignor.builder()
                .name(consignorInfo.getConsignorName())
                .phone(consignorInfo.getConsignorPhone())
                .mobile(consignorInfo.getConsignorMobile())
                .zipCode(consignorInfo.getConsignorZipCode())
                .address(jdAddress)
                .build();
    }

    public JdAddress buildAddress(OrderData input, AddressInfo addressInfo) {
        boolean isNeedGis = input.getProductInfos().stream()
                .map(ProductInfo::getProductNo)
                .anyMatch(productCode -> productCode.equals(FreightProductEnum.DP.getCode()) || productCode.equals(FreightProductEnum.ZCZD.getCode()));
        if (isNeedGis) {
            return JdAddress.builder()
                    .provinceNo(ObjectUtils.defaultIfNull(addressInfo.getProvinceNo(), addressInfo.getProvinceNoGis()))
                    .provinceName(ObjectUtils.defaultIfNull(addressInfo.getProvinceName(), addressInfo.getProvinceNameGis()))
                    .cityNo(ObjectUtils.defaultIfNull(addressInfo.getCityNo(), addressInfo.getCityNoGis()))
                    .cityName(ObjectUtils.defaultIfNull(addressInfo.getCityName(), addressInfo.getCityNameGis()))
                    .countyNo(ObjectUtils.defaultIfNull(addressInfo.getCountyNo(), addressInfo.getCountyNoGis()))
                    .countyName(ObjectUtils.defaultIfNull(addressInfo.getCountyName(), addressInfo.getCountyNameGis()))
                    .townNo(ObjectUtils.defaultIfNull(addressInfo.getTownNo(), addressInfo.getTownNoGis()))
                    .townName(ObjectUtils.defaultIfNull(addressInfo.getTownName(), addressInfo.getTownNameGis()))
                    .detailAddress(addressInfo.getAddress())
                    .provinceNoGis(addressInfo.getProvinceNoGis())
                    .provinceNameGis(addressInfo.getProvinceNameGis())
                    .cityNoGis(addressInfo.getCityNoGis())
                    .cityNameGis(addressInfo.getCityNameGis())
                    .countyNoGis(addressInfo.getCountyNoGis())
                    .countyNameGis(addressInfo.getCountyNameGis())
                    .townNoGis(addressInfo.getTownNoGis())
                    .townNameGis(addressInfo.getTownNameGis())
                    .regionNo(addressInfo.getRegionNo())
                    .regionName(addressInfo.getRegionName())
                    .enAddress(addressInfo.getEnAddress())
                    .build();
        }
        return JdAddress.builder()
                .provinceNo(addressInfo.getProvinceNo())
                .provinceName(addressInfo.getProvinceName())
                .cityNo(addressInfo.getCityNo())
                .cityName(addressInfo.getCityName())
                .countyNo(addressInfo.getCountyNo())
                .countyName(addressInfo.getCountyName())
                .townNo(addressInfo.getTownNo())
                .townName(addressInfo.getTownName())
                .detailAddress(addressInfo.getAddress())
                .provinceNoGis(addressInfo.getProvinceNoGis())
                .provinceNameGis(addressInfo.getProvinceNameGis())
                .cityNoGis(addressInfo.getCityNoGis())
                .cityNameGis(addressInfo.getCityNameGis())
                .countyNoGis(addressInfo.getCountyNoGis())
                .countyNameGis(addressInfo.getCountyNameGis())
                .townNoGis(addressInfo.getTownNoGis())
                .townNameGis(addressInfo.getTownNameGis())
                .regionNo(addressInfo.getRegionNo())
                .regionName(addressInfo.getRegionName())
                .enAddress(addressInfo.getEnAddress())
                .build();
    }

    public Shipment getShipmentByOmsInput(OrderData input, FulfillmentInfo fulfillmentInfo, EcpOrderInfo ecpOrderInfo, PrintExtendInfo printExtendInfo) {
        ReReceiveInfo reReceiveInfo = getShipmentExtendProps(input.getShipmentInfo());
        Map<String, String> serviceRequirements = input.getShipmentInfo().getServiceRequirements();
        Map<String, String> specialSignRequirements = null;
        if (serviceRequirements != null && serviceRequirements.get(ShipperConstants.SPECIAL_SIGN_REQUIREMENTS) != null) {
            specialSignRequirements = JsonUtil.readValueSafe(serviceRequirements.get(ShipperConstants.SPECIAL_SIGN_REQUIREMENTS), Map.class);
        }
        Map<String, String> warehousingRequirements = null;
        if (serviceRequirements != null && serviceRequirements.get("warehousingRequirements") != null) {
            warehousingRequirements = JsonUtil.readValueSafe(serviceRequirements.get("warehousingRequirements"), Map.class);
        }
        Map<String, String> luxurySecurityRequirements = null;
        if (serviceRequirements != null && serviceRequirements.get("luxurySecurityRequirements") != null) {
            luxurySecurityRequirements = JsonUtil.readValueSafe(serviceRequirements.get("luxurySecurityRequirements"), Map.class);
        }
        return Shipment.builder()
//                .expectDate(this.getShipmentExpectDateByInput(input))
                .bdOwnerNo(input.getCustomerInfo().getAccount2No())
                .expectDeliveryEndTime(input.getShipmentInfo().getExpectDeliveryEndTime())
                .expectDeliveryStartTime(input.getShipmentInfo().getExpectDeliveryStartTime())
                .shipperNo(input.getShipmentInfo().getShipperNo())
                .shipperName(input.getShipmentInfo().getShipperName())
                .shipperType(input.getShipmentInfo().getShipperType())
                .endStationNo(input.getShipmentInfo().getEndStationNo())
                .endStationName(input.getShipmentInfo().getEndStationName())
                .contactlessReceiveType(input.getShipmentInfo().getContactlessType())
                .assignedAddress(input.getShipmentInfo().getAssignedAddress())
                .halfReceive(input.getShipmentInfo().getSignandHalfReturn())
                .deliveryType(input.getShipmentInfo().getDeliveryType())
                .billingType(buildBillingType(input))
                .contactPerson(ObjectUtils.nullIfNull(ecpOrderInfo.getAfterSaleInfo(), AfterSaleInfo::getContactPerson))
                .contactPhone(ObjectUtils.nullIfNull(ecpOrderInfo.getAfterSaleInfo(), AfterSaleInfo::getContactPhone))
                .contactAddress(ObjectUtils.nullIfNull(ecpOrderInfo.getAfterSaleInfo(), AfterSaleInfo::getContactAddress))
                .heavyGoodsUpstairs(buildHeavyGoodsUpstairs(input.getShipmentInfo()))
                .hidePrivacyType(buildHidePrivacyType(input.getShipmentInfo()))
                .logisticsOriginalPackage(fulfillmentInfo == null || fulfillmentInfo.getLogisticsOriginalPackage() == null ? 0 : Integer.parseInt(fulfillmentInfo.getLogisticsOriginalPackage()))
                .deductibleCompensation(fulfillmentInfo == null || fulfillmentInfo.getDeductibleCompensation() == null ? "0" : fulfillmentInfo.getDeductibleCompensation())
                // 城配标识 somark37
                .cityDeliveryType(input.getShipmentInfo().getExtendProps() != null && "2".equals(input.getShipmentInfo().getExtendProps().get("activeCityDelivery")))
                // 期望城配标识
                .expectCityDeliveryType(input.getShipmentInfo().getExtendProps() != null && "2".equals(input.getShipmentInfo().getExtendProps().get("expectCityDelivery")))
                .unloadingDock(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("unloadingDock"))
                // 青龙配运标识 somark62
                .tmsDeliveryType(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("lastDeliveryType"))
                .sellerShipperNo(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("sellerShipperNo"))
                .sellerShipperName(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("sellerShipperName"))

                // 开箱验货标志 soMark119
                // wiki: cf.jd.com/pages/viewpage.action?pageId=907600591
                .checkGoodsType(input.getShipmentInfo().getServiceRequirements() == null ? null : input.getShipmentInfo().getServiceRequirements().get("unpackingFlag"))
                .thirdExpressType(getShipmentTextAttr(input.getShipmentInfo(), "thirdExpressType"))
                .CNTemplate(printExtendInfo == null ? null : ObjectUtils.nullIfNull(printExtendInfo.getCNTemplate(), Integer::parseInt))
                .transportMode(ObjectUtils.nullIfNull(input.getShipmentInfo().getExtendProps(), map -> map.get("transportMode")))
                // 城配时效
                .expectDeliveryStartDate(getShipmentTextDate(input.getShipmentInfo(), "expectDeliveryStartDate"))
                .cityDeliveryStartDate(getShipmentTextDate(input.getShipmentInfo(), "cityDeliveryStartDate"))
                .cityDeliveryEndDate(getShipmentTextDate(input.getShipmentInfo(), "cityDeliveryEndDate"))
                .cityDeliveryDeadline(getShipmentTextAttr(input.getShipmentInfo(), "cityDeliveryDeadline"))
                .cityDeadlineType(getShipmentTextType(input.getShipmentInfo(), "cityDeadlineType"))
                .agingName(getShipmentTextAttr(input.getShipmentInfo(), "agingName"))
                .transportType(input.getShipmentInfo().getTransportType())
                .partialPayment(getShipmentBooleanType(input.getShipmentInfo(), "partialPayment"))
                .needExternalWaybillOnly(getShipmentTextType(input.getShipmentInfo(), "needExternalWaybillOnly"))
                .expectTransportType(getShipmentTextType(input.getShipmentInfo(), "expectTransportType"))
                .waybillNo(input.getRefOrderInfo() == null ? null : input.getRefOrderInfo().getWaybillNo())
                //仅转三方场景使用
                .thirdWaybill(input.getRefOrderInfo().getExtendProps() == null ? null : input.getRefOrderInfo().getExtendProps().get("thirdWaybillNo"))
                .tpWaybill(input.getRefOrderInfo().getExtendProps() == null ? null : input.getRefOrderInfo().getExtendProps().get("tpWaybillNo"))
                .printExtendInfo(this.buildPrintExtendInfo(printExtendInfo))
                // 签单返还
                .signBillTypeEnum(SignBillTypeEnum.fromCode(MapUtils.getString(input.getExtendProps(), "reReceiveMode", "0")))
                .reReceiveName(Objects.isNull(reReceiveInfo) ? null : reReceiveInfo.getReReceiveName())
                .reReceiveMobile(Objects.isNull(reReceiveInfo) ? null : reReceiveInfo.getReReceiveMobile())
                .reReceivePhone(Objects.isNull(reReceiveInfo) ? null : reReceiveInfo.getReReceivePhone())
                .reReceiveAdress(Objects.isNull(reReceiveInfo) ? null : reReceiveInfo.getReReceiveAddress())
                .deliveryPerformanceChannel(fulfillmentInfo.getDeliveryPerformanceChannel())
                .thirdExpressProduct(getShipmentTextAttr(input.getShipmentInfo(), "thirdExpressProduct"))
                .vehicleType(input.getShipmentInfo().getVehicleType())
                .express(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("express"))
                .signIDCode(SpecialSignPropsEnum.SIGN_ID_CODE.getValue(specialSignRequirements))
                .signType(SpecialSignPropsEnum.SIGN_TYPE.getValue(specialSignRequirements))
                .activationCardService(SpecialSignPropsEnum.ACTIVATION_SERVICE.getValue(specialSignRequirements))
                .packageProduceMode(PackageProduceModeEnum.fromCode(mapAttrToStringType(input.getShipmentInfo().getServiceRequirements(), "packageProduceMode")))
                .firstWaybillIssue(mapAttrInfoToBooleanType(input.getShipmentInfo().getServiceRequirements(), "firstWaybillIssue"))
                .warmLayer(input.getShipmentInfo().getWarmLayer())
                .goodsIsolateType(GoodsIsolateTypeEnum.fromCode(mapAttrToStringType(input.getShipmentInfo().getServiceRequirements(), "goodsIsolateType")))
                .planDeliveryTime(input.getShipmentInfo().getPlanDeliveryTime())
                .expectDeliveryTimeFlag(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("expectDeliveryTimeFlag"))
                .deliveryIntoWarehouse(mapAttrInfoToBooleanType(input.getShipmentInfo().getServiceRequirements(), "deliveryIntoWarehouse"))
                .warehousingAppointmentCode(mapAttrToStringType(warehousingRequirements, "warehousingAppointmentCode"))
                .warehousingAppointmentStartTime(mapAttrToStringType(warehousingRequirements, "warehousingAppointmentStartTime"))
                .warehousingAppointmentEndTime(mapAttrToStringType(warehousingRequirements, "warehousingAppointmentEndTime"))
                .warehousingAppointmentRemark(mapAttrToStringType(warehousingRequirements, "warehousingAppointmentRemark"))
                .luxurySecurity(mapAttrInfoToBooleanType(input.getShipmentInfo().getServiceRequirements(), "luxurySecurity"))
                .deliveryForcePhoto(mapAttrInfoToBooleanType(luxurySecurityRequirements, "deliveryForcePhoto"))
                .forceContact(mapAttrInfoToBooleanType(luxurySecurityRequirements, "forceContact"))
                .refusalAllowed(Optional.ofNullable(input.getShipmentInfo().getExtendProps()).map(props -> props.get("refusalAllowed")).orElse(null))
                .expectDeliveryResource(input.getShipmentInfo().getExpectDeliveryResource())
                .waybillNoFetchType(input.getShipmentInfo().getWaybillNoFetchType())
                .deliveryInBatches(mapAttrInfoToBooleanType(input.getShipmentInfo().getServiceRequirements(), "deliveryInBatches"))
                .deliveryService(DeliveryServiceEnum.fromCode(input.getShipmentInfo().getServiceRequirements().get("deliveryService")))
                .nonSelfSignCall(SpecialSignPropsEnum.NON_SELF_SIGN_CALL.getValue(serviceRequirements))
                .consignmentControlRules(mapAttrInfoToBooleanType(input.getShipmentInfo().getServiceRequirements(), "consignmentControlRules"))
                .packagingStandards(serviceRequirements == null ? null : serviceRequirements.get(ShipperConstants.PACKAGING_STANDARDS))
                .dataTransferRequirements(ShipmentPropsEnum.DATA_TRANSFER_REQUIREMENTS.getValue(serviceRequirements))
                .consignmentType(Optional.ofNullable(input.getShipmentInfo().getExtendProps()).map(props -> props.get("consignmentType")).orElse(null))
                .customerTransportType(Optional.ofNullable(input.getShipmentInfo().getExtendProps()).map(props -> props.get("customerTransportType")).orElse(null))
                .customerTransportName(Optional.ofNullable(input.getShipmentInfo().getExtendProps()).map(props -> props.get("customerTransportName")).orElse(null))
                .pickupType(PickupTypeEnum.fromCode(input.getShipmentInfo().getPickupType()))
                .tcExpectPickupStartTime(input.getShipmentInfo().getExpectPickupStartTime())
                .tcExpectPickupEndTime(input.getShipmentInfo().getExpectPickupEndTime())
                .productSplitCargoQuantityType(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("productSplitCargoQuantityType"))
                .serviceRequirements(serviceRequirements)
                .flowType(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("flowType"))
                .customerContactlessType(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("customerContactlessType"))
                .instantDeliveryType(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("instantDeliveryType"))
                .gisFenceId(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("gisFenceId"))
                .build();
    }

    private com.jdl.sc.ofw.outbound.dto.vo.PrintExtendInfo buildPrintExtendInfo(PrintExtendInfo printExtendInfo) {
        if (printExtendInfo == null) {
            return com.jdl.sc.ofw.outbound.dto.vo.PrintExtendInfo.builder().build();
        }
        return com.jdl.sc.ofw.outbound.dto.vo.PrintExtendInfo.builder()
                .businessType(printExtendInfo.getBusinessType())
                .appointDeliveryTime(printExtendInfo.getAppointDeliveryTime())
                .destinationCode(printExtendInfo.getDestinationCode())
                .destinationName(printExtendInfo.getDestinationName())
                .receiveMode(printExtendInfo.getReceiveMode())
                .gatherCenterName(printExtendInfo.getGatherCenterName())
                .monthlyAccount(printExtendInfo.getMonthlyAccount())
                .sellerRemark(printExtendInfo.getSellerRemark())
                .packageMark(printExtendInfo.getPackageMark())
                .sendMode(printExtendInfo.getSendMode())
                .sendWebsiteCode(printExtendInfo.getSendWebsiteCode())
                .shipment(printExtendInfo.getShipment())
                .sendWebsiteName(printExtendInfo.getSendWebsiteName())
                .thirdPayment(printExtendInfo.getThirdPayment())
                .thirdSite(printExtendInfo.getThirdSite())
                .customPrintInfo(printExtendInfo.getCustomPrintInfo())
                .isvSoTypeName(printExtendInfo.getIsvSoTypeName())
                .matchWmsPrintTemplateFlag(printExtendInfo.getMatchWmsPrintTemplateFlag())
                .build();
    }

    public Shipment getShipmentByOmsInput(OrderData input, FulfillmentInfo fulfillmentInfo) {
        return Shipment.builder()
//                .expectDate(this.getShipmentExpectDateByInput(input))

                // fixme 开始结束是相同时间
                .expectDeliveryEndTime(input.getShipmentInfo().getExpectDeliveryEndTime())
                .expectDeliveryStartTime(input.getShipmentInfo().getExpectDeliveryStartTime())
                .shipperNo(input.getShipmentInfo().getShipperNo())
                .shipperName(input.getShipmentInfo().getShipperName())
                .shipperType(input.getShipmentInfo().getShipperType())
                .endStationNo(input.getShipmentInfo().getEndStationNo())
                .endStationName(input.getShipmentInfo().getEndStationName())
                .contactlessReceiveType(input.getShipmentInfo().getContactlessType())
                .assignedAddress(input.getShipmentInfo().getAssignedAddress())
                .halfReceive(input.getShipmentInfo().getSignandHalfReturn())
                .deliveryType(input.getShipmentInfo().getDeliveryType())
                .billingType(buildBillingType(input))
                .logisticsOriginalPackage(fulfillmentInfo == null || fulfillmentInfo.getLogisticsOriginalPackage() == null ? 0 : Integer.parseInt(fulfillmentInfo.getLogisticsOriginalPackage()))
                .deductibleCompensation(fulfillmentInfo == null || fulfillmentInfo.getDeductibleCompensation() == null ? "0" : fulfillmentInfo.getDeductibleCompensation())
                // 城配标识 somark37
                .cityDeliveryType(input.getShipmentInfo().getExtendProps() != null && "2".equals(input.getShipmentInfo().getExtendProps().get("activeCityDelivery")))
                .expectCityDeliveryType(input.getShipmentInfo().getExtendProps() != null && "2".equals(input.getShipmentInfo().getExtendProps().get("expectCityDelivery")))
                // 青龙配运标识 somark62
                .tmsDeliveryType(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("lastDeliveryType"))
                .transportMode(input.getShipmentInfo().getExtendProps() == null ? null : input.getShipmentInfo().getExtendProps().get("transportMode"))
                // 开箱验货标志 soMark119
                // wiki: cf.jd.com/pages/viewpage.action?pageId=907600591
                .checkGoodsType(input.getShipmentInfo().getServiceRequirements() == null ? null : input.getShipmentInfo().getServiceRequirements().get("unpackingFlag"))
                // 城配时效
                .expectDeliveryStartDate(getShipmentTextDate(input.getShipmentInfo(), "expectDeliveryStartDate"))
                .cityDeliveryStartDate(getShipmentTextDate(input.getShipmentInfo(), "cityDeliveryStartDate"))
                .cityDeliveryEndDate(getShipmentTextDate(input.getShipmentInfo(), "cityDeliveryEndDate"))
                .cityDeliveryDeadline(getShipmentTextAttr(input.getShipmentInfo(), "cityDeliveryDeadline"))
                .cityDeadlineType(getShipmentTextType(input.getShipmentInfo(), "cityDeadlineType"))
                .agingName(getShipmentTextAttr(input.getShipmentInfo(), "agingName"))
                .partialPayment(getShipmentBooleanType(input.getShipmentInfo(), "partialPayment"))
                .waybillNoFetchType(input.getShipmentInfo().getWaybillNoFetchType())
                .build();
    }

    public Warehouse getWarehouseByOmsInput(OrderData input) {
        return Warehouse.builder()
                .warehouseNo(input.getConsignorInfo().getCustomerWarehouse().getActualWarehouseNo())
                .originalWarehouseNo(input.getConsignorInfo().getCustomerWarehouse().getWarehouseNo())
                .warehouseName(input.getConsignorInfo().getCustomerWarehouse().getActualWarehouseName())
                .warehouseType(input.getConsignorInfo().getCustomerWarehouse().getActualWarehouseType())
                // 下仓时效
                .transportWmsStartDate(getShipmentTextDate(input.getShipmentInfo(), "transportWmsStartDate"))
                .transportWmsEndDate(getShipmentTextDate(input.getShipmentInfo(), "transportWmsEndDate"))
                .wmsPlanDeliveryTime(getShipmentTextAttr(input.getShipmentInfo(), "wmsPlanDeliveryTime"))
                .wmsDeadlineType(getShipmentTextType(input.getShipmentInfo(), "wmsDeadlineType"))
                .build();
    }

    public Finance getFinanceOmsByInput(OrderData input, EcpOrderInfo ecpOrderInfo) {
        return Finance.builder()
                .orderAmount(ecpOrderInfo.getOrderAmount() == null ? null :
                        Money.builder().amount(ecpOrderInfo.getOrderAmount().getAmount())
                                .currencyCode(ecpOrderInfo.getOrderAmount().getCurrencyCode()
                                ).build())
                .paymentType(ecpOrderInfo.getPaymentType() == null ? null : Integer.valueOf(ecpOrderInfo.getPaymentType()))
                .payTime(ecpOrderInfo.getPayTime())
                .settlementType(input.getFinanceInfo().getSettlementType())
                .jdlMark(ecpOrderInfo.getJdlMark())
                .build();
    }

    public RefOrderInfo getRefOrderInfoByOmsInput(OrderData input, EcpOrderInfo ecpOrderInfo) {
        List<String> waybillNos = null;
        if (input.getRefOrderInfo() != null) {
            waybillNos = Lists.newArrayList(input.getRefOrderInfo().getWaybillNo());
        }
        return RefOrderInfo.builder()
                .waybillNo(waybillNos)
                .giftType(ecpOrderInfo.getOrderGiftType())
                .orderGiftRelationList(ecpOrderInfo.getOrderGiftRelationList() == null || ecpOrderInfo.getOrderGiftRelationList().isEmpty() ? null :
                        ecpOrderInfo.getOrderGiftRelationList().stream()
                                .map(orderGiftRelation -> OrderGiftRelation.builder()
                                        .orderNo(orderGiftRelation.getOrderNo())
                                        .orderGiftRelationType(orderGiftRelation.getOrderGiftRelationType())
                                        .build()
                                ).collect(Collectors.toList()))
                .serviceType(ecpOrderInfo.getServiceType())
                .serviceTypeName(ecpOrderInfo.getServiceTypeName())
                .groupOrderNo(Objects.isNull(input.getRefOrderInfo()) ? null : MapUtils.getString(input.getRefOrderInfo().getExtendProps(), "groupOrderNo"))
                .sellerPurchaseOrderNo(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "relationNo"))
                .relationNo(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "relationNo"))
                .allocationOrderNo(ObjectUtils.nullIfNull(input.getRefOrderInfo().getExtendProps(), extendProps -> extendProps.get("allocationOrderNo")))
                .purchaseOrderNo(input.getRefOrderInfo().getPurchaseOrderNo())
                .tcPurchaseOrderNo(ObjectUtils.nullIfNull(input.getRefOrderInfo().getExtendProps(), extendProps -> extendProps.get("tcPurchaseOrderNo")))
                .mainOrderNos(ObjectUtils.nullIfNull(input.getRefOrderInfo().getExtendProps(), extendProps -> extendProps.get("mainOrderNos")))
                .subOrderNos(ObjectUtils.nullIfNull(input.getRefOrderInfo().getExtendProps(), extendProps -> extendProps.get("subOrderNos")))
                .build();
    }

    /**
     * 判断是否为正品鉴定单一段订单
     */
    private boolean isAppraisalOrder(OrderData orderData) {
        return MapUtils.isNotEmpty(orderData.getExtendProps())
                && orderData.getExtendProps().containsKey(ExtendPropsEnum.APPRAISAL_INSTITUTION_NAME.getKey());
    }

    private ChannelSourceEnum parseChannelSource(OrderData orderData) {
        // 优先根据soSource解析channelSource,正品鉴定单一段订单无soSource字段,通过systemCaller解析
        ChannelSourceEnum channelSource;
        if (isAppraisalOrder(orderData)) {
            channelSource = ChannelSourceEnum.fromCode(orderData.getChannelInfo().getSystemCaller());
        } else {
            channelSource = ChannelSourceEnum.fromSoSource(ExtendPropsEnum.SO_SOURCE.getValue(orderData.getExtendProps()));
        }
        return channelSource;
    }

    public Channel getChannelByOmsInput(OrderData input, EcpOrderInfo ecpOrderInfo, FulfillmentInfo fulfillmentInfo) {
        boolean isSplitOrder = isSplitOrder(input);
        return Channel.builder()
                // fixme 本期暂时不接                       ispoppresale
                .pin(ecpOrderInfo.getBuyerNo())
                .channelNo(input.getChannelInfo().getChannelNo())
                .channelShopNo(input.getChannelInfo().getChannelShopNo())
                .bdSellerNo(fulfillmentInfo == null ? null : fulfillmentInfo.getBdSellerNo())
                .channelSource(parseChannelSource(input))
                .channelName(input.getChannelInfo().getChannelName())
                .channelOrderNo(isSplitOrder ? getChannelOrderNo(input.getChannelInfo()) : input.getChannelInfo().getChannelOrderNo())
                .channelOrderCreateTime(input.getChannelInfo().getChannelOperateTime())
                .shopNo(input.getChannelInfo().getSecondLevelChannel())
                .shopName(input.getChannelInfo().getSecondLevelChannelName())
                .customerOrderNo(isSplitOrder ? getCustomerOrderNo(input.getChannelInfo()) : input.getChannelInfo().getCustomerOrderNo())
                .systemCaller(input.getChannelInfo().getSystemCaller())
                .isvSource(input.getChannelInfo().getSystemSubCaller())
                .sellerChannelNo(Optional.ofNullable(ecpOrderInfo.getSellerChannelInfo())
                        .map(SellerChannelInfo::getSellerChannelNo)
                        .orElse(null))
                .childChannelOrderNos(getChildrenOrders(input.getOrderNo(), input.getChannelInfo(), CHILD_CHANNEL_ORDER_NOS_KEY))
                .childCustomerOrderNos(getChildrenOrders(input.getOrderNo(), input.getChannelInfo(), CHILD_CUSTOMER_ORDER_NOS_KEY))
                .customerShopName(ecpOrderInfo.getShopName())
                .build();
    }

    public Map<String, String> getPropsForScm(OrderData input, FulfillmentInfo fulfillmentInfo) {
        Map<String, String> map = new HashMap(3);
        map.put(ExtendPropsEnum.ORDER_MARK.getKey(), ExtendPropsEnum.ORDER_MARK.getValue(input.getExtendProps()));
        map.put(ExtendPropsEnum.SENDPAY.getKey(), ExtendPropsEnum.SENDPAY.getValue(input.getExtendProps()));
        map.put(ExtendPropsEnum.APPRAISAL_INSTITUTION_NAME.getKey(), ExtendPropsEnum.APPRAISAL_INSTITUTION_NAME.getValue(input.getExtendProps()));
        map.put(FulfillmentInfoEnum.SO_MAIN_PARAM_JFS_KEY.getKey(), fulfillmentInfo.getSoMainParamJfsKey());
        map.put(ExtendPropsEnum.PROFESSION_TYPE.getKey(), ExtendPropsEnum.PROFESSION_TYPE.getValue(input.getExtendProps()));
        map.put(ExtendPropsEnum.CAR_BUSINESS_LINE.getKey(), ExtendPropsEnum.CAR_BUSINESS_LINE.getValue(input.getExtendProps()));
        map.put(ExtendPropsEnum.ORDER_CLOB_OSS_KEY.getKey(), ExtendPropsEnum.ORDER_CLOB_OSS_KEY.getValue(input.getExtendProps()));
        return map;
    }

    public Fulfillment getFulfillment(OrderData input, FulfillmentInfo fulfillmentInfo, PrintInfo printInfo, PrintExtendInfo printExtendInfo, FulfillmentFlag fulfillmentFlag, EcpOrderInfo ecpOrderInfo) {
        SmartPatternInfo smartPatternInfo = input.getSmartPatternInfo();
        boolean existSmartPatternExtendProps = smartPatternInfo.getExtendProps() != null && smartPatternInfo.getExtendProps().containsKey("smartPatternExtendProps");
        SmartPatternExtendProps smartPatternExtendProps = SmartPatternExtendProps.builder().build();
        try {
            /**
             * 订单中心定义的智能策略扩展属性
             * https://cf.jd.com/pages/viewpage.action?pageId=907600572
             */
            if (existSmartPatternExtendProps) {
                smartPatternExtendProps = JsonUtil.readValue(smartPatternInfo.getExtendProps().get("smartPatternExtendProps"), SmartPatternExtendProps.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("smartPatternExtendProps 序列化异常:" + e.getMessage());
        }
        String customField = fulfillmentInfo.getCustomField();
        /*
         * https://joyspace.jd.com/pages/LuUqEpnaKgMqaDCQFu6j
         * JITX-新增ka扩展点: extendProps#fulfillmentInfo#customField#kaExtensionPoint
         */
        Integer kaExtensionPoint = null;
        boolean printInfoFromSeller = true;
        String sellerExternalOrderNo = null;
        if (StringUtils.isNotBlank(customField)) {
            if (customField.startsWith(ECLP_JIMKV_PREFIX)) {
                customField = eclpJimkvLoadServiceAcl.load(customField);
            }
            Map<String, String> customFieldMap = JsonUtil.parse(customField, Map.class);
            if (customFieldMap.get(CustomFieldEnum.PRINTINFO_FROM_SELLER.getKey()) != null) {
                printInfoFromSeller = Boolean.parseBoolean(String.valueOf(customFieldMap.get(CustomFieldEnum.PRINTINFO_FROM_SELLER.getKey())));
            }
            kaExtensionPoint = MapUtils.getInteger(customFieldMap, CustomFieldEnum.KA_EXTENSION_POINT.getKey());
            if (StringUtils.isNotBlank(customFieldMap.get(CustomFieldEnum.SELLER_EXTERNAL_ORDER_NO.getKey()))) {
                sellerExternalOrderNo = customFieldMap.get(CustomFieldEnum.SELLER_EXTERNAL_ORDER_NO.getKey());
            }
        }

        return Fulfillment.builder()
                .occupyResult(smartPatternInfo.getStockOperationRule() == null
                        || smartPatternInfo.getStockOperationRule().getOccupyResult() == null || input.getSmartPatternInfo().getStockOperationRule().getOccupyResult() == 1)
                .stockDetailList(buildStockDetail(input.getSmartPatternInfo().getExtendProps()))
                .partialFulfillment(input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 3
                        || input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 4)
                .cargoFulfillmentWay(input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 2
                        || input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 3)
                .outboundStrategy(input.getSmartPatternInfo().getWarehouseRule().getOutboundStrategy())
                .sellerAssignPackType(fulfillmentInfo.getIsvPackType())
                .sellerPackTypeNo(fulfillmentInfo.getIsvPackTypeNo())
                .sellerPackTypeName(fulfillmentInfo.getIsvPackTypeName())
                .sellerIndividuationPrintInfo(printInfo == null ? null : processCustomerPrintInfo(printInfo.getCustomerPrintInfo()))
                .thirdInsured(ObjectUtils.nullIfNull(printExtendInfo, PrintExtendInfo::getThirdInsured))
                .thirdGuaranteeMoney(ObjectUtils.nullIfNull(printExtendInfo, PrintExtendInfo::getThirdGuaranteeMoney))
                .invoiceEnumFlag(InvoiceEnum.fromCode(fulfillmentInfo.getGoodWithInvoiceFlag()))
                .orderPriority(SmartPatternPropsEnum.OUTBOUND_PRIORITY.getValue(input.getSmartPatternInfo().getExtendProps()))
                .orderUrgencyType(SmartPatternPropsEnum.OUTBOUND_URGENCY.getValue(input.getSmartPatternInfo().getExtendProps()))
                .skipMobileDecrypt(fulfillmentFlag != null && Objects.equals(fulfillmentFlag.getSkipMobileDecrypt(), "1"))
                .quarantineCert(!Objects.isNull(input.getFulfillmentInfo()) && Objects.equals(MapUtils.getString(input.getFulfillmentInfo().getFulfillmentSign(), "quarantineCert"), "1"))
                .trustJDMetrics("1".equals(fulfillmentInfo.getTrustJDMetrics()))
                .printComponentType(printExtendInfo == null ? null : printExtendInfo.getPrintComponentType())
                .customField(fulfillmentInfo.getCustomField())
                .tmDeliveryByJgFlag("1".equals(smartPatternExtendProps.getTmDeliveryByJgFlag()))
                .clpsCainiaoElecFlag("1".equals(smartPatternExtendProps.getClpsCainiaoElecFlag()))
                .tmOrderFlag(smartPatternExtendProps.getTmOrderFlag())
                .packageSignFlag("1".equals(smartPatternExtendProps.getPackageSignFlag()))
                .invoiceChecker(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "invoiceChecker"))
                .paymentType(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "paymentType"))
                .saleType(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "saleType"))
                .supervisionCode(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "supervisionCode"))
                .warmLayer(Objects.isNull(input.getFulfillmentInfo()) ? null : input.getFulfillmentInfo().getWarmLayer())
                .batchNo(ExtendPropsEnum.BATCH_NO.getValue(input.getExtendProps()))
                .batchQuantity(ExtendPropsEnum.BATCH_QUANTITY.getValue(input.getExtendProps()))
                .stationCode(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "stationCode"))
                .requireConsigneeTimeTo(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "requireConsigneeTimeTo"))
                .crossDockType(Objects.isNull(smartPatternInfo.getCrossDockRule()) ? null : ObjectUtils.defaultIfNull(smartPatternInfo.getCrossDockRule().getCrossDockType(), Object::toString, null))
                .mjCompanyCode(getShipmentTextAttr(input.getShipmentInfo(), "componyCode"))
                .sellerCustomPictureUrl(ObjectUtils.nullIfNull(input.getFulfillmentInfo(), (fulfillment) -> FulfillmentExtendEnum.SELLER_CUSTOM_PICTURE_URL.getValue(fulfillment.getExtendProps())))
                .adventSafeDays(ObjectUtils.nullIfNull(input.getFulfillmentInfo(), (fulfillment) -> FulfillmentExtendEnum.ADVENT_SAFE_DAYS.getValue(fulfillment.getExtendProps())))
                .packDetailsCollect(Objects.isNull(input.getFulfillmentInfo()) ? PackDetailsCollectEnum.NOT_COLLECT : PackDetailsCollectEnum.fromCode(MapUtils.getInteger(input.getFulfillmentInfo().getExtendProps(), "consumableCollectMode")))
                .logicParam(input.getSmartPatternInfo().getStockOperationRule().getLogicalFactor())
                .salesmanAttribute(Optional.ofNullable(input.getExtendProps()).map(props -> props.get("salesmanAttribute")).orElse(null))
                .customerSignforAttributes(Optional.ofNullable(input.getExtendProps()).map(props -> props.get("customerSignforAttributes")).orElse(null))
                .printInfoFromSeller(printInfoFromSeller)
                .kaExtensionPoint(kaExtensionPoint)
                .platformBusinessModel(ecpOrderInfo.getPlatformBusinessModel())
                .boxInfos(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "boxInfos"))
                .skyWorthWaybill(printInfo == null ? null : printInfo.getSkyWorthWaybill())
                .occupyResultType(smartPatternInfo.getStockOperationRule().getOccupyResultType())
                .platformExpectWmsDeliveryTime(ObjectUtils.nullIfNull(input.getFulfillmentInfo(), (fulfillment) -> FulfillmentExtendEnum.PLATFORM_EXPECT_WMS_DELIVERY_TIME.getValue(fulfillment.getExtendProps())))
                .platformExpectPickupTime(ObjectUtils.nullIfNull(input.getFulfillmentInfo(), (fulfillment) -> FulfillmentExtendEnum.PLATFORM_EXPECT_PICKUP_TIME.getValue(fulfillment.getExtendProps())))
                .packageRestrictions(input.getSmartPatternInfo().getWarehouseRule().getPackageRestrictions())
                .ecpOrderDeliveryStrategy(ecpOrderInfo.getEcpOrderDeliveryStrategy())
                .ecpFirstOrderNo(ecpOrderInfo.getEcpOrderNo())
                .ecpOrderSalesStrategy(ecpOrderInfo.getEcpOrderSalesStrategy())
                .frozenDamageMethod(ObjectUtils.nullIfNull(input.getFulfillmentInfo(), inputFulfillment -> ObjectUtils.nullIfNull(inputFulfillment.getFulfillmentSign(), sign -> sign.get("frozenDamageMethod"))))
                .productSplitResultSource(ProductSplitResultSourceEnum.fromCode(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "productSplitResultSource")))
                .reusedHistoryWaybillType(ObjectUtils.nullIfNull(input.getFulfillmentInfo(), inputFulfillment -> ObjectUtils.nullIfNull(inputFulfillment.getFulfillmentSign(), sign -> sign.get("reusedHistoryWaybillNo"))))
                .isPrintMedicinePriceBalance(PrintMedicinePriceBalanceEnum.fromCode(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "isPrintMedicinePriceBalance")))
                .serialNumberCheckChannel(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "serialNumberCheckChannel"))
                .processingWay(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "processingWay"))
                .memberCardId(ecpOrderInfo.getMemberCardId())
                .memberName(ecpOrderInfo.getMemberName())
                .customizeOrderPrintInfo(getCustomizeOrderPrintInfo(input.getOrderNo(), printInfo))
                .ecpOrderType(ecpOrderInfo.getEcpOrderType())
                .warehouseShipmentMode(WarehouseShipmentModeEnum.fromCode(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "warehouseShipmentMode")))
                .cargoGiftRelationInfo(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "cargoGiftRelationInfo"))
                .jitPromise(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "jitPromise"))
                .productionPromiseType(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "productionPromiseType"))
                .stockShortsProcessWay(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "stockShortsProcessWay"))
                .sevenFreshStoreName(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "sevenFreshStoreName"))
                .estimatedDeliveryTimePeriod(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "estimatedDeliveryTimePeriod"))
                .packagingFee(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "packagingFee"))
                .customerBoxInfos(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "customerBoxInfos"))
                .sellerExternalOrderNo(sellerExternalOrderNo)
                .handoverPickupRequirements(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "handoverPickupRequirements"))
                .orderMainSubFlag(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "orderMainSubFlag"))
                .etaTraceId(Objects.isNull(input.getFulfillmentInfo()) ? null : MapUtils.getString(input.getFulfillmentInfo().getExtendProps(), "etaTraceId"))
                .build();
    }

    private String getCustomizeOrderPrintInfo(String orderNo, PrintInfo printInfo) {
        if (printInfo == null) {
            return null;
        }
        if (printInfo.getCustomizeOrderPrintInfo() == null) {
            return null;
        }
        final String customizeOrderPrintInfo;
        try {
            customizeOrderPrintInfo = JsonUtil.toJson(printInfo.getCustomizeOrderPrintInfo());
        } catch (Exception e) {
            throw new RuntimeException("getCustomizeOrderPrintInfo 序列化异常:" + e.getMessage());
        }
        return dtcJimKvAgent.save(this.getClass().getSimpleName(), "getCustomizeOrderPrintInfo", orderNo, customizeOrderPrintInfo);
    }

    private String processCustomerPrintInfo(String customerPrintInfo) {
        if (StringUtils.isBlank(customerPrintInfo)) {
            return null;
        }
        final Map<String, Object> sellerIndividuationMap = JsonUtil.parse(customerPrintInfo, Map.class);
        Object orderInfo = sellerIndividuationMap.get(SELLER_INDIVIDUATION_ORDER_INFO_KEY);
        Object skuInfo = sellerIndividuationMap.get(SELLER_INDIVIDUATION_SKU_INFO_KEY);
        if (orderInfo instanceof String) {
            //商家下发的orderExtendInfo可能是字符串，需转map，否则生产协同会报转换异常
            sellerIndividuationMap.put(SELLER_INDIVIDUATION_ORDER_INFO_KEY, JsonUtil.readValueSafe((String) orderInfo, Map.class));
        }
        if (skuInfo instanceof String) {
            sellerIndividuationMap.put(SELLER_INDIVIDUATION_SKU_INFO_KEY, JsonUtil.readValueSafe((String) skuInfo, List.class));
        }
        return JsonUtil.toJsonSafe(sellerIndividuationMap);
    }

    public Fulfillment getAppraisalFulfillment(OrderData input, FulfillmentInfo fulfillmentInfo) {
        SmartPatternInfo smartPatternInfo = input.getSmartPatternInfo();
        if (smartPatternInfo.getStockOperationRule() == null
                || smartPatternInfo.getStockOperationRule().getOccupyResult() == null) {
            return Fulfillment.builder()
                    .occupyResult(true)
                    .build();
        }
        String customField = fulfillmentInfo.getCustomField();
        boolean printInfoFromSeller = true;
        if (StringUtils.isNotBlank(customField)) {
            if (customField.startsWith(ECLP_JIMKV_PREFIX)) {
                customField = eclpJimkvLoadServiceAcl.load(customField);
            }
            Map<String, String> customFieldMap = JsonUtil.parse(customField, Map.class);
            if (customFieldMap.get(CustomFieldEnum.PRINTINFO_FROM_SELLER.getKey()) != null) {
                printInfoFromSeller = Boolean.parseBoolean(String.valueOf(customFieldMap.get(CustomFieldEnum.PRINTINFO_FROM_SELLER.getKey())));
            }
        }
        return Fulfillment.builder()
                .occupyResult(input.getSmartPatternInfo().getStockOperationRule().getOccupyResult() == 1)
                .stockDetailList(buildStockDetail(input.getSmartPatternInfo().getExtendProps()))
                .partialFulfillment(input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 3
                        || input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 4)
                .cargoFulfillmentWay(input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 2
                        || input.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay() == 3)
                .printInfoFromSeller(printInfoFromSeller)
                .logicParam(input.getSmartPatternInfo().getStockOperationRule().getLogicalFactor())
                .build();
    }

    //增加 开关
    public Integer getOrderClob(OrderData input) {
        final String orderClobOssKey = MapUtils.getString(input.getExtendProps(), ExtendPropsEnum.ORDER_CLOB_OSS_KEY.getKey());
        if (StringUtils.isNotBlank(orderClobOssKey)){
            return OrderClobEnum.CLOB.getCode();
        }
        return null;
    }

    interface FulfillmentExtendProsProcess<T> {
        T getValue(FulfillmentExtendEnum fulfillmentExtendEnum, Map<String, String> fulfillmentExtendMap);

    }

    @Getter
    @AllArgsConstructor
    public enum FulfillmentExtendEnum {

        SELLER_CUSTOM_PICTURE_URL("sellerCustomPictureUrl", "商家自定义图片链接地址(好评卡信息)", (prosEnum, extendMap) -> ObjectUtils.nullIfNull(extendMap, (map) -> map.get(prosEnum.getKey()))),
        ADVENT_SAFE_DAYS("adventSafeDays", "临期安全天数", (prosEnum, extendMap) -> ObjectUtils.nullIfNull(extendMap, (map) -> map.get(prosEnum.getKey()))),
        PLATFORM_EXPECT_WMS_DELIVERY_TIME("platformExpectWmsDeliveryTime", "平台要求出库时间", (prosEnum, extendMap) -> ObjectUtils.nullIfNull(extendMap, (map) -> map.get(prosEnum.getKey()))),
        PLATFORM_EXPECT_PICKUP_TIME("platformExpectPickupTime", "平台要求揽收时间", (prosEnum, extendMap) -> ObjectUtils.nullIfNull(extendMap, (map) -> map.get(prosEnum.getKey()))),


        ;
        private String key;
        private String desc;
        FulfillmentExtendProsProcess fulfillmentExtendProsProcess;

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) fulfillmentExtendProsProcess.getValue(this, map);
        }
    }

    interface CargoSignProcess<T> {
        T getValue(CargoSignEnum cargoSignEnum, Map<String, String> cargoSignMap);
    }

    @Getter
    @AllArgsConstructor
    public enum CargoSignEnum {

        VIRTUAL_GOODS("virtualGoods", "是否虚拟品 0:否 1:是", (cargoSignEnum, cargoSignMap) -> cargoSignMap == null ? null : cargoSignMap.get(cargoSignEnum.getKey())),
        CHECK_UNIT_SN_CODE("checkUniSnCode", "序列号是否验重", (cargoSignEnum, cargoSignMap) -> cargoSignMap == null ? null : cargoSignMap.get(cargoSignEnum.getKey())),
        PROCESSING("processing", "是否加工品 0:否 1:是", (cargoSignEnum, cargoSignMap) -> cargoSignMap == null ? null : cargoSignMap.get(cargoSignEnum.getKey())),
        FROZEN_PRODUCTS("frozenProducts", "冻损商品 0:否 1:是", (cargoSignEnum, cargoSignMap) -> cargoSignMap == null ? null : cargoSignMap.get(cargoSignEnum.getKey())),
        OUTBOUND_BY_BOX_RULE("outboundByBoxRule", "按理论箱件出库 0:否 1:是", (cargoSignEnum, cargoSignMap) -> cargoSignMap == null ? null : cargoSignMap.get(cargoSignEnum.getKey())),
        ;
        private String key;
        private String desc;
        CargoSignProcess cargoSignProcess;

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) cargoSignProcess.getValue(this, map);
        }
    }


    interface CargoExtendPropsProcess<T> {
        T getValue(CargoExtendPropsEnum propsEnum, Map<String, String> extendProps);
    }

    @Getter
    public enum CargoExtendPropsEnum {
        UNIQUE_CODE("uniqueCodeCollectionWay", "唯一码采集管理", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),

        SELLER_CUSTOM_PICTURE_URL("sellerCustomPictureUrl", "商家自定义图片链接地址(吊牌信息)", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        CARGO_BRAND("cargoBrand", "货品品牌", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        BRAND_NO("brandNo", "品牌编码", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        INSURE_PRICE("insurePrice", "保价金额", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        ACTUAL_RECEIVABLE_QUANTITY("actualReceivableQuantity", "实际可签收数量", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        PACKAGEING_DETAILS("packagingDetails", "包装系数", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        CONSIGNEE_WAREHOUSE_NAME("consigneeWarehouseName", "收货库房", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        CONSIGNEE_LOC_NAME("consigneeLocName", "收货储位", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        CARGO_CUSTOM_FIELD("cargoCustomField", "商家个性化属性", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        SELLER_GOODS_REMARK("sellerGoodsRemark", "货品备注", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        AVIATION_REG_COD("aviationRegCod", "航空备案编号", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        TRACEABILITY_CODE_MANAGE("traceabilityCodeManage", "是否溯源码管理", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        EXT_COLLECTION_UNIQ_CODE("extCollectionUniqCode", "是否采集唯一码-外系统", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        WHOLE_BOX_OUT("wholeBoxOut", "整箱出标识、箱规、及母品SKU", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        STANDARD_WEIGHT("standardWeight", "标称重量", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        PRODUCTION_WAY("productionWay", "货品生产方式", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        WEIGH_PIECE_WORK_QUANTITY("weighPieceworkQuantity", "计划称重计件个数", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        PROCESSING_SERVICES_TYPE("processingServicesType", "内加工服务", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        CUSTOMER_PROCESSING_INFO("customerProcessingInfo", "加工信息", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        ;
        private String key;
        private String desc;
        CargoExtendPropsProcess propsProcess;

        CargoExtendPropsEnum(String key, String desc, CargoExtendPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) propsProcess.getValue(this, map);
        }
    }

    interface CargoSubExtendPropsProcess<T> {
        T getValue(CargoSubExtendPropsEnum propsEnum, Map<String, String> extendProps);
    }

    @Getter
    public enum CargoSubExtendPropsEnum {
        PRINT_NAME("printName", "货品打印名称", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        BATCH_RANGE("batchRange", "货品扩展大字段", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        WEAK_BATCH_RANGE("weakBatchRange", "弱指定批属性范围", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey()));
        private String key;
        private String desc;
        CargoSubExtendPropsProcess propsProcess;

        CargoSubExtendPropsEnum(String key, String desc, CargoSubExtendPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null || !map.containsKey("cargoExtendProps")) {
                return null;
            }
            Map<String, String> cargoSubExtendProps = null;
            try {
                cargoSubExtendProps = JsonUtil.readValue(map.get("cargoExtendProps"), Map.class);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return (T) propsProcess.getValue(this, cargoSubExtendProps);
        }
    }

    interface GoodsExtendPropsProcess<T> {
        T getValue(GoodsExtendPropsEnum propsEnum, Map<String, String> extendProps);
    }

    @Getter
    public enum GoodsExtendPropsEnum {
        AGENT_SALE_GOODSNO("agentSaleGoodsNo", "代销商品编码", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        AGENT_SALE_GOODS("agentSaleInfos", "代销商品编码", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        SALES_ATTRIBUTE("salesAttribute", "商品销售属性", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey())),
        GOODS_EDI_REMARK("goodsEdiRemark", "商家自定义商品信息", (propsEnum, props) -> props == null ? null : props.get(propsEnum.getKey()));
        private String key;
        private String desc;
        GoodsExtendPropsProcess propsProcess;

        GoodsExtendPropsEnum(String key, String desc, GoodsExtendPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) propsProcess.getValue(this, map);
        }
    }


    interface SmartPatternPropsProcess<T> {
        T getValue(SmartPatternPropsEnum propsEnum, Map<String, String> extendProps);
    }

    @Getter
    public enum SmartPatternPropsEnum {
        EXPECT_OUTBOUND_TIME("expectOutboundTime", "期望出库完成时间", (propsEnum, props) -> props.get(propsEnum.getKey())),
        ORDER_TEMPORARY_STORAGE("orderTemporaryStorage", "临时存储位置:1-暂存在出库前；2-暂存在出库后交接前；3-暂存在末端分拣中心；4-暂存在末端站点；",
                (propsEnum, props) -> props.get(propsEnum.getKey()) == null ? null : props.get(propsEnum.getKey())),
        OUTBOUND_PRIORITY("outboundPriority", "仓储生产优先级",
                (SmartPatternPropsProcess<Integer>) (propsEnum, extendProps) -> {
                    if (extendProps.get(propsEnum.getKey()) != null) {
                        String propValue = extendProps.get(propsEnum.getKey());
                        if (StringUtils.isBlank(propValue)) {
                            return null;
                        }
                        return Integer.parseInt(propValue);
                    }
                    return null;
                }),
        OUTBOUND_URGENCY("outboundUrgency", "仓库生产加急: 0-否、1-陆运加急、2-航空加急", (propsEnum, props) -> props.get(propsEnum.getKey()))
        ;
        private String key;
        private String desc;
        SmartPatternPropsProcess propsProcess;

        SmartPatternPropsEnum(String key, String desc, SmartPatternPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) propsProcess.getValue(this, map);
        }
    }

    interface ShipmentExtendPropsProcess<T> {
        T getValue(ShipmentPropsEnum propsEnum, Map<String, String> extendProps);
    }

    @Getter
    public enum ShipmentPropsEnum {
        MARINE_CONTRABAND("marineContraband", "海运违禁品", (propsEnum, props) -> props.get(propsEnum.getKey())),
        NEXT_DAY_AGING_PROMOTION("nextDayAgingPromotion", "次日达时效提升", (propsEnum, props) -> props.get(propsEnum.getKey())),
        SELF_PICKUP_TYPE("selfPickupType", "sop自提：0 sop自提柜、7 sop自提店", (propsEnum, props) -> props.get(propsEnum.getKey())),
        DATA_TRANSFER_REQUIREMENTS("dataTransferRequirements", "数据传输要求", (propsEnum, props) -> props.get(propsEnum.getKey())),
        ;
        private String key;
        private String desc;
        ShipmentExtendPropsProcess propsProcess;

        ShipmentPropsEnum(String key, String desc, ShipmentExtendPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) propsProcess.getValue(this, map);
        }
    }

    interface SpecialSignPropsProcess<T> {
        T getValue(SpecialSignPropsEnum specialSignPropsEnum, Map<String, String> specialSignRequirements);
    }

    @Getter
    public enum SpecialSignPropsEnum {
        SIGN_TYPE("signType", "签收类型", (propsEnum, props) -> props.get(propsEnum.getKey())),
        SIGN_ID_CODE("signIDCode", "签收验证码", (propsEnum, props) -> props.get(propsEnum.getKey())),
        ACTIVATION_SERVICE("activationService", "激活号卡", (propsEnum, props) -> props.get(propsEnum.getKey())),
        NON_SELF_SIGN_CALL("nonSelfSignCall", "非本人签收强制外呼标识", (propsEnum, props) -> props.get(propsEnum.getKey()));
        private String key;
        private String desc;
        SpecialSignPropsProcess propsProcess;

        SpecialSignPropsEnum(String key, String desc, SpecialSignPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) propsProcess.getValue(this, map);
        }
    }

    interface ExtendPropsProcess<T> {
        T getValue(ExtendPropsEnum propsEnum, Map<String, String> extendProps);
    }


    @Getter
    public enum ExtendPropsEnum {
        ORDER_MARK("orderMark", "eclp订单标记位", (propsEnum, props) -> props.get(propsEnum.getKey())),
        SENDPAY("sendpay", "eclp订单标记位", (propsEnum, props) -> props.get(propsEnum.getKey())),
        ORDER_SELL_MODEL("orderSellModel", "零售订单销售模式,1:B2C订单, 2:B2B订单，不指定默认1",
                (propsEnum, props) -> props.get(propsEnum.getKey()) == null ? null : Integer.valueOf((String) props.get(propsEnum.getKey()))),
        ECP_ORDER_INFO("ecpOrderInfo", "零售订单信息", (propsEnum, props) -> props.get(propsEnum.getKey())),
        AGENT_SALES("agentSales", "代销标识", (propsEnum, props) -> "2".equals(props.get(propsEnum.getKey()))),
        PRE_SALE_STAGE("preSaleStage", "预售阶段", (propsEnum, props) -> propsEnum.getKey() == null || props.get(propsEnum.getKey()) == null ? null : Integer.valueOf((String) props.get(propsEnum.getKey()))),
        FLASH_SALE("flashSale", "闪购订单", (propsEnum, props) -> props.get(propsEnum.getKey())),
        FULFILLMENT_INFO("fulfillmentInfo", "履约信息", (propsEnum, props) -> props.get(propsEnum.getKey())),
        PRINT_EXTEND_INFO("printExtendInfo", "打印信息", (propsEnum, props) -> props.get(propsEnum.getKey())),
        APPRAISAL_INSTITUTION_NAME("appraisalInstitutionName", "鉴定单信息", (propsEnum, props) -> props.get(propsEnum.getKey())),
        PROFESSION_TYPE("professionType", "行业标识（78、79标位组合）", (propsEnum, props) -> props.get(propsEnum.getKey())),
        SO_SOURCE("soSource", "订单来源(1:ISV，2:POP)", (propsEnum, props) -> props.get(propsEnum.getKey()) == null ? null : Integer.valueOf((String) props.get(propsEnum.getKey()))),
        BATCH_NO("batchNo", "集单批次号", (propsEnum, props) -> props.get(propsEnum.getKey())),
        BATCH_QUANTITY("batchQuantity", "集单批次数", (propsEnum, props) -> props.get(propsEnum.getKey())),
        CAR_BUSINESS_LINE("carBusinessLine", "汽车业务线标记", (propsEnum, props) -> props.get(propsEnum.getKey()) == null ? null : props.get(propsEnum.getKey())),
        PAY_TIME("payTime", "支付时间", (propsEnum, props) -> props.get(propsEnum.getKey())),
        MERGE_STATUS("mergeStatus", "合单状态", (propsEnum, props) -> props.get(propsEnum.getKey()) == null ? null : props.get(propsEnum.getKey())),
        ORDER_CLOB_OSS_KEY("orderClobIssueOssKey", "订单中心大报文ossKey", (propsEnum, props) -> props.get(propsEnum.getKey()) == null ? null : props.get(propsEnum.getKey()))
        ;

        private String key;
        private String desc;
        ExtendPropsProcess propsProcess;

        ExtendPropsEnum(String key, String desc, ExtendPropsProcess propsProcess) {
            this.key = key;
            this.desc = desc;
            this.propsProcess = propsProcess;
        }

        public <T> T getValue(Map<String, String> map) {
            if (map == null) {
                return null;
            }
            return (T) propsProcess.getValue(this, map);
        }
    }

    @Getter
    public enum FulfillmentInfoEnum {
        SO_MAIN_PARAM_JFS_KEY("soMainParamJfsKey", "JFS字段"),
        FLASH_SALE_GOODS_TYPE("flashSaleGoodsType", "闪购订单商品类型"),
        ;
        private String key;
        private String desc;

        FulfillmentInfoEnum(String key, String desc) {
            this.key = key;
            this.desc = desc;
        }

    }

    private BillingTypeEnum buildBillingType(OrderData input) {
        if (!input.getExtendProps().containsKey("orderMark")) {
            return null;
        }
        String orderMark = input.getExtendProps().get("orderMark");
        BillingTypeEnum billingTypeEnum = BillingTypeEnum.fromCode(Character.getNumericValue(orderMark.charAt(10)));
        if (billingTypeEnum.equals(BillingTypeEnum.DEFAULT)) {
            return null;
        }
        return billingTypeEnum;
    }

    private List<com.jdl.sc.ofw.outbound.dto.vo.StockDetail> buildStockDetail(Map<String, String> extendProps) {
        if (MapUtils.isEmpty(extendProps)
                || !extendProps.containsKey("stockShorts")
                || StringUtils.isBlank(extendProps.get("stockShorts"))) {
            return null;
        }

        List<StockDetail> stockDetails = null;
        try {
            stockDetails = JsonUtil.readList(extendProps.get("stockShorts"), StockDetail.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(stockDetails)) {
            return null;
        }

        return stockDetails.stream().map(stockDetail -> com.jdl.sc.ofw.outbound.dto.vo.StockDetail.builder()
                .isvCargoNo(stockDetail.getIsvGoodsNo())
                .cargoNo(stockDetail.getGoodsNo())
                .useStockNum(stockDetail.getUseStockNum())
                .useShortNum(stockDetail.getUseShortNum())
                .realShortNum(stockDetail.getRealShortNum())
                .warehouseNo(String.valueOf(stockDetail.getWarehouseId()))
                .partialFulfillment(stockDetail.isPart())
                .build()).collect(Collectors.toList());
    }

    private List<com.jdl.sc.ofw.outbound.dto.vo.BatchInfo> buildBatchInfos(List<BatchInfo> batchInfos) {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return null;
        }
        return batchInfos.stream().map(batchInfo -> com.jdl.sc.ofw.outbound.dto.vo.BatchInfo.builder()
                .quantity(Quantity.builder()
                        .unit(batchInfo.getQuantity().getUnit())
                        .value(batchInfo.getQuantity().getValue())
                        .build())
                .batchSource(batchInfo.getBatchSource())
                .attributeInfoList(buildBatchAttribute(batchInfo.getAttributes()))
                .batchAttr(buildBatchAttr(batchInfo.getAttributes()))
                .batchAttrList(buildBatchAttrList(batchInfo.getAttributes()))
                .build()).collect(Collectors.toList());
    }

    private List<com.jdl.sc.ofw.outbound.dto.vo.AttributeInfo> buildBatchAttribute(List<AttributeInfo> attributeInfoList) {
        return attributeInfoList.stream().map(attributeInfo -> com.jdl.sc.ofw.outbound.dto.vo.AttributeInfo.builder()
                .key(attributeInfo.getKey())
                .value(attributeInfo.getValue())
                .build()).collect(Collectors.toList());
    }

    /**
     * 将批属性信息提前加工用于后续计算使用
     * 加工格式为 [{key:value}....]
     *
     * @param attributeInfoList
     * @return
     */
    private List<String> buildBatchAttrList(List<AttributeInfo> attributeInfoList) {
        if (CollectionUtils.isEmpty(attributeInfoList)) {
            return null;
        }
        List<String> attributeInfos = attributeInfoList.stream().map(this::attrInfoToStr).collect(Collectors.toList());
        attributeInfos.sort(Comparator.comparing(String::hashCode));
        return attributeInfos;
    }

    private boolean buildHeavyGoodsUpstairs(ShipmentInfo shipmentInfo) {
        if (shipmentInfo == null) {
            return false;
        }
        return shipmentInfo.getServiceRequirements() != null && "1".equals(shipmentInfo.getServiceRequirements().get("heavyGoodsUpstairsFlag"));
    }

    private boolean buildHidePrivacyType(ShipmentInfo shipmentInfo) {
        if (shipmentInfo == null) {
            return false;
        }
        return shipmentInfo.getServiceRequirements() != null && "1".equals(shipmentInfo.getServiceRequirements().get("hidePrivacyType"));
    }

    /**
     * 将批属性信息提前加工用于后续计算使用
     * 先针对批属性key做排序，在进行加工
     * 加工格式为 value-value-value...
     *
     * @param attributeInfoList
     * @return
     */
    private String buildBatchAttr(List<AttributeInfo> attributeInfoList) {
        if (CollectionUtils.isEmpty(attributeInfoList)) {
            return null;
        }
        attributeInfoList.sort(Comparator.comparing(AttributeInfo::getKey));
        return attributeInfoList.stream().map(AttributeInfo::getValue).collect(Collectors.joining(";"));
    }

    private String mapAttrToStringType(Map<String, String> map, String key) {
        return MapUtils.isEmpty(map) ? null : map.get(key);
    }

    private Integer mapAttrToIntegerType(Map<String, String> map, String key) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return ObjectUtils.nullIfNull(map.get(key), Integer::valueOf);
    }

    private boolean mapAttrInfoToBooleanType(Map<String, String> map, String key) {
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        return "1".equals(map.get(key));
    }

    private String attrInfoToStr(AttributeInfo attributeInfo) {
        return attributeInfo.getKey() + ":" + attributeInfo.getValue();
    }

    private Date getShipmentTextDate(ShipmentInfo shipmentInfo, String key) {
        String shipmentTextAttr = getShipmentTextAttr(shipmentInfo, key);
        return StringUtils.isEmpty(shipmentTextAttr) ? null : DateUtil.stringToDate(shipmentTextAttr);
    }

    private Integer getShipmentTextType(ShipmentInfo shipmentInfo, String key) {
        String shipmentTextAttr = getShipmentTextAttr(shipmentInfo, key);
        return StringUtils.isEmpty(shipmentTextAttr) ? null : Integer.parseInt(shipmentTextAttr);
    }

    private Boolean getShipmentBooleanType(ShipmentInfo shipmentInfo, String key) {
        String shipmentTextAttr = getShipmentTextAttr(shipmentInfo, key);
        return "1".equals(shipmentTextAttr);
    }

    private String getShipmentTextAttr(ShipmentInfo shipmentInfo, String key) {
        if (shipmentInfo == null || shipmentInfo.getExtendProps() == null
                || !shipmentInfo.getExtendProps().containsKey("shipmentExtendProps")) {
            return null;
        }
        String shipmentText = shipmentInfo.getExtendProps().get("shipmentExtendProps");
        if (StringUtils.isEmpty(shipmentText)) {
            return null;
        }
        JsonNode jsonNode = JsonUtil.toJsonNodeSafe(shipmentText);
        return jsonNode == null ? null : jsonNode.path(key).textValue();
    }

    public com.jdl.sc.ofw.outbound.dto.vo.Invoice buildInvoice(Invoice invoice) {
        List<com.jdl.sc.ofw.outbound.dto.vo.InvoiceDetail> invoiceDetailList = null;
        if (!Objects.isNull(invoice) && org.apache.commons.collections.CollectionUtils.isNotEmpty(invoice.getInvoiceDetailDTOList())) {
            invoiceDetailList = invoice.getInvoiceDetailDTOList().stream().map(invoiceDetailDTO -> com.jdl.sc.ofw.outbound.dto.vo.InvoiceDetail.builder()
                    .goodsName(invoiceDetailDTO.getGoodsName())
                    .amount(invoiceDetailDTO.getAmount())
                    .rate(invoiceDetailDTO.getRate())
                    .price(invoiceDetailDTO.getPrice())
                    .quantity(invoiceDetailDTO.getQuantity())
                    .remark(invoiceDetailDTO.getRemark())
                    .type(invoiceDetailDTO.getType())
                    .unit(invoiceDetailDTO.getUnit())
                    .build()
            ).collect(Collectors.toList());
        }
        return Objects.isNull(invoice)
                ? null
                : com.jdl.sc.ofw.outbound.dto.vo.Invoice.builder()
                .invoiceTax(invoice.getInvoiceTax())
                .elecInvoiceUrl(invoice.getElecInvoiceUrl())
                .invoiceContent(invoice.getInvoiceContent())
                .invoiceTitle(invoice.getInvoiceTitle())
                .invoiceSource(invoice.getInvoiceSource())
                .bankAccount(invoice.getBankAccount())
                .printModel(invoice.getPrintModel())
                .invoiceTotalAmount(invoice.getInvoiceTotalAmount())
                .address(invoice.getAddress())
                .phoneNumber(invoice.getPhoneNumber())
                .address(invoice.getAddress())
                .invoiceDetailList(invoiceDetailList)
                .invoiceBiz(invoice.getInvoiceBiz())
                .build();
    }

    private ReReceiveInfo getShipmentExtendProps(ShipmentInfo shipmentInfo) {
        if (shipmentInfo == null) {
            return null;
        }
        String shipmentText = MapUtils.getString(shipmentInfo.getExtendProps(), "shipmentExtendProps");
        if (StringUtils.isEmpty(shipmentText)) {
            return null;
        }
        JsonNode jsonNode = JsonUtil.toJsonNodeSafe(shipmentText);
        String receiveStr = Objects.isNull(jsonNode) || Objects.isNull(jsonNode.findPath("reReceiveInfo"))
                ? null : jsonNode.findPath("reReceiveInfo").toString();
        if (StringUtils.isEmpty(receiveStr)) {
            return null;
        }
        ReReceiveInfo reReceiveInfo = JsonUtil.readValueSafe(receiveStr, ReReceiveInfo.class);
        return reReceiveInfo == null ? null : reReceiveInfo;
    }

    @Getter
    public enum ExtendedFieldEnum {
        SWITCH_PDD_NEW_INTERFACE_KEY("pddNewInterface", "1"),
        ;
        private String key;
        private String value;

        ExtendedFieldEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }
    }

    /**
     * 是否为拆分订单（寻源拆分、普通子单）
     */
    private boolean isSplitOrder(OrderData order) {
        if (MapUtils.isEmpty(order.getOrderSign())) {
            return false;
        }
        //寻源拆分订单
        if (ORDER_SIGN_SOURCING_SPLIT_VALUE.equals(order.getOrderSign().get(ORDER_SIGN_SOURCING_SPLIT_KEY))) {
            return true;
        }
        //大小件同仓拆单
        if (ORDER_SIGN_SPLIT_ORDER_SUB_VALUE.equals(order.getOrderSign().get(ORDER_SIGN_SPLIT_ORDER_KEY))) {
            return true;
        }
        return false;
    }

    private String getChannelOrderNo(ChannelInfo channelInfo) {
        if (MapUtils.isEmpty(channelInfo.getExtendProps())) {
            return null;
        }
        return channelInfo.getExtendProps().get(PARENT_CHANNEL_ORDER_NO_KEY);
    }

    private String getCustomerOrderNo(ChannelInfo channelInfo) {
        if (MapUtils.isEmpty(channelInfo.getExtendProps())
                || !channelInfo.getExtendProps().containsKey(PARENT_CUSTOMER_ORDER_NO_KEY)) {
            throw new ParameterInvalidException(UnifiedErrorSpec.Business.ORDER_ADAPT_EXCEPTION)
                    .withCustom("拆分订单，客户单号未下传");
        }
        String sourceOrderNo = channelInfo.getExtendProps().get(PARENT_CUSTOMER_ORDER_NO_KEY);
        if (StringUtils.isEmpty(sourceOrderNo)) {
            throw new ParameterInvalidException(UnifiedErrorSpec.Business.ORDER_ADAPT_EXCEPTION)
                    .withCustom("拆分订单，客户单号未下传");
        }
        return sourceOrderNo;
    }

    private String getChildrenOrders(String orderNo, ChannelInfo channelInfo, String key) {
        if (MapUtils.isEmpty(channelInfo.getExtendProps())) {
            return null;
        }
        String childOrderNos = channelInfo.getExtendProps().get(key);
        alarmLength(orderNo, childOrderNos, key);
        return childOrderNos;
    }

    private void alarmLength(String orderNo, String childOrderNos, String key) {
        if (StringUtils.isEmpty(childOrderNos)) {
            return;
        }
        try {
            if (childOrderNos.split(CHILD_ORDER_NOS_SPLIT).length > 500) {
                Profiler.businessAlarm(CHILD_ORDER_NOS_LENGTH_UMP_KEY, System.currentTimeMillis(), "订单:" + orderNo + "，合单场景-子单号(" + key + ")长度 > 500");
            }
        } catch (Exception e) {
            log.warn("订单:{}，合单场景-子单号({})长度 > 500 业务告警出现异常", orderNo, key, e);
        }
    }
}
