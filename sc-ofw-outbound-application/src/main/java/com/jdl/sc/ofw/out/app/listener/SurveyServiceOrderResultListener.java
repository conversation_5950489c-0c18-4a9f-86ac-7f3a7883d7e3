package com.jdl.sc.ofw.out.app.listener;

import com.github.jknack.handlebars.internal.lang3.StringUtils;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.ofw.out.app.listener.dto.SurveyResultInfo;
import com.jdl.sc.ofw.out.app.listener.dto.SurveyServiceOrderResultMessage;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractFilterEnvMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.mq.MessageReceive;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.dto.request.ResumeRequest;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 监听tms_service_feedback
 */
@Slf4j
public class SurveyServiceOrderResultListener extends AbstractFilterEnvMessageTemplate {

    @Resource
    private OrderHoldAcl orderHoldAcl;

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private OrderWorkflowLockAbility orderWorkflowLockAbility;

    /** 不符合入户条件 */
    private final static Integer NOT_ELIGIBLE = 0;

    public SurveyServiceOrderResultListener(@Qualifier("eclpBdPredicate") MessageReceive messageReceive) {
        super(messageReceive);
    }

    @Override
    protected void onMessage(String message) throws Exception {
        if (message == null) {
            if (log.isInfoEnabled()) {
                log.info("OmsOrderStatusListener.message为空消息无需处理");
            }
            return;
        }
        SurveyServiceOrderResultMessage resultMessage;
        try {
            resultMessage = JsonUtil.readValue(message, SurveyServiceOrderResultMessage.class);
        } catch (Exception e) {
            log.error("SurveyServiceOrderResultListener.onMessage 序列化异常", e);
            return;
        }

        String validateResult = validate(resultMessage);
        if (StringUtils.isNotBlank(validateResult)) {
            log.info(validateResult);
            return;
        }

        SurveyResultInfo resultInfo;
        try {
            resultInfo = JsonUtil.readValue(resultMessage.getSurveyResultInfo(), SurveyResultInfo.class);
        } catch (Exception e) {
            log.error("SurveyServiceOrderResultListener.SurveyResultInfo 序列化异常", e);
            throw new RuntimeException("SurveyServiceOrderResultListener.SurveyResultInfo 序列化异常:" + e.getMessage());
        }

        //勘测成功 且 支架自备 更新BracketOrderIssued未已下发辅品订单 并 恢复暂停下发大件中间件
        if (isSurveySuccess(resultMessage, resultInfo)
                && resultInfo.getStentSelfProvided() != null
                && resultInfo.getStentSelfProvided() == 1) {
            OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(resultMessage.getEclpOrderNo());
            if (model == null) {
                log.error("大件勘测结果处理，根据百川单号[{}]查询OFC，返回数据为空", resultMessage.getEclpOrderNo());
                return;
            }
            if (!model.isMainOrderSurveyInitiative()) {
                return;
            }
            //更新为已下发
            modifyBracketOrderIssued(model, resultMessage);
            //异常恢复
            resumePause(resultMessage);
        }

    }

    /**
     * 判断是否勘测成功
     * resultMessage 或 resultMessage.getSurveyResult() 为空 则勘测失败
     * resultInfo 或 resultInfo.getFinalResult() 为空 则勘测失败
     * resultMessage.getSurveyResult为true 且 resultInfo.getFinalResult不为0  则勘测成功
     * @param resultMessage
     * @param resultInfo
     * @return
     */
    private boolean isSurveySuccess(SurveyServiceOrderResultMessage resultMessage, SurveyResultInfo resultInfo) {
        if (resultMessage == null || resultMessage.getSurveyResult() == null) {
            return false;
        }
        if (resultInfo == null || resultInfo.getFinalResult() == null) {
            return false;
        }
        if (!resultMessage.getSurveyResult()) {
            return false;
        }
        return !NOT_ELIGIBLE.equals(resultInfo.getFinalResult());
    }

    /**
     * 恢复暂停
     *
     * @param resultMessage
     */
    private void resumePause(SurveyServiceOrderResultMessage resultMessage) {
        OrderHoldRecord orderHoldRecord = orderHoldAcl.queryOneHoldRecord(QueryRequest.builder()
                .orderNo(resultMessage.getEclpOrderNo())
                .code(PauseReasonEnum.WAITING_FOR_MERCHANTS_TO_ISSUE_BRACKET_ORDER.code())
                .build());
        if (orderHoldRecord == null) {
            return;
        }
        final ResumeRequest resumeRequest = ResumeRequest.builder()
                .orderNo(resultMessage.getEclpOrderNo())
                .operator("SurveyServiceOrderResultListener")
                .code(PauseReasonEnum.WAITING_FOR_MERCHANTS_TO_ISSUE_BRACKET_ORDER.code())
                .build();
        orderHoldAcl.orderHoldResume(resumeRequest);
    }

    /**
     * 更新为已下发
     *
     * @param resultMessage
     */
    private void modifyBracketOrderIssued(OrderFulfillmentModel model, SurveyServiceOrderResultMessage resultMessage) {
        ReentrantLock lock = null;
        try {
            lock = orderWorkflowLockAbility.acquireLock(model.getOrderNo(), "SurveyServiceOrderResultListener.onMessage");
        } catch (AcquireLockException e) {
            log.warn("大件勘测订单:{}, 勘测结果回传获取锁失败！", model.getOrderNo());
            throw e;
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        try {
            FulfillmentCommand fulfillmentCommand = Optional.ofNullable(model.getFulfillmentCommand())
                    .orElseGet(() -> FulfillmentCommand.builder().build());
            fulfillmentCommand.assignBracketOrderIssued(Boolean.TRUE);
            model.assignFulfillmentCommand(fulfillmentCommand);
            outboundModelRepository.persist(model);
        } catch (Exception e) {
            log.error("大件勘测订单:{}, 勘测结果回传更新已下发状态失败！", model.getOrderNo());
            throw e;
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
    }

    private String validate(SurveyServiceOrderResultMessage resultMessage) {
        String validateResult = null;
        if (resultMessage == null) {
            validateResult = "勘测结果消息体为空";
        }
        if (StringUtils.isBlank(resultMessage.getEclpOrderNo())
                || !resultMessage.getEclpOrderNo().startsWith("ESL")) {
            validateResult = "勘测结果消息体中第三方单号为空或非ESL开头";
        }

        if (resultMessage.getSurveyResult() == null || !resultMessage.getSurveyResult()) {
            validateResult = "勘测结果消息体中勘测结果为false";
        }
        return validateResult;
    }
}
