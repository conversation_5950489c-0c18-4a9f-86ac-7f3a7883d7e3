package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Builder
@Getter
@Jacksonized
public class AlphaWaybillInterceptResult {

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 来源  "供应链OFC"
     */
    private String source;

    /**
     * 三方承运商ID
     *  https://cloud.jdl.com/#/open-business-document/access-guide/207/54463
     */
    private Integer providerId;

    /**
     * 三方承运商编码
     */
    private String providerCode;

    /**
     * 三方承运商名称
     */
    private String providerName;

    /**
     * 拦截状态
     */
    private Integer state;

    /**
     * 备注
     */
    private String remark;
}
