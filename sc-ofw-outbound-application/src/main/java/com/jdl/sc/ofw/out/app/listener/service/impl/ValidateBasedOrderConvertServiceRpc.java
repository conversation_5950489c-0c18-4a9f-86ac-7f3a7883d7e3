package com.jdl.sc.ofw.out.app.listener.service.impl;

import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Profile({"!ab"})
public class ValidateBasedOrderConvertServiceRpc implements OrderConvertServiceAcl {
    private static final String SALE_ORDER_PREFIX = "ESL";

    private static final String IDENTITY_ORDER_PREFIX = "ITS";

    @Override
    public String convert(String orderNo) {
        if (!validateSalesOrder(orderNo)) {
            log.info("忽略非销售出库单回传: {}", orderNo);
            return null;
        }
        return orderNo;
    }

    public static boolean validateSalesOrder(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return false;
        }
        return orderNo.startsWith(SALE_ORDER_PREFIX) || orderNo.startsWith(IDENTITY_ORDER_PREFIX);
    }

}
