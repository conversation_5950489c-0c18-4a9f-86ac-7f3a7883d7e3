package com.jdl.sc.ofw.out.app.service.callback;

import com.jdl.sc.ofw.outbound.api.callback.OutboundCallBackService;
import com.jdl.sc.ofw.outbound.dto.callback.OutboundStatusCallBackRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component("outboundCallBackServiceImpl")
@Slf4j
public class OutboundCallBackServiceImpl implements OutboundCallBackService {

    @Override
    public boolean statusCallBack(OutboundStatusCallBackRequest request) {
        return false;
    }
}
