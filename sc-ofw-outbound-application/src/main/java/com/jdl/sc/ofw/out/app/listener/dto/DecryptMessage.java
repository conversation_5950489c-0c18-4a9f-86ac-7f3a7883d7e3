package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.util.List;

@Getter
@Builder
@Jacksonized
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("all")
public class DecryptMessage implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 配运主产品编码
     */
    private String productCode;

    /**
     * 收件人姓名
     */
    private String consigneeName;

    /**
     * 收件人手机号
     */
    private String consigneePhone;

    /**
     * 虚拟号
     */
    private String consigneeMobile;

    /**
     * 收件人手机号
     */
    private String virtualMobile;

    /**
     * 虚拟号过期时间（默认为空)
     */
    private String virtualMobileTime;


    /**
     * 收件人省
     */
    private String provinceName;

    /**
     * 收件人市
     */
    private String cityName;

    /**
     * 收件人县
     */
    private String countyName;

    /**
     * 收件人镇
     */
    private String townName;

    /**
     * 收件人详细地址
     */
    private String consigneeAddress;
    /**
     * 时间戳
     */
    private String ts;

    /**
     * 加密类型
     */
    private String encryptMode;

    /**
     * 增值服务信息
     */
    private List<AddProduct> addedProducts;

}
