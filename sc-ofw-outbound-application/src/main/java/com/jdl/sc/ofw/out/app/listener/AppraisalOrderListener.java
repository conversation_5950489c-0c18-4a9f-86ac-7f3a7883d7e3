package com.jdl.sc.ofw.out.app.listener;

import cn.jdl.oms.core.model.RefOrderInfo;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.app.listener.dto.CreateOutboundOrderMessage;
import com.jdl.sc.ofw.out.app.listener.dto.oms.OrderData;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.EcpOrderInfo;
import com.jdl.sc.ofw.out.app.listener.dto.oms.extend.FulfillmentInfo;
import com.jdl.sc.ofw.out.app.service.OutboundCreateAgent;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 鉴定单MQ监听
 */
@Slf4j
@Component
public class AppraisalOrderListener extends AbstractMessageTemplate {

    @Resource(name = "outboundServiceImpl")
    private OutboundService outboundService;

    @Resource
    private ReceiveOrderTranslator receiveOrderTranslator;

    /**
     * 正品鉴定新业务身份
     */
    @Value("${appraisal.businessUnit}")
    private String newBusinessUnit;
    /**
     * 正品鉴定新业务类型
     */
    @Value("${ofw.standard.businessType}")
    private String newBusinessType;

    @Deprecated
    @Resource
    private OutboundCreateAgent outboundCreateAgent;



    @Override
    protected void onMessage(String message) {
        CreateOutboundOrderMessage createOutboundOrderMessage;
        try {
            createOutboundOrderMessage = JsonUtil.readValue(message, CreateOutboundOrderMessage.class);
        } catch (Exception e) {
            log.error("message 序列化异常", e);
            throw new RuntimeException("message 序列化异常:" + e.getMessage());
        }

        OutboundCreateRequest request;
        try {
            request = this.buildReceiveRequestByOmsMsg(createOutboundOrderMessage.getData());
        } catch (RuntimeException e) {
            log.error("request转化异常", e);
            throw e;
        }
        try {
            this.outboundService.createByAgent(request);
        } catch (Throwable t) {
            log.error("接单失败, failInfo:", t);
            throw t;
        }
    }

    private OutboundCreateRequest buildReceiveRequestByOmsMsg(OrderData input) {
        EcpOrderInfo ecpOrderInfo = EcpOrderInfo.builder().build();
        if (input.getExtendProps() != null &&
                input.getExtendProps()
                        .containsKey(ReceiveOrderTranslator.ExtendPropsEnum.ECP_ORDER_INFO.getKey())) {
            try {
                ecpOrderInfo = JsonUtil.readValue(
                        ReceiveOrderTranslator.ExtendPropsEnum.ECP_ORDER_INFO.
                                getValue(input.getExtendProps()), EcpOrderInfo.class);
            } catch (Exception e) {
                log.error("ecpOrderInfo 序列化异常", e);
                throw new RuntimeException("ecpOrderInfo 序列化异常:" + e.getMessage());
            }
        }

        FulfillmentInfo fulfillmentInfo = FulfillmentInfo.builder().build();
        if (input.getExtendProps() != null &&
                input.getExtendProps()
                        .containsKey(ReceiveOrderTranslator.ExtendPropsEnum.FULFILLMENT_INFO.getKey())) {
            try {
                fulfillmentInfo = JsonUtil.readValue(
                        ReceiveOrderTranslator.ExtendPropsEnum.FULFILLMENT_INFO.getValue(input.getExtendProps()), FulfillmentInfo.class);
            } catch (Exception e) {
                log.error("fulfillmentInfo 序列化异常", e);
                throw new RuntimeException("fulfillmentInfo 序列化异常:" + e.getMessage());
            }
        }

        // 判断运单号是否存在
        RefOrderInfo refOrderInfo = input.getRefOrderInfo();
        if (Objects.isNull(refOrderInfo) || StringUtils.isBlank(refOrderInfo.getWaybillNo())) {
            log.error("refOrderInfo 运单信息为空:{}", JsonUtil.toJsonSafe(refOrderInfo));
            throw new RuntimeException("refOrderInfo 运单信息为空");
        }

        return OutboundCreateRequest.builder()
                .orderNo(input.getOrderNo())
                .orderType(input.getOrderType())
                .businessUnit(newBusinessUnit)
                .businessType(newBusinessType)
                .orderBusinessType(ReceiveOrderTranslator.ExtendPropsEnum.ORDER_SELL_MODEL.getValue(input.getExtendProps()))
                .basicInfo(receiveOrderTranslator.getBasicInfoByOmsInput(input))
                .customer(receiveOrderTranslator.getCustomerByOmsInput(input))
                .cargos(receiveOrderTranslator.getCargosByOmsInput(input))
                .goods(receiveOrderTranslator.getGoodsByOmsInput(input))
                .solution(receiveOrderTranslator.getSolutionByOmsInput(input))
                .products(receiveOrderTranslator.getProductsByOmsInput(input))
                .consignee(receiveOrderTranslator.getConsigneeByOmsInput(input))
                .consignor(receiveOrderTranslator.getConsignorByOmsInput(input))
                .shipment(receiveOrderTranslator.getShipmentByOmsInput(input, null))
                .finance(receiveOrderTranslator.getFinanceOmsByInput(input, ecpOrderInfo))
                .refOrderInfo(receiveOrderTranslator.getRefOrderInfoByOmsInput(input, ecpOrderInfo))
                .channel(receiveOrderTranslator.getChannelByOmsInput(input, ecpOrderInfo, null))
                .remark(input.getRemark())
                .buyerRemark(ecpOrderInfo.getBuyerRemark())
                .sellerRemark(ecpOrderInfo.getSellerRemark())
                .extendProps(receiveOrderTranslator.getPropsForScm(input, fulfillmentInfo))
                .createTime(input.getOrderCreateTime())
                .fulfillment(receiveOrderTranslator.getAppraisalFulfillment(input,fulfillmentInfo))
                .build();
    }

}
