package com.jdl.sc.ofw.out.app.listener.service.impl;

import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.util.ReplayReferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@Profile({"ab"})
public class ReplayBasedOrderConvertServiceRpc implements OrderConvertServiceAcl {

    @Resource
    private ReplayReferService replayReferService;

    @Override
    public String convert(String orderNo) {
        if (!ValidateBasedOrderConvertServiceRpc.validateSalesOrder(orderNo) || orderNo.startsWith("ESL000000")) {
            return null;
        }
        // 传入方法名，参数类型，参数值
        Object result = replayReferService.getReplayService().$invoke("eclpToOfwOrderNo",
                new String[]{"java.lang.String"},
                new Object[]{orderNo}
        );
        if (result == null) {
            log.info("未识别订单: {}", orderNo);
            return null;
        }
        String transformedOrderNo = (String) result;
        //过滤AB环境转换后非ASL单号
        if (!transformedOrderNo.startsWith("ASL")) {
            return null;
        }
        log.info("单号转换, before: {}, after: {}", orderNo, transformedOrderNo);
        return transformedOrderNo;
    }
}
