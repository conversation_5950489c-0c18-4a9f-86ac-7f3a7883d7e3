package com.jdl.sc.ofw.out.app.service;

import com.jdl.sc.ofw.out.common.specification.Notification;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.outbound.api.OutboundQueryService;
import com.jdl.sc.ofw.outbound.dto.OutboundQueryRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component("outboundQueryServiceImpl")
@Slf4j
public class OutboundQueryServiceImpl implements OutboundQueryService {

    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Override
    public OutboundQueryResponse query(OutboundQueryRequest request) {
        if (request == null || StringUtils.isBlank(request.getOrderNo())) {
            throw new IllegalArgumentException("请求参数异常：订单号为空");
        }
        String json = outboundModelRepository.getModelJsonMapByOrderNo(request.getOrderNo());
        return OutboundQueryResponse.builder().data(json).build();
    }
}
