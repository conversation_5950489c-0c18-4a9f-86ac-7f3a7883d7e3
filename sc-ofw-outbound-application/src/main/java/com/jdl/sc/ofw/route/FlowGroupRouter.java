package com.jdl.sc.ofw.route;

import com.jd.jsf.gd.client.GroupRouter;
import com.jd.jsf.gd.config.ConsumerGroupConfig;
import com.jd.jsf.gd.msg.Invocation;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.service.adapter.GrayscaleAdapter;
import com.jdl.sc.ofw.outbound.dto.FlowRestartRequest;
import com.jdl.sc.ofw.outbound.dto.FlowResumeRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCancelRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyWaybillRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundRetransportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * FileName: FlowGroupRouter
 * Description: batrix流程恢复、重启
 * [@author]:   quhuafeng
 * [@date]:     2023/4/264:07 PM
 */
@Slf4j
@Service("flowGroupRouter")
public class FlowGroupRouter implements GroupRouter, InitializingBean {
    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Value("${routing.config}")
    private String routingConfg;

    @Value("${jsf.consumerGroup.alias}")
    private String consumerGroupAlias;

    @Resource
    private GrayscaleAdapter grayscaleAdapter;

    private static Map<String, String> aliasMap = new HashMap<>();

    @Override
    public String router(Invocation invocation, ConsumerGroupConfig config) {
        Object[] objects = invocation.getArgs();
        if(objects.length == 0 ) {
            return "";
        }
        String orderNo = null;
        if(objects[0] instanceof FlowResumeRequest) {
            // 暂停恢复
            FlowResumeRequest orderResumeRequest = (FlowResumeRequest)objects[0];
            orderNo = orderResumeRequest.getOrderNo();
        } else if (objects[0] instanceof OutboundModifyRequest){
            // 修改
            OutboundModifyRequest outboundModifyRequest = (OutboundModifyRequest) objects[0];
            orderNo = outboundModifyRequest.getOrderNo();
        } else if(objects[0] instanceof FlowRestartRequest) {
            // 重启
            FlowRestartRequest orderRestartRequest = (FlowRestartRequest) objects[0];
            orderNo = orderRestartRequest.getOrderNo();
        }else if(objects[0] instanceof OutboundCancelRequest) {
            OutboundCancelRequest request = (OutboundCancelRequest) objects[0];
            orderNo = request.getOrderNo();
        } else if (objects[0] instanceof OutboundRetransportRequest) {
            // 重新下仓
            OutboundRetransportRequest request = (OutboundRetransportRequest) objects[0];
            orderNo = request.getOrderNo();
        }else if (objects[0] instanceof OutboundModifyWaybillRequest) {
            OutboundModifyWaybillRequest request = (OutboundModifyWaybillRequest) objects[0];
            orderNo = request.getOrderNo();
        } else {
            log.error("分流层参数类型匹配失败，请检查该业务是否接入。request:{}", JsonUtil.toJsonSafe(objects));
        }
        if(StringUtils.isEmpty(orderNo)) {
            log.error("分流层请求订单号为空,request:{}", JsonUtil.toJsonSafe(objects));
            return "";
        }
        OrderFulfillmentModel model =  outboundModelRepository.getModelByOrderNo(orderNo);
        if(model == null) {
            log.error("分流层查询订单为空,订单号:{}", orderNo);
            return "";
        }
        String alias =  aliasMap.get(model.getBatrixInfo().getBusinessUnit());

        boolean couldGrayscale = grayscaleAdapter.couldGrayscale(
                model,
                StringUtils.join(".", invocation.getClazzName(), invocation.getMethodName())
        );
        if (couldGrayscale) {
            String _alias = alias;
            if (StringUtils.isBlank(_alias)) {
                _alias = invocation.getAlias();
            }
            _alias += "_backup";
            if (StringUtils.contains(consumerGroupAlias, _alias)) {
                alias = _alias;
            }else {
                log.info("分流层没有找到备用别名：{}", _alias);
            }
        }
        log.info("分流层匹配JSF别名为：{}", alias);
        return alias;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if(this.routingConfg == null) {
            throw new RuntimeException("未加载到分流配置文件");
        }
        log.info("分流配置文件加载：{}", this.routingConfg);
        String[] routeArr = this.routingConfg.split(",");
        for(String route: routeArr) {
            String[] routeMap = route.split(":");
            aliasMap.put(routeMap[0], routeMap[1]);
        }
        log.info("分流规则加载完成,config:{}", JsonUtil.toJsonSafe(aliasMap));
    }
}
