package com.jdl.sc.ofw.out.app.listener.ka;

import com.jdl.sc.ofw.out.app.listener.AbstractSaleOutboundOrderListener;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 无印港澳销售出接单
 * <AUTHOR>
 */
@Component
public class MujiOrderCenterListener extends AbstractSaleOutboundOrderListener {
    public MujiOrderCenterListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                   ReceiveOrderTranslator receiveOrderTranslator,
                                   @Value("${ofw.standard.businessType}")String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }
}
