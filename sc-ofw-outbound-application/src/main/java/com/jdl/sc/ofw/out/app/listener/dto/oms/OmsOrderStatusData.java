package com.jdl.sc.ofw.out.app.listener.dto.oms;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.core.model.ChannelInfo;
import com.fasterxml.jackson.annotation.JsonRootName;
import lombok.Data;

import java.util.Date;

@Data
@JsonRootName(value = "orderInfo")
public class OmsOrderStatusData {

    private String orderNo;

    private BusinessIdentity businessIdentity;

    private ChannelInfo channelInfo;

    private Integer orderStatus;

    private Date orderStatusOperateTime;
}
