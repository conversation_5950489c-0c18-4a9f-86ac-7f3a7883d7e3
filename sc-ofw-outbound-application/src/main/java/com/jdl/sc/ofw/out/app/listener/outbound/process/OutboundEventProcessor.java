package com.jdl.sc.ofw.out.app.listener.outbound.process;

import cn.jdl.oms.search.dto.Order;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.ofw.out.app.listener.dto.OutboundEvent;
import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.app.service.constants.BusinessIdentityEnums;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.dto.ModifyStatusCommand;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.domain.service.OutboundModelService;
import com.jdl.sc.ofw.outbound.exception.AcquireConcurrentLockException;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.dto.request.ResumeRequest;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;

@Slf4j
@Component
public class OutboundEventProcessor {

    @Resource
    private OrderWorkflowLockAbility orderWorkflowLockAbility;

    @Resource
    private OutboundModelService outboundModelService;

    @Resource
    private OrderHoldAcl orderHoldAcl;

    @Value("${mq.consumer.fresh.fulfillment.complete.topic}")
    private String FRESH_FULFILLMENT_COMPLETE_TOPIC;

    public void process(String orderNo) throws Exception  {
        OrderHoldRecord orderHoldRecord = orderHoldAcl.queryOneHoldRecord(QueryRequest.builder()
                .orderNo(orderNo)
                .code(PauseReasonEnum.WAIT_OUTBOUND_RESULT_PAUSE_EXCEPTION.code())
                .build());
        if (orderHoldRecord == null) {
            log.info("未查询到同城速配等待仓出库暂停信息，不执行恢复流程，orderNo:{}", orderNo);
            return;
        }
        ReentrantLock lock;
        try {
            lock = orderWorkflowLockAbility.acquireLock(orderNo, "outboundEventListener.onMessage");
        } catch (AcquireLockException e) {
            log.warn("同城速配订单:{}, 仓出库结果处理获取锁失败！", orderNo);
            throw new AcquireConcurrentLockException();
        }
        try {
            ModifyStatusCommand modifyCommand = ModifyStatusCommand.builder()
                    .orderNo(orderNo)
                    .operator("outboundEventListener")
                    .operateTime(new Date())
                    .status(OrderStatusEnum.OUT_WMS.getCode())
                    .build();
            outboundModelService.modifyStatus(modifyCommand);
        } catch (AcquireLockException e) {
            log.warn("OFW修改状态失败，获取并发错失败，orderNo:{}", orderNo);
            throw new AcquireConcurrentLockException(e.getMessage());
        } catch (Exception e) {
            log.error("OFW修改状态失败，orderNo:{}", orderNo);
            throw new InfrastructureException(UnifiedErrorSpec.Business.MODIFY_FAILED, e)
                    .withCustom("OFW修改状态失败，orderNo:" + orderNo);
        } finally {
            lock.unlock();
        }
        final ResumeRequest resumeRequest = ResumeRequest.builder()
                .orderNo(orderNo)
                .operator("outboundEventListener")
                .code(PauseReasonEnum.WAIT_OUTBOUND_RESULT_PAUSE_EXCEPTION.code())
                .build();
        orderHoldAcl.orderHoldResume(resumeRequest);
    }

}
