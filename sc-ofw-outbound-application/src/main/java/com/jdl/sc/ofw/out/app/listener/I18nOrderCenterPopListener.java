package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class I18nOrderCenterPopListener extends AbstractSaleOutboundOrderListener {
    public I18nOrderCenterPopListener(@Qualifier("outboundServiceImpl") OutboundService outboundService,
                                      ReceiveOrderTranslator receiveOrderTranslator,
                                      @Value("${ofw.standard.businessType}") String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }
}
