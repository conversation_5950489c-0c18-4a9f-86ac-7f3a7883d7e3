package com.jdl.sc.ofw.out.app.service.oms;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.ModifyOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.ModifyOutboundOrderOfcResponse;
import cn.jdl.ofc.supplychain.api.oms.ModifyOutboundOrderOfcService;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.SmartPatternInfo;
import com.google.common.collect.Maps;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.app.service.constants.ResponseConstant;
import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.product.SolutionAttributeEnum;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundModifyResponse;
import com.jdl.sc.ofw.outbound.dto.vo.Cargo;
import com.jdl.sc.ofw.outbound.dto.vo.Channel;
import com.jdl.sc.ofw.outbound.dto.vo.Consignee;
import com.jdl.sc.ofw.outbound.dto.vo.Fulfillment;
import com.jdl.sc.ofw.outbound.dto.vo.Goods;
import com.jdl.sc.ofw.outbound.dto.vo.JdAddress;
import com.jdl.sc.ofw.outbound.dto.vo.OrderSign;
import com.jdl.sc.ofw.outbound.dto.vo.Product;
import com.jdl.sc.ofw.outbound.dto.vo.Shipment;
import com.jdl.sc.ofw.outbound.dto.vo.Solution;
import com.jdl.sc.ofw.outbound.dto.vo.Warehouse;
import com.jdl.sc.ofw.outbound.spec.enums.SignBillTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.dto.request.ResumeRequest;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import com.jdl.sc.order.pause.dto.OrderPauseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service("modifyOutboundOrderOfcService")
public class ModifyOutboundOrderOfcServiceImpl implements ModifyOutboundOrderOfcService {

    private static final String OMS_REACCEPTED_CODE = "1";

    //重货上楼标识 1: 修改成重货上楼
    private static final String HEAVY_GOODS_UPSTAIRS = "1";

    @Resource(name = "outboundServiceImpl")
    private OutboundService outboundService;

    @Resource
    private OrderHoldHandler orderHoldHandler;

    @Resource
    private ReceiveOrderTranslator receiveOrder;

    @Resource
    private OrderHoldAcl orderHoldAcl;

    @Value("${exception.whiteList.forbid:-}")
    private String resumeExceptionUserWhiteList;

    @Override
    public ModifyOutboundOrderOfcResponse modifyOrder(RequestProfile requestProfile, ModifyOutboundOrderOfcRequest request) {
        ModifyOutboundOrderOfcResponse response = new ModifyOutboundOrderOfcResponse();
        response.setCode(String.valueOf(ResponseConstant.SUCCESS_CODE));

        OutboundModifyResponse modifyResponse = outboundService.modify(buildModifyRequestByOfwMsg(request));
        if (!Objects.equals(modifyResponse.getCode(), ResponseConstant.SUCCESS_CODE)) {
            response.setCode(String.valueOf(modifyResponse.getCode()));
            response.setMessage(modifyResponse.getMessage());
            return response;
        }

        if (StringUtils.isNotEmpty(buildSubOrderNos(request.getRefOrderInfo()))) {
            try {
                resumePause(request.getOrderNo(), request.getOperator(), PauseReasonEnum.WAITING_FOR_MERCHANTS_TO_ISSUE_BRACKET_ORDER);
            } catch (Exception e) {
                log.error("异常恢复失败,pauseCode: " + PauseReasonEnum.WAITING_FOR_MERCHANTS_TO_ISSUE_BRACKET_ORDER.getCode() + ", orderNo:" + request.getOrderNo(), e);
                response.setCode(String.valueOf(ResponseConstant.FAILED_CODE));
                response.setMessage("异常恢复失败,pauseCode: " + PauseReasonEnum.WAITING_FOR_MERCHANTS_TO_ISSUE_BRACKET_ORDER.getCode() + ", orderNo:" + request.getOrderNo());
                return response;
            }
        }

        // extendProps为空，不包含异常恢复相关,提前返回
        if (MapUtils.isEmpty(request.getExtendProps())) {
            log.info("request.getExtendProps = null");
            return response;
        }

        String acceptResult = request.getExtendProps() == null ? null : request.getExtendProps().get("acceptResult");

        //单纯的修改动作，不需要进行暂停的恢复处理
        if (StringUtils.isEmpty(acceptResult)) {
            log.info("修改成功");
            return response;
        }

        //开始暂停的相关恢复动作
        String pauseId = request.getExtendProps().get("pauseId");
        String resumeCode = request.getExtendProps().get("resumeCode");
        //历史pop businessUnit数据为空，故默认为：cn_jdl_sc-pop
        String businessUnit = ObjectUtils.defaultIfNull(request.getBusinessIdentity(), BusinessIdentity::getBusinessUnit, "cn_jdl_sc-pop");
        try {


            //疫情暂停强制下发白名单逻辑
            if (StringUtils.isNotBlank(resumeCode)
                    && PauseReasonEnum.FORBID.getCode().equals(resumeCode)
                    && !"OrderPauseAutoResumeJob".equals(request.getOperator())
                    //强制下发人员白名单
                    && !resumeExceptionUserWhiteList.contains(request.getOperator())) {
                log.info("orderNo:{},用户无权限恢复疫情异常", request.getOrderNo());
                response.setCode(String.valueOf(ResponseConstant.FAILED_CODE));
                response.setMessage("用户无权限恢复疫情异常！");
                return response;

            }
            // POP订单拉回 只有POP场景存在 ，写入异常中心及暂停表逻辑
            else if (!OMS_REACCEPTED_CODE.equals(acceptResult) && //写入拉回暂停数据
                    //代表POP场景
                    "cn_jdl_sc-pop".equals(businessUnit)) {
                log.info("orderNo:{},OMS不受理,订单暂停", request.getOrderNo());
                pause(request);
                return response;
            }

            // ISV场景的订单拉回和库存不足异常重新处理，重启流程
            // ISV拉回的处理，拉回异常，订单中心要求重新履约
            else if (isNotPOP(request.getBusinessIdentity()) && request.getExtendProps().containsKey("operationType") &&
                    //订单拉回
                    ("300".equals(request.getExtendProps().get("operationType")) ||
                            //库存不足
                            "100".equals(request.getExtendProps().get("operationType")))) {
                orderHoldHandler.restart(request.getOrderNo(), request.getOperator());
                return response;
            }

            // step running 执行暂停恢复逻辑
            // 订单中心要求OFW重新履约
            boolean resumeSucceeded = orderHoldHandler.resume(request.getOrderNo(), resumeCode, request.getOperator());
            log.info("OMS重新受理成功，恢复执行暂停的履约, pauseId: {}，结果:{}", pauseId, resumeSucceeded);
            if (!resumeSucceeded) {
                throw new RuntimeException("异常恢复失败");
            }
        } catch (Exception e) {
            log.error("异常处理失败,pauseId: " + pauseId + ", orderNo:" + request.getOrderNo(), e);
            response.setCode(String.valueOf(ResponseConstant.FAILED_CODE));
            response.setMessage("异常处理失败");
        }


        return response;
    }

    /**
     * 恢复暂停
     *
     */
    private void resumePause(String orderNo, String operator, PauseReasonEnum pauseReasonEnum) {
        OrderHoldRecord orderHoldRecord = orderHoldAcl.queryOneHoldRecord(QueryRequest.builder()
                .orderNo(orderNo)
                .code(pauseReasonEnum.code())
                .build());
        if (orderHoldRecord == null) {
            return;
        }
        final ResumeRequest resumeRequest = ResumeRequest.builder()
                .orderNo(orderNo)
                .operator(operator)
                .code(pauseReasonEnum.code())
                .build();
        orderHoldAcl.orderHoldResume(resumeRequest);
    }

    //POP场景拉回，与ISV流程同步后下线，暂时在OFW写入
    private void pause(ModifyOutboundOrderOfcRequest request) {
        OrderPauseInfo pauseInfo = OrderPauseInfo.builder()
                .orderNo(request.getOrderNo())
                .operateUser(request.getOperator())
                .pauseReason(PauseReasonEnum.PULL_BACK)
                .build();
        if (!orderHoldHandler.pause(pauseInfo)) {
            log.error("异常暂停处理失败, orderNo:{}", request.getOrderNo());
            throw new RuntimeException(request.getOrderNo() + "异常暂停处理失败");
        }
    }

    private OutboundModifyRequest buildModifyRequestByOfwMsg(ModifyOutboundOrderOfcRequest omsRequest) {
        return OutboundModifyRequest.builder()
                .orderNo(omsRequest.getOrderNo())
                .operateTime(new Date())
                .operator(omsRequest.getOperator())
                .consignee(buildConsignee(omsRequest.getConsigneeInfo()))
                .cargos(buildCargos(omsRequest.getBusinessIdentity(), omsRequest.getCargoInfos()))
                .occupyResult(buildOccupyResult(omsRequest.getSmartPatternInfo()))
                .heavyGoodsUpstairs(buildHeavyGoodsUpStairs(omsRequest))
                .extendProps(omsRequest.getExtendProps())
                .warehouse(buildWarehouseInfo(omsRequest))
                .outboundUrgency(buildOutboundUrgency(omsRequest.getSmartPatternInfo()))
                .shouldPayMoney(buildCollectionMoney(omsRequest.getShipmentInfo()))
                .purchaseOrderNo(buildPurchaseOrderNo(omsRequest.getRefOrderInfo()))
                .products(buildProducts(omsRequest.getProductInfos()))
                .deliveryIntoWarehouse(buildDeliveryIntoWarehouse(omsRequest.getProductInfos()))
                .enterHouseAppointment(buildEnterHouseAppointment(omsRequest.getShipmentInfo()))
                .shipment(buildShipment(omsRequest.getShipmentInfo(), omsRequest.getExtendProps()))
                .solution(buildSolution(omsRequest.getShipmentInfo()))
                .fulfillment(this.buildFulfillment(omsRequest.getExtendProps()))
                .reReceiveMode(omsRequest.getExtendProps() == null ? null : omsRequest.getExtendProps().get("reReceiveMode"))
                .waybillNo(omsRequest.getRefOrderInfo() == null ? null : omsRequest.getRefOrderInfo().getWaybillNo())
                .deliveryOrderNo(omsRequest.getRefOrderInfo() == null ? null : omsRequest.getRefOrderInfo().getDeliveryOrderNo())
                .fulfillmentThirdWaybillNo(buildFulfillmentThirdWaybillNo(omsRequest.getRefOrderInfo()))
                .systemCaller(omsRequest.getChannelInfo() == null ? null : omsRequest.getChannelInfo().getSystemCaller())
                .orderSign(buildOrderSign(omsRequest.getOrderSign()))
                .channel(buildChannel(omsRequest.getChannelInfo()))
                .goods(buildGoods(omsRequest.getBusinessIdentity(), omsRequest.getGoodsInfos()))
                .subOrderNos(buildSubOrderNos(omsRequest.getRefOrderInfo()))
                .build();
    }

    private String buildSubOrderNos(RefOrderInfo refOrderInfo) {
        if (refOrderInfo == null || MapUtils.isEmpty(refOrderInfo.getExtendProps())) {
            return null;
        }
        return refOrderInfo.getExtendProps().get("subOrderNos");
    }

    @Nullable
    private Boolean buildEnterHouseAppointment(ShipmentInfo shipmentInfo) {
        if (shipmentInfo == null || MapUtils.isEmpty(shipmentInfo.getServiceRequirements())) {
            return null;
        }
        String enterHouseAppointment = shipmentInfo.getServiceRequirements().get("deliveryIntoWarehouse");
        if (enterHouseAppointment == null) {
            return null;
        }
        return "1".equals(enterHouseAppointment);
    }
    private Fulfillment buildFulfillment(Map<String, String> extendProps) {
        return Optional.ofNullable(extendProps)
                .map(props -> props.get("customerSignforAttributes"))
                .map(attr -> Fulfillment.builder()
                        .customerSignforAttributes(attr)
                        .build())
                .orElse(null);
    }


    /**
     * <a href="https://cf.jd.com/pages/viewpage.action?pageId=907600593">OMS(ToB)配送要求说明-修改</a>
     */
    public Shipment buildShipment(ShipmentInfo shipmentInfo, Map<String, String> extendProps) {
        if (shipmentInfo == null && extendProps == null) {
            return null;
        }
        Shipment.ShipmentBuilder shipmentBuilder = Shipment.builder();
        if (shipmentInfo != null) {
            Map<String, String> serviceRequirements = shipmentInfo.getServiceRequirements();
            Map<String, String> specialSignRequirements = null;
            if (serviceRequirements != null && serviceRequirements.get(ShipperConstants.SPECIAL_SIGN_REQUIREMENTS) != null) {
                specialSignRequirements = JsonUtil.readValueSafe(serviceRequirements.get(ShipperConstants.SPECIAL_SIGN_REQUIREMENTS), Map.class);
            }
            final Map<String, String> shipmentInfoExtendProps = Optional.ofNullable(shipmentInfo.getExtendProps()).orElse(Maps.newHashMap());
            shipmentBuilder.shipperNo(shipmentInfo.getShipperNo())
                    .shipperName(shipmentInfo.getShipperName())
                    .shipperType(shipmentInfo.getShipperType())
                    .halfReceive(shipmentInfo.getSignandHalfReturn())
                    .signType(ReceiveOrderTranslator.SpecialSignPropsEnum.SIGN_TYPE.getValue(specialSignRequirements))
                    .platformLogisticsService(shipmentInfoExtendProps.get("platformLogisticsService"))
                    .expectDeliveryStartTime(shipmentInfo.getExpectDeliveryStartTime())
                    .expectDeliveryEndTime(shipmentInfo.getExpectDeliveryEndTime());
        }
        if (extendProps != null) {
            shipmentBuilder.signBillTypeEnum(SignBillTypeEnum.fromCode(MapUtils.getString(extendProps, "reReceiveMode")));
        }
        return shipmentBuilder.build();
    }

    public Solution buildSolution(ShipmentInfo shipmentInfo) {
        if (shipmentInfo == null || shipmentInfo.getServiceRequirements() == null) {
            return null;
        }
        return Solution.builder()
                .attributes(shipmentInfo.getServiceRequirements())
                .build();
    }

    private Boolean buildDeliveryIntoWarehouse(List<ProductInfo> productInfos) {
        if (CollectionUtils.isEmpty(productInfos)) {
            return Boolean.FALSE;
        }
        return productInfos.stream().anyMatch(p -> ProductEnum.LL_DELIVERY_TO_WAREHOUSE_39.getCode().equals(p.getProductNo()));
    }

    private List<Product> buildProducts(List<ProductInfo> productInfos) {
        if (productInfos == null) {
            return null;
        }
        return productInfos.stream().map(p -> Product.builder()
                        .no(p.getProductNo())
                        .name(p.getProductName())
                        .type(p.getProductType())
                        .parentNo(p.getParentNo())
                        .attributes(p.getProductAttrs())
                        .operateType(MapUtils.emptyIfNull(p.getExtendProps()).get("operateType"))
                        .build())
                .collect(Collectors.toList());
    }


    private String buildPurchaseOrderNo(RefOrderInfo refOrderInfo) {
        return refOrderInfo == null ? null : refOrderInfo.getPurchaseOrderNo();
    }

    private Boolean buildHeavyGoodsUpStairs(ModifyOutboundOrderOfcRequest omsRequest) {
        if (omsRequest.getShipmentInfo() == null || omsRequest.getShipmentInfo().getServiceRequirements() == null) {
            return null;
        }
        if (omsRequest.getShipmentInfo().getServiceRequirements().get(SolutionAttributeEnum.HEAVY_GOODS_UPSTAIRS_FLAG.getValue()) == null) {
            return null;
        }
        //1 代表重货上楼
        return HEAVY_GOODS_UPSTAIRS.equals(omsRequest.getShipmentInfo().getServiceRequirements().get(SolutionAttributeEnum.HEAVY_GOODS_UPSTAIRS_FLAG.getValue()));
    }

    private Warehouse buildWarehouseInfo(ModifyOutboundOrderOfcRequest omsRequest) {
        if (omsRequest.getConsignorInfo() == null || omsRequest.getConsignorInfo().getCustomerWarehouse() == null) {
            return null;
        }
        return Warehouse.builder()
                .warehouseNo(omsRequest.getConsignorInfo().getCustomerWarehouse().getActualWarehouseNo())
                .warehouseName(omsRequest.getConsignorInfo().getCustomerWarehouse().getActualWarehouseName())
                .warehouseType(omsRequest.getConsignorInfo().getCustomerWarehouse().getActualWarehouseType())
                .build();
    }

    private Boolean buildOccupyResult(SmartPatternInfo smartPatternInfo) {
        if (smartPatternInfo == null || smartPatternInfo.getStockOperationRule() == null
                || smartPatternInfo.getStockOperationRule().getOccupyResult() == null) {
            return null;
        }
        Integer occupyResult = smartPatternInfo.getStockOperationRule().getOccupyResult();
        return occupyResult == 1;
    }

    private String buildOutboundUrgency(SmartPatternInfo smartPatternInfo) {
        if (smartPatternInfo == null || MapUtils.isEmpty(smartPatternInfo.getExtendProps())) {
            return null;
        }
        return smartPatternInfo.getExtendProps().get("outboundUrgency");
    }

    private String buildCollectionMoney(ShipmentInfo shipmentInfo) {
        if (shipmentInfo == null || MapUtils.isEmpty(shipmentInfo.getServiceRequirements())) {
            return null;
        }
        return shipmentInfo.getServiceRequirements().get("shouldPayMoney");
    }

    private List<Cargo> buildCargos(BusinessIdentity businessIdentity, List<CargoInfo> cargoInfos) {
        if (CollectionUtils.isEmpty(cargoInfos)) {
            return null;
        }
        boolean isPOP = businessIdentity == null || "cn_jdl_sc-pop".equals(businessIdentity.getBusinessUnit());
        return receiveOrder.getCargosByOmsInput(isPOP, cargoInfos);
    }

    private List<Goods> buildGoods(BusinessIdentity businessIdentity, List<GoodsInfo> goodsInfos){
        if (CollectionUtils.isEmpty(goodsInfos)) {
            return null;
        }
        boolean isPOP = businessIdentity == null || "cn_jdl_sc-pop".equals(businessIdentity.getBusinessUnit());
        return receiveOrder.getGoodsByOmsInput(isPOP, goodsInfos);
    }

    private boolean isNotPOP(BusinessIdentity businessIdentity) {
        return businessIdentity != null && !"cn_jdl_sc-pop".equals(businessIdentity.getBusinessUnit());
    }

    private Consignee buildConsignee(ConsigneeInfo consigneeInfo) {
        if (consigneeInfo == null) {
            return null;
        }
        JdAddress jdAddress = null;
        if (consigneeInfo.getAddressInfo() != null) {
            jdAddress = JdAddress.builder()
                    .provinceNo(consigneeInfo.getAddressInfo().getProvinceNo())
                    .provinceName(consigneeInfo.getAddressInfo().getProvinceName())
                    .cityNo(consigneeInfo.getAddressInfo().getCityNo())
                    .cityName(consigneeInfo.getAddressInfo().getCityName())
                    .countyNo(consigneeInfo.getAddressInfo().getCountyNo())
                    .countyName(consigneeInfo.getAddressInfo().getCountyName())
                    .townNo(consigneeInfo.getAddressInfo().getTownNo())
                    .townName(consigneeInfo.getAddressInfo().getTownName())
                    .provinceNoGis(consigneeInfo.getAddressInfo().getProvinceNoGis())
                    .provinceNameGis(consigneeInfo.getAddressInfo().getProvinceNameGis())
                    .cityNoGis(consigneeInfo.getAddressInfo().getCityNoGis())
                    .cityNameGis(consigneeInfo.getAddressInfo().getCityNameGis())
                    .countyNoGis(consigneeInfo.getAddressInfo().getCountyNoGis())
                    .countyNameGis(consigneeInfo.getAddressInfo().getCountyNameGis())
                    .townNoGis(consigneeInfo.getAddressInfo().getTownNoGis())
                    .townNameGis(consigneeInfo.getAddressInfo().getTownNameGis())
                    .detailAddress(consigneeInfo.getAddressInfo().getAddress())
                    .build();
        }
        return Consignee.builder()
                .name(consigneeInfo.getConsigneeName())
                .phone(consigneeInfo.getConsigneePhone())
                .mobile(consigneeInfo.getConsigneeMobile())
                .zipCode(consigneeInfo.getConsigneeZipCode())
                .email(consigneeInfo.getConsigneeEmail())
                .address(jdAddress)
                .virtualNumberExpirationDate(
                        Optional.ofNullable(consigneeInfo.getExtendProps())
                                .map(extendPropsMap -> extendPropsMap.get("virtualExpirationTime"))
                                .orElse(null))
                .encryptMode(
                        Optional.ofNullable(consigneeInfo.getExtendProps())
                                .map(extendPropsMap -> extendPropsMap.get("encryptMode"))
                                .orElse(null))
                .build();
    }

    private String buildFulfillmentThirdWaybillNo(RefOrderInfo refOrderInfo) {
        if (refOrderInfo != null && refOrderInfo.getExtendProps() != null) {
            return refOrderInfo.getExtendProps().get("fulfillmentThirdWaybillNo");
        }
        return null;
    }

    private OrderSign buildOrderSign(Map<String, String> orderSign) {
        if (MapUtils.isEmpty(orderSign)) {
            return null;
        }
        return OrderSign.builder()
                .businessSubsidy(orderSign.get("businessSubsidy"))
                .build();
    }

    private Channel buildChannel(ChannelInfo channelInfo) {
        if (channelInfo == null) {
            return null;
        }
        return Channel.builder()
                .extendProps(channelInfo.getExtendProps())
                .build();
    }
}
