package com.jdl.sc.ofw.out.app.service;

import com.jdl.sc.core.cache.jimdb.JimClient;
import com.jdl.sc.core.jsf.JsfConstants;
import com.jdl.sc.ofw.out.app.translator.OutboundModelTranslator;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.domain.cache.CacheKeyEnum;
import com.jdl.sc.ofw.out.domain.cache.KeyGenerator;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.service.OutboundModelServiceV2;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateRequest;
import com.jdl.sc.ofw.outbound.dto.OutboundCreateResponse;
import com.jdl.sc.ofw.outbound.spec.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Copyright (C), 2021-2023
 * FileName: OutboundCreateAgent
 * Author:   huafeng.qu
 * Date:     2023/2/14 10:30 上午
 * Description: OFC销售出接单新老流程切换代理类
 */
@Component
@Slf4j
public class OutboundCreateAgent implements InitializingBean {

    @Resource
    private OutboundModelServiceV2 newModelService;

    @Resource
    private OutboundModelTranslator outboundModelTranslator;

    @Resource
    private JimClient jimClient;

    /**
     * 销售出接单新老流量切换代理
     *
     * @param request
     * @param forceClean
     * @return
     */
    public OutboundCreateResponse create(OutboundCreateRequest request, boolean forceClean) {
        try {
            OrderFulfillmentModel model = outboundModelTranslator.toModel(request);
            //默认垂直业务身份
            model.getBatrixInfo().setYId("JDL");
            String tid = MDC.get(JsfConstants.LOG4J_MDC_TRACE_ID);
            if (StringUtils.isBlank(tid)) {
                tid = UuidUtil.getTerseUuid();
            }
            log.info("create tid:{}", tid);
            this.newCreateFlow(model, tid, forceClean);
            return OutboundCreateResponse.builder().build();
        } catch (Exception e) {
            log.error("create error,orderNo:{}", request.getOrderNo(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private void newCreateFlow(OrderFulfillmentModel model, String traceId, boolean forceClean) throws Exception {
        this.acquireConcurrentLock(model.getOrderNo());
        try{
            newModelService.create(model, traceId, forceClean);
        } finally {
            this.releaseConcurrentLock(model.orderNo());
        }
    }

    /**
     * 接单并发锁，防止MQ并行下发同样的订单
     * @param orderNo
     */
    private void acquireConcurrentLock(String orderNo) {
        boolean isLock = jimClient.setIfNotExists(KeyGenerator.receiveOrderConcurrentLockKey(orderNo),
                CacheKeyEnum.ORDER_RECEIVE_CONCURRENT_LOCK.getTimeout(),
                CacheKeyEnum.ORDER_RECEIVE_CONCURRENT_LOCK.getUnit());
        if(!isLock) {
            log.warn("接单并发锁加锁失败,单号：{}, key :{}", orderNo, KeyGenerator.receiveOrderConcurrentLockKey(orderNo));
            throw new AcquireLockException(UnifiedErrorSpec.Business.LOCK_ACQUIRE_EXCEPTION).withVars(orderNo);
        }
    }
    /**
     * 释放并发锁
     * @param orderNo
     */
    private void releaseConcurrentLock(String orderNo) {
        jimClient.delete(KeyGenerator.receiveOrderConcurrentLockKey(orderNo));
    }


    @Override
    public void afterPropertiesSet() throws Exception {
    }

}
