package com.jdl.sc.ofw.out.app.listener.mutong;


import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.core.utils.StringUtils;
import com.jdl.sc.ofw.out.app.listener.dto.WmsStatusRelayMessage;
import com.jdl.sc.ofw.out.app.listener.service.OrderConvertServiceAcl;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.horiz.infra.mq.AbstractMessageTemplate;
import com.jdl.sc.ofw.out.horiz.infra.facade.mutong.MuTongFaced;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;

@Slf4j
@Component
public class PullBackCancelListener extends AbstractMessageTemplate {
    @Resource
    private OrderConvertServiceAcl orderConvertServiceAcl;

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private OutboundModelRepository outboundModelRepository;
    @Resource
    private MuTongFaced muTongFaced;
    /**
     * 拉回状态
     */
    private static final String WMS_PULL_BACK_STATUS_CODE = "10031";

    @Override
    public void onMessage(String message) throws Exception {
        WmsStatusRelayMessage wmsStatusRelayMessage;
        try {
            wmsStatusRelayMessage = JsonUtil.parse(message, WmsStatusRelayMessage.class);
            if (wmsStatusRelayMessage == null) {
                return;
            }
        } catch (Exception e) {
            log.error("WMS状态回传处理异常 报文类型转换异常", e);
            throw e;
        }
        String orderNo = orderConvertServiceAcl.convert(wmsStatusRelayMessage.getOrderNo());
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        if (!orderCenterAcl.checkOrderExist(orderNo)) {
            return;
        }
        // 非拉回信息不处理
        if (!ObjectUtils.nullSafeEquals(WMS_PULL_BACK_STATUS_CODE, wmsStatusRelayMessage.getStatus())) {
            return;
        }
        // 查询订单履约信息
        OrderFulfillmentModel model = outboundModelRepository.getModelByOrderNo(orderNo);

        if (!model.isBipOrder()) {
            return;
        }
        String operateTime = wmsStatusRelayMessage.getOperateTime().replace("T", " ");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //发起牧童取消
        muTongFaced.pullbackCancel(model, wmsStatusRelayMessage.getOperator(), sdf.parse(operateTime));
    }
}
