package com.jdl.sc.ofw.out.app.listener;

import com.jdl.sc.ofw.out.app.translator.ReceiveOrderTranslator;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description 销售出订单->Pop
 * <AUTHOR>
 * @Date 2023-04-18 16:27
 **/
@Component
public class ColdChainOrderCenterPopListener extends AbstractSaleOutboundOrderListener {
    public ColdChainOrderCenterPopListener(@Qualifier("outboundServiceImpl")OutboundService outboundService,
                                           ReceiveOrderTranslator receiveOrderTranslator,
                                           @Value("${ofw.standard.businessType}")String businessType) {
        super(outboundService, receiveOrderTranslator, businessType);
    }
}
