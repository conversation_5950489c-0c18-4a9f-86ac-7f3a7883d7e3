package com.jdl.sc.ofw.out.app.listener.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Builder
@Getter
@Jacksonized
public class SurveyServiceOrderResultMessage {

    /**
     * 第三方单号
     */
    private String thirdpartyParentBillNo;

    /**
     * 勘测结果,true-通过,false-不通过
     */
    private Boolean surveyResult;

    /**
     * 勘测结果详情JSON字符串
     */
    private String surveyResultInfo;

    /**
     * 销售订单号
     */
    private String eclpOrderNo;

}
