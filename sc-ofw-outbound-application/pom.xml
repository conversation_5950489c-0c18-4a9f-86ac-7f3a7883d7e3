<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sc-ofw-outbound</artifactId>
        <groupId>com.jdl.sc</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sc-ofw-outbound-application</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>core-preheat</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-horiz-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-deprecated</artifactId>
        </dependency>
        <!--   log4j starts  -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jdl.ofc</groupId>
            <artifactId>ofc-supplychain-oms-client</artifactId>
        </dependency>
        <!--   log4j ends  -->
    </dependencies>

</project>