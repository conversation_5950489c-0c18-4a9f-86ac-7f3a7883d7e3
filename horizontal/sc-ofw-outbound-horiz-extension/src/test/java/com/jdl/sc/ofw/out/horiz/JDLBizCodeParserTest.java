package com.jdl.sc.ofw.out.horiz;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jd.eclp.master.shipper.domain.Ship;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jd.matrix.sdk.base.*;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import cn.jdl.batrix.sdk.base.BBizCodeParser;
import com.jd.matrix.core.context.*;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import java.util.ArrayList;
import com.jd.matrix.sdk.base.DomainModel;
import java.util.List;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;

import com.jdl.sc.ofw.out.horiz.JDLBizCodeParser;

import com.jd.matrix.sdk.base.BizProductParser;
import lombok.extern.slf4j.Slf4j;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the JDLBizCodeParser class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class JDLBizCodeParserTest {

    @Test
    public void testParseScenarioWithNullInput() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        List<String> result = parser.parseScenario(null);
        assertNotNull(result);
    }

    @Test 
    public void testParseScenarioWithValidInput() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        BizContext bizContext = new BizContext();
        List<String> result = parser.parseScenario(bizContext);
        assertNotNull(result);
    }

    @Test
    public void testFilterWithOrderFulfillmentModel() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().build();
        assertTrue(parser.filter(model));
    }

    @Test 
    public void testFilterWithOrderFulfillmentContext() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        assertTrue(parser.filter(context));
    }

    @Test
    public void testFilterWithBizContext() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        BizContext bizContext = new BizContext();
        assertFalse(parser.filter(bizContext));
    }

    @Test
    public void testFilterWithNull() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        assertFalse(parser.filter(null));
    }

    @Test
    public void testParseProductScenarioNonOrderFulfillmentContext() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
        BizContext bizContext = new BizContext();
        List<String> result = parser.parseProductScenario(bizContext);
        assertEquals(1, result.size());
    }

    @Test 
    public void testParseProductScenarioOrderFulfillmentContextKingKongExpress() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();

        BusinessIdentity identity = new BusinessIdentity();
        identity.setBusinessScene("test_scene");
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().businessIdentity(identity).build();
        
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().build();
        context.assignModel(model);

        List<String> result = parser.parseProductScenario(context);
        assertEquals(5, result.size());
    }

    @Test
    public void testParseProductScenarioOrderFulfillmentContextJpzsfShipper() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();

        BusinessIdentity identity = new BusinessIdentity();
        identity.setBusinessScene("test_scene");
//        context.setBusinessIdentity(identity);
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(identity).build();
        

        Shipment shipment = Shipment.builder()
                .shipperType(ShipperTypeEnum.JPZSF)
                .needExternalWaybillOnly(true)
                .build();
//        shipment.setShipperType(ShipperTypeEnum.JPZSF);
//        shipment.setNeedExternalWaybillOnly(true);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .shipment(shipment)
                .build();

        context.assignModel(model);

        List<String> result = parser.parseProductScenario(context);
        assertEquals(3, result.size());
    }

    @Test
    public void testParseProductScenarioOrderFulfillmentContextMatrixAppraisal() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
//        OrderFulfillmentContext context = new OrderFulfillmentContext();
        BusinessIdentity identity = new BusinessIdentity();
        identity.setBusinessScene("test_scene");
//        context.setBusinessIdentity(identity);
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(identity)
                .build();
        
//        OrderFulfillmentModel model = new OrderFulfillmentModel();
        BatrixInfo batrixInfo = new BatrixInfo();
        batrixInfo.setBusinessType("integrated_services_sc");
//        model.setBatrixInfo(batrixInfo);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(batrixInfo)
                .build();
        context.assignModel(model);

        List<String> result = parser.parseProductScenario(context);
        assertEquals(3, result.size());
    }

    @Test
    public void testParseProductScenarioOrderFulfillmentContextTikTok() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
//        OrderFulfillmentContext context = new OrderFulfillmentContext();
        BusinessIdentity identity = new BusinessIdentity();
        identity.setBusinessScene("test_scene");
//        context.setBusinessIdentity(identity);
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(identity)
                .build();
//        OrderFulfillmentModel model = new OrderFulfillmentModel();
        Shipment shipment = Shipment.builder()
                .shipperType(ShipperTypeEnum.JDPS)
                .deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.TIK_TOK)
                .build();
//        shipment.setShipperType(ShipperTypeEnum.JDPS);
//        shipment.setDeliveryPerformanceChannel(DeliveryPerformanceChannelEnum.TIK_TOK);
//        model.setShipment(shipment);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .shipment(shipment)
                .build();
        context.assignModel(model);

        List<String> result = parser.parseProductScenario(context);
        assertEquals(3, result.size());
    }

    @Test
    public void testParseProductScenarioOrderFulfillmentModelAllInOne() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();
//        OrderFulfillmentModel model = new OrderFulfillmentModel();
        Channel channel = Channel.builder().channelSource(ChannelSourceEnum.ALL_IN_ONE).build();
//        channel.ass
//        channel.setChannelSource(ChannelSourceEnum.ALL_IN_ONE);
//        model.assignCh(channel);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder().channel(channel).build();

        List<String> result = parser.parseProductScenario(model);
        assertEquals(2, result.size());
    }

    @Test
    public void testParseProductScenarioOrderFulfillmentModelCityDelivery() {
        // generated by JoyCoder taskId 907a5722ae0d
        JDLBizCodeParser parser = new JDLBizCodeParser();

        Shipment shipment = Shipment.builder().build();

        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .shipment(shipment)
                .build();
        List<String> result = parser.parseProductScenario(model);
        assertEquals(2, result.size());
    }

}