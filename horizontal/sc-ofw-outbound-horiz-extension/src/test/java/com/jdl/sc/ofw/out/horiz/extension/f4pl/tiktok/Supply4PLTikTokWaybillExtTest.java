package com.jdl.sc.ofw.out.horiz.extension.f4pl.tiktok;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jd.csc.channel.intserv.waybill.WaybillInfoVo;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ReflectionUtils;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.model.Make;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductBusinessLineEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.rpc.BizPlatformServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.EclpDictAcl;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.out.horiz.infra.facade.ThirdPartWaybillCreateServiceFacade;
import com.jdl.sc.ofw.out.horiz.infra.facade.TikTokDfWaybillFacade;
import com.jdl.sc.ofw.out.horiz.infra.trafficcontrol.TrafficController;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.TiktokJpUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;

import static com.jdl.sc.ofw.out.horiz.infra.rpc.impl.EclpDictRpc.LIMIT_SALES_PLATFORM;
import static com.jdl.sc.ofw.out.horiz.infra.rpc.impl.EclpDictRpc.TIK_TOK_THIRD_CATEGORY;
import static com.jdl.sc.ofw.out.horiz.infra.rpc.impl.EclpDictRpc.TIK_TOK_TRANSIT_WAREHOUSE_ADDRESS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;



@RunWith(MockitoJUnitRunner.class)
public class Supply4PLTikTokWaybillExtTest {
    @InjectMocks
    private Supply4PLTikTokWaybillExt defaultExt;

    @Mock
    private ElectronicSheetConfigAcl electronicSheetConfigAcl;

    @Mock(name = "tikTokBizPlatformServiceRpc")
    private BizPlatformServiceAcl tikTokBizPlatformServiceRpc;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Mock
    private EclpDictAcl eclpDictAcl;

    @Mock
    private TrafficController trafficController;

    @Mock
    private TikTokDfWaybillFacade tikTokDfWaybillFacade;
    @Mock
    private TiktokJpUtils tiktokJpUtils;
    @Mock
    private ThirdPartWaybillCreateServiceFacade thirdPartWaybillCreateServiceFacade;


    private OrderFulfillmentContext context = OrderFulfillmentContext.builder()
            .businessIdentity(new BusinessIdentity())
            .model(Make.anOutboundModel())
            .build();
    @Test(expected = PauseException.class)
    public void testTikTokJdWaybillProcessor() throws Exception {
        when(switchKeyUtils.isTikTokDfDeptNo(any())).thenReturn(true);
        when(switchKeyUtils.isTiktokSupplyDistributionDept(any())).thenReturn(true);
        // when(trafficController.isHit(any(),any())).thenReturn(true);
        context.getModel().getChannel().assignChannelNo(PlatformConstants.CHANNEL_NO_DYDF);
        context.getModel().getShipment().assignWayBill("");
        context.getModel().getProducts().enroll(Product.builder().no(FreightProductEnum.TKZH.getCode()).businessLine(ProductBusinessLineEnum.TRANSPORT).type(ProductTypeEnum.MAIN_PRODUCT).build());
        defaultExt.execute(context);
    }


    @Test(expected = PauseException.class)
    public void tiktokTransitWarehouseTest() throws Exception {

        when(switchKeyUtils.isTiktokTransitWarehouseOpen()).thenReturn(true);
        when(eclpDictAcl.getDictionaryValue(any(), eq(LIMIT_SALES_PLATFORM))).thenReturn("channelOrderNo");
        // when(eclpDictAcl.getDictionaryValues(eq(TIK_TOK_THIRD_CATEGORY))).thenReturn(new HashMap<>());
        OrderFulfillmentModel model = context.getModel();
        model.getConsignee().assignEncryptMode("1");
        Fulfillment fulfillment = Fulfillment.builder()
                .customField(JsonUtil.toJsonSafe(new HashMap<String, String>() {{
                    put("isDyJdKd", "1");
                }}))
                .build();
        ReflectionUtils.setFieldValue(OrderFulfillmentModel.class, model, "fulfillment", fulfillment);
        defaultExt.execute(context);

        model.assignCargos(new ArrayList<Cargo>(){{
            add(Cargo.builder()
                    .thirdCategoryNo("10990")
                    .thirdCategoryName("酒水")
                    .build());
        }});

        context.getModel().getConsignee().getAddress().assignDetailAddress("北京市通州区京东总部2号楼");
        when(eclpDictAcl.getDictionaryValues(eq(TIK_TOK_TRANSIT_WAREHOUSE_ADDRESS))).thenReturn(new HashMap<String, String>(){{
            put("11", "北京市通州区");
        }});
        when(eclpDictAcl.getDictionaryValues(eq(TIK_TOK_THIRD_CATEGORY))).thenReturn(new HashMap<String,String>(){{
            put("10990", "酒水");
        }});
        when(trafficController.isHit(any(), any())).thenReturn(true);

        defaultExt.execute(context);
    }

    @Test(expected = PauseException.class)
    public void testTiktokInstant_getWaybillAndPrintInfo_OK() throws Exception {
        when(tiktokJpUtils.isJpInstant(any())).thenReturn(true);
        when(switchKeyUtils.isTikTokDfDeptNo(any())).thenReturn(false);
        when(switchKeyUtils.isTiktokSupplyDistributionDept(any())).thenReturn(true);
        when(thirdPartWaybillCreateServiceFacade.getWaybill(any())).thenReturn(aWaybillInfo());
        when(thirdPartWaybillCreateServiceFacade.getWaybillPrintInfo(any(), any())).thenReturn(aWaybillInfo());

        context.getModel().getShipment().assignWayBill(null);
        defaultExt.execute(context);
    }

    private WaybillInfoVo aWaybillInfo() {
        return new WaybillInfoVo("JDV00001", "testPrintInfo", "1.0");
    }
}