package com.jdl.sc.ofw.out.horiz.ext;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.ldop.center.api.presort.dto.PreSortCalculateParam;
import com.jd.ldop.center.api.presort.dto.PreallocationResult;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeRequest;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeResponse;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.model.Make;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.exec.Command;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.rpc.PreSortCalcExecutorApiAcl;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.domain.rpc.WarehouseServiceAcl;
import com.jdl.sc.ofw.out.horiz.extension.promise.CompletePromiseInfoDefaultExt;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ext.PromiseTransportationTypeExt;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PreSortCalcTranslator;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PromiseTranslator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CompletePromiseInfoDefaultExtTest {

    @InjectMocks
    private CompletePromiseInfoDefaultExt completePromiseInfoDefaultExt;

    @Mock
    private OrderFulfillmentContext context;

    @Mock
    private OrderFulfillmentModel model;

    @Mock
    private WarehouseServiceAcl warehouseServiceAcl;

    @Mock
    private PromiseAcl promiseAcl;

    @Mock
    private PromiseTransportationTypeExt promiseTransportationTypeExt;

    @Mock
    private PromiseTranslator promiseTranslator;

    @Mock
    private ProductCenterServiceAcl productCenterServiceAcl;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Mock
    private PreSortCalcExecutorApiAcl preSortCalcExecutorApiAcl;

    @Mock
    private PreSortCalcTranslator preSortCalcTranslator;

    @Mock
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    private Command command;

    @Before
    public void setUp() {
        WarehouseAndDeliveryTimeResponse response = new WarehouseAndDeliveryTimeResponse();
        response.setResultCode(1);
        response.setSendpay("100000000000000000000000000000000000000000000000000");
        when(promiseAcl.getPromise(any())).thenReturn(response);
    }

    @Test
    public void testExecute_DefaultCase() {
        ProductCollection productCollection = new ProductCollection();
        Product product = ProductEnum.SMALL_BUSINESS_WAREHOUSE.toProduct();
        productCollection.enroll(product);
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .basicInfo(Make.aBasicInfo())
                .batrixInfo(Make.aBatrixInfo())
                .shipment(Make.aShipment())
                .warehouse(Make.storeWarehouse())
                .consignee(Make.aConsignee())
                .channel(Channel.builder().channelNo("1").build())
                .cargos(Lists.newArrayList(Make.aCargo(), Make.aCargo(), Make.aCargo()))
                .customer(Make.aCustomer())
                .products(productCollection)
                .nonstandardProducts(new NonstandardProducts())
                .orderBusinessType(OrderBusinessTypeEnum.B2C)
                .build();
        final OrderFulfillmentContext context = new OrderFulfillmentContext(
                new BusinessIdentity() {{
                    setBusinessScene("test");
                    setBusinessUnit("test");
                }},
                model,
                command
        );
        completePromiseInfoDefaultExt.execute(context);
    }

    @Test
    public void execute() {

        ProductCollection productCollection = new ProductCollection();
        productCollection.enroll(ProductEnum.LL_SX_W.toProduct());
        productCollection.enroll(ProductEnum.TKLD.toProduct());
        when(switchKeyCommonUtils.staticRouteTimeDeptNoContains(any())).thenReturn(true);

        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity())
                .model(Make.anOutboundModel_B2C_JDPS(new ProductCollection()))
                .build();
        when(productCenterServiceAcl.isTrustSellerTransport(any())).thenReturn(true);
        when(switchKeyUtils.containBpromiseDeptNo(any())).thenReturn(true);
        WarehouseAndDeliveryTimeResponse response = new WarehouseAndDeliveryTimeResponse();
        response.setResultCode(1);
        response.setCoddate(new Date());
        response.setSendpay("100000000000000000000000000000000000000000000000000");
        when(promiseAcl.getPromise(any())).thenReturn(response);
        completePromiseInfoDefaultExt.execute(context);
        //s1
        response.setStoreDeliveryHandoverTime(new Date());
        completePromiseInfoDefaultExt.execute(context);
        //s2
        response.setExpectPackageDate(new Date());
        completePromiseInfoDefaultExt.execute(context);
        when(promiseTranslator.modelToRequest(any(), any(), any())).thenReturn(new WarehouseAndDeliveryTimeRequest());
        PreallocationResult preallocationResult = new PreallocationResult();
        preallocationResult.setSiteId(1);
        when(preSortCalcExecutorApiAcl.computePresort(any(), any())).thenReturn(preallocationResult);
        when(preSortCalcTranslator.buildComputePresortRequest(any(), any())).thenReturn(new PreSortCalculateParam());
        when(warehouseServiceAcl.getWarehouseById(any())).thenReturn(new Warehouse());
        when(model.isDeliveryCollection()).thenReturn(true);
        completePromiseInfoDefaultExt.execute(context);
        context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity())
                .model(Make.anOutboundModel_B2C_JDPS(productCollection))
                .build();
        completePromiseInfoDefaultExt.execute(context);
    }
}

