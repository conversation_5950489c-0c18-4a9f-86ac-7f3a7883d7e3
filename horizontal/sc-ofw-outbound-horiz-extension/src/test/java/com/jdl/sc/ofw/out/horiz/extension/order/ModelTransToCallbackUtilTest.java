package com.jdl.sc.ofw.out.horiz.extension.order;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.translator.MarkTranslator;
import com.jdl.sc.ofw.out.horiz.infra.trafficcontrol.TrafficController;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.dto.OrderStatusCallback;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * This class is used for unit testing the ModelTransToCallbackUtil class.(Generated by JoyCoder)
 * @author: fanchangliang
 * @date: 2024-09-19
 */
@RunWith(MockitoJUnitRunner.class)
public class ModelTransToCallbackUtilTest {

	@Mock
	private MarkTranslator markTranslator;

	@InjectMocks
	private ModelTransToCallbackUtil modelTransToCallbackUtil;

    @Mock
    private TrafficController trafficController;

    @Mock
    private OrderMarkDiffUtil orderMarkDiffUtil;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Test
    public void testBuildOrderStatusCallbackWmsComplete() throws Exception {
        // generated by JoyCoder taskId 36be9886e61b
        // Setup
        String modelJson = "{\"business\":\"cn_jdl_sc-isv.sc_ofc_outbound_sale.fulfillmentGenerate\",\"businessIdentity\":{\"businessUnit\":\"cn_jdl_sc-isv\",\"businessType\":\"sc_ofc_outbound_sale\",\"businessScene\":\"fulfillmentGenerate\"},\"extStrategy\":\"cn_jdl_sc-isv.fulfillmentGenerate\",\"traceId\":\"b31827071a1d4491a5a2846b8e6ea5d4\",\"bizId\":\"***********\",\"model\":{\"batrixInfo\":{\"businessUnit\":\"cn_jdl_sc-isv\",\"businessType\":\"sc_ofc_outbound_sale\",\"businessScene\":\"fulfillmentGenerate\",\"yId\":\"JDL\"},\"orderNo\":\"ESL000000***********\",\"orderStatus\":\"100000\",\"orderType\":\"100\",\"orderBusinessType\":2,\"basicInfo\":{\"preSaleStage\":0,\"agentSales\":false},\"customer\":{\"accountNo\":\"EBU0000000000569\",\"accountName\":\"vmi22\",\"customerLevel\":\"1\"},\"cargos\":[{\"name\":\"云云测试-VMI商品\",\"no\":\"EMG4398060931309\",\"level\":\"100\",\"quantity\":{\"value\":1,\"unit\":\"件\"},\"occupyQuantity\":{\"value\":1,\"unit\":\"件\"},\"refGoodsType\":\"1\",\"refGoodsNo\":\"R1\",\"isvCargoNo\":\"1130715\",\"uniqueCode\":\"0\",\"length\":0,\"width\":0,\"height\":0,\"volume\":0,\"weight\":0,\"marineContraband\":false,\"virtualType\":\"0\",\"cargoServiceInfo\":[{\"serviceCode\":\"sc-a-0067\"}]}],\"goods\":[{\"type\":\"1\",\"no\":\"ESG4398060777428\",\"name\":\"云云测试-VMI商品\",\"price\":{\"currencyCode\":\"CNY\"},\"amount\":{\"currencyCode\":\"CNY\"},\"quantity\":{\"value\":1},\"channelGoodsNo\":\"ESG4398060777428\",\"refCargoNo\":\"R1\"}],\"consignee\":{\"address\":{\"detailAddress\":\"北京市大兴区亦庄经济开发区经海路\",\"provinceNameGis\":\"北京\",\"provinceNoGis\":\"1\",\"cityNameGis\":\"大兴区\",\"cityNoGis\":\"2810\",\"countyNameGis\":\"亦庄经济开发区\",\"countyNoGis\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"townName\":\"亦庄经济开发区\",\"townNo\":\"51081\"},\"name\":\"橘子\",\"phone\":\"18018018088\",\"mobile\":\"18018018088\",\"encryptMode\":\"2\",\"virtualNumberExpirationDate\":\"2024-10-18 14:48:01\"},\"shipment\":{\"deliveryPerformanceChannel\":\"4\",\"distributionWarehouse\":\"1\",\"shipperNo\":\"CYS0000010\",\"shipperType\":1,\"shipperName\":\"京东配送\",\"expectDeliveryStartTime\":1679398357000,\"expectDeliveryEndTime\":1679398357000,\"expectDeliveryStartDate\":1679367600000,\"cityDeliveryStartDate\":1679389020000,\"cityDeliveryEndDate\":1679389200000,\"bdOwnerNo\":\"27K21560\",\"ignorePromise\":false,\"contactlessReceiveType\":0,\"halfReceive\":1,\"deliveryType\":1,\"presortInfo\":{},\"transportationType\":1,\"transportModeEnum\":\"0\",\"logisticsOriginalPackage\":0,\"deductibleCompensation\":\"0\",\"cityDeliveryType\":false,\"expectCityDeliveryType\":false,\"tmsDeliveryType\":\"2\",\"checkGoodsTypeEnum\":\"0\",\"needExternalWaybillOnly\":false,\"firstWaybillIssue\":false,\"printExtendInfo\":{\"sendMode\":\"null\",\"receiveMode\":\"null\",\"thirdPayment\":\"null\"},\"contactPerson\":\"季程成\",\"contactPhone\":\"18623344544\",\"heavyGoodsUpstairs\":false,\"hidePrivacyType\":false,\"cityDeliveryDeadline\":\"2023-03-21 16:57:00\",\"cityDeadlineType\":0,\"agingName\":\"非城配时效\",\"reReceiveName\":\"签单返还收件人姓名\",\"reReceiveMobile\":\"22222222222\",\"reReceivePhone\":\"11111111111\",\"reReceiveAdress\":\"河北省张家口市桥东区工业路街道钻石南路工行家属院2号3单301\",\"signBillTypeEnum\":\"0\",\"CNTemplate\":false,\"packageProduceMode\":\"0\",\"express\":\"0\",\"activationCardService\":\"0\",\"signType\":\"0\"},\"warehouse\":{\"warehouseNo\":\"110008916\",\"warehouseName\":\"55测试仓\",\"warehouseType\":1,\"stockControl\":false,\"transportWmsStartDate\":1679331600000,\"transportWmsEndDate\":1679413800000,\"wmsDeadlineType\":0,\"wmsPlanDeliveryTime\":\"2023-03-22 22:00:38\",\"originalWarehouseNo\":\"110008916\"},\"finance\":{\"paymentType\":0,\"settlementType\":1},\"refOrderInfo\":{\"waybillNo\":[null]},\"channel\":{\"pin\":\"vmi22\",\"channelSource\":\"ISV\",\"channelShopNo\":\"ESP0000000004777\",\"channelShipmentNo\":\"SF\",\"channelOrderCreateTime\":1679398357000,\"channelNo\":\"6\",\"channelName\":\"其他\",\"shopNo\":\"ESP0000000004777\",\"shopName\":\"线下实体店\",\"customerOrderNo\":\"DHHK1679398357747\",\"bdSellerNo\":\"0030001\",\"isvSource\":\"ISV0000000000054\",\"systemCaller\":\"EDI\"},\"products\":{\"codeToProductMap\":{\"sc-m-0001\":{\"no\":\"sc-m-0001\",\"name\":\"中小件商务仓\",\"type\":1,\"businessLine\":1,\"attributes\":{},\"billingMode\":\"2\",\"billingModeName\":\"按件计费\"},\"ed-m-0002\":{\"no\":\"ed-m-0002\",\"name\":\"特快送\",\"type\":1,\"businessLine\":2,\"attributes\":{}},\"sc-a-0028\":{\"no\":\"sc-a-0028\",\"name\":\"耗材计费\",\"type\":2,\"businessLine\":1,\"attributes\":{}},\"wu\":{\"no\":\"wu\",\"attributes\":{}}}},\"nonstandardProducts\":{\"enrolledServices\":{\"expectOutboundTime\":\"2023-03-21 19:32:37\",\"orderTemporaryStorage\":\"1\"}},\"solution\":{\"no\":\"sc-s-0005\",\"name\":\"sc-s-0005\",\"attributes\":{\"guaranteeMoney\":\"100.0\",\"unpackingFlag\":\"0\",\"heavyGoodsUpstairsFlag\":\"0\",\"insured\":\"true\",\"hidePrivacyType\":\"0\",\"firstWaybillIssue\":\"0\",\"specialSignRequirements\":\"{\\\"activationService\\\":\\\"0\\\",\\\"signType\\\":\\\"0\\\"}\",\"packageProduceMode\":\"0\"}},\"buyerRemark\":\"加油\",\"orderMark\":\"00000000010010000000020000000000000000000000000000001000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\",\"createTime\":1679398358000,\"fulfillment\":{\"occupyResult\":true,\"productionByPack\":false,\"partialFulfillment\":false,\"partialPayment\":false,\"cargoFulfillmentWay\":true,\"professionType\":\"00\",\"skipMobileDecrypt\":false,\"orderUrgencyType\":\"0\",\"trustJDMetrics\":false,\"customField\":\"{\\\"insurePriceFlag\\\":1}\",\"tmDeliveryByJgFlag\":false,\"clpsCainiaoElecFlag\":false,\"tmOrderFlag\":\"0\",\"packageSignFlag\":false,\"invokedCainiaoPlatform\":false},\"invoice\":{\"invoiceDetailList\":[{\"rate\":\"0.17\"}],\"invoiceSource\":1,\"invoiceBiz\":\"0\"}}}";
        OrderFulfillmentContext context = JsonUtil.readValue(modelJson, OrderFulfillmentContext.class);
        // when(markTranslator.modelToMark(any(OrderFulfillmentModel.class))).thenReturn("testMark");
        when(trafficController.isHitNotDegrade(any(OrderFulfillmentModel.class), any(String.class))).thenReturn(true);
        doNothing().when(orderMarkDiffUtil).diff(any(),any());
        context.getModel().getShipment().assignDeliveryPerformanceChannel(null);
        ReflectionTestUtils.setField(context.getModel().getFulfillment(), "preProductionMinutes", 10);
        ReflectionTestUtils.setField(context.getModel().getFulfillment(), "productionDuration", 5);
        ReflectionTestUtils.setField(context.getModel().getFulfillment(), "transitDuration", 40);
        // Test
        OrderStatusCallback result = modelTransToCallbackUtil.buildOrderStatusCallback(context.getModel());
        System.out.println(JsonUtil.toJson(result));
        // Verify
        assertNotNull(result);
        assertEquals(result.getShipment().getDeliveryPerformanceChannel(), "-128");
        // Add more assertions based on the expected result
    }

}