package com.jdl.sc.ofw.out.horiz.extension.shipment;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.ofw.out.common.constant.OvasConstants;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.ability.pause.hanlder.OrderHoldRecalculateHandler;
import com.jdl.sc.ofw.out.domain.dto.ModifyCommand;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModifySupplyShipmentInfoExtTest {


    @InjectMocks
    private ModifySupplyShipmentInfoExt modifySupplyShipmentInfoExt;

    @Mock
    private SwitchKeyCommonUtils switchKeyCommonUtils;
    @Mock
    private OrderHoldRecalculateHandler orderHoldRecalculateHandler;

    @Test
    public void executeTest() {

        when(switchKeyCommonUtils.mujiHKDCdeptNoContains(any())).thenReturn(false);
        Map<String, String> serviceRequirements = new HashMap<>();
        serviceRequirements.put(OvasConstants.CHOOSE_TIME_DELIVERY_SERVICE, "testInfo");
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity(){{
                    setBusinessUnit("test");
                    setBusinessScene("test");
                }})
                .model(OrderFulfillmentModel.builder()
                        .shipment(Shipment.builder()
                                .expectDeliveryEndTime(new Date())
                                .build())
                        .customer(Customer.builder().accountNo("EBU0001").build())
                        .build())
                .command(ModifyCommand.builder()
                        .shipment(Shipment.builder()
                                .bunchQty("24")
                                .kilometers("100")
                                .expectDeliveryEndTime(new Date())
                                .consolidationPointCode("code")
                                .consolidationPointName("name")
                                .serviceRequirements(serviceRequirements)
                                .build())
                        .build())
                .build();

        modifySupplyShipmentInfoExt.execute(context);
        assertEquals("24", context.getModel().getShipment().getBunchQty());
        assertEquals("100", context.getModel().getShipment().getKilometers());
        assertEquals("testInfo", context.getModel().getShipment().getServiceRequirements().get(OvasConstants.CHOOSE_TIME_DELIVERY_SERVICE));
    }
}