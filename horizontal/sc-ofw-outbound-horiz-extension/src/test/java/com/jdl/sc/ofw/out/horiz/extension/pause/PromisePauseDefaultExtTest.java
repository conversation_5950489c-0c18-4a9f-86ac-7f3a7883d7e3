package com.jdl.sc.ofw.out.horiz.extension.pause;

import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.promise.service.domain.TransferTimeResponse;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.model.Make;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.domain.rpc.WarehouseServiceAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PromiseTranslator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PromisePauseDefaultExtTest {

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Mock
    private PromiseTranslator promiseTranslator;

    @Mock
    private WarehouseServiceAcl warehouseServiceAcl;

    @Mock
    private PromiseAcl promiseAcl;

    @InjectMocks
    private PromisePauseDefaultExt promisePauseDefaultExt;

    private OrderFulfillmentContext context;
    private Warehouse warehouse;

    @Before
    public void setUp() {
        OrderFulfillmentModel model = Make.anOutboundModel();
        context = mock(OrderFulfillmentContext.class);

        warehouse = mock(Warehouse.class);

        when(context.getModel()).thenReturn(model);
        when(warehouseServiceAcl.getWarehouseById(any())).thenReturn(warehouse);
    }

    @Test
    public void testExecute_WhenSwitchOff_ShouldNotPause() {
        when(switchKeyUtils.isContractPromisePause(any())).thenReturn(false);

        promisePauseDefaultExt.execute(context);

        verify(promiseAcl, never()).getTransferTime(any());
    }

    @Test(expected = PauseException.class)
    public void testExecute_WhenTransferTimeResponseIsNull_ShouldThrowException() {
        when(switchKeyUtils.isContractPromisePause(any())).thenReturn(true);
        when(promiseAcl.getTransferTime(any())).thenReturn(null);

        promisePauseDefaultExt.execute(context);
    }

    @Test(expected = PauseException.class)
    public void testExecute_WhenCurrentTimeBeforeTransferTime_ShouldPause() {
        when(switchKeyUtils.isContractPromisePause(any())).thenReturn(true);

        TransferTimeResponse response = mock(TransferTimeResponse.class);
        when(response.getResultCode()).thenReturn(1);
        when(response.getTransferTime()).thenReturn(new Date(System.currentTimeMillis() + 10000));
        when(promiseAcl.getTransferTime(any())).thenReturn(response);
        promisePauseDefaultExt.execute(context);

    }

    @Test
    public void testExecute_WhenCurrentTimeAfterTransferTime_ShouldNotPause() {
        when(switchKeyUtils.isContractPromisePause(any())).thenReturn(true);

        TransferTimeResponse response = mock(TransferTimeResponse.class);
        when(response.getResultCode()).thenReturn(1);
        when(response.getTransferTime()).thenReturn(new Date(System.currentTimeMillis() - 10000));
        when(promiseAcl.getTransferTime(any())).thenReturn(response);

        promisePauseDefaultExt.execute(context);
    }
}
