package com.jdl.sc.ofw.out.horiz.extension.order;

import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.dict.CityDeliveryType;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.translator.MarkTranslator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.out.horiz.infra.trafficcontrol.TrafficController;
import com.jdl.sc.ofw.outbound.spec.dto.OrderStatusCallback;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * FileName: ModelTransToCallbackUtil
 * Description: 回传参数转换公共工具类
 * [@author]:   quhuafeng
 * [@date]:     2024/7/2413:48
 */
@Component
public class ModelTransToCallbackUtil {


    @Resource
    private MarkTranslator markTranslator;
    @Resource
    private SwitchKeyUtils switchKeyUtils;

    @Resource
    private TrafficController trafficController;

    @Resource
    private OrderMarkDiffUtil orderMarkDiffUtil;
    /**
     * 清空数据标识
     */
    private static final String CLEAR_DATA_FLAG = "-128";
    public OrderStatusCallback buildOrderStatusCallback(OrderFulfillmentModel model ){
        Map<String, String> extendProps = new HashMap<String, String>();
        OrderStatusCallback.Solution solution = null;
        //下仓状态回传处理标识
        boolean wmsComplete = OrderStatusEnum.TRANSPORT_WMS.equals(model.getOrderStatus());
        if (wmsComplete) {
            String orderMark = markTranslator.modelToMark(model);
            orderMarkDiffUtil.diff(model, orderMark);
            extendProps.put("orderMark", orderMark);
            // 订单优先级为null,回传空字符串""
            Integer orderPriority = model.getFulfillment().getOrderPriority();
            extendProps.put("outboundPriority", orderPriority == null ? StringUtils.EMPTY : String.valueOf(orderPriority));
            // 订单加急类型
            OrderUrgencyType orderUrgencyType = model.getFulfillment().getOrderUrgencyType();
            if (orderUrgencyType != null) {
                extendProps.put("outboundUrgency", orderUrgencyType.getOmsCode());
            }
            //回传时增加订单来源用于区分回传
            if (model.getChannel().getChannelSource() != null){
                extendProps.put("channelSource", model.getChannel().getChannelSource().getCode());
            }
            solution = OrderStatusCallback.Solution.builder()
                    .no(model.getSolution().getNo())
                    .name(model.getSolution().getName())
                    .attributes(model.getSolution().getAttributes())
                    .build();
        }
        OrderStatusCallback orderStatusCallback = OrderStatusCallback.builder()
                .orderNo(model.orderNo())
                .status(model.getOrderStatus().getCode())
                .operateTime(new Date())
                .extendProps(extendProps)
                .encryptMode(model.getConsignee().getEncryptMode())
                .virtualNumberExpirationDate(model.getConsignee().getVirtualNumberExpirationDate())
                .shipment(
                        OrderStatusCallback.Shipment.builder()
                                .wayBill(model.getShipment().getWayBill())
                                .transportationType(model.getShipment().getTransportationType())
                                .shipperNo(model.getShipment().getShipperNo())
                                .shipperName(model.getShipment().getShipperName())
                                .shipperType(model.getShipment().getShipperType().getCode())
                                .contactPerson(model.getShipment().getContactPerson())
                                .contactPhone(model.getShipment().getContactPhone())
                                .contactAddress(model.getShipment().getContactAddress())
                                .expectDeliveryStartTime(model.getShipment().getExpectDeliveryStartTime())
                                .expectDeliveryEndTime(model.getShipment().isIgnorePromise() ? null : model.getShipment().getExpectDeliveryEndTime())
                                .firstWaybillIssue(model.getShipment().getFirstWaybillIssue())
                                .thirdWaybill(model.getShipment().getThirdWayBill())
                                .parentWaybillNo(model.getShipment().getParentWaybillNo())
                                .externalCarrierCode(model.getShipment().getExternalCarrierCode())
                                .packageProduceModeEnum(model.getShipment().getPackageProduceMode())
                                .mjCompanyCode(model.getFulfillment().getMjCompanyCode())
                                .cainiaoPlatformWaybill(model.getFulfillment().getCainiaoPlatformWaybill())
                                .extendProps(buildExtendProps(model))
                                .expectDeliveryResource(ObjectUtils.nullIfNull(model.getShipment().getExpectDeliveryResource(), ExpectDeliveryResourceEnum:: getCode))
                                .deliveryDate(model.getShipment().getDeliveryDate())
                                .batchStartTime(model.getShipment().getBatchStartTime())
                                .batchEndTime(model.getShipment().getBatchEndTime())
                                .endCenterName(model.getShipment().getEndCenterName())
                                .endCenterNo(model.getShipment().getEndCenterNo())
                                .bunchQty(model.getShipment().getBunchQty())
                                .cardNo(model.getShipment().getCardNo())
                                .kilometers(model.getShipment().getKilometers())
                                .presortInfo(this.buildPresortInfo(model))
                                .thirdPartExpressType(model.getShipment().getThirdPartExpressType())
                                .thirdPartExpressProduct(model.getShipment().getThirdExpressProduct())
                                .luxurySecurity(model.getShipment().getLuxurySecurity())
                                .deliveryForcePhoto(model.getShipment().getDeliveryForcePhoto())
                                .forceContact(model.getShipment().getForceContact())
                                .dispatchNo(model.getShipment().getDispatchNo())
                                .deliveryStaffName(model.getShipment().getDeliveryStaffName())
                                .vehicleNumber(model.getShipment().getVehicleNumber())
                                .operateTime(model.getShipment().getOperateTime())
                                .printExtendInfo(OrderStatusCallback.PrintExtendInfo.builder()
                                        .codingMappingOut(model.getShipment().getPrintExtendInfo().getCodingMappingOut())
                                        .twoDimensionCode(model.getShipment().getPrintExtendInfo().getTwoDimensionCode())
                                        .destTransferCode(model.getShipment().getPrintExtendInfo().getDestTransferCode())
                                        .destRouteLabel(model.getShipment().getPrintExtendInfo().getDestRouteLabel())
                                        .codingMapping(model.getShipment().getPrintExtendInfo().getCodingMapping())
                                        .destTeamCode(model.getShipment().getPrintExtendInfo().getDestTeamCode())
                                        .proCode(model.getShipment().getPrintExtendInfo().getProCode())
                                        .alreadyDoDecryptAddressMj(model.getShipment().getPrintExtendInfo().getAlreadyDoDecryptAddressMj())
                                        .build())
                                .bdOwnerNo(model.getShipment().getBdOwnerNo())
                                .warmLayer(translateWarmLayer(model))
                                .deliveryPerformanceChannel(translateDeliveryPerformanceChannel(model))
                                .highValue(model.getShipment().getHighValue())
                                .frozen(model.getShipment().getFrozen())
                                .refrigerate(model.getShipment().getRefrigerate())
                                .frangible(model.getShipment().getFrangible())
                                .fresh(model.getShipment().getFresh())
                                .overweight(model.getShipment().getOverweight())
                                .distributionWarehouse(model.getShipment().getDistributionWarehouse())
                                .build()
                )
                .consignee(model.isKingKongExpress() ? OrderStatusCallback.Consignee.builder()
                        .name(model.getConsignee().getName())
                        .mobile(model.getConsignee().getMobile())
                        .phone(model.getConsignee().getPhone())
                        .address(OrderStatusCallback.JdAddress.builder()
                                .detailAddress(model.getConsignee().getAddress().getDetailAddress())
                                .build())
                        .build() : null)
                .products(ObjectUtils.defaultIfNull(model.getProducts(), productCollection ->
                                productCollection.getProducts().stream()
                                        .map(product -> OrderStatusCallback.Product.builder()
                                                .no(product.getNo())
                                                .billingType(product.getBillingMode())
                                                .billingTypeName(product.getBillingModeName())
                                                .name(product.getName())
                                                .type(ObjectUtils.nullIfNull(product.getType(), ProductTypeEnum::getCode))
                                                .attributes(product.getAttributes())
                                                .build()
                                        ).collect(Collectors.toList())
                        , new ArrayList<>()))
                .solution(solution)
                .orderAmount(ObjectUtils.nullIfNull(model.getFinance(), finance -> ObjectUtils.nullIfNull(finance.getOrderAmount(),
                        money -> OrderStatusCallback.Money.builder()
                                .amount(money.getAmount())
                                .currencyCode(money.getCurrencyCode())
                                .build())))
                .channel(OrderStatusCallback.Channel.builder()
                        .channelShipmentNo(model.getChannel().getChannelShipmentNo())
                        .build())
                .fulfillment(OrderStatusCallback.Fulfillment.builder()
                        .merchantRoute(model.getFulfillment().getMerchantRoute())
                        .signDisclaimer(model.getFulfillment().getSignDisclaimer())
                        .trustJDMetrics(model.getFulfillment().isTrustJDMetrics())
                        .forceIssueProduction(Optional.ofNullable(model.getFulfillmentCommand()).map(FulfillmentCommand::getResumeInstructions).map(String::valueOf).orElse(null))
                        .isConsumableBilling(model.getFulfillment().getConsumableBilling())
                        .materialSolutionIds(model.getFulfillment().getMaterialSolutionIds())
                        .pickCallback(model.getFulfillment().getPickCallback())
                        .timelinessDegradeType(model.getFulfillment().getTimelinessDegradeType())
                        .warehouseDeliveryCollaborationMode(Optional.ofNullable(model.getFulfillmentCommand()).map(FulfillmentCommand::getWarehouseDeliveryCollaborationMode).orElse(null))
                        .deliveryInstallationMode(model.getFulfillment().getDeliveryInstallationMode())
                        .preProductionMinutes(model.getFulfillment().getPreProductionMinutes())
                        .productionDuration(model.getFulfillment().getProductionDuration())
                        .transitDuration(model.getFulfillment().getTransitDuration())
                        .build())
                .cargos(model.getCargos().stream()
                        .map(cargo -> OrderStatusCallback.Cargo.builder()
                                .cargoNo(cargo.getNo())
                                .cargoLevel(cargo.getLevel())
                                .cargoLineNo(cargo.getCargoLineNo())
                                .cargoServiceInfo(buildCargoServiceInfo(cargo))
                                .build())
                        .collect(Collectors.toList()))
                .refOrderInfo(OrderStatusCallback.RefOrderInfo.builder()
                        .deliveryOrderNo(model.getRefOrderInfo().getDeliveryOrderNo())
                        .purchaseOrderNo(model.getRefOrderInfo().getPurchaseOrderNo())
                        .targetWarehouseCustomerPurchaseOrderNo(model.getRefOrderInfo().getTargetWarehouseCustomerPurchaseOrderNo())
                        .build())
                .finance(OrderStatusCallback.Finance.builder()
                        .attachFees(ObjectUtils.nullIfNull(model.getFinance(),
                                finance -> ObjectUtils.nullIfNull(finance.getAttachFees(),
                                        attachFees -> attachFees.stream()
                                                .map(attachFee -> OrderStatusCallback.CostInfo.builder()
                                                        .costNo(attachFee.getCostNo())
                                                        .costName(attachFee.getCostName())
                                                        .build())
                                                .collect(Collectors.toList()))))
                        .materialTimeLayer(ObjectUtils.nullIfNull(model.getFinance(),
                                finance -> finance.getMaterialTimeLayer()))
                        .build())
                .build();
        return orderStatusCallback;
    }

    /**
     * 转换配送渠道字段
     *
     * @return -128 代表清空, null代表不更新, 其他值代表更新
     */
    private String translateDeliveryPerformanceChannel(OrderFulfillmentModel model) {
        if (trafficController.isHitNotDegrade(model, TrafficController.BOUNDLESS_JP_PLAINTEXT)) {
            return ObjectUtils.defaultIfNull(model.getShipment().getDeliveryPerformanceChannel(), DeliveryPerformanceChannelEnum::getCode, CLEAR_DATA_FLAG);
        }
        return null;
    }


    private String translateWarmLayer(OrderFulfillmentModel model) {
        if (switchKeyUtils.isColdChainUseWarmLayerDept(model.getCustomer().getAccountNo())) {
            return StringUtils.isBlank(model.getShipment().getWarmLayer()) ? CLEAR_DATA_FLAG : model.getShipment().getWarmLayer();
        }
        return null;
    }
    private OrderStatusCallback.PresortInfo buildPresortInfo(OrderFulfillmentModel model) {

        return Optional.ofNullable(model.getShipment())
                .map(Shipment::getPresortInfo)
                .filter(Objects::nonNull)
                .map(presortInfo -> OrderStatusCallback.PresortInfo.builder()
                        .siteName(presortInfo.getSiteName())
                        .siteNo(presortInfo.getSiteNo())
                        .siteType(presortInfo.getSiteType())
                        .roadArea(presortInfo.getRoadArea())
                        .aoiCode(presortInfo.getAoiCode())
                        .build())
                .orElse(null);
    }


    private Map<String, String> buildExtendProps(OrderFulfillmentModel model) {
        Map<String, String> extendProps = new HashMap<>(4);
        Map<String, String> shipmentExtendProps = buildShipmentExtendProps(model);
        extendProps.put("shipmentExtendProps", shipmentExtendProps.isEmpty() ? null : JsonUtil.toJsonSafe(shipmentExtendProps));
        if (model.getShipment().getTransportModeEnum() != null) {
            extendProps.put("transportMode",model.getShipment().getTransportModeEnum().getCode());
        }
        if (model.getShipment().getTmsDeliveryType() != null){
            extendProps.put("lastDeliveryType",model.getShipment().getTmsDeliveryType().getOmsCode());
        }
        extendProps.put("activeCityDelivery", CityDeliveryType.fromValue(model.getShipment().isCityDeliveryType()).getOmsCode());
        return extendProps;
    }

    private Map<String, String> buildShipmentExtendProps(OrderFulfillmentModel model) {
        Map<String, String> shipmentExtendProps = new HashMap<>(16);
        shipmentExtendProps.put("expectDeliveryStartDate", DateUtil.dateToString(model.getShipment().getExpectDeliveryStartDate()));
        shipmentExtendProps.put("cityDeliveryStartDate", DateUtil.dateToString(model.getShipment().getCityDeliveryStartDate()));
        shipmentExtendProps.put("cityDeliveryEndDate", DateUtil.dateToString(model.getShipment().getCityDeliveryEndDate()));
        shipmentExtendProps.put("cityDeliveryDeadline", model.getShipment().getCityDeliveryDeadline());
        shipmentExtendProps.put("cityDeadlineType", model.getShipment().getCityDeadlineType() == null ? null : String.valueOf(model.getShipment().getCityDeadlineType().getCode()));
        shipmentExtendProps.put("agingName", model.getShipment().getAgingName());
        shipmentExtendProps.put("transportWmsStartDate", DateUtil.dateToString(model.getWarehouse().getTransportWmsStartDate()));
        shipmentExtendProps.put("transportWmsEndDate", DateUtil.dateToString(model.getWarehouse().getTransportWmsEndDate()));
        shipmentExtendProps.put("wmsPlanDeliveryTime", model.getWarehouse().getWmsPlanDeliveryTime());
        shipmentExtendProps.put("wmsDeadlineType", model.getWarehouse().getWmsDeadlineType() == null ? null : String.valueOf(model.getWarehouse().getWmsDeadlineType()));
        shipmentExtendProps.put("partialPayment", model.getFulfillment().isPartialPayment() ? "1" : "0");
        shipmentExtendProps.put("externalCarrierCode", model.getShipment().getExternalCarrierCode());
        shipmentExtendProps.put("siteShipType", String.valueOf(model.getShipment().getShipperType().getSiteShipType().getCode()));
        // 删除扩展属性中value为null的值
        shipmentExtendProps.values().removeIf(Objects::isNull);
        return shipmentExtendProps;
    }

    /**
     * 组装货品增值服务项
     */
    private List<OrderStatusCallback.CargoServiceInfo> buildCargoServiceInfo(Cargo cargo) {
        if (CollectionUtils.isEmpty(cargo.getCargoServiceInfo())) {
            return new ArrayList<>();
        }
        return cargo.getCargoServiceInfo().stream()
                .map(cargoServiceInfo -> OrderStatusCallback.CargoServiceInfo.builder()
                        .serviceCode(cargoServiceInfo.getServiceCode())
                        .serviceName(cargoServiceInfo.getServiceName())
                        .serviceRequirement(cargoServiceInfo.getServiceRequirement())
                        .remark(cargoServiceInfo.getRemark())
                        .extendProps(new HashMap<String, String>(){{put("operateType", cargoServiceInfo.getOperateType());}})
                        .serviceSign(cargoServiceInfo.getServiceSign())
                        .businessLine(cargoServiceInfo.getBusinessLine())
                        .build())
                .collect(Collectors.toList());
    }


}
