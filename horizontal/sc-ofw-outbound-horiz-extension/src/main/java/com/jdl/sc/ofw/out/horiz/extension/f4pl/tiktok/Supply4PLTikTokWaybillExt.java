package com.jdl.sc.ofw.out.horiz.extension.f4pl.tiktok;

import com.jd.csc.channel.intserv.waybill.WaybillInfoVo;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.dict.ThirdShipperEnum;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.common.exception.RetryExecutionException;
import com.jdl.sc.ofw.out.common.exception.RpcTimeOutException;
import com.jdl.sc.ofw.out.domain.dto.BizPlatformPrintInfo;
import com.jdl.sc.ofw.out.domain.dto.BizPlatformRequest;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetPlatform;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLWaybillExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductBusinessLineEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderExtendProps;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import com.jdl.sc.ofw.out.domain.model.vo.JdAddress;
import com.jdl.sc.ofw.out.domain.rpc.BizPlatformServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.EclpDictAcl;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.out.horiz.JDLApp;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.facade.ThirdPartWaybillCreateServiceFacade;
import com.jdl.sc.ofw.out.horiz.infra.facade.TikTokDfWaybillFacade;
import com.jdl.sc.ofw.out.horiz.infra.facade.TiktokWaybillDecryptFacade;
import com.jdl.sc.ofw.out.horiz.infra.rpc.dto.bizplatform.BizPlatformCommandEnum;
import com.jdl.sc.ofw.out.horiz.infra.trafficcontrol.TrafficController;
import com.jdl.sc.ofw.out.horiz.infra.util.ResumeTimeCreator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.out.horiz.infra.util.TiktokJpUtils;
import com.jdl.sc.ofw.outbound.spec.enums.PackageProduceModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.jdl.sc.ofw.out.horiz.infra.rpc.impl.EclpDictRpc.LIMIT_SALES_PLATFORM;
import static com.jdl.sc.ofw.out.horiz.infra.rpc.impl.EclpDictRpc.TIK_TOK_THIRD_CATEGORY;
import static com.jdl.sc.ofw.out.horiz.infra.rpc.impl.EclpDictRpc.TIK_TOK_TRANSIT_WAREHOUSE_ADDRESS;

@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.GENERATE + JDLApp.SEPARATOR + JDLApp.SCENARIO_TIKTOK})
@Primary
public class Supply4PLTikTokWaybillExt implements ISupply4PLWaybillExt {
    @Resource
    private ElectronicSheetConfigAcl electronicSheetConfigAcl;

    @Resource(name = "tikTokBizPlatformServiceRpc")
    private BizPlatformServiceAcl tikTokBizPlatformServiceRpc;

    @Resource
    private TiktokWaybillDecryptFacade tiktokWaybillDecryptFacade;

    @Resource
    private TrafficController trafficController;

    @Resource
    private EclpDictAcl eclpDictAcl;

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    @Resource
    private TikTokDfWaybillFacade tikTokDfWaybillFacade;
    @Resource
    private TiktokJpUtils tiktokJpUtils;
    @Resource
    private ThirdPartWaybillCreateServiceFacade thirdPartWaybillCreateServiceFacade;


    /**
     * 场景条件：
     * ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())
     * && DeliveryPerformanceChannelEnum.TIK_TOK.equals(model.getShipment().getDeliveryPerformanceChannel())
     */
    @Override
    public void execute(OrderFulfillmentContext context) throws Exception {
        OrderFulfillmentModel model = context.getModel();
        boolean existFreightProduct = model.getProducts().getProducts().stream()
                .anyMatch(product -> ProductTypeEnum.MAIN_PRODUCT.equals(product.getType())
                        && ProductBusinessLineEnum.TRANSPORT.equals(product.getBusinessLine()));
        this.dealTikTokProcess(model, existFreightProduct, null);
    }

    protected void dealTikTokProcess(OrderFulfillmentModel model, boolean existFreightProduct, String orderNo) {
        // 运单号不为空，则不调用产业平台
        if (StringUtils.isBlank(model.getShipment().getWayBill())) {
            String waybillCode = null;
            try {
                if (tiktokJpUtils.isJpInstant(model)) {
                    // 调外平台获取运单号和打印信息
                    WaybillInfoVo waybillInfoVo = thirdPartWaybillCreateServiceFacade.getWaybill(model);
                    waybillCode = waybillInfoVo.getWaybillCode();
                    WaybillInfoVo waybillPrintInfoVo = thirdPartWaybillCreateServiceFacade.getWaybillPrintInfo(model, waybillCode);
                    BizPlatformPrintInfo bizPlatformPrintInfo = BizPlatformPrintInfo.builder()
                            .encryptedData(waybillPrintInfoVo.getPrintData())
                            .signature(waybillPrintInfoVo.getSign())
                            .build();
                    model.getShipment().getPrintExtendInfo().assignCustomPrintInfo(JsonUtil.stringify(bizPlatformPrintInfo));
                } else {
                    ElectronicSheetInfo electronicSheetInfo = electronicSheetConfigAcl.queryConfig(electronicSheetConfigAcl.buildElectronicSheetRequest(model, model.getChannel().getShopNo(), ElectronicSheetPlatform.DY, existFreightProduct));
                    BizPlatformRequest.BizPlatformRequestBuilder bizPlatformRequestBuilder = getBizPlatformRequestBuilderBase(model, existFreightProduct, orderNo, electronicSheetInfo);
                    if (switchKeyUtils.isTikTokDfDeptNo(model.getCustomer().getAccountNo()) && PlatformConstants.CHANNEL_NO_DYDF.equals(model.getChannel().getChannelNo())) {
                        bizPlatformRequestBuilder.tictokDaifaShopId(model.getEdiRemark() == null || StringUtils.isBlank(model.getEdiRemark().getTictokDaifaShopId()) ? null : Long.valueOf(model.getEdiRemark().getTictokDaifaShopId()))
                                .businessType(BizPlatformCommandEnum.DOU_YIN_GET_ORDER_DAI_FA.getBusType())
                                .operateType(BizPlatformCommandEnum.DOU_YIN_GET_ORDER_DAI_FA.getOperaType());
                        BizPlatformRequest bizPlatformRequest = bizPlatformRequestBuilder.build();
                        waybillCode = tikTokDfWaybillFacade.getWaybill(bizPlatformRequest);
                    } else {
                        BizPlatformRequest bizPlatformRequest = bizPlatformRequestBuilder.build();
                        waybillCode = tikTokBizPlatformServiceRpc.getWaybill(bizPlatformRequest);
                    }
                }
            } catch (RpcTimeOutException e) {
                throw new RetryExecutionException(UnifiedErrorSpec.ACL.RPC_TIMEOUT_EXCEPTION, e).withCustom(e.getMessage());
            }
            model.getShipment().assignWayBill(waybillCode);
            model.getShipment().assignTransportPresetNo(StringUtils.isNotBlank(waybillCode));
        }

        if (StringUtils.isBlank(model.getConsignee().getEncryptMode())) {
            log.info("{}抖音京配订单等收件人信息更新hold单", model.getOrderNo());
            throw new PauseException(PauseReasonEnum.TIK_TOK_WAIT_CONSIGNEE_INFO, ResumeTimeCreator.calculate(1));
        }
        log.info("订单{}创建预制运单。终端进行解密", model.getOrderNo());
        //处理抖音中转仓
        this.processTiktokTransitWarehouse(model);

    }

    /**
     * 构造抖音新流程，调产业平台入参的公共部分
     */
    protected BizPlatformRequest.BizPlatformRequestBuilder getBizPlatformRequestBuilderBase(OrderFulfillmentModel model, boolean existFreightProduct, String orderNo, ElectronicSheetInfo electronicSheetInfo) {
        return BizPlatformRequest.builder()
                .electronicSheetInfo(electronicSheetInfo)
                .shipperNo(existFreightProduct ? ThirdShipperEnum.JDKY.getShipperNo() : ThirdShipperEnum.JD.getShipperNo())
                .businessType(BizPlatformCommandEnum.DOU_YIN_GET_ORDER.getBusType())
                .operateType(BizPlatformCommandEnum.DOU_YIN_GET_ORDER.getOperaType())
                .consigneeNameEnc(model.getConsignee().getConsigneeNameEnc())
                .consigneePhoneEnc(model.getConsignee().getConsigneePhoneEnc())
                .consigneeMobileEnc(model.getConsignee().getConsigneeMobileEnc())
                .consigneeAddrEnc(model.getConsignee().getConsigneeAddrEnc())
                .provinceName(model.getConsignee().getOriginProvinceName() != null ? model.getConsignee().getOriginProvinceName() : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getProvinceName))
                .cityName(model.getConsignee().getOriginCityName() != null ? model.getConsignee().getOriginCityName() : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getCityName))
                .countyName(model.getConsignee().getOriginCountyName() != null ? model.getConsignee().getOriginCountyName() : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getCountyName))
                .townName(model.getConsignee().getOriginTownName() != null ? model.getConsignee().getOriginTownName() : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getTownName))
                .cargos(model.getCargos())
                .channelOrderNo(model.getChannel().getChannelOrderNo())
                .vendorName(model.getCustomer().getAccountName())
                .vendorCode(model.getCustomer().getAccountNo())
                .warehouseNo(model.getWarehouse().getOriginalWarehouseNo())
                .orderNo(StringUtils.isNotEmpty(orderNo) ? orderNo : model.orderNo())
                .packCount(1)
                .weight(null)
                .volume(null)
                .trustJDMetrics(model.getFulfillment().isTrustJDMetrics())
                .shipperType(model.getShipment().getShipperType())
                .products(model.getProducts().getProducts())
                .thirdExpressProduct(model.getShipment().getThirdExpressProduct())
                .customerOrderNo(model.getChannel().getCustomerOrderNo())
                .electronicUserId(this.getElectronicUserId(model))
                .orderChannel(this.getOrderChannel(model))
                .consigneeDetailAddress(Optional.ofNullable(model.getConsignee())
                        .map(Consignee::getConsigneeAddrEnc)
                        .orElse(null))
                .businessUnit(model.getBatrixInfo().getBusinessUnit());
    }

    private void processTiktokTransitWarehouse(OrderFulfillmentModel model) {
        if (!switchKeyUtils.isTiktokTransitWarehouseOpen() || !trafficController.isHit(model, TrafficController.TIKTOK_TRANSIT_WAREHOUSE)) {
            log.info("抖音京配订单:{}，中转仓业务，事业部：{},未配置切量，跳过", model.getOrderNo(), model.getCustomer().getAccountNo());
            return;
        }
        //抖音中转仓
        if (!isTiktokTransitWarehouse(model)) {
            log.info("抖音京配订单, 非中转仓地址 orderNo:{}", model.getOrderNo());
            return;
        }
        log.info("抖音京配订单, 是中转仓地址, 中转仓货品校验 orderNo:{}", model.getOrderNo());
        model.getShipment().assignPackageProduceMode(PackageProduceModeEnum.SINGLE_PACKAGE);
        model.getConsignee().assignIsTransitWarehouse(true);
        //货品 校验
        Map<String, String> notSupportThirdCategoryMap = eclpDictAcl.getDictionaryValues(TIK_TOK_THIRD_CATEGORY);
        this.validTransitWarehouseCargo(model, notSupportThirdCategoryMap);
    }

    private void validTransitWarehouseCargo(OrderFulfillmentModel model, Map<String, String> notSupportThirdCategoryMap) {
        if (MapUtils.isEmpty(notSupportThirdCategoryMap)) {
            return;
        }
        //随单货品中有不能下发的 分类
        Map<String, String> existNotSupportThirdCategory = model.getCargos().stream()
                .filter(cargo -> StringUtils.isNotBlank(cargo.getThirdCategoryNo()) && StringUtils.isNotBlank(cargo.getThirdCategoryName()))
                .filter(cargo -> notSupportThirdCategoryMap.containsKey(cargo.getThirdCategoryNo()))
                .collect(Collectors.toMap(Cargo::getThirdCategoryNo, Cargo::getThirdCategoryName, (a, b) -> a));

        if (MapUtils.isNotEmpty(existNotSupportThirdCategory)) {
            log.info("订单{}含有抖音中转仓不支持的货品分类,分类号:{}", model.getOrderNo(), JsonUtil.toJsonSafe(existNotSupportThirdCategory));
            StringJoiner msgJoiner = new StringJoiner("、");
            existNotSupportThirdCategory.keySet().forEach(categoryNo -> {
                msgJoiner.add(String.join("-", categoryNo, existNotSupportThirdCategory.get(categoryNo)));
            });
            throw new PauseException(PauseReasonEnum.TIK_TOK_TRANSIT_WAREHOUSE_NOT_SUPPORT_CATEGORY)
                    .withCustom(String.format("订单目的地为抖音中转仓：抖音平台运营要求, %s 品类不允许中转仓收货", msgJoiner));
        }
    }

    /**
     * 中转仓地址判断
     */
    private boolean isTiktokTransitWarehouse(OrderFulfillmentModel model) {

        String transitSalesPlatform = eclpDictAcl.getDictionaryValue(model.getChannel().getChannelNo(), LIMIT_SALES_PLATFORM);
        Map<String, String> transitAddressMap = eclpDictAcl.getDictionaryValues(TIK_TOK_TRANSIT_WAREHOUSE_ADDRESS);
        if (StringUtils.isNotBlank(transitSalesPlatform) && MapUtils.isNotEmpty(transitAddressMap)) {
            //中转站收货地址
            return transitAddressMap.values().stream()
                    .anyMatch(confAddr -> model.getConsignee().getAddress().getDetailAddress().startsWith(confAddr));
        }
        return false;
    }

    protected Long getElectronicUserId(OrderFulfillmentModel model) {
        String channelNo = model.getChannel().getChannelNo();
        if (switchKeyUtils.isTiktokSupplyDistributionDept(model.getCustomer().getAccountNo())) {
            String userId = Optional.of(model)
                    .map(OrderFulfillmentModel::getEdiRemark)
                    .map(EdiRemarkInfo::getDeliveryOrder)
                    .map(DeliveryOrderInfo::getExtendProps)
                    .map(DeliveryOrderExtendProps::getUserId)
                    .orElse(null);
            if (PlatformConstants.TIKTOK_SUPPLY_DISTRIBUTION_CHANNEL_NO.equals(channelNo) && NumberUtils.isCreatable(userId)) {
                return Long.parseLong(userId);
            }
        }
        return PlatformConstants.getTikTokSuperMarketUserId(channelNo);
    }

    protected String getOrderChannel(OrderFulfillmentModel model) {
        String channelNo = model.getChannel().getChannelNo();
        if (switchKeyUtils.isTiktokSupplyDistributionDept(model.getCustomer().getAccountNo())) {
            if (PlatformConstants.TIKTOK_SUPPLY_DISTRIBUTION_CHANNEL_NO.equals(channelNo)) {
                return PlatformConstants.TIKTOK_SUPPLY_DISTRIBUTION_CHANNEL;
            }
        }
        return PlatformConstants.getTikTokSuperMarketOrderChannel(channelNo);
    }
}
