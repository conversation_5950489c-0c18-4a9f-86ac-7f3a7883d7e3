package com.jdl.sc.ofw.out.horiz.extension.pause;


import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.promise.service.domain.PromiseControlResponse;
import com.jd.promise.service.domain.TransferTimeRequest;
import com.jd.promise.service.domain.TransferTimeResponse;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.extension.pause.IPromisePauseExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.domain.rpc.WarehouseServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.enums.OrderControlMode;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PromiseTranslator;
import com.jdl.sc.ofw.out.horiz.infra.util.ResumeTimeCreator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

/**
 * 时效控单-默认扩展点
 */
@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.GENERATE})
public class PromisePauseDefaultExt implements IPromisePauseExt {

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    @Resource
    private PromiseTranslator promiseTranslator;

    @Resource
    private WarehouseServiceAcl warehouseServiceAcl;

    @Resource
    private PromiseAcl promiseAcl;

    @Override
    public void execute(OrderFulfillmentContext context) {
        OrderFulfillmentModel model = context.getModel();
        // 上线开关
        if (!switchKeyUtils.isContractPromisePause(model.getCustomer().getAccountNo())) {
            return;
        }
        Warehouse warehouse = warehouseServiceAcl.getWarehouseById(model.getWarehouse().getWarehouseId());
        TransferTimeRequest transferTimeRequest = promiseTranslator.translateTransferTimeReq(model, warehouse);
        TransferTimeResponse transferTimeResponse = promiseAcl.getTransferTime(transferTimeRequest);
        if (transferTimeResponse == null || transferTimeResponse.getResultCode() != 1 || transferTimeResponse.getTransferTime() == null) {
            log.info("查询promise下传时间，返回值错误, orderNo:{}", model.getOrderNo());
            throw new PauseException(PauseReasonEnum.PROMISE_HOLD_EXCEPTION).withCustom(JsonUtil.toJsonSafe(transferTimeResponse));
        }
        // 若未到下传时间，则控单
        Date now = new Date();
        if (now.before(transferTimeResponse.getTransferTime())) {
            log.info("时效控单，当前日期早于订单下传日期, 进行控单,  orderNo:{}", model.getOrderNo());
            PromiseControlResponse promiseControlResponse = transferTimeResponse.getPromiseControlResponse();
            throw new PauseException(PauseReasonEnum.OTHER, ResumeTimeCreator.calculate(120))
                    .withCustom("时效控单，原因:" + Optional.ofNullable(promiseControlResponse).map(PromiseControlResponse::getSuspendReason).orElse("") + "，预计下传时间:" + DateUtil.dateToString(transferTimeResponse.getTransferTime()));
        }
    }
}
