package com.jdl.sc.ofw.out.horiz.extension.promise;

import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.ldop.center.api.presort.dto.PreSortCalculateSceneEnum;
import com.jd.ldop.center.api.presort.dto.PreallocationResult;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeRequest;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeResponse;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdl.sc.core.profiler.Watcher;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.constant.NonstandardProductConstants;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.DomainServiceException;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.extension.promise.ICompletePromiseInfoExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.PreSortCalcExecutorApiAcl;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.domain.rpc.WarehouseServiceAcl;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ext.PromiseTransportationTypeExt;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PreSortCalcTranslator;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PromiseTranslator;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 补齐时效信息
 *
 * <AUTHOR>
 */
@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.GENERATE}, index = 100)
@Primary
public class CompletePromiseInfoDefaultExt implements ICompletePromiseInfoExt {
    @Resource
    private PromiseAcl promiseAcl;
    @Resource
    private ProductCenterServiceAcl productCenterServiceAcl;
    @Resource
    private PromiseTranslator promiseTranslator;
    @Resource
    private WarehouseServiceAcl warehouseServiceAcl;
    @Resource
    private PromiseTransportationTypeExt promiseTransportationTypeExt;
    @Resource
    private PreSortCalcTranslator preSortCalcTranslator;
    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    @Value("${ducc.promise.211.switch:false}")
    private boolean promise211Switch;

    private static final String AGEING_SERVICE = "ageingService";

    /**
     * 时效sendpayUpdate中的时效降级订单标识
     */
    private static final Integer TIMELINESS_DEGRADE_TYPE_MARK = 712;

    /**
     * 中划线
     */
    private static final String DELIMITER = "-";
    /**
     * 时间类型：上门揽收时间
     */
    public static final int DATE_TYPE_COLLECT = 1;
    /**
     * 时间类型：打包时间
     */
    public static final int DATE_TYPE_PACKAGE = 2;
    /**
     * wms
     */
    public static final String SOURCE_WMS = "wms";

    private static final String PROMISE_SITE_WARNING = "promise.site.warning";

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private PreSortCalcExecutorApiAcl preSortCalcExecutorApiAcl;

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    /**
     * 京准达 解析标位和PromiseMsg中的时效
     * 校验标位对应的时效和PromiseMsg中的时效，如果不一致抛异常
     * 117位=1，118位=0，2H
     * 117位=1，118位=2，2H
     * 117位=1，118位=3，1H
     * 117位=1，118位=5，1H
     * 117位=1，118位=6，0.5H
     * 117位=1，118位=8，0.5H
     * 117位=2，118位=0，2H
     * 117位=2，118位=2，2H
     * 117位=2，118位=3，1H
     * 117位=2，118位=5，1H
     * 117位=2，118位=6，0.5H
     * 117位=2，118位=8，0.5H
     * 117位=4，118位=0，5H
     * 117位=3，118位=8，5H
     */
    private static final Map<String, Long> sendPayPromise = new HashMap<>(14);

    static {
        sendPayPromise.put("10", 120L);
        sendPayPromise.put("12", 120L);
        sendPayPromise.put("13", 60L);
        sendPayPromise.put("15", 60L);
        sendPayPromise.put("16", 30L);
        sendPayPromise.put("18", 30L);
        sendPayPromise.put("20", 120L);
        sendPayPromise.put("22", 120L);
        sendPayPromise.put("23", 60L);
        sendPayPromise.put("25", 60L);
        sendPayPromise.put("26", 30L);
        sendPayPromise.put("28", 30L);
        sendPayPromise.put("40", 300L);
        sendPayPromise.put("38", 300L);
    }

    @Override
    public void execute(@NotNull OrderFulfillmentContext context) {
        //获取时效信息
        OrderFulfillmentModel model = context.getModel();
        Warehouse warehouse = warehouseServiceAcl.getWarehouseById(model.getWarehouse().getWarehouseId());
        TransportationTypeEnum transportationType = populateTransportationType(model);
        WarehouseAndDeliveryTimeRequest request = modelToRequest(model, warehouse, transportationType);
        if(switchKeyCommonUtils.staticRouteTimeDeptNoContains(model.getCustomer().getAccountNo())){
            supplySiteInfo(request, model, warehouse);
        }
        WarehouseAndDeliveryTimeResponse response = promiseAcl.getPromise(request);
        if (response == null || response.getResultCode() != 1) {
            log.warn("Promise返回为空或返回失败，跳过时效处理，流程继续");
            Watcher.fault(true);
            return;
        }
        //处理时效标位
        processPromiseMark(model, response.getSendpay());
        //给妥投时间赋值
        processDeliveryTime(model, response);
        //处理时效产品
        processPromiseProduct(model, response.getSendpay());
        // 判断是否为需要时效的订单,不需要时效则将运输类型设置为NO_PROMISE
        if (notNeedCodDate(model)) {
            model.getShipment().assignTransportationType(TransportationTypeEnum.NO_PROMISE);
            return;
        }
        //基本信息补齐
        processBaseInfo(model, response);
        if (response.getCoddate() == null) {
            return;
        }
        //精准达预计时间
        processAccurateDelivery(model, response);
        //极速达预计时间
        processLightningDelivery(model, response);
    }

    private void processPromiseProduct(OrderFulfillmentModel model, String sendPay) {
        //ducc开关，默认关闭不执行
        if(!promise211Switch){
            return;
        }
        if (StringUtils.isEmpty(sendPay)) {
            return;
        }
        char seventeen = sendPay.charAt(0);
        if ('1' != seventeen) {
            return;
        }
        String addProduct = productCenterServiceAcl.readParamValue(model.getCustomer().getAccountNo(), AGEING_SERVICE);
        if (StringUtils.isEmpty(addProduct) || !addProduct.contains(ProductEnum.PROMISE_211.getCode())) {
            return;
        }
        //非B2C订单、非京东平台、非sc-m-0001(中小件商务仓产品)，不追加211时效增值服务
        if (!OrderBusinessTypeEnum.B2C.equals(model.getOrderBusinessType())
                || !"1".equals(model.getChannel().getChannelNo())
                || !model.getProducts().getProductCodes().contains(ProductEnum.SMALL_BUSINESS_WAREHOUSE.getCode())) {
            return;
        }
        //追加211时效增值产品编码
        model.getProducts().enroll(ProductEnum.PROMISE_211.toProduct(), Boolean.TRUE, model);
    }

    protected TransportationTypeEnum populateTransportationType(OrderFulfillmentModel model){
        return promiseTransportationTypeExt.populateTransportationType(model);
    }

    protected WarehouseAndDeliveryTimeRequest modelToRequest(OrderFulfillmentModel model, Warehouse warehouse, TransportationTypeEnum transportationType){
        return promiseTranslator.modelToRequest(model, warehouse, transportationType);
    }

    /**
     * 极速达处理
     */
    private void processLightningDelivery(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        if (!model.getProducts().isEnrolled(ProductEnum.LIGHTNING_DELIVERY)) {
            return;
        }
        // 预计送达时间，格式为xx年xx月xx日 xx:xx-xx:xx送达
        Date[] dates;
        try {
            dates = DateUtil.processDateRange(response.getCoddate(), response.getPromiseMsg());
        } catch (Exception e) {
            log.error("promise返回结果-->{} - {} promise{}解析时效,response.promiseMsg:{}错误",
                    model.orderNo(), model.getChannel().getChannelOrderNo(), ProductEnum.LIGHTNING_DELIVERY.getDesc(), response.getPromiseMsg());
            throw new DomainServiceException(UnifiedErrorSpec.Business.ORDER_BUSINESS_EXCEPTION, e).withCustom(
                    "promise返回结果-->" + model.orderNo() + " - " +
                            model.getChannel().getChannelOrderNo() + " promise" +
                            ProductEnum.LIGHTNING_DELIVERY.getDesc() + "解析时效错误" + e.getMessage()
            );
        }
        //处理成功
        model.getShipment().assignExpectDeliveryStartTime(dates[0]);
        model.getShipment().assignExpectDeliveryEndTime(dates[1]);
    }

    /**
     * 精准达处理
     */
    private void processAccurateDelivery(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        if (!model.getProducts().isEnrolled(ProductEnum.ACCURATE_DELIVERY)) {
            return;
        }
        //获取时长
        Long promiseMark = sendPayPromise.get(new String(new char[]{response.getSendpay().charAt(116), response.getSendpay().charAt(117)}));
        if (promiseMark == null) {
            log.info("soNo:{},sendPay标位超出京准达业务范围,京准达降级处理,sendPay:{}",
                    model.getOrderNo(), response.getSendpay());
            //移除精准达产品
            model.getProducts().remove(ProductEnum.ACCURATE_DELIVERY);
            return;
        }
        // 预计送达时间，格式为xx年xx月xx日 xx:xx-xx:xx送达
        Date[] dates;
        try {
            dates = DateUtil.processDateRange(response.getCoddate(), response.getPromiseMsg());
        } catch (Exception e) {
            log.error("promise返回结果-->{} - {} promise{}解析时效,response.promiseMsg:{}错误",
                    model.orderNo(), model.getChannel().getChannelOrderNo(), ProductEnum.ACCURATE_DELIVERY.getDesc(), response.getPromiseMsg());
            throw new DomainServiceException(UnifiedErrorSpec.Business.ORDER_BUSINESS_EXCEPTION, e).withCustom(
                    "promise返回结果-->" + model.orderNo() + " - " +
                            model.getChannel().getChannelOrderNo() + " promise" +
                            ProductEnum.ACCURATE_DELIVERY.getDesc() + "解析时效错误" + e.getMessage()
            );
        }

        //时长比较判断
        Duration duration = new Duration(dates[0].getTime(), dates[1].getTime());
        if (promiseMark != duration.getStandardMinutes()) {
            log.info("soNo:{},sendPay标位和返回的时效不符合,京准达降级处理,promiseMsg:{},promiseMark:{},sendPay:{}",
                    model.getOrderNo(), response.getPromiseMsg(), promiseMark, response.getSendpay());

            //移除精准达产品
            model.getProducts().remove(ProductEnum.ACCURATE_DELIVERY);
            return;
        }
        //处理成功
        model.getShipment().assignExpectDeliveryStartTime(dates[0]);
        model.getShipment().assignExpectDeliveryEndTime(dates[1]);
    }

    /**
     * 补齐基本信息
     *
     * @param model
     * @param response
     */
    private void processBaseInfo(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        model.getShipment().assignFenceId(response.getFenceId());
        model.getShipment().assignFenceType(response.getFenceType());
        // 如果信任商家的运输方式,则使用商家建单时候传入的运输方式,否则使用promise推断运输方式
        if (!productCenterServiceAcl.isTrustSellerTransport(model.getCustomer().getAccountNo())) {
            model.getShipment().assignTransportationType(deduceTransportTypeFromPromise(model, response));
        }
        if (response.getCoddate() == null) {
            model.getShipment().assignIgnorePromise(true);
            return;
        } else {
            model.getShipment().assignIgnorePromise(false);
        }
        // 时效信息更新赋值
        model.getShipment().assignExpectDeliveryStartTime(response.getCoddate());
        model.getShipment().assignExpectDeliveryEndTime(response.getCoddate());
        if (switchKeyUtils.containBpromiseDeptNo(model.getCustomer().getAccountNo())) {
            processCollectTime(model, response);
        }
        //时效降级信息
        model.getFulfillment().assignTimelinessDegradeType(MapUtils.getInteger(response.getSendpayUpdate(), TIMELINESS_DEGRADE_TYPE_MARK));
    }

    /**
     * 揽收时间处理
     * 若只获取到“仓配交接时间”，则使用【仓配交接时间-1h】作为“取件开始时间”下传配运。
     * 若未获取到“仓配交接时间”，或“预计打包时间”小于当前时间 或 【仓配交接时间-1h】小于当前时间，则“取件开始/结束时间”均不下传。
     *
     * @param model
     * @param response
     */
    private void processCollectTime(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        if (response.getStoreDeliveryHandoverTime() == null) {
            return;
        }
        Date collectStartTime = response.getExpectPackageDate() == null ? DateUtil.plusMinutes(response.getStoreDeliveryHandoverTime(), -60) : response.getExpectPackageDate();
        Date collectEndTime = response.getStoreDeliveryHandoverTime();
        Date now = new Date();
        if (collectStartTime.before(now)) {
            log.warn("揽收开始时间早于当前时间，取件开始/结束时间均不下传，orderNo:{}", model.orderNo());
            return;
        }
        model.getShipment().assignCollectStartTime(collectStartTime);
        model.getShipment().assignCollectEndTime(collectEndTime);
    }

    /**
     * 根据promise返回结果返回最终运输方式类型
     */
    private TransportationTypeEnum deduceTransportTypeFromPromise(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        // 陆运
        if (com.jd.fce.dos.service.enums.TransportationTypeEnum.LAND.getValue() == response.getTransportType()) {
            return TransportationTypeEnum.LAND;
        }
        // 航空
        if (com.jd.fce.dos.service.enums.TransportationTypeEnum.AVIATION.getValue() == response.getTransportType()) {
            // 当promise返回的运输方式为航空,但是商家的期望运输方式为陆运时,使用商家期望运输方式
            if (model.getShipment().getSellerExpectTransport() == TransportationTypeEnum.LAND) {
                return TransportationTypeEnum.LAND;
            } else {
                return TransportationTypeEnum.AVIATION;
            }
        }
        // 陆运填仓
        if (com.jd.fce.dos.service.enums.TransportationTypeEnum.FLIGHT_FILLING.getValue() == response.getTransportType()) {
            return TransportationTypeEnum.LAND_WITH_FILLING;
        }
        log.warn("Promise返回未识别的transportType，重置运输类型，跳过时效处理，流程继续");
        return TransportationTypeEnum.NO_PROMISE;
    }


    /**
     * 处理妥投时间
     */
    private void processDeliveryTime(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        model.getShipment().assignDeliveryDate(response.getDeliveryDate());
        model.getShipment().assignBatchStartTime(response.getBatchStartTime());
        model.getShipment().assignBatchEndTime(response.getBatchEndTime());
    }

    /**
     * 不需要时效的订单 只需要跨区标 TODO 目前POP没有
     */
    private boolean notNeedCodDate(OrderFulfillmentModel model) {
        if (ShipperTypeEnum.ZI_CSF == model.getShipment().getShipperType()
                || ShipperTypeEnum.SELF_PICKUP == model.getShipment().getShipperType()
                || ShipperTypeEnum.JPZSF == model.getShipment().getShipperType()) {
            log.info("orderNo:{}承运商类型{}，不需要时效", model.orderNo(), model.getShipment().getShipperType().getName());
            return true;
        }
        return false;
    }


    /**
     * 时效打标
     *
     * @param model
     * @param sendPay
     */
    public static void processPromiseMark(OrderFulfillmentModel model, String sendPay) {
        if (StringUtils.isEmpty(sendPay)) {
            return;
        }

        // Sendpay第17位=2时，异地生成订单（跨配送中心订单）
        char seventeen = sendPay.charAt(16);
        char[] promiseTime211 = {sendPay.charAt(0), sendPay.charAt(29), sendPay.charAt(34)};
        if ('2' == seventeen) {
            model.getNonstandardProducts().enroll(NonstandardProductConstants.ACROSS_DISTRIBUTION_CENTER_ORDER);
        }
        model.getNonstandardProducts().enroll(NonstandardProductConstants.PROMISE_TIME_211, String.valueOf(promiseTime211));
    }

    /**
     * 补充网点信息，调预分拣接口获取不到时降级为不赋值
     */
    private void supplySiteInfo(WarehouseAndDeliveryTimeRequest request, OrderFulfillmentModel model, Warehouse warehouse) {
        try {
            // 判断是否仓配揽收
            boolean isDeliveryCollection = model.isDeliveryCollection();
            if (isDeliveryCollection) {
                // 仓配揽收的处理逻辑
                request.setStartSiteId(getBaseSite(model.orderNo(), model, PreSortCalculateSceneEnum.PICK_UP.code));
                request.setEndSiteId(getBaseSite(model.orderNo(), model, PreSortCalculateSceneEnum.SEND.code));
                request.setPromiseTimeType(DATE_TYPE_COLLECT);
            } else {
                // 非仓配揽收的处理逻辑
                request.setEndSiteId(getBaseSite(model.orderNo(), model, PreSortCalculateSceneEnum.SEND.code));
                request.setPromiseTimeType(DATE_TYPE_PACKAGE);
                request.setDmsCode(generateDmsCode(warehouse));
            }
        } catch (Exception e) {
            log.warn("调前置预分拣接口获取网点信息异常，降级为不获取，orderNo:{}", model.getOrderNo(), e);
            Profiler.businessAlarm(env + "." + PROMISE_SITE_WARNING, " 商家:" + model.getCustomer().getAccountNo() + "调前置预分拣接口获取网点信息异常，降级为不获取,单号:" + model.getOrderNo());
        }

    }

    /**
     * 获取站点信息
     *
     * @param orderNo 订单编号
     * @param model 订单履行模型
     * @param calcSceneType 预分拣计算类型 1-揽件 2-派件
     * @return 青龙站点ID
     */
    private Integer getBaseSite(String orderNo, OrderFulfillmentModel model, Integer calcSceneType) {
        PreallocationResult preallocationResult = preSortCalcExecutorApiAcl.computePresort(orderNo,
                preSortCalcTranslator.buildComputePresortRequest(model, calcSceneType));
        return Optional.ofNullable(preallocationResult).map(PreallocationResult::getSiteId).orElse(null);
    }

    /**
     * 生成 DMS 代码的方法
     * @param warehouse
     * @return
     */
    private  String generateDmsCode(Warehouse warehouse) {
        return String.join(DELIMITER, SOURCE_WMS, warehouse.getDistributionNo(), warehouse.getErpWarehouseNo());
    }
}
