package com.jdl.sc.ofw.out.horiz.extension.serviceplus;

import com.jd.matrix.sdk.annotation.Extension;
import com.jd.serviceplus.order.dto.channel.GenericGoodsInfo;
import com.jd.serviceplus.order.dto.ofc.OFCCommitOrderRequest;
import com.jd.serviceplus.order.dto.ofc.OFCCustomerAddress;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.constant.OvasConstants;
import com.jdl.sc.ofw.out.domain.extension.serviceplus.IDeliveryInstallationExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ServicePlusAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.dto.serviceplus.ServicePlusCheckSkuResult;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 接单-送装服务-默认扩展点
 * <a href="https://joyspace.jd.com/pages/2CF7H1jx846tWKOTXVBo">供应链服务+</a>
 */
@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.GENERATE})
public class DeliveryInstallationDefaultExt implements IDeliveryInstallationExt {

    @Resource
    private ServicePlusAcl servicePlusAcl;

    @Resource
    private ProductCenterServiceAcl productCenterServiceAcl;

    @Override
    public void execute(OrderFulfillmentContext context) {
        OrderFulfillmentModel model = context.getModel();
        if (!ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())) {
            return;
        }
        final String deliveryInstallationMode = productCenterServiceAcl.readParamValue(model.getCustomer().getAccountNo(), OvasConstants.DELIVERY_INSTALLATION_MODE);
        if (StringUtils.isBlank(deliveryInstallationMode) || OvasConstants.NO_INSTALLATION.equals(deliveryInstallationMode)) {
            return;
        }
        // 目前只支持送装分离，不支持送装一体
        if (!OvasConstants.DELIVERY_OR_INSTALLATION.equals(deliveryInstallationMode)) {
            return;
        }

        try {
            // 订单的商品，有哪些安装服务
            final List<ServicePlusCheckSkuResult> servicePlusCheckSkuResults = servicePlusAcl.checkSku(model.orderNo(),
                    servicePlusAcl.buildOFCCheckSkuRequest(model.getCargos(), model.getCustomer().getAccountNo()));
            if (CollectionUtils.isEmpty(servicePlusCheckSkuResults)) {
                log.info("订单{}商品无京东服务+服务", model.orderNo());
                return;
            }

            final Set<String> cargosHaveInstallServiceSet = getCargosHaveInstallService(servicePlusCheckSkuResults, model.orderNo());
            if (CollectionUtils.isEmpty(cargosHaveInstallServiceSet)) {
                return;
            }

            // 异步返回服务单号。 本期 不用接收服务单号
            servicePlusAcl.create(buildOFCCommitOrderRequest(model, cargosHaveInstallServiceSet));
            model.getFulfillment().assignDeliveryInstallationMode(deliveryInstallationMode);
        } catch (Exception e) {
            log.error("订单{}调用京东服务+接口失败。业务降级", model.orderNo(), e);
        }
    }

    /**
     * 有服务+安装服务的商品
     */
    private Set<String> getCargosHaveInstallService(List<ServicePlusCheckSkuResult> servicePlusCheckSkuResults, String orderNo) {
        // 筛选出 3-安装服务，并获取skuCode
        final Set<String> cargosHaveInstallServiceSet = servicePlusCheckSkuResults.stream()
                .filter(servicePlusCheckSkuResult -> {
                    final Integer serviceItem = servicePlusCheckSkuResult.getServiceItem();
                    return serviceItem != null && serviceItem == 3;
                })
                .map(ServicePlusCheckSkuResult::getSkuCode)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(cargosHaveInstallServiceSet)) {
            log.info("订单{}商品无京东服务+服务：安装服务", orderNo);
        }
        return cargosHaveInstallServiceSet;
    }

    public OFCCommitOrderRequest buildOFCCommitOrderRequest(OrderFulfillmentModel model, Set<String> cargosHaveInstallServiceSet) {
        return OFCCommitOrderRequest.builder()
                .orderNumber(model.getOrderNo())
                .companyCode(model.getCustomer().getAccountNo())
                .companyName(model.getCustomer().getAccountName())
                .goodsInfoList(
                        model.getCargos().stream()
                                .filter(cargo -> cargosHaveInstallServiceSet.contains(cargo.getNo()))
                                .map(cargo ->
                                        GenericGoodsInfo.builder()
                                                .goodsCode(cargo.getNo())
                                                .goodsQty(cargo.getQuantity().getValue().intValue())
                                                .build()
                                )
                                .collect(Collectors.toList())
                )
                .customerAddressList(
                        Collections.singletonList(
                                OFCCustomerAddress.builder()
                                        .addressType(1) // 与服务+明确默认1。地址类型,枚举值：1-服务地址(家电清洗等)/取件地址(洗衣)；2-收货地址(洗衣)；3-旧件寄回地址；4-移桩后新地址；
                                        .customerContactName(model.getConsignee().getName())
                                        .customerContactPhone(StringUtils.isNotBlank(model.getConsignee().getMobile()) ? model.getConsignee().getMobile() : model.getConsignee().getPhone())
                                        .customerFullAddress(model.getConsignee().getAddress().getDetailAddress())
                                        .isVirtual("2".equals(model.getConsignee().getEncryptMode())) // 0-明文，1-加密，2-虚拟号
                                        .virtualNumber(StringUtils.isNotBlank(model.getConsignee().getMobile()) ? model.getConsignee().getMobile() : model.getConsignee().getPhone())
                                        .virtualNumberExpirationDate(model.getConsignee().getVirtualNumberExpirationDate())
                                        .build()
                        )
                )
                .build();
    }
}
