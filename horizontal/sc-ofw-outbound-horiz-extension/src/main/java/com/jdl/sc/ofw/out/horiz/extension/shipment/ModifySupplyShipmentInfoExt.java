package com.jdl.sc.ofw.out.horiz.extension.shipment;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.ability.pause.hanlder.OrderHoldRecalculateHandler;
import com.jdl.sc.ofw.out.common.constant.OvasConstants;
import com.jdl.sc.ofw.out.domain.dto.ModifyCommand;
import com.jdl.sc.ofw.out.domain.extension.shipment.ISupplyShipmentExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.PresortInfo;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.outbound.spec.enums.TransportModeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;


import java.util.HashMap;
import java.util.Map;

@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.MODIFY})
public class ModifySupplyShipmentInfoExt implements ISupplyShipmentExt {

    @Resource
    private OrderHoldRecalculateHandler orderHoldRecalculateHandler;

    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;
    @Override
    public void execute(OrderFulfillmentContext context) {
        ModifyCommand modifyCommand = context.getCommand();
        if (modifyCommand == null || modifyCommand.getShipment() == null) {
            return;
        }
        OrderFulfillmentModel persistenceModel = context.getModel();

        if (modifyCommand.getShipment().getPresortInfo() != null) {
            PresortInfo presortInfo = persistenceModel.getShipment().getPresortInfo();
            persistenceModel.getShipment().assignPresortInfo(PresortInfo.builder()
                    .siteNo(ObjectUtils.defaultIfNull(modifyCommand.getShipment().getPresortInfo().getSiteNo(),
                            ObjectUtils.nullIfNull(presortInfo, PresortInfo::getSiteNo)))
                    .siteName(ObjectUtils.defaultIfNull(modifyCommand.getShipment().getPresortInfo().getSiteName(),
                            ObjectUtils.nullIfNull(presortInfo, PresortInfo::getSiteName)))
                    .roadArea(ObjectUtils.defaultIfNull(modifyCommand.getShipment().getPresortInfo().getRoadArea(),
                            ObjectUtils.nullIfNull(presortInfo, PresortInfo::getRoadArea)))
                    .siteType(ObjectUtils.nullIfNull(presortInfo, PresortInfo::getSiteType))
                    .aoiCode(ObjectUtils.nullIfNull(presortInfo, PresortInfo::getAoiCode))
                    .build());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getDispatchNo())) {
            persistenceModel.getShipment().assignDispatchNo(modifyCommand.getShipment().getDispatchNo());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getLoadSequence())) {
            persistenceModel.getShipment().assignLoadSequence(modifyCommand.getShipment().getLoadSequence());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getCardNo())) {
            persistenceModel.getShipment().assignCardNo(modifyCommand.getShipment().getCardNo());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getPlanBeginEnterTime())){
            persistenceModel.getShipment().assignPlanBeginEnterTime(modifyCommand.getShipment().getPlanBeginEnterTime());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getTransportCount())) {
            persistenceModel.getShipment().assignTransportCount(modifyCommand.getShipment().getTransportCount());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getBunchQty())) {
            persistenceModel.getShipment().assignBunchQty(modifyCommand.getShipment().getBunchQty());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getKilometers())) {
            persistenceModel.getShipment().assignKilometers(modifyCommand.getShipment().getKilometers());
        }
        if (modifyCommand.getShipment().getTransportModeEnum() == TransportModeEnum.SORT_CENTER_COLLECT
                || modifyCommand.getShipment().getTransportModeEnum() == TransportModeEnum.DIRECT_DELIVERY) {
            persistenceModel.getShipment().assignTransportModeEnum(modifyCommand.getShipment().getTransportModeEnum());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getDeliveryStaffName())) {
            persistenceModel.getShipment().assignDeliveryStaffName(modifyCommand.getShipment().getDeliveryStaffName());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getVehicleNumber())) {
            persistenceModel.getShipment().assignVehicleNumber(modifyCommand.getShipment().getVehicleNumber());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getOperateTime())) {
            persistenceModel.getShipment().assignOperateTime(modifyCommand.getShipment().getOperateTime());
        }
        if (modifyCommand.getShipment().getSignBillTypeEnum() != null) {
            persistenceModel.getShipment().assignSignBillType(modifyCommand.getShipment().getSignBillTypeEnum());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getSignType())) {
            persistenceModel.getShipment().assignSignType(modifyCommand.getShipment().getSignType());
        }
        if (StringUtils.isNotBlank(modifyCommand.getShipment().getPlatformLogisticsService())) {
            persistenceModel.getShipment().assignPlatformLogisticsService(modifyCommand.getShipment().getPlatformLogisticsService());
        }
        if (modifyCommand.getShipment().getDeliveryNetworkResults() != null){
            persistenceModel.getShipment().assignDeliveryNetworkResults(modifyCommand.getShipment().getDeliveryNetworkResults());
        }
        if(switchKeyCommonUtils.mujiHKDCdeptNoContains(persistenceModel.getCustomer().getAccountNo())){
            if(modifyCommand.getShipment().getExpectDeliveryEndTime() != null){
                persistenceModel.getShipment().assignExpectDeliveryEndTime(modifyCommand.getShipment().getExpectDeliveryEndTime());
                orderHoldRecalculateHandler.orderHoldResumeTimeRecalculate(context.getModel(), modifyCommand.getOperator());
            }
        }
        if (modifyCommand.getShipment().getConsolidationPointCode() != null){
            persistenceModel.getShipment().assignConsolidationPointCode(modifyCommand.getShipment().getConsolidationPointCode());
        }
        if (modifyCommand.getShipment().getConsolidationPointName() != null){
            persistenceModel.getShipment().assignConsolidationPointName(modifyCommand.getShipment().getConsolidationPointName());
        }
        if (modifyCommand.getShipment().getServiceRequirements() != null
                && modifyCommand.getShipment().getServiceRequirements().get(OvasConstants.CHOOSE_TIME_DELIVERY_SERVICE) != null) {
            Map<String, String> serviceRequirements = ObjectUtils.defaultIfNull(persistenceModel.getShipment().getServiceRequirements(), new HashMap<>());
            serviceRequirements.put(OvasConstants.CHOOSE_TIME_DELIVERY_SERVICE, modifyCommand.getShipment().getServiceRequirements().get(OvasConstants.CHOOSE_TIME_DELIVERY_SERVICE));
            persistenceModel.getShipment().assignServiceRequirements(serviceRequirements);
        }
    }
}
