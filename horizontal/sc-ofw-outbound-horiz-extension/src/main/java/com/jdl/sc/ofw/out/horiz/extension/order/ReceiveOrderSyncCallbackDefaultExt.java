package com.jdl.sc.ofw.out.horiz.extension.order;

import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.FulfillmentInfo;
import cn.jdl.oms.supplychain.model.CallBackOutboundOrderRequest;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.BusinessDomainException;
import com.jdl.sc.ofw.out.domain.extension.order.IOrderSyncCallbackExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.rpc.OrderCenterAcl;
import com.jdl.sc.ofw.out.domain.translator.MarkTranslator;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.util.BusinessIdentityUtil;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.GENERATE})
public class ReceiveOrderSyncCallbackDefaultExt implements IOrderSyncCallbackExt {

    @Resource
    private OrderCenterAcl orderCenterAcl;

    @Resource
    private MarkTranslator markTranslator;

    @Override
    public void execute(OrderFulfillmentContext context) {

        final OrderFulfillmentModel model = context.getModel();
        try {
            final CallBackOutboundOrderRequest request = this.buildCallBackOutboundOrderRequest(model);
            orderCenterAcl.callBackOrder(request);
        }catch (Exception e){
            log.error("回传订单中心异常 orderNo:{}", model.getOrderNo(), e);
            throw new BusinessDomainException(UnifiedErrorSpec.Business.ORDER_BUSINESS_EXCEPTION);
        }
    }

    protected CallBackOutboundOrderRequest buildCallBackOutboundOrderRequest(OrderFulfillmentModel model) throws Exception {

        CallBackOutboundOrderRequest request = new CallBackOutboundOrderRequest();
        request.setBusinessIdentity(BusinessIdentityUtil.createIdentity(getOmsBusinessEnum()));
        request.setOrderNo(model.getOrderNo());
        request.setFulfillmentInfo(this.buildFulfilmentInfo(model));
        //订单扩展信息
        final HashMap<String, String> extendProps = new HashMap<>();
        if (DeliveryPerformanceChannelEnum.VIDEO_STORE.equals(model.getShipment().getDeliveryPerformanceChannel())){
            extendProps.put("orderMark", markTranslator.modelToMark(model));
            final HashMap<String, String> fulfillmentInfoMap = new HashMap<>();
            fulfillmentInfoMap.put("trustJDMetrics", BooleanUtils.toIntegerObject(model.getFulfillment().isTrustJDMetrics()).toString());
            extendProps.put("fulfillmentInfo", JsonUtil.toJson(fulfillmentInfoMap));
        }
        request.setExtendProps(extendProps);
        // 渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(new Date());
        channelInfo.setSystemCaller("SupplyOFC");
        request.setChannelInfo(channelInfo);
        request.setOperator("SupplyOFC");
        return request;
    }

    protected FulfillmentInfo buildFulfilmentInfo(OrderFulfillmentModel model) throws Exception {
        FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
        fulfillmentInfo.setExtendProps(this.buildFulfilmentInfoExtendProps(model));
        return fulfillmentInfo;
    }



    protected Map<String, String> buildFulfilmentInfoExtendProps(OrderFulfillmentModel model) throws Exception {
        final Map<String, String> fulfilmentInfoExtendProps = new HashMap<>();
        //微信视频号回传预取号和三方运单号
        wxVideoCallbackFulfilmentInfoExtendProps(fulfilmentInfoExtendProps, model);
        //七鲜时效追踪ID
        sevenFreshEtaTraceIdExtendProps(fulfilmentInfoExtendProps, model);
        return fulfilmentInfoExtendProps;
    }

    private void wxVideoCallbackFulfilmentInfoExtendProps(Map<String, String> fulfilmentInfoExtendProps, OrderFulfillmentModel model) throws Exception {
        final Shipment shipment = model.getShipment();
        if (!DeliveryPerformanceChannelEnum.VIDEO_STORE.equals(shipment.getDeliveryPerformanceChannel())){
            return;
        }
        if (shipment.getThirdPreOrderId() != null){
            final HashMap<String, String> thirdWaybillMap = new HashMap<>();
            if (ShipperTypeEnum.JPZSF.equals(model.getShipment().getShipperType()) && shipment.getThirdWayBill() != null) {
                thirdWaybillMap.put(shipment.getThirdWayBill(), shipment.getThirdPreOrderId());
            }
            if ((ShipperTypeEnum.ZI_CSF.equals(model.getShipment().getShipperType()) || ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType()))
                    && shipment.getWayBill() != null){
                thirdWaybillMap.put(shipment.getWayBill(), shipment.getThirdPreOrderId());
            }
            fulfilmentInfoExtendProps.put("uniqueIdElectronicWaybillPlatform", JsonUtil.toJson(Stream.of(thirdWaybillMap).collect(Collectors.toList())));
        }
    }

    /**
     * 七鲜时效追踪ID扩展属性处理
     * 从模型的 customField 中获取 etaTraceId 并添加到扩展属性中
     */
    private void sevenFreshEtaTraceIdExtendProps(Map<String, String> fulfilmentInfoExtendProps, OrderFulfillmentModel model) {
        // 从 customField 中获取 etaTraceId
        String customField = model.getFulfillment().getCustomField();
        if (StringUtils.isNotBlank(customField)) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, String> customFieldMap = JsonUtil.readValueSafe(customField, HashMap.class);
                String etaTraceId = customFieldMap.get("etaTraceId");
                if (StringUtils.isNotBlank(etaTraceId)) {
                    fulfilmentInfoExtendProps.put("etaTraceId", etaTraceId);
                    log.info("订单{}获取到etaTraceId: {}", model.getOrderNo(), etaTraceId);
                }
            } catch (Exception e) {
                log.warn("订单{}解析customField获取etaTraceId失败: {}", model.getOrderNo(), e.getMessage());
            }
        }
    }

    /**
     * 回传时业务身份
     * @return
     */
    protected BusinessIdentityUtil.OmsBusinessEnum getOmsBusinessEnum() {
        return BusinessIdentityUtil.OmsBusinessEnum.SALE_ORDER;
    }

}
