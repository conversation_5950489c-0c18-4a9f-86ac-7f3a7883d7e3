<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sc-ofw-outbound</artifactId>
        <groupId>com.jdl.sc</groupId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sc-ofw-outbound-horiz-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <com.clps.ucs.intserv.version>0.0.1-SNAPSHOT</com.clps.ucs.intserv.version>
        <com.clps.ucs.export.version>0.0.1-SNAPSHOT</com.clps.ucs.export.version>
        <zhongyouex.basic.version>1.0.0-SNAPSHOT</zhongyouex.basic.version>
        <zhongyouex.clothes.version>2.0-SNAPSHOT</zhongyouex.clothes.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>ser-sku-open-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.csc</groupId>
            <artifactId>csc-channel-intserv</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.etms.receive</groupId>
            <artifactId>etms-receive-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.warehouse.fulfillment</groupId>
            <artifactId>warehouse-fulfillment-query-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>core-jsf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>laf-extension-core</artifactId>
                    <groupId>com.jd.laf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-repository-support</artifactId>
        </dependency>
        <!--   log4j starts  -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <!--   log4j ends  -->

        <!-- mybatis -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>goods-center-domain</artifactId>
            <version>${goods.center.domain.versoin}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>goods-center-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.eclp</groupId>
                    <artifactId>goods-center-domain</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>core-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.open</groupId>
            <artifactId>open-ovas-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 事业部编码校验 -->
        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>eclp-master-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.dos</groupId>
                    <artifactId>dos-area-contract</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 产品中心 -->
        <dependency>
            <groupId>com.jd.open.sp</groupId>
            <artifactId>open-lpc-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--产品中心-->
        <dependency>
            <groupId>com.jd.open.sp</groupId>
            <artifactId>open-lpc-web-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- FIXME 退货仓？？ -->
        <dependency>
            <groupId>com.jd.eclp2.wms</groupId>
            <artifactId>eclp2_rtw_api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.eclp2.wms</groupId>
                    <artifactId>eclp2_common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--GIS-->
        <dependency>
            <groupId>com.jd.addresstranslation</groupId>
            <artifactId>addressTranslation-api</artifactId>
            <version>1.7.10-SNAPSHOT</version>
        </dependency>
        <!--下发三合一-->
        <dependency>
            <groupId>com.jd.lbcc.delivery</groupId>
            <artifactId>lbcc-delivery-api</artifactId>
            <version>1.1.130</version>
        </dependency>
        <!--下发全渠道-->
        <dependency>
            <groupId>com.jd.y</groupId>
            <artifactId>omnic-ms-sdk</artifactId>
            <version>1.3.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-domain</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <!--下发wms-->
        <dependency>
            <groupId>com.jd.staig</groupId>
            <artifactId>receiver-rpc</artifactId>
            <version>1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>staig-syncsender-rpc</artifactId>
            <groupId>com.jd.staig</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--包裹标签（大全表）查询服务-->
        <dependency>
            <groupId>com.jd.ql.basic</groupId>
            <artifactId>ql-basic-client</artifactId>
            <version>1.2.29-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 取消快退-->
        <dependency>
            <groupId>com.jd.fce.orbls</groupId>
            <artifactId>orbls-contract</artifactId>
            <version>0.0.18-jdk1.6-SNAPSHOT</version>
        </dependency>
        <!--商品服务-->
        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>eclp-goods-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>eclp-common</artifactId>
                    <groupId>com.jd.eclp</groupId>
                </exclusion>
                    <exclusion>
                        <groupId>com.jd.eclp</groupId>
                        <artifactId>goods-center-domain</artifactId>
                    </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.dos</groupId>
            <artifactId>dos-area-contract</artifactId>
            <version>0.3.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jim-cli-api</artifactId>
                    <groupId>com.jd.jim.cli</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jim-cli-jedis</artifactId>
                    <groupId>com.jd.jim.cli</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.ump</groupId>
                    <artifactId>profiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--京仓订单调台账服务-->
        <dependency>
            <groupId>com.jd.orderBank</groupId>
            <artifactId>order-bank-export</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jboss.resteasy</groupId>
                    <artifactId>resteasy-jaxrs</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metricsclient</artifactId>
                    <groupId>metricsclient</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.ump</groupId>
                    <artifactId>profiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jdl.express</groupId>
            <artifactId>jdl-eca-api</artifactId>
        </dependency>
        <!-- 商城订单中间件 -->
        <dependency>
            <groupId>com.jd.ioms</groupId>
            <artifactId>order-ioms-export</artifactId>
            <version>1.2.0-SNAPSHOT</version>
        </dependency>
        <!-- 商城ofc -->
        <dependency>
            <artifactId>jd-ofc-escort-export</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.jd.ofc</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- 承运商分单 -->
        <dependency>
            <groupId>com.jd.fce</groupId>
            <artifactId>eclp-os-service-contract</artifactId>
            <version>1.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <version>1.4.1-HOTFIX-T1</version>
            <type>pom</type> <!-- type 不能省略 -->
        </dependency>
        <!--jimdb相关-->
        <dependency>
            <groupId>com.jd.jmiss</groupId>
            <artifactId>jim-cli-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmiss</groupId>
            <artifactId>jim-cli-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmiss</groupId>
            <artifactId>jim-cli-driver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmiss</groupId>
            <artifactId>jim-cli-jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.open.sp</groupId>
            <artifactId>open-lpc-cp-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.clps</groupId>
            <artifactId>clps-ucs-intserv</artifactId>
            <version>${com.clps.ucs.intserv.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.clps</groupId>
            <artifactId>clps-ucs-export</artifactId>
            <version>${com.clps.ucs.export.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 纯配订单适配接口  -->
        <dependency>
            <groupId>cn.jdl.express</groupId>
            <artifactId>jdl-eca-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongyouex</groupId>
            <artifactId>zhongyouex-basic-api</artifactId>
            <version>${zhongyouex.basic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongyouex.clothes</groupId>
            <artifactId>zhongyouex-clothes-api</artifactId>
            <version>${zhongyouex.clothes.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.edi</groupId>
            <artifactId>edi-base-api-jdk6</artifactId>
            <version>1.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zhongyouex.clothes</groupId>
            <artifactId>zhongyouex-clothes-api</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.etms.preseparate</groupId>
            <artifactId>preseparate-saf-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>   <!-- 青龙预分拣接口升级 end -->
        </dependency>
        <dependency>
            <groupId>com.jd.promise</groupId>
            <artifactId>promise-dos-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-lfc-trans-pipe-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-lfc-trans-pipe-spec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.etms.vrs</groupId>
            <artifactId>vrs-platform-outer-api</artifactId>
            <version>2.8.56-SNAPSHOT</version>
        </dependency>
        <!-- B2B合同物流 -->
        <dependency>
            <groupId>com.jdwl.scm.tms</groupId>
            <artifactId>scm-basic-contractLogistics-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>eclp-jimkv-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jdl.pms</groupId>
            <artifactId>jdl-pms-client</artifactId>
            <version>0.0.28-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.jd.jfs</groupId>
            <artifactId>jfs-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jdl.pms</groupId>
            <artifactId>jdl-pms-basic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eclp2.wms</groupId>
            <artifactId>eclp2-wms-po-api</artifactId>
        </dependency>
        <!--jimkv-->
        <dependency>
            <groupId>com.jd.jimkv.cli</groupId>
            <artifactId>jimkv-cli-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.bms</groupId>
            <artifactId>bms-account-rpc-outer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-weblab-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.tc.seller.wb</groupId>
            <artifactId>workbench-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>wbms-wcs-client</artifactId>
            <groupId>com.jd.cp.wbms.wcs</groupId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>omc-jsf-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.express</groupId>
            <artifactId>express-o2o-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.eclp.edi</groupId>
            <artifactId>eclp-edi-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.las</groupId>
            <artifactId>las-servicecenter-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.ql.idservice</groupId>
            <artifactId>idservice-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.las</groupId>
            <artifactId>las-servicecenter-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.promise</groupId>
            <artifactId>external-front-store-client</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
