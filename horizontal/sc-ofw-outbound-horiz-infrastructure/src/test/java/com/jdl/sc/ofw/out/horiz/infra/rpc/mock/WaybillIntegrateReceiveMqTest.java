package com.jdl.sc.ofw.out.horiz.infra.rpc.mock;

import org.junit.Test;
import com.jd.lbcc.delivery.api.dto.request.*;
import static org.mockito.Mockito.when;
import com.jd.ldop.center.api.update.dto.WaybillUpdateBeforePickupDTO;

import com.jdl.sc.ofw.out.horiz.infra.rpc.mock.WaybillIntegrateReceiveMq;

import com.jd.jsf.gd.*;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import org.springframework.context.annotation.Profile;
import com.jdl.sc.ofw.out.horiz.infra.rpc.WaybillIntegrateReceiveAcl;
import com.jdl.sc.ofw.out.common.exception.StopExecutionException;
import com.jdl.sc.ofw.out.horiz.infra.rpc.util.ReplayReferService;
import com.jd.ldop.delivery.api.dto.VirtualSiteDTO;
import com.jd.ldop.delivery.api.dto.response.PreSortResultDTO;
import com.jd.ldop.delivery.api.dto.response.ResponseDTO;
import com.jd.ldop.center.api.receive.dto.*;
import com.jd.ldop.delivery.api.dto.response.IntegrateResultDTO;
import static org.mockito.ArgumentMatchers.any;
import com.jd.ldop.center.api.update.dto.*;
import com.jd.jsf.gd.msg.ResponseListener;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mock;
import com.jdl.sc.ofw.out.horiz.infra.facade.mq.JmqMessageProducer;
import com.jdl.sc.ofw.out.horiz.infra.collect.*;
import com.jd.ldop.delivery.api.dto.*;
import static org.junit.Assert.*;
import com.jdl.sc.core.json.JsonUtil;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import com.jd.ldop.center.api.waybill.dto.*;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import java.util.HashMap;
import com.jd.ldop.delivery.api.dto.response.*;
import org.springframework.beans.factory.annotation.Value;
import org.junit.runner.RunWith;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import com.jd.jsf.gd.GenericService;
import javax.annotation.Resource;
import com.jd.ldop.delivery.api.dto.request.WaybillIntegrateDTO;
import com.jd.ldop.delivery.api.dto.request.*;
import org.springframework.stereotype.Component;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the WaybillIntegrateReceiveMq class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class WaybillIntegrateReceiveMqTest {

	@Mock
	private GenericService genericService;

	@Mock
	private JmqMessageProducer jmqMessageProducer;

	@InjectMocks
	private WaybillIntegrateReceiveMq waybillIntegrateReceiveMq;

	@Mock
	private ReplayReferService replayReferService;

    @Test
    public void testModifyAppraisalWithNullInput() {
        // generated by JoyCoder taskId 75a8b73e168c
        WaybillIntegrateReceiveMq waybillIntegrateReceiveMq = new WaybillIntegrateReceiveMq();
        waybillIntegrateReceiveMq.modifyAppraisal(null);
    }

    @Test 
    public void testModifyAppraisalWithEmptyDTO() {
        // generated by JoyCoder taskId 75a8b73e168c
        WaybillIntegrateReceiveMq waybillIntegrateReceiveMq = new WaybillIntegrateReceiveMq();
        WaybillUpdateBeforePickupDTO dto = new WaybillUpdateBeforePickupDTO();
        waybillIntegrateReceiveMq.modifyAppraisal(dto);
    }

    @Test
    public void testModifyAppraisalWithFullDTO() {
        // generated by JoyCoder taskId 75a8b73e168c
        WaybillIntegrateReceiveMq waybillIntegrateReceiveMq = new WaybillIntegrateReceiveMq();
        
        WaybillUpdateBeforePickupDTO dto = new WaybillUpdateBeforePickupDTO();
        dto.setWaybillCode("TEST123");
        dto.setReceiveName("Test Name");
        dto.setReceiveTel("1234567890");
        dto.setReceiveMobile("13800138000");
        dto.setSignReturn(1);
        dto.setGuaranteeValue(1);
        dto.setGuaranteeValueAmount(100.0);
        dto.setDeliveryUpstairs(1);
        dto.setSendToWarehouse(1);
        dto.setReceiveAddress("Test Address");
        dto.setPackagingService(1);
        dto.setProductType(1);
        
        waybillIntegrateReceiveMq.modifyAppraisal(dto);
    }

    @Test
    public void testModifyAppraisalWithMaxValues() {
        // generated by JoyCoder taskId 75a8b73e168c
        WaybillIntegrateReceiveMq waybillIntegrateReceiveMq = new WaybillIntegrateReceiveMq();
        
        WaybillUpdateBeforePickupDTO dto = new WaybillUpdateBeforePickupDTO();
        dto.setWaybillCode("TEST123");
        dto.setReceiveName("Test Name"); // 50 chars
        dto.setReceiveTel("12345678901234567890"); // 20 chars
        dto.setReceiveMobile("123456789012345678901234567890"); // 30 chars
        dto.setSignReturn(8);
        dto.setGuaranteeValue(Integer.MAX_VALUE);
        dto.setGuaranteeValueAmount(Double.MAX_VALUE);
        dto.setDeliveryUpstairs(1);
        dto.setSendToWarehouse(1);
        dto.setReceiveAddress("A"); // 256 chars
        dto.setPackagingService(1);
        dto.setProductType(15);
        
        waybillIntegrateReceiveMq.modifyAppraisal(dto);
    }

    @Test
    public void testModifyAppraisalWithMinValues() {
        // generated by JoyCoder taskId 75a8b73e168c
        WaybillIntegrateReceiveMq waybillIntegrateReceiveMq = new WaybillIntegrateReceiveMq();
        
        WaybillUpdateBeforePickupDTO dto = new WaybillUpdateBeforePickupDTO();
        dto.setWaybillCode("TEST123");
        dto.setReceiveName("");
        dto.setReceiveTel("");
        dto.setReceiveMobile("");
        dto.setSignReturn(0);
        dto.setGuaranteeValue(0);
        dto.setGuaranteeValueAmount(0.0);
        dto.setDeliveryUpstairs(0);
        dto.setSendToWarehouse(0);
        dto.setReceiveAddress("");
        dto.setPackagingService(0);
        dto.setProductType(0);
        
        waybillIntegrateReceiveMq.modifyAppraisal(dto);
    }

    @Test
    public void testReceiveWaybillSuccess() throws Exception {
        // generated by JoyCoder taskId 75a8b73e168c
        // Prepare test data
        String orderNo = "123";
        String customerOrderNo = "456";
        WaybillIntegrateDTO waybillIntegrateDTO = new WaybillIntegrateDTO();
        waybillIntegrateDTO.setWaybillType(1);
        
        Map<String, Object> preSortResult = new HashMap<>();
        preSortResult.put("siteId", 100);
        preSortResult.put("siteName", "TestSite");
        preSortResult.put("siteType", 1);
        preSortResult.put("road", "TestRoad");
        preSortResult.put("smileType", 1);

        Map<String, Object> virtualSiteDTO = new HashMap<>();
        virtualSiteDTO.put("virtualSiteId", 200);
        virtualSiteDTO.put("virtualSiteName", "VirtualSite");
        virtualSiteDTO.put("branchCode", "B001");
        virtualSiteDTO.put("settlementCode", "S001");

        Map<String, Object> result = new HashMap<>();
        result.put("waybillCode", "WB001");
        result.put("preSortResult", preSortResult);
        result.put("virtualSiteDTO", virtualSiteDTO);

        // Mock dependencies
        Mockito.when(replayReferService.getReplayService()).thenReturn(genericService);
        Mockito.when(genericService.$invoke(eq("queryPresortInfo"), any(), any())).thenReturn(result);
        Mockito.doNothing().when(jmqMessageProducer).send(any(), any(), any());

        // Execute
        ResponseDTO<IntegrateResultDTO> response = waybillIntegrateReceiveMq.receiveWaybill(orderNo, waybillIntegrateDTO, customerOrderNo);

        // Verify
        Assert.assertEquals(ResponseDTO.SUCCESS_CODE, response.getStatusCode());
        Assert.assertNotNull(response.getData());
        Assert.assertEquals("WB001", response.getData().getWaybillCode());
        Assert.assertEquals(0, response.getData().getPreSortResultDTO().getResultCode().intValue());
        Assert.assertEquals("成功", response.getData().getPreSortResultDTO().getResultMessage());
    }

    @Test(expected = InfrastructureException.class) 
    public void testReceiveWaybillJsonConversionError() {
        // generated by JoyCoder taskId 75a8b73e168c
        waybillIntegrateReceiveMq.receiveWaybill(null, null, null);
    }

    @Test(expected = StopExecutionException.class)
    public void testReceiveWaybillNoEclpRecord() {
        // generated by JoyCoder taskId 75a8b73e168c
        // Prepare test data
        String orderNo = "123";
        String customerOrderNo = "456"; 
        WaybillIntegrateDTO waybillIntegrateDTO = new WaybillIntegrateDTO();
        waybillIntegrateDTO.setWaybillType(1);

        // Mock dependencies
        Mockito.when(replayReferService.getReplayService()).thenReturn(genericService);
        Mockito.when(genericService.$invoke(eq("queryPresortInfo"), any(), any())).thenReturn(null);

        // Execute
        waybillIntegrateReceiveMq.receiveWaybill(orderNo, waybillIntegrateDTO, customerOrderNo);
    }

    @Test(expected = RuntimeException.class)
    public void testReceiveWaybillMqSendError() {
        // generated by JoyCoder taskId 75a8b73e168c
        // Prepare test data
        String orderNo = "123";
        String customerOrderNo = "456";
        WaybillIntegrateDTO waybillIntegrateDTO = new WaybillIntegrateDTO();
        waybillIntegrateDTO.setWaybillType(1);

        Map<String, Object> result = new HashMap<>();
        result.put("waybillCode", "WB001");

        // Mock dependencies
        Mockito.when(replayReferService.getReplayService()).thenReturn(genericService);
        Mockito.when(genericService.$invoke(eq("queryPresortInfo"), any(), any())).thenReturn(result);
        Mockito.doThrow(new RuntimeException()).when(jmqMessageProducer).send(any(), any(), any());

        // Execute
        waybillIntegrateReceiveMq.receiveWaybill(orderNo, waybillIntegrateDTO, customerOrderNo);
    }

    @Test
    public void testReceiveWaybillNoPreSortResult() {
        // generated by JoyCoder taskId 75a8b73e168c
        // Prepare test data
        String orderNo = "123";
        String customerOrderNo = "456";
        WaybillIntegrateDTO waybillIntegrateDTO = new WaybillIntegrateDTO();
        waybillIntegrateDTO.setWaybillType(1);

        Map<String, Object> result = new HashMap<>();
        result.put("waybillCode", "WB001");

        // Mock dependencies
        Mockito.when(replayReferService.getReplayService()).thenReturn(genericService);
        Mockito.when(genericService.$invoke(eq("queryPresortInfo"), any(), any())).thenReturn(result);
        Mockito.doNothing().when(jmqMessageProducer).send(any(), any(), any());

        // Execute
        ResponseDTO<IntegrateResultDTO> response = waybillIntegrateReceiveMq.receiveWaybill(orderNo, waybillIntegrateDTO, customerOrderNo);

        // Verify
        Assert.assertEquals(ResponseDTO.SUCCESS_CODE, response.getStatusCode());
        Assert.assertNotNull(response.getData());
        Assert.assertEquals("WB001", response.getData().getWaybillCode());
    }

    @Test
    public void testReceiveWaybillNoVirtualSite() {
        // generated by JoyCoder taskId 75a8b73e168c
        // Prepare test data
        String orderNo = "123";
        String customerOrderNo = "456";
        WaybillIntegrateDTO waybillIntegrateDTO = new WaybillIntegrateDTO();
        waybillIntegrateDTO.setWaybillType(1);

        Map<String, Object> preSortResult = new HashMap<>();
        preSortResult.put("siteId", 100);
        preSortResult.put("siteName", "TestSite");
        preSortResult.put("siteType", 1);
        preSortResult.put("road", "TestRoad");
        preSortResult.put("smileType", 1);

        Map<String, Object> result = new HashMap<>();
        result.put("waybillCode", "WB001");
        result.put("preSortResult", preSortResult);

        // Mock dependencies
        Mockito.when(replayReferService.getReplayService()).thenReturn(genericService);
        Mockito.when(genericService.$invoke(eq("queryPresortInfo"), any(), any())).thenReturn(result);
        Mockito.doNothing().when(jmqMessageProducer).send(any(), any(), any());

        // Execute
        ResponseDTO<IntegrateResultDTO> response = waybillIntegrateReceiveMq.receiveWaybill(orderNo, waybillIntegrateDTO, customerOrderNo);

        // Verify
        Assert.assertEquals(ResponseDTO.SUCCESS_CODE, response.getStatusCode());
        Assert.assertNotNull(response.getData());
        Assert.assertEquals("WB001", response.getData().getWaybillCode());
        Assert.assertNull(response.getData().getVirtualSiteDTO());
    }

}