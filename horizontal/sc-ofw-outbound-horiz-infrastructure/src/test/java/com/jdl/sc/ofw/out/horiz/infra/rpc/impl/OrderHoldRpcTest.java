package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jdl.sc.order.hold.common.Response;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;

import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.OrderHoldRpc;

import com.jdl.sc.order.hold.api.dto.request.*;
import java.util.List;
import com.jdl.sc.order.hold.api.dto.request.CreateRequest;
import lombok.AllArgsConstructor;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.OrderHoldQueryService;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.order.hold.api.ReDecisionQueryService;
import com.jdl.sc.order.hold.common.*;
import java.util.Arrays;
import com.jdl.sc.order.hold.api.dto.request.ResumeRequest;
import com.jdl.sc.order.hold.api.dto.*;
import com.jdl.sc.order.hold.api.dto.request.ModifyRequest;
import com.jdl.sc.order.hold.api.dto.response.HoldResult;
import static com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum.RE_DECISION_PAUSE;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.order.hold.api.dto.request.HoldRequest;
import com.jdl.sc.order.hold.api.dto.response.ReDecisionRecord;
import com.jdl.sc.order.hold.api.dto.request.CloseRequest;
import com.jdl.sc.order.hold.api.OrderHoldService;
import com.jdl.sc.order.hold.api.dto.response.*;
import org.springframework.stereotype.Component;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the OrderHoldRpc class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderHoldRpcTest {

	@InjectMocks
	private OrderHoldRpc orderHoldRpc;

	@Mock
	private OrderHoldService orderHoldService;

	@Mock
	private ReDecisionQueryService reDecisionQueryService;

	@Mock
	private OrderHoldQueryService orderHoldQueryService;

    @Test
    public void testConstructorWithValidParameters() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        OrderHoldService orderHoldService = mock(OrderHoldService.class);
        OrderHoldQueryService orderHoldQueryService = mock(OrderHoldQueryService.class);
        ReDecisionQueryService reDecisionQueryService = mock(ReDecisionQueryService.class);

        // Act
        OrderHoldRpc orderHoldRpc = new OrderHoldRpc(orderHoldService, orderHoldQueryService, reDecisionQueryService);

        // Assert
        assertNotNull(orderHoldRpc);
    }

    @Test(expected = NullPointerException.class)
    public void testConstructorWithNullOrderHoldService() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        OrderHoldQueryService orderHoldQueryService = mock(OrderHoldQueryService.class);
        ReDecisionQueryService reDecisionQueryService = mock(ReDecisionQueryService.class);

        // Act
        new OrderHoldRpc(null, orderHoldQueryService, reDecisionQueryService);
    }

    @Test(expected = NullPointerException.class)
    public void testConstructorWithNullOrderHoldQueryService() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        OrderHoldService orderHoldService = mock(OrderHoldService.class);
        ReDecisionQueryService reDecisionQueryService = mock(ReDecisionQueryService.class);

        // Act
        new OrderHoldRpc(orderHoldService, null, reDecisionQueryService);
    }

    @Test(expected = NullPointerException.class)
    public void testConstructorWithNullReDecisionQueryService() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        OrderHoldService orderHoldService = mock(OrderHoldService.class);
        OrderHoldQueryService orderHoldQueryService = mock(OrderHoldQueryService.class);

        // Act
        new OrderHoldRpc(orderHoldService, orderHoldQueryService, null);
    }

    @Test(expected = NullPointerException.class) 
    public void testConstructorWithAllNullParameters() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Act
        new OrderHoldRpc(null, null, null);
    }

    @Test
    public void queryReDecisionListSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        String orderNo = "TEST001";
        List<ReDecisionRecord> records = new ArrayList<>();
        ReDecisionRecord record = ReDecisionRecord.builder()
                .orderNo(orderNo)
                .build();
//        record.setOrderNo(orderNo);
        records.add(record);
        
        Response<List<ReDecisionRecord>> response = new Response<>(200, "success", records);
        Mockito.when(reDecisionQueryService.queryList(any(String.class), any(String.class)))
                .thenReturn(response);

        // Act
        List<ReDecisionRecord> result = orderHoldRpc.queryReDecisionList(orderNo);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void queryReDecisionListRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        String orderNo = "TEST002";
        Response<List<ReDecisionRecord>> response = new Response<>(200, "异常记录不存在", null);
        Mockito.when(reDecisionQueryService.queryList(any(String.class), any(String.class)))
                .thenReturn(response);

        // Act
        List<ReDecisionRecord> result = orderHoldRpc.queryReDecisionList(orderNo);

        // Assert
        assertNull(result);
    }

    @Test
    public void queryReDecisionListServiceFailure() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        String orderNo = "TEST003";
        Response<List<ReDecisionRecord>> response = new Response<>(500, "接口返回失败", null);
        Mockito.when(reDecisionQueryService.queryList(any(String.class), any(String.class)))
                .thenReturn(response);

        // Act
        List<ReDecisionRecord> result = orderHoldRpc.queryReDecisionList(orderNo);

        // Assert
        assertNull(result);
    }

    @Test
    public void queryReDecisionListServiceException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        String orderNo = "TEST004";
        Mockito.when(reDecisionQueryService.queryList(any(String.class), any(String.class)))
                .thenThrow(new RuntimeException("Service Exception"));

        // Act
        List<ReDecisionRecord> result = orderHoldRpc.queryReDecisionList(orderNo);

        // Assert
        assertNull(result);
    }

    @Test
    public void queryReDecisionListEmptyResponse() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        String orderNo = "TEST005";
        List<ReDecisionRecord> emptyList = new ArrayList<>();
        Response<List<ReDecisionRecord>> response = new Response<>(200, "success", emptyList);
        Mockito.when(reDecisionQueryService.queryList(any(String.class), any(String.class)))
                .thenReturn(response);

        // Act
        List<ReDecisionRecord> result = orderHoldRpc.queryReDecisionList(orderNo);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testOrderHoldModifySuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ModifyRequest request = new ModifyRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");
        
        Response<Void> response = new Response<>(200, "success", null);
        Mockito.when(orderHoldService.orderHoldModify(any(ModifyRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldModify(request);
    }

    @Test
    public void testOrderHoldModifyRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ModifyRequest request = new ModifyRequest();
        request.setOrderNo("123");
        request.setOperator("test"); 
        request.setCode("code");

        Response<Void> response = new Response<>(500, "异常记录不存在", null);
        Mockito.when(orderHoldService.orderHoldModify(any(ModifyRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldModify(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldModifyReturnFailure() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ModifyRequest request = new ModifyRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");

        Response<Void> response = new Response<>(500, "failure", null);
        Mockito.when(orderHoldService.orderHoldModify(any(ModifyRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldModify(request);
    }

    @Test(expected = InfrastructureException.class) 
    public void testOrderHoldModifyException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ModifyRequest request = new ModifyRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");

        Mockito.when(orderHoldService.orderHoldModify(any(ModifyRequest.class)))
            .thenThrow(new RuntimeException("test exception"));

        // Act
        orderHoldRpc.orderHoldModify(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldModifyNullResponse() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ModifyRequest request = new ModifyRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");

        Mockito.when(orderHoldService.orderHoldModify(any(ModifyRequest.class))).thenReturn(null);

        // Act
        orderHoldRpc.orderHoldModify(request);
    }

    @Test
    public void testOrderHoldJudgeSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Prepare test data
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        HoldRequest holdRequest = new HoldRequest();
        holdRequest.setOrderInfo(orderInfo);

        HoldResult holdResult = HoldResult.builder().build();
        Response<HoldResult> response = new Response<>(200, "success", holdResult);

        // Mock
        Mockito.when(orderHoldService.orderHoldJudge(any(HoldRequest.class))).thenReturn(response);

        // Execute
        HoldResult result = orderHoldRpc.orderHoldJudge(holdRequest);

        // Verify
        Assert.assertEquals(holdResult, result);
    }

    @Test
    public void testOrderHoldJudgeRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Prepare test data
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        HoldRequest holdRequest = new HoldRequest();
        holdRequest.setOrderInfo(orderInfo);

        HoldResult holdResult = HoldResult.builder().build();
        Response<HoldResult> response = new Response<>(200, "异常记录不存在", holdResult);

        // Mock
        Mockito.when(orderHoldService.orderHoldJudge(any(HoldRequest.class))).thenReturn(response);

        // Execute
        HoldResult result = orderHoldRpc.orderHoldJudge(holdRequest);

        // Verify
        Assert.assertEquals(holdResult, result);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldJudgeReturnFailure() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Prepare test data
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        HoldRequest holdRequest = new HoldRequest();
        holdRequest.setOrderInfo(orderInfo);

        HoldResult holdResult = HoldResult.builder().build();
        Response<HoldResult> response = new Response<>(500, "error", holdResult);

        // Mock
        Mockito.when(orderHoldService.orderHoldJudge(any(HoldRequest.class))).thenReturn(response);

        // Execute
        orderHoldRpc.orderHoldJudge(holdRequest);
    }

    @Test(expected = InfrastructureException.class) 
    public void testOrderHoldJudgeException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Prepare test data
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        HoldRequest holdRequest = new HoldRequest();
        holdRequest.setOrderInfo(orderInfo);

        // Mock
        Mockito.when(orderHoldService.orderHoldJudge(any(HoldRequest.class))).thenThrow(new RuntimeException());

        // Execute
        orderHoldRpc.orderHoldJudge(holdRequest);
    }

    @Test
    public void testOrderHoldResumeSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ResumeRequest request = new ResumeRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");
        
        Response<Void> response = new Response<>(200, "success", null);
        when(orderHoldService.orderHoldResume(any(ResumeRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResume(request);
    }

    @Test
    public void testOrderHoldResumeRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ResumeRequest request = new ResumeRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");
        
        Response<Void> response = new Response<>(200, "异常记录不存在", null);
        when(orderHoldService.orderHoldResume(any(ResumeRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResume(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldResumeResponseFailed() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ResumeRequest request = new ResumeRequest();
        request.setOrderNo("123");
        request.setOperator("test"); 
        request.setCode("code");
        
        Response<Void> response = new Response<>(500, "error", null);
        when(orderHoldService.orderHoldResume(any(ResumeRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResume(request);
    }

    @Test(expected = InfrastructureException.class) 
    public void testOrderHoldResumeException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        ResumeRequest request = new ResumeRequest();
        request.setOrderNo("123");
        request.setOperator("test");
        request.setCode("code");
        
        when(orderHoldService.orderHoldResume(any(ResumeRequest.class))).thenThrow(new RuntimeException());

        // Act
        orderHoldRpc.orderHoldResume(request);
    }

    @Test
    public void testQueryOneHoldRecordSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        QueryRequest queryRequest = new QueryRequest();
        OrderHoldRecord expectedRecord = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> response = new Response<>(200, "success", expectedRecord);
        
        Mockito.when(orderHoldQueryService.queryOne(any(QueryRequest.class)))
               .thenReturn(response);

        // Act
        OrderHoldRecord result = orderHoldRpc.queryOneHoldRecord(queryRequest);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testQueryOneHoldRecordRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        QueryRequest queryRequest = new QueryRequest();
        Response<OrderHoldRecord> response = new Response<>(200, "异常记录不存在", null);
        
        Mockito.when(orderHoldQueryService.queryOne(any(QueryRequest.class)))
               .thenReturn(response);

        // Act
        OrderHoldRecord result = orderHoldRpc.queryOneHoldRecord(queryRequest);

        // Assert
        assertNull(result);
    }

    @Test
    public void testQueryOneHoldRecordResponseFailure() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        QueryRequest queryRequest = new QueryRequest();
        Response<OrderHoldRecord> response = new Response<>(500, "error", null);
        
        Mockito.when(orderHoldQueryService.queryOne(any(QueryRequest.class)))
               .thenReturn(response);

        // Act & Assert
        try {
            orderHoldRpc.queryOneHoldRecord(queryRequest);
            fail("Should throw InfrastructureException");
        } catch (InfrastructureException e) {
            assertEquals(UnifiedErrorSpec.ACL.RETURN_EXCEPTION.code(), e.code());
        }
    }

    @Test
    public void testQueryOneHoldRecordServiceException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        QueryRequest queryRequest = new QueryRequest();
        
        Mockito.when(orderHoldQueryService.queryOne(any(QueryRequest.class)))
               .thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        try {
            orderHoldRpc.queryOneHoldRecord(queryRequest);
            fail("Should throw InfrastructureException");
        } catch (InfrastructureException e) {
            assertEquals(UnifiedErrorSpec.ACL.CALL_EXCEPTION.code(), e.code());
        }
    }

    @Test
    public void testQueryOneHoldRecordNullRequest() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        Mockito.when(orderHoldQueryService.queryOne(null))
               .thenThrow(new NullPointerException());

        // Act & Assert
        try {
            orderHoldRpc.queryOneHoldRecord(null);
            fail("Should throw InfrastructureException");
        } catch (InfrastructureException e) {
            assertEquals(UnifiedErrorSpec.ACL.CALL_EXCEPTION.code(), e.code());
        }
    }

    @Test
    public void orderHoldCloseSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        closeRequest.setId(1L);
        closeRequest.setOrderNo("123");
        closeRequest.setCode("CODE1");
        
        OrderHoldRecord record = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> queryResponse = new Response<>(200, "success", record);
        Response<Void> closeResponse = new Response<>(200, "success", null);
        
        when(orderHoldQueryService.queryOne(any(QueryRequest.class))).thenReturn(queryResponse);
        when(orderHoldService.orderHoldClose(any(CloseRequest.class))).thenReturn(closeResponse);

        // Act
        orderHoldRpc.orderHoldClose(closeRequest);
    }

    @Test
    public void orderHoldCloseWhenQueryReturnsNull() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        closeRequest.setId(1L);
        closeRequest.setOrderNo("123");
        closeRequest.setCode("CODE1");
        
        Response<OrderHoldRecord> queryResponse = new Response<>(200, "success", null);
        
        when(orderHoldQueryService.queryOne(any(QueryRequest.class))).thenReturn(queryResponse);

        // Act
        orderHoldRpc.orderHoldClose(closeRequest);
    }

    @Test
    public void orderHoldCloseWhenQueryReturnsExceptionMessage() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        closeRequest.setId(1L);
        closeRequest.setOrderNo("123");
        closeRequest.setCode("CODE1");
        
        Response<OrderHoldRecord> queryResponse = new Response<>(500, "异常记录不存在", null);
        
        when(orderHoldQueryService.queryOne(any(QueryRequest.class))).thenReturn(queryResponse);

        // Act
        orderHoldRpc.orderHoldClose(closeRequest);
    }

    @Test(expected = InfrastructureException.class)
    public void orderHoldCloseWhenQueryReturnsFailed() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        closeRequest.setId(1L);
        closeRequest.setOrderNo("123");
        closeRequest.setCode("CODE1");
        
        Response<OrderHoldRecord> queryResponse = new Response<>(500, "error", null);
        
        when(orderHoldQueryService.queryOne(any(QueryRequest.class))).thenReturn(queryResponse);

        // Act
        orderHoldRpc.orderHoldClose(closeRequest);
    }

    @Test(expected = InfrastructureException.class) 
    public void orderHoldCloseWhenCloseReturnsFailed() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        closeRequest.setId(1L);
        closeRequest.setOrderNo("123");
        closeRequest.setCode("CODE1");
        
        OrderHoldRecord record = OrderHoldRecord.builder().build();
        Response<OrderHoldRecord> queryResponse = new Response<>(200, "success", record);
        Response<Void> closeResponse = new Response<>(500, "error", null);
        
        when(orderHoldQueryService.queryOne(any(QueryRequest.class))).thenReturn(queryResponse);
        when(orderHoldService.orderHoldClose(any(CloseRequest.class))).thenReturn(closeResponse);

        // Act
        orderHoldRpc.orderHoldClose(closeRequest);
    }

    @Test(expected = InfrastructureException.class)
    public void orderHoldCloseWhenExceptionThrown() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        closeRequest.setId(1L);
        closeRequest.setOrderNo("123");
        closeRequest.setCode("CODE1");
        
        when(orderHoldQueryService.queryOne(any(QueryRequest.class))).thenThrow(new RuntimeException());

        // Act
        orderHoldRpc.orderHoldClose(closeRequest);
    }

    @Test
    public void testOrderHoldCreateSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CreateRequest request = new CreateRequest();
        request.setOrderNo("123");
        Response<Void> response = new Response<>(200, "success", null);
        Mockito.when(orderHoldService.orderHoldCreate(any(CreateRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldCreate(request);
    }

    @Test
    public void testOrderHoldCreateRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CreateRequest request = new CreateRequest();
        request.setOrderNo("123");
        Response<Void> response = new Response<>(200, "异常记录不存在", null);
        Mockito.when(orderHoldService.orderHoldCreate(any(CreateRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldCreate(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldCreateResponseFailed() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CreateRequest request = new CreateRequest();
        request.setOrderNo("123");
        Response<Void> response = new Response<>(500, "failed", null);
        Mockito.when(orderHoldService.orderHoldCreate(any(CreateRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldCreate(request);
    }

    @Test(expected = InfrastructureException.class) 
    public void testOrderHoldCreateException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CreateRequest request = new CreateRequest();
        request.setOrderNo("123");
        Mockito.when(orderHoldService.orderHoldCreate(any(CreateRequest.class))).thenThrow(new RuntimeException());

        // Act
        orderHoldRpc.orderHoldCreate(request);
    }

    @Test
    public void testOrderHoldCreateWithAllFields() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CreateRequest request = new CreateRequest("trace123", "order123", "type1", "op1", 
            "code1", "snapshot1", "msg1", new Date(), 1, "bu1", "scene1",
            "acc1", "ship1", "wh1", "cat1", "catName1", "props1");
        Response<Void> response = new Response<>(200, "success", null);
        Mockito.when(orderHoldService.orderHoldCreate(any(CreateRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldCreate(request);
    }

    @Test
    public void testOrderHoldCreateMinimalFields() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CreateRequest request = new CreateRequest();
        request.setOrderNo("123");
        request.setOrderType("type1");
        request.setOperator("op1");
        request.setCode("code1");
        Response<Void> response = new Response<>(200, "success", null);
        Mockito.when(orderHoldService.orderHoldCreate(any(CreateRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldCreate(request);
    }

    @Test
    public void testOrderHoldResumeWorkCloseSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest("trace123", 1L, "order123", "code1", "type1", "operator1");
        Response<Void> response = new Response<>(200, "success", null);
        Mockito.when(orderHoldService.orderHoldResumeWorkClose(any(CloseRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeWorkClose(closeRequest);

        // Assert
    }

    @Test
    public void testOrderHoldResumeWorkCloseRecordNotExist() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest("trace123", 1L, "order123", "code1", "type1", "operator1");
        Response<Void> response = new Response<>(200, "异常记录不存在", null);
        Mockito.when(orderHoldService.orderHoldResumeWorkClose(any(CloseRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeWorkClose(closeRequest);

        // Assert
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldResumeWorkCloseReturnFailure() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest("trace123", 1L, "order123", "code1", "type1", "operator1");
        Response<Void> response = new Response<>(500, "error", null);
        Mockito.when(orderHoldService.orderHoldResumeWorkClose(any(CloseRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeWorkClose(closeRequest);
    }

    @Test(expected = InfrastructureException.class) 
    public void testOrderHoldResumeWorkCloseException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest("trace123", 1L, "order123", "code1", "type1", "operator1");
        Mockito.when(orderHoldService.orderHoldResumeWorkClose(any(CloseRequest.class))).thenThrow(new RuntimeException());

        // Act
        orderHoldRpc.orderHoldResumeWorkClose(closeRequest);
    }

    @Test
    public void testOrderHoldResumeWorkCloseNullRequest() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        CloseRequest closeRequest = new CloseRequest();
        Response<Void> response = new Response<>(200, "success", null);
        Mockito.when(orderHoldService.orderHoldResumeWorkClose(any(CloseRequest.class))).thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeWorkClose(closeRequest);

        // Assert
    }

    @Test
    public void testOrderHoldResumeTimeRecalculateSuccess() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        HoldRequest request = new HoldRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        request.setOrderInfo(orderInfo);
        
        Response<Void> response = new Response<>(0, "success", null);
        when(orderHoldService.orderHoldResumeTimeRecalculate(any(HoldRequest.class)))
                .thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeTimeRecalculate(request);
    }

    @Test
    public void testOrderHoldResumeTimeRecalculateIgnorableError30000() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        HoldRequest request = new HoldRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        request.setOrderInfo(orderInfo);

        Response<Void> response = new Response<>(30000, "ignorable error", null);
        when(orderHoldService.orderHoldResumeTimeRecalculate(any(HoldRequest.class)))
                .thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeTimeRecalculate(request);
    }

    @Test
    public void testOrderHoldResumeTimeRecalculateIgnorableError30002() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        HoldRequest request = new HoldRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        request.setOrderInfo(orderInfo);

        Response<Void> response = new Response<>(30002, "ignorable error", null);
        when(orderHoldService.orderHoldResumeTimeRecalculate(any(HoldRequest.class)))
                .thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeTimeRecalculate(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldResumeTimeRecalculateNonSuccessResponse() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        HoldRequest request = new HoldRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        request.setOrderInfo(orderInfo);

        Response<Void> response = new Response<>(1, "error", null);
        when(orderHoldService.orderHoldResumeTimeRecalculate(any(HoldRequest.class)))
                .thenReturn(response);

        // Act
        orderHoldRpc.orderHoldResumeTimeRecalculate(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testOrderHoldResumeTimeRecalculateException() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Arrange
        HoldRequest request = new HoldRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo("123");
        request.setOrderInfo(orderInfo);

        when(orderHoldService.orderHoldResumeTimeRecalculate(any(HoldRequest.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        orderHoldRpc.orderHoldResumeTimeRecalculate(request);
    }

    @Test(expected = InfrastructureException.class) 
    public void testOrderHoldResumeTimeRecalculateNullRequest() {
        // generated by JoyCoder taskId ff8ec8254ce5
        // Act
        orderHoldRpc.orderHoldResumeTimeRecalculate(null);
    }

}