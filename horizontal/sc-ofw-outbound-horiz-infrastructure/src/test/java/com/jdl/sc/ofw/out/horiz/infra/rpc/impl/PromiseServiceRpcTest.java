package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jdl.promise.external.front.store.contract.promise.api.PromiseService;
import com.jdl.promise.external.front.store.contract.promise.dto.ConfigInfo;
import com.jdl.promise.external.front.store.contract.promise.dto.PromiseData;
import com.jdl.promise.external.front.store.contract.promise.param.PromiseRequest;
import com.jdl.promise.external.front.store.contract.resp.RpcResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromiseServiceRpcTest {

    @Mock
    private PromiseService promiseService;

    @InjectMocks
    private PromiseServiceRpc promiseServiceRpc;
    private PromiseRequest request;

    @Before
    public void setUp() {
        request = new PromiseRequest("123", "source", new Date(), "orderId", "storeId", "fenceId", "productId", 1, null);
    }

    @Test
    public void testGetPromiseInfoSuccessResponse() {
        when(promiseService.getPromiseInfo(any(PromiseRequest.class))).thenReturn(aSuccessResponse());

        RpcResponse<PromiseData> response = promiseServiceRpc.getPromiseInfo(request);

        assertNotNull(response);
    }

    @Test
    public void testGetPromiseInfoNullResponse() {
        when(promiseService.getPromiseInfo(any(PromiseRequest.class))).thenReturn(null);

        RpcResponse<PromiseData> response = promiseServiceRpc.getPromiseInfo(request);

        assertNull(response);
    }

    @Test
    public void testGetPromiseInfoFailResponse() {
        when(promiseService.getPromiseInfo(any(PromiseRequest.class))).thenReturn(aFailResponse());

        RpcResponse<PromiseData> response = promiseServiceRpc.getPromiseInfo(request);

        assertNull(response);
    }

    @Test
    public void testGetPromiseInfoNullDataResponse() {
        when(promiseService.getPromiseInfo(any(PromiseRequest.class))).thenReturn(RpcResponse.success(null, "ok"));

        RpcResponse<PromiseData> response = promiseServiceRpc.getPromiseInfo(request);

        assertNull(response);
    }

    @Test
    public void testGetPromiseInfoException() {
        when(promiseService.getPromiseInfo(any(PromiseRequest.class))).thenThrow(new RuntimeException());

        RpcResponse<PromiseData> response = promiseServiceRpc.getPromiseInfo(request);

        assertNull(response);
    }

    private RpcResponse<PromiseData> aSuccessResponse() {
        PromiseData promiseData = new PromiseData("orderId", new Date(), new Date(), new Date(), new Date(), new Date(), new ConfigInfo(1, 1, 1, 1));
        return RpcResponse.success(promiseData, "ok");
    }

    private RpcResponse<PromiseData> aFailResponse() {
        return RpcResponse.fail(500, "error");
    }

}