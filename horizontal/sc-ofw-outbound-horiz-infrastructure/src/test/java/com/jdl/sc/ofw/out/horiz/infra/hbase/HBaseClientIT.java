package com.jdl.sc.ofw.out.horiz.infra.hbase;

import com.jdl.sc.ofw.out.horiz.infra.hbase.config.OutboundDBConfig;
import com.jdl.sc.ofw.out.horiz.infra.repository.entity.HBaseOutboundEntity;
import com.jdl.sc.ofw.out.horiz.infra.utils.HBaseRowKeyUtils;
import org.apache.hadoop.hbase.filter.CompareFilter;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-hbase.xml"})
@Ignore
public class HBaseClientIT {

    @Autowired
    HBaseClient hBaseClient;

    @Autowired
    private OutboundDBConfig outboundDBConfig;

    @Test
    public void get() throws Exception {
        HBaseTableName tableName = HBaseTableName.builder().tableSpace(outboundDBConfig.getTableSpace()).tableName(outboundDBConfig.getTableName()).build();
        Map<String, String> map = hBaseClient.
                get(tableName,
                        HBaseRowKeyUtils.getRowKeyByOrderNo("ESL0010000000812"));
    }

    @Test
    public void delete() throws Exception {
        HBaseTableName tableName = HBaseTableName.builder().tableSpace(outboundDBConfig.getTableSpace()).tableName(outboundDBConfig.getTableName()).build();
        hBaseClient.delete(tableName, HBaseRowKeyUtils.getRowKeyByOrderNo("ESL0010000000978"));
        if (true) {
            return;
        }
        for (int i = 0; i < 10000; i++) {
            String orderNo = "ESL0112" + String.format("%06d", i);
            hBaseClient.delete(tableName, HBaseRowKeyUtils.getRowKeyByOrderNo(orderNo));

        }

    }

    @Test
    public void scan() throws Exception {
        HBaseTableName tableName = HBaseTableName.builder().tableSpace(outboundDBConfig.getTableSpace()).tableName(outboundDBConfig.getTableName()).build();
        FilterList filterList = new FilterList();
        filterList.addFilter(new SingleColumnValueFilter(Bytes.toBytes(outboundDBConfig.getFamily()),
                Bytes.toBytes("order_no"),
                CompareFilter.CompareOp.EQUAL, Bytes.toBytes("ESL0010000000812")

        ));


        List<Map<String, String>> map = hBaseClient.
                scan(tableName, filterList, null, null);
    }

    @Test
    public void scanOrderStatus() throws Exception {
        HBaseTableName tableName = HBaseTableName.builder().tableSpace(outboundDBConfig.getTableSpace()).tableName(outboundDBConfig.getTableName()).build();


        List<Map<String, String>> listMap = hBaseClient.
                scan(tableName, null, null, null);
        for (Map<String, String> map : listMap) {
            String status = map.get(outboundDBConfig.getFamily() + "_" + HBaseOutboundEntity.STATUS_KEY);
            String orderNo = map.get(outboundDBConfig.getFamily() + "_order_no");
            System.out.println("-------------" + status + ":" + orderNo);
        }
    }


    @Test
    public void dataRepair() throws Exception {
        HBaseTableName tableName = HBaseTableName.builder().tableSpace(outboundDBConfig.getTableSpace()).tableName(outboundDBConfig.getTableName()).build();
        List<Map<String, String>> orders = hBaseClient.
                scan(tableName, null, null, null);

        for (Iterator<Map<String, String>> iterator = orders.iterator(); iterator.hasNext(); ) {
            Map<String, String> order = iterator.next();
            hBaseClient.put(tableName, HBaseRow.builder()
                    .rowkey(HBaseRowKeyUtils.getRowKeyByOrderNo(order.get(outboundDBConfig.getFamily() + "_order_no")))
                    .family(outboundDBConfig.getFamily())
                    .columns(
                            Lists.newArrayList(
                                    HBaseColumn.builder()
                                            .qualifier("data")
                                            .value(order.get(outboundDBConfig.getFamily() + "_data").replace("\"orderType\":100", "\"orderType\":\"100\""))
                                            .build()
                            ))
                    .build(), null);
        }

    }

    @Test
    public void process() throws Exception {
        HBaseTableName tableName = HBaseTableName.builder().tableSpace(outboundDBConfig.getTableSpace()).tableName(outboundDBConfig.getTableName()).build();
        hBaseClient.put(tableName,
                HBaseRow.builder()
                        .rowkey(HBaseRowKeyUtils.getRowKeyByOrderNo("test00000002"))
                        .family(outboundDBConfig.getFamily())
                        .columns(
                                Lists.newArrayList(
                                        HBaseColumn.builder()
                                                .qualifier("orderNo")
                                                .value("test00000002")
                                                .build()
                                ))
                        .build(), null);

        Map<String, String> map = hBaseClient.
                get(tableName,
                        HBaseRowKeyUtils.getRowKeyByOrderNo("ESL0010000000812"));

        Assert.assertEquals("test00000002", map.get("orderNo"));
    }
}
