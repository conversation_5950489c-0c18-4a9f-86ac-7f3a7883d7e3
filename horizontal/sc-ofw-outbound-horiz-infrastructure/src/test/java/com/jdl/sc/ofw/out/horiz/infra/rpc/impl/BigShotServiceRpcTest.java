package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jd.ldop.alpha.waybill.api.dto.waybill.WaybillDTO;
import com.jd.ldop.alpha.waybill.api.dto.request.QueryBigShotDTO;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import lombok.Builder;
import com.jd.ldop.alpha.waybill.api.WaybillBigShotApi;
import com.jdl.sc.ofw.out.domain.model.*;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import java.math.BigDecimal;
import java.util.ArrayList;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.Data;
import com.jdl.sc.ofw.out.common.dict.GoodsTypeEnum;
import com.jd.ldop.delivery.api.dto.response.IntegrateResultDTO;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.out.horiz.infra.collect.CollectContext;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import lombok.Setter;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import javax.validation.constraints.NotNull;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.common.dict.DecryptCommandEnum;
import com.jd.ldop.alpha.waybill.api.dto.waybill.WaybillResponseDTO;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import org.springframework.context.annotation.Profile;
import com.jd.ldop.alpha.waybill.api.WaybillCheckApi;
import com.jdl.sc.lfc.trans.pipe.spec.enums.TransPipeErrorEnum;
import com.jd.ldop.alpha.lp.api.dto.output.StoreVirtualSiteRelationDTO;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import com.jd.ldop.alpha.lp.api.ThirdDistributeQueryApi;
import java.util.List;
import com.jdl.sc.ofw.out.domain.model.orderclob.ClobTypeEnums;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.TmsWaybillTypeEnum.OTHER;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.outbound.spec.enums.*;
import com.jd.ldop.delivery.api.dto.response.ResponseDTO;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipModeEnum;
import java.util.Objects;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.SiteShipTypeEnum.*;
import com.jdl.sc.ofw.out.horiz.infra.collect.ParamCollectProcessor;
import com.jd.ldop.alpha.lp.api.dto.output.ThirdProviderConfDTO;
import com.jdl.sc.ofw.out.horiz.infra.collect.MockResponse;
import lombok.extern.jackson.Jacksonized;
import com.jdl.sc.ofw.out.domain.rpc.BigShotServiceAcl;
import lombok.Getter;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import com.jd.ldop.alpha.waybill.api.BigShotQueryApi;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.vo.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusPhaseEnum;
import org.springframework.stereotype.Component;
import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jd.ldop.alpha.waybill.api.dto.response.WaybillResultDTO;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Cancel;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.TmsWaybillTypeEnum.JP;
import com.jd.ldop.alpha.waybill.api.dto.waybill.BigShotDTO;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.collections4.CollectionUtils;
import com.jdl.sc.ofw.out.horiz.infra.collect.*;
import java.util.Optional;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import cn.jdl.batrix.sdk.base.BDomainAsyncModel;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.core.json.JsonUtil;

import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.BigShotServiceRpc;

import com.jdl.sc.ofw.outbound.spec.enums.errorspec.*;
import com.jd.ldop.alpha.waybill.api.dto.request.*;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import java.io.Serializable;
import com.jd.ldop.alpha.waybill.api.dto.request.BigShotQueryDTO;
import com.jd.ldop.alpha.waybill.api.dto.waybill.*;
import lombok.AllArgsConstructor;
import com.jdl.sc.ofw.out.domain.model.product.ExpressProductEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.WmsCarrierTypeEnum.*;
import com.jd.ldop.alpha.lp.api.dto.output.*;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.outbound.spec.enums.QueryWaybillTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import java.util.Arrays;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jd.ldop.alpha.lp.api.ThirdProviderConfApi;
import java.util.Date;
import com.jd.ldop.delivery.api.dto.response.*;
import com.jdl.sc.ofw.out.horiz.infra.collect.CollectInfoEnum;
import com.jd.ldop.alpha.waybill.api.dto.response.*;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import lombok.NoArgsConstructor;
import com.jd.ldop.alpha.waybill.api.WaybillReceiveApi;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the BigShotServiceRpc class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class BigShotServiceRpcTest {

	@Mock
	private WaybillCheckApi waybillCheckApi;

	@Mock
	private ThirdProviderConfApi thirdProviderConfApi;

	@InjectMocks
	private BigShotServiceRpc bigShotServiceRpc;

	@Mock
	private WaybillReceiveApi waybillReceiveApi;

	@Mock
	private ThirdDistributeQueryApi thirdDistributeQueryApi;

	@Mock
	private BigShotQueryApi bigShotQueryApi;

	@Mock
	private WaybillBigShotApi waybillBigShotApi;

	@Mock
	private ParamCollectProcessor paramCollectProcessor;

    @Test
    public void testQueryBigShotMessageBigShotSuccess() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        BigShotQueryDTO queryDTO = new BigShotQueryDTO();
        queryDTO.setBusinessCode("TEST001");
        
        BigShotDTO expectedDTO = new BigShotDTO();
        expectedDTO.setBigShotCode("BS001");
        
        ResponseDTO<BigShotDTO> response = new ResponseDTO<>(ResponseDTO.SUCCESS_CODE, "Success", expectedDTO);
        
        // Mock
        when(bigShotQueryApi.queryBigShotMessage(any(BigShotQueryDTO.class))).thenReturn(null);
        
        // Execute
        BigShotDTO result = bigShotServiceRpc.queryBigShotMessageBigShot(queryDTO);
        
        // Verify
        assertNotNull(result);
    }

    @Test(expected = InfrastructureException.class) 
    public void testQueryBigShotMessageBigShotNullData() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        BigShotQueryDTO queryDTO = new BigShotQueryDTO();
        queryDTO.setBusinessCode("TEST002");

        ResponseDTO<BigShotDTO> response = new ResponseDTO<>(ResponseDTO.SUCCESS_CODE, "Success", (String) null);
        
        // Mock
        when(bigShotQueryApi.queryBigShotMessage(any(BigShotQueryDTO.class))).thenReturn(null);
        
        // Execute
        bigShotServiceRpc.queryBigShotMessageBigShot(queryDTO);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryBigShotMessageBigShotException() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        BigShotQueryDTO queryDTO = new BigShotQueryDTO();
        queryDTO.setBusinessCode("TEST003");
        
        // Mock
        when(bigShotQueryApi.queryBigShotMessage(any(BigShotQueryDTO.class))).thenThrow(new RuntimeException());
        
        // Execute
        bigShotServiceRpc.queryBigShotMessageBigShot(queryDTO);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryBigShotMessageBigShotNullResponse() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        BigShotQueryDTO queryDTO = new BigShotQueryDTO();
        queryDTO.setBusinessCode("TEST004");
        
        // Mock
        when(bigShotQueryApi.queryBigShotMessage(any(BigShotQueryDTO.class))).thenReturn(null);
        
        // Execute
        bigShotServiceRpc.queryBigShotMessageBigShot(queryDTO);
    }

    @Test
    public void testCheckReceiveWaybillSuccess() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        String orderNo = "123456";
        ResponseDTO expectedResponse = new ResponseDTO(ResponseDTO.SUCCESS_CODE, "Success");
        
        Mockito.when(waybillCheckApi.checkReceiveWaybill(any(WaybillDTO.class)))
               .thenReturn(null);

        // Act
        com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO result = bigShotServiceRpc.checkReceiveWaybill(waybillDTO, orderNo);

        // Assert
        assertNotNull(result);
    }

    @Test(expected = InfrastructureException.class) 
    public void testCheckReceiveWaybillWhenApiThrowsException() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        String orderNo = "123456";
        
        Mockito.when(waybillCheckApi.checkReceiveWaybill(any(WaybillDTO.class)))
               .thenThrow(new RuntimeException());

        // Act
        bigShotServiceRpc.checkReceiveWaybill(waybillDTO, orderNo);
    }

    @Test
    public void testCheckReceiveWaybillWithNullWaybillDTO() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = null;
        String orderNo = "123456";
        ResponseDTO expectedResponse = new ResponseDTO(ResponseDTO.SUCCESS_CODE, "Success");
        
        Mockito.when(waybillCheckApi.checkReceiveWaybill(any(WaybillDTO.class)))
               .thenReturn(com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO.ofData(expectedResponse));

        // Act
        com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO result = bigShotServiceRpc.checkReceiveWaybill(waybillDTO, orderNo);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testCheckReceiveWaybillWithNullOrderNo() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        String orderNo = null;
        ResponseDTO expectedResponse = new ResponseDTO(ResponseDTO.SUCCESS_CODE, "Success");
        
        Mockito.when(waybillCheckApi.checkReceiveWaybill(any(WaybillDTO.class)))
               .thenReturn(com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO.ofData(expectedResponse));

        // Act
        com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO result = bigShotServiceRpc.checkReceiveWaybill(waybillDTO, orderNo);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testQueryBigShotMessageWaybillSuccess() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        QueryBigShotDTO queryBigShotDTO = new QueryBigShotDTO();
        queryBigShotDTO.setProviderId(123);
        queryBigShotDTO.setWaybillCode("WB001");

        BigShotDTO expectedBigShotDTO = new BigShotDTO();
        expectedBigShotDTO.setBigShotCode("BS001");
        
        ResponseDTO responseDTO = ResponseDTO.buildSuccess();

        // Mock external call
        when(waybillBigShotApi.queryBigShotMessage(any(QueryBigShotDTO.class)))
            .thenReturn(com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO.ofData(responseDTO));

        // Execute test
        BigShotDTO result = bigShotServiceRpc.queryBigShotMessageWaybill(queryBigShotDTO);

        // Verify results
        assertNotNull(result);
    }

    @Test(expected = InfrastructureException.class) 
    public void testQueryBigShotMessageWaybillNullData() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        QueryBigShotDTO queryBigShotDTO = new QueryBigShotDTO();
        queryBigShotDTO.setProviderId(123);
        queryBigShotDTO.setWaybillCode("WB001");

        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setData(null);

        // Mock external call
        when(waybillBigShotApi.queryBigShotMessage(any(QueryBigShotDTO.class)))
            .thenReturn(com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO.ofData(responseDTO));

        // Execute test - should throw exception
        bigShotServiceRpc.queryBigShotMessageWaybill(queryBigShotDTO);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryBigShotMessageWaybillApiException() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        QueryBigShotDTO queryBigShotDTO = new QueryBigShotDTO();
        queryBigShotDTO.setProviderId(123);
        queryBigShotDTO.setWaybillCode("WB001");

        // Mock external call to throw exception
        when(waybillBigShotApi.queryBigShotMessage(any(QueryBigShotDTO.class)))
            .thenThrow(new RuntimeException("API Error"));

        // Execute test - should throw exception
        bigShotServiceRpc.queryBigShotMessageWaybill(queryBigShotDTO);
    }

    @Test
    public void testQueryBigShotMessageWaybillWithConstructor() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        QueryBigShotDTO queryBigShotDTO = new QueryBigShotDTO(123, "WB001");
        
        BigShotDTO expectedBigShotDTO = new BigShotDTO();
        expectedBigShotDTO.setBigShotCode("BS001");
        
        ResponseDTO responseDTO = new ResponseDTO(ResponseDTO.SUCCESS_CODE, 
            ResponseDTO.SUCCESS_MESSAGE, expectedBigShotDTO);

        // Mock external call
        when(waybillBigShotApi.queryBigShotMessage(any(QueryBigShotDTO.class)))
            .thenReturn(com.jd.ldop.alpha.waybill.api.dto.response.ResponseDTO.ofData(responseDTO));

        // Execute test
        BigShotDTO result = bigShotServiceRpc.queryBigShotMessageWaybill(queryBigShotDTO);

        // Verify results 
        assertNotNull(result);
    }

    @Test
    public void testQueryVirtualConfigSuccess() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        StoreVirtualSiteRelationDTO input = new StoreVirtualSiteRelationDTO();
        String orderNo = "123456";

        StoreVirtualSiteRelationDTO expectedResult = new StoreVirtualSiteRelationDTO();
        List<StoreVirtualSiteRelationDTO> resultList = new ArrayList<>();
        resultList.add(expectedResult);

        ResponseDTO<List<StoreVirtualSiteRelationDTO>> response = new ResponseDTO<>(ResponseDTO.SUCCESS_CODE, "success", resultList);

        // Mock
        when(thirdDistributeQueryApi.queryVirtualCodeSiteByStoreId(any(StoreVirtualSiteRelationDTO.class))).thenReturn(null);

        // Execute
        StoreVirtualSiteRelationDTO result = bigShotServiceRpc.queryVirtualConfig(input, orderNo);

        // Verify
        Assert.assertEquals(expectedResult, result);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryVirtualConfigApiException() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        StoreVirtualSiteRelationDTO input = new StoreVirtualSiteRelationDTO();
        String orderNo = "123456";

        // Mock
        when(thirdDistributeQueryApi.queryVirtualCodeSiteByStoreId(any(StoreVirtualSiteRelationDTO.class))).thenThrow(new RuntimeException());

        // Execute
        bigShotServiceRpc.queryVirtualConfig(input, orderNo);
    }

    @Test(expected = InfrastructureException.class) 
    public void testQueryVirtualConfigResponseNotSuccess() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        StoreVirtualSiteRelationDTO input = new StoreVirtualSiteRelationDTO();
        String orderNo = "123456";

        ResponseDTO<List<StoreVirtualSiteRelationDTO>> response = new ResponseDTO<>(ResponseDTO.FAIL_CODE, "failed");

        // Mock
        when(thirdDistributeQueryApi.queryVirtualCodeSiteByStoreId(any(StoreVirtualSiteRelationDTO.class))).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryVirtualConfig(input, orderNo);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryVirtualConfigEmptyResult() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        StoreVirtualSiteRelationDTO input = new StoreVirtualSiteRelationDTO();
        String orderNo = "123456";

        ResponseDTO<List<StoreVirtualSiteRelationDTO>> response = new ResponseDTO<>(ResponseDTO.SUCCESS_CODE, "success", new ArrayList<>());

        // Mock
        when(thirdDistributeQueryApi.queryVirtualCodeSiteByStoreId(any(StoreVirtualSiteRelationDTO.class))).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryVirtualConfig(input, orderNo);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryVirtualConfigNullResult() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        StoreVirtualSiteRelationDTO input = new StoreVirtualSiteRelationDTO();
        String orderNo = "123456";

        ResponseDTO<List<StoreVirtualSiteRelationDTO>> response = new ResponseDTO<>(ResponseDTO.SUCCESS_CODE, "success", (String) null);

        // Mock
        when(thirdDistributeQueryApi.queryVirtualCodeSiteByStoreId(any(StoreVirtualSiteRelationDTO.class))).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryVirtualConfig(input, orderNo);
    }

    @Test
    public void testReceiveWaybillWhenMockResponseIsAbAndHasWaybillInfo() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        MockResponse mockResponse = MockResponse.builder()
                .wayBillInfo("123456")
                .build();
        when(paramCollectProcessor.collect(any(CollectContext.class))).thenReturn(mockResponse);

        // Act
        String result = bigShotServiceRpc.receiveWaybill(waybillDTO, QueryWaybillTypeEnum.WAYBILL, "customerOrderNo", "orderNo", "deptNo");

        // Assert
        assertEquals("123456", result);
    }

    @Test
    public void testReceiveWaybillWhenMockResponseIsNull() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        when(paramCollectProcessor.collect(any(CollectContext.class))).thenReturn(null);

        WaybillResultDTO resultDTO = new WaybillResultDTO(Arrays.asList("789"));
        WaybillResponseDTO<WaybillResultDTO> responseDTO = new WaybillResponseDTO<>(0, "success", resultDTO);
        when(waybillReceiveApi.receiveWaybill(any())).thenReturn(responseDTO);

        // Act
        String result = bigShotServiceRpc.receiveWaybill(waybillDTO, QueryWaybillTypeEnum.WAYBILL, "customerOrderNo", "orderNo", "deptNo");

        // Assert
        assertEquals("789", result);
    }

    @Test(expected = InfrastructureException.class)
    public void testReceiveWaybillWhenWaybillResultDTOIsNull() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        when(paramCollectProcessor.collect(any(CollectContext.class))).thenReturn(null);
        when(waybillReceiveApi.receiveWaybill(any())).thenReturn(null);

        // Act
        bigShotServiceRpc.receiveWaybill(waybillDTO, QueryWaybillTypeEnum.WAYBILL, "customerOrderNo", "orderNo", "deptNo");
    }

    @Test(expected = InfrastructureException.class)
    public void testReceiveWaybillWhenStatusCodeIsError() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        when(paramCollectProcessor.collect(any(CollectContext.class))).thenReturn(null);

        WaybillResultDTO resultDTO = new WaybillResultDTO();
        WaybillResponseDTO<WaybillResultDTO> responseDTO = new WaybillResponseDTO<>(1, "error", resultDTO);
        when(waybillReceiveApi.receiveWaybill(any())).thenReturn(responseDTO);

        // Act
        bigShotServiceRpc.receiveWaybill(waybillDTO, QueryWaybillTypeEnum.WAYBILL, "customerOrderNo", "orderNo", "deptNo");
    }

    @Test(expected = InfrastructureException.class) 
    public void testReceiveWaybillWhenWaybillCodeListIsEmpty() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        when(paramCollectProcessor.collect(any(CollectContext.class))).thenReturn(null);

        WaybillResultDTO resultDTO = new WaybillResultDTO(Collections.emptyList());
        WaybillResponseDTO<WaybillResultDTO> responseDTO = new WaybillResponseDTO<>(0, "success", resultDTO);
        when(waybillReceiveApi.receiveWaybill(any())).thenReturn(responseDTO);

        // Act
        bigShotServiceRpc.receiveWaybill(waybillDTO, QueryWaybillTypeEnum.WAYBILL, "customerOrderNo", "orderNo", "deptNo");
    }

    @Test(expected = InfrastructureException.class)
    public void testReceiveWaybillWhenExceptionThrown() {
        // generated by JoyCoder taskId d3e0f739b931
        // Arrange
        WaybillDTO waybillDTO = new WaybillDTO();
        when(paramCollectProcessor.collect(any(CollectContext.class))).thenThrow(new RuntimeException());

        // Act
        bigShotServiceRpc.receiveWaybill(waybillDTO, QueryWaybillTypeEnum.WAYBILL, "customerOrderNo", "orderNo", "deptNo");
    }

    @Test
    public void testQueryThirdCarrierConfFromSuccess() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .customer(Customer.builder().accountNo("account1").build())
                .warehouse(Warehouse.builder().warehouseNo("WH001").build())
                .shipment(Shipment.builder().externalCarrierCode("carrier1").build())
                .build();

        ThirdProviderConfDTO expectedData = new ThirdProviderConfDTO();
        ResponseDTO<ThirdProviderConfDTO> response = new ResponseDTO<>(0, "success", expectedData);

        when(thirdProviderConfApi.queryThirdCarrierConfFrom(any(), any(), any())).thenReturn(null);

        // Execute
        ThirdProviderConfDTO result = bigShotServiceRpc.queryThirdCarrierConfFrom(model);

        // Verify
        assertNotNull(result);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryThirdCarrierConfFromNullResponse() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .customer(Customer.builder().accountNo("account1").build())
                .warehouse(Warehouse.builder().warehouseNo("WH001").build())
                .shipment(Shipment.builder().externalCarrierCode("carrier1").build())
                .build();

        when(thirdProviderConfApi.queryThirdCarrierConfFrom(any(), any(), any())).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryThirdCarrierConfFrom(model);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryThirdCarrierConfFromNullStatusCode() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .customer(Customer.builder().accountNo("account1").build())
                .warehouse(Warehouse.builder().warehouseNo("WH001").build())
                .shipment(Shipment.builder().externalCarrierCode("carrier1").build())
                .build();

        ResponseDTO<ThirdProviderConfDTO> response = new ResponseDTO<>();
        response.setStatusCode(null);

        when(thirdProviderConfApi.queryThirdCarrierConfFrom(any(), any(), any())).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryThirdCarrierConfFrom(model);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryThirdCarrierConfFromNonZeroStatusCode() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .customer(Customer.builder().accountNo("account1").build())
                .warehouse(Warehouse.builder().warehouseNo("WH001").build())
                .shipment(Shipment.builder().externalCarrierCode("carrier1").build())
                .build();

        ResponseDTO<ThirdProviderConfDTO> response = new ResponseDTO<>(1, "error", (String) null);

        when(thirdProviderConfApi.queryThirdCarrierConfFrom(any(), any(), any())).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryThirdCarrierConfFrom(model);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryThirdCarrierConfFromNullData() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .customer(Customer.builder().accountNo("account1").build())
                .warehouse(Warehouse.builder().warehouseNo("WH001").build())
                .shipment(Shipment.builder().externalCarrierCode("carrier1").build())
                .build();

        ResponseDTO<ThirdProviderConfDTO> response = new ResponseDTO<>(0, "success", (String) null);

        when(thirdProviderConfApi.queryThirdCarrierConfFrom(any(), any(), any())).thenReturn(null);

        // Execute
        bigShotServiceRpc.queryThirdCarrierConfFrom(model);
    }

    @Test(expected = InfrastructureException.class)
    public void testQueryThirdCarrierConfFromException() {
        // generated by JoyCoder taskId d3e0f739b931
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .customer(Customer.builder().accountNo("account1").build())
                .warehouse(Warehouse.builder().warehouseNo("WH001").build())
                .shipment(Shipment.builder().externalCarrierCode("carrier1").build())
                .build();

        when(thirdProviderConfApi.queryThirdCarrierConfFrom(any(), any(), any())).thenThrow(new RuntimeException());

        // Execute
        bigShotServiceRpc.queryThirdCarrierConfFrom(model);
    }

}