package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jd.csc.channel.intserv.PlatformEnum;
import com.jd.csc.channel.intserv.waybill.ExtInfo;
import com.jd.csc.channel.intserv.waybill.PackageInfo;
import com.jd.csc.channel.intserv.waybill.build.BuildWaybillCodeRequest;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetRequest;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.Quantity;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.any;

@RunWith(MockitoJUnitRunner.class)
public class TiktokInstantPlatformStandardServiceRpcTest {

    @InjectMocks
    private TiktokInstantPlatformStandardServiceRpc tiktokInstantPlatformStandardServiceRpc;

    @Mock
    private ElectronicSheetConfigAcl electronicSheetConfigAcl;

    @Test
    public void testGetPlatformEnum() {
        assertEquals(PlatformEnum.DY_INSTANT, tiktokInstantPlatformStandardServiceRpc.getPlatformEnum());
    }

    @Test
    public void testSupportForTikTok() {
        DeliveryPerformanceChannelEnum tikTok = DeliveryPerformanceChannelEnum.TIK_TOK;
        assertTrue(tiktokInstantPlatformStandardServiceRpc.support(tikTok));
    }

    @Test
    public void testSupportForOtherChannels() {
        assertFalse(tiktokInstantPlatformStandardServiceRpc.support(DeliveryPerformanceChannelEnum.JD));
    }

    @Test
    public void testGetPauseReasonEnumToken() {
        PauseReasonTypeEnum pauseReasonTypeEnum = PauseReasonTypeEnum.TOKEN;
        PauseReasonEnum result = tiktokInstantPlatformStandardServiceRpc.getPauseReasonEnum(pauseReasonTypeEnum);
        assertNotNull(result);
    }

    @Test
    public void testGetPauseReasonEnumPrint() {
        PauseReasonTypeEnum pauseReasonTypeEnum = PauseReasonTypeEnum.PRINT;
        PauseReasonEnum result = tiktokInstantPlatformStandardServiceRpc.getPauseReasonEnum(pauseReasonTypeEnum);
        assertNotNull(result);
    }

    @Test
    public void testGetPauseReasonEnumWaybill() {
        PauseReasonTypeEnum pauseReasonTypeEnum = PauseReasonTypeEnum.WAYBILL;
        PauseReasonEnum result = tiktokInstantPlatformStandardServiceRpc.getPauseReasonEnum(pauseReasonTypeEnum);
        assertNotNull(result);
    }

    @Test
    public void testGetPauseReasonEnumNull() {
        try {
            tiktokInstantPlatformStandardServiceRpc.getPauseReasonEnum(null);
            fail("Expected UnsupportedOperationException");
        } catch (UnsupportedOperationException e) {
            assertEquals("必须指定暂停异常类型", e.getMessage());
        }
    }

    @Test
    public void testGetExtInfo() {
        // Test case for customFieldMap containing only OUT_WAREHOUSE_ID_KEY
        OrderFulfillmentModel model1 = OrderFulfillmentModel.builder()
                .fulfillment(Fulfillment.builder().customField("{\"outWarehouseId\": 123}").build())
                .build();
        ExtInfo extInfo = tiktokInstantPlatformStandardServiceRpc.getExtInfo(model1);
        assertNotNull(extInfo);

        // Test case for customFieldMap containing only STORE_ID_KEY
        OrderFulfillmentModel model2 = OrderFulfillmentModel.builder()
                .fulfillment(Fulfillment.builder().customField("{\"storeId\": 123}").build())
                .build();
        ExtInfo extInfo2 = tiktokInstantPlatformStandardServiceRpc.getExtInfo(model2);
        assertNotNull(extInfo2);

        // Test case for customFieldMap containing neither key
        OrderFulfillmentModel model3 = OrderFulfillmentModel.builder()
                .fulfillment(Fulfillment.builder().customField("{}").build())
                .build();
        ExtInfo extInfo3 = tiktokInstantPlatformStandardServiceRpc.getExtInfo(model3);
        assertNotNull(extInfo3);
    }

    @Test
    public void testGetBuildWaybillCodeRequestNullModel() {
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .fulfillment(Fulfillment.builder().customField("{\"outWarehouseId\": 123,\"storeId\": 456}").build())
                .warehouse(Warehouse.builder().warehouseNo("12345").build())
                .build();
        BuildWaybillCodeRequest request = tiktokInstantPlatformStandardServiceRpc.getBuildWaybillCodeRequest(model);

        assertNotNull(request);
    }

    @Test
    public void testGetElectronicSheetInfoWhenElectronicSheetInfoIsValid() {
        // generated by JoyCoder taskId a0601566450a
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .shipment(Shipment.builder().shipperNo("CYS00001").build())
                .warehouse(Warehouse.builder().originalWarehouseNo("12344").build())
                .fulfillment(Fulfillment.builder().customField("{\"outWarehouseId\": 123,\"storeId\": 456}").build())
                .customer(Customer.builder().accountNo("EBU0001").build())
                .channel(Channel.builder().shopNo("123").build())
                .products(new ProductCollection())
                .build();
        ElectronicSheetInfo electronicSheetInfo = ElectronicSheetInfo.builder().authToken("token").build();
        Mockito.when(electronicSheetConfigAcl.queryConfigInfo(any(ElectronicSheetRequest.class))).thenReturn(electronicSheetInfo);

        ElectronicSheetInfo result = tiktokInstantPlatformStandardServiceRpc.getElectronicSheetInfo(model);

        assertNotNull(result);
    }

    @Test(expected = PauseException.class)
    public void testGetElectronicSheetInfoWhenElectronicSheetInfoIsNull() {
        // generated by JoyCoder taskId a0601566450a
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .shipment(Shipment.builder().shipperNo("CYS00001").build())
                .warehouse(Warehouse.builder().originalWarehouseNo("12344").build())
                .fulfillment(Fulfillment.builder().customField("{\"outWarehouseId\": 123,\"storeId\": 456}").build())
                .customer(Customer.builder().accountNo("EBU0001").build())
                .channel(Channel.builder().shopNo("123").build())
                .products(new ProductCollection())
                .build();
        Mockito.when(electronicSheetConfigAcl.queryConfigInfo(any())).thenReturn(null);

        tiktokInstantPlatformStandardServiceRpc.getElectronicSheetInfo(model);
    }

    @Test(expected = PauseException.class)
    public void testGetElectronicSheetInfoWhenElectronicSheetInfo_authTokenIsNull() {
        // generated by JoyCoder taskId a0601566450a
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .shipment(Shipment.builder().shipperNo("CYS00001").build())
                .warehouse(Warehouse.builder().originalWarehouseNo("12344").build())
                .fulfillment(Fulfillment.builder().customField("{\"outWarehouseId\": 123,\"storeId\": 456}").build())
                .customer(Customer.builder().accountNo("EBU0001").build())
                .channel(Channel.builder().shopNo("123").build())
                .products(new ProductCollection())
                .build();
        ElectronicSheetInfo electronicSheetInfo = ElectronicSheetInfo.builder().build();
        Mockito.when(electronicSheetConfigAcl.queryConfigInfo(any(ElectronicSheetRequest.class))).thenReturn(electronicSheetInfo);

        tiktokInstantPlatformStandardServiceRpc.getElectronicSheetInfo(model);
    }

    @Test
    public void testGetPackageListInfo() {
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("ESL0000001")
                .shipment(Shipment.builder().shipperNo("CYS00001").build())
                .warehouse(Warehouse.builder().originalWarehouseNo("12344").build())
                .fulfillment(Fulfillment.builder().customField("{\"outWarehouseId\": 123,\"storeId\": 456}").build())
                .customer(Customer.builder().accountNo("EBU0001").build())
                .channel(Channel.builder().shopNo("123").build())
                .products(new ProductCollection())
                .cargos(Collections.singletonList(Cargo.builder().no("123").name("测试商品").quantity(Quantity.builder().value(new BigDecimal("1")).build()).build()))
                .build();

        List<PackageInfo> packageInfoList = tiktokInstantPlatformStandardServiceRpc.getPackageInfoList(model);

        assertNotNull(packageInfoList);
    }

}