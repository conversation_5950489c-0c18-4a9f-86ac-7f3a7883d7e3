package com.jdl.sc.ofw.out.horiz.infra.rpc.translator;


import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.etms.vrs.api.cityaging.resp.CityAgingInfoExtResp;
import com.jd.etms.vrs.api.common.dto.CommonDto;
import com.jd.promise.service.domain.TransferTimeRequest;
import com.jd.promise.service.domain.WarehouseJitTimeRequest;
import com.jdl.sc.ofw.out.domain.model.Make;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.*;
import com.jdl.sc.ofw.out.domain.translator.MarkTranslator;
import com.jdl.sc.ofw.out.horiz.infra.rpc.CityAgingNewApiAcl;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.jdl.sc.ofw.out.domain.model.Make.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-04-03 17:01
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PromiseTranslatorTest {


    @InjectMocks
    private PromiseTranslator promiseTranslator;

    @Mock
    private CityAgingNewApiAcl cityAgingNewApiAcl;

    @Mock
    private MarkTranslator markTranslator;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    public static ProductCollection productCollection;

    @Before
    public void setUp(){
        productCollection = new ProductCollection();
    }

    @Test
    public void t1(){
        //productCollection.enroll(ProductEnum.LL_HD.toProduct());
        productCollection.enroll(ProductEnum.LL_SD.toProduct());
        Warehouse warehouse = warehouseBuilder();
        OrderFulfillmentModel model1 = Make.anCommonOutboundModel(OrderBusinessTypeEnum.B2C, aShipment(), productCollection);
        promiseTranslator.modelToRequest(model1, warehouse, TransportationTypeEnum.LAND);
    }
    @Test
    public void t2(){
        productCollection.enroll(ProductEnum.LL_SD.toProduct());
        Warehouse warehouse = warehouseBuilder();
        OrderFulfillmentModel model1 = anCommonOutboundModel1(OrderBusinessTypeEnum.B2C, aShipment(), productCollection);
        CityAgingInfoExtResp cityAgingInfoExtResp = new CityAgingInfoExtResp();
        cityAgingInfoExtResp.setAgingNameCode(2);
        List<CityAgingInfoExtResp> list = new ArrayList<>();
        list.add(cityAgingInfoExtResp);
        CommonDto<List<CityAgingInfoExtResp>> dto = new CommonDto<>();
        dto.setData(list);
        dto.setCode(1);
        when(cityAgingNewApiAcl.queryMixtureProductAging(any())).thenReturn(dto);
        when(switchKeyUtils.isColdChainPromiseUpdateNewFlow(any())).thenReturn(true);
        promiseTranslator.modelToRequest(model1, warehouse, TransportationTypeEnum.LAND);
        promiseTranslator.modelToCityPromiseRequest(model1, warehouse);
        promiseTranslator.modelToForbiddenRequest(model1,warehouse);
    }


    @Test
    public void t3(){
        productCollection.enroll(ProductEnum.LL_SD.toProduct());
        Warehouse warehouse = warehouseBuilder();
        OrderFulfillmentModel model1 = anCommonOutboundModel1(OrderBusinessTypeEnum.B2C, aShipment(), productCollection);
        List<CityAgingInfoExtResp> list = new ArrayList<>();
        CommonDto<List<CityAgingInfoExtResp>> dto = new CommonDto<>();
        dto.setData(list);
        when(cityAgingNewApiAcl.queryMixtureProductAging(any())).thenReturn(dto);
        try {
            promiseTranslator.modelToRequest(model1, warehouse, TransportationTypeEnum.LAND);
        }catch(Exception e){
            log.error("抛出异常：{}",e);
        }
    }


    @Test
    public void t4(){
        productCollection.enroll(ProductEnum.LL_SD.toProduct());
        Warehouse warehouse = warehouseBuilder();
        OrderFulfillmentModel model1 = anCommonOutboundModel1(OrderBusinessTypeEnum.B2C, aShipment(), productCollection);
        CityAgingInfoExtResp cityAgingInfoExtResp = new CityAgingInfoExtResp();
        cityAgingInfoExtResp.setAgingNameCode(1);
        List<CityAgingInfoExtResp> list = new ArrayList<>();
        list.add(cityAgingInfoExtResp);
        CommonDto<List<CityAgingInfoExtResp>> dto = new CommonDto<>();
        dto.setData(list);
        dto.setCode(1);
        try {
            promiseTranslator.modelToRequest(model1, warehouse, TransportationTypeEnum.LAND);
        }catch(Exception e){
            log.error("抛出异常：{}",e);
        }
    }

    @Test
    public void t5(){
        productCollection.enroll(ProductEnum.LL_SD.toProduct());
        Warehouse warehouse = warehouseBuilder();
        OrderFulfillmentModel model1 = anCommonOutboundModel1(OrderBusinessTypeEnum.B2C, aShipment(), productCollection);
        CityAgingInfoExtResp cityAgingInfoExtResp = new CityAgingInfoExtResp();
        cityAgingInfoExtResp.setAgingNameCode(4);
        List<CityAgingInfoExtResp> list = new ArrayList<>();
        list.add(cityAgingInfoExtResp);
        CommonDto<List<CityAgingInfoExtResp>> dto = new CommonDto<>();
        dto.setData(list);
        dto.setCode(1);
        when(cityAgingNewApiAcl.queryMixtureProductAging(any())).thenReturn(dto);
        try {
            promiseTranslator.modelToRequest(model1, warehouse, TransportationTypeEnum.LAND);
        }catch(Exception e){
            log.error("抛出异常：{}",e);
        }
    }

    @Test
    public void testTranslateTransferTimeReq_POPOrder() {
        Warehouse warehouse = warehouseBuilder();
        OrderFulfillmentModel model = anCommonOutboundModel1(OrderBusinessTypeEnum.B2C, aShipment(), productCollection);
        
        TransferTimeRequest result = promiseTranslator.translateTransferTimeReq(model, warehouse);
    }

    private Warehouse warehouseBuilder(){
        Warehouse warehouse = new Warehouse();
        warehouse.setWarehouseNo("1");
        warehouse.setProvinceId(1L);
        warehouse.setCityId(1L);
        warehouse.setCountyId(1L);
        warehouse.setProvinceId(1L);
        warehouse.setDistributionId(1L);
        warehouse.setDistributionNo("1");
        warehouse.setErpWarehouseNo("1");
        return warehouse;
    }

    private OrderFulfillmentModel anCommonOutboundModel1(OrderBusinessTypeEnum OrderBusinessType, Shipment shipment, ProductCollection productCollection) {
        return OrderFulfillmentModel.builder()
                .basicInfo(aBasicInfo())
                .batrixInfo(aBatrixInfo())
                .shipment(shipment)
                .warehouse(aWarehouse())
                .consignee(aConsignee())
                .consignor(aConsignor())
                .channel(Channel.builder()
                        .channelNo("12")
                        .channelSource(ChannelSourceEnum.ISV)
                        .channelOrderNo("0")
                        .shopNo("1")
                        .channelOrderCreateTime(new Date())
                        .customerOrderNo("")
                        .channelShipmentNo("SF")
                        .build())
                .cargos(Lists.newArrayList(aCargo(), aCargo(), aCargo()))
                .customer(aCustomer())
                .products(productCollection)
                .orderBusinessType(OrderBusinessType)
                .orderNo("123456")
                .finance(aFinance())
                .solution(aSolution())
                .refOrderInfo(aRefOrderInfo())
                .nonstandardProducts(new NonstandardProducts())
                .goods(Collections.singletonList(aGoods()))
                .fulfillment(aFulfillment())
                .ediRemark(EdiRemarkInfo.builder().deliveryOrder(DeliveryOrderInfo.builder().build()).build())
                .build();
    }
}
