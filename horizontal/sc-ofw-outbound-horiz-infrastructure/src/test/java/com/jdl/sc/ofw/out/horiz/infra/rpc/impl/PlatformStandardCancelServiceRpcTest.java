package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.JDPS;
import com.jdl.sc.ofw.out.domain.model.product.*;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import lombok.Builder;
import com.jdl.sc.ofw.out.domain.model.*;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import java.math.BigDecimal;
import java.util.ArrayList;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.out.common.dict.GoodsTypeEnum;
import org.apache.commons.lang3.StringUtils;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ThirdPartPlatformCancelAcl;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import lombok.Setter;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import javax.validation.constraints.NotNull;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jd.jsf.gd.server.*;
import java.util.Set;
import com.jdl.sc.ofw.out.common.dict.DecryptCommandEnum;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import com.jd.csc.channel.intserv.Response;
import org.springframework.context.annotation.Profile;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import java.util.List;
import com.jdl.sc.ofw.out.domain.model.orderclob.ClobTypeEnums;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.TmsWaybillTypeEnum.OTHER;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.outbound.spec.enums.*;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipModeEnum;
import java.util.Objects;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetRequest;
import org.springframework.stereotype.Service;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.SiteShipTypeEnum.*;
import com.jdl.sc.ofw.out.horiz.infra.rpc.PlatformStandardServiceAcl;
import lombok.extern.jackson.Jacksonized;
import lombok.Getter;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import org.springframework.beans.factory.annotation.Value;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import java.util.concurrent.CompletableFuture;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import java.util.Map;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.vo.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusPhaseEnum;
import com.jd.csc.channel.intserv.PlatformEnum;
import java.util.concurrent.TimeUnit;
import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Cancel;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jd.csc.channel.intserv.*;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.csc.channel.intserv.waybill.WaybillInfoVo;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.TmsWaybillTypeEnum.JP;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Optional;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jd.jsf.gd.util.*;
import cn.jdl.batrix.sdk.base.BDomainAsyncModel;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.*;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.JPZSF;
import javax.annotation.Resource;
import static com.jdl.sc.ofw.out.common.constant.OmsConstant.SYSTEM_CALLER;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jd.csc.channel.intserv.waybill.*;
import java.io.Serializable;
import com.jd.csc.channel.intserv.Request;
import com.jd.csc.channel.intserv.waybill.WaybillCodeCancelRequest;
import lombok.AllArgsConstructor;
import com.jdl.sc.ofw.out.domain.model.product.ExpressProductEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.WmsCarrierTypeEnum.*;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import java.util.EnumMap;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import java.util.Arrays;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.common.exception.BusinessDomainException;
import java.util.Date;
import com.jd.csc.channel.intserv.waybill.WaybillCodeRequest;
import com.jd.csc.channel.intserv.PlatformStandardService;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.ZI_CSF;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import lombok.NoArgsConstructor;

import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.PlatformStandardCancelServiceRpc;


import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the PlatformStandardCancelServiceRpc class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class PlatformStandardCancelServiceRpcTest {

	@Mock
	private PlatformStandardService platformStandardService;

	@Mock
	private RpcContext rpcContext;

	@InjectMocks
	private PlatformStandardCancelServiceRpc platformStandardCancelServiceRpc;

	@Mock
	private ElectronicSheetConfigAcl electronicSheetConfigAcl;

    @Test
    public void testSupportForCaiNiao() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertTrue(service.support(DeliveryPerformanceChannelEnum.CAI_NIAO));
    }

    @Test
    public void testSupportForPDD() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertTrue(service.support(DeliveryPerformanceChannelEnum.PDD));
    }

    @Test
    public void testSupportForTikTok() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertTrue(service.support(DeliveryPerformanceChannelEnum.TIK_TOK));
    }

    @Test
    public void testSupportForKS() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertTrue(service.support(DeliveryPerformanceChannelEnum.KS));
    }

    @Test
    public void testSupportForUnsupportedChannel() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertFalse(service.support(DeliveryPerformanceChannelEnum.JD));
    }

    @Test
    public void testSupportForNullChannel() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertFalse(service.support(null));
    }

    @Test
    public void testSupportForVIP() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertFalse(service.support(DeliveryPerformanceChannelEnum.VIP));
    }

    @Test
    public void testSupportForMT() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        PlatformStandardCancelServiceRpc service = new PlatformStandardCancelServiceRpc();
        assertFalse(service.support(DeliveryPerformanceChannelEnum.MT)); 
    }

    @Test
    public void testGetWaybillCodeSuccess() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Arrange
        WaybillCodeRequest waybillCodeRequest = new WaybillCodeRequest();
        String orderNo = "TEST123";
//        platformStandardCancelServiceRpc.setAppName("testApp");

        WaybillInfoVo waybillInfoVo = new WaybillInfoVo("WB001", "print data", "1.0");
        Response<WaybillInfoVo> expectedResponse = new Response<>("tid1", 200, "success", waybillInfoVo);

        Mockito.when(platformStandardService.getWaybillCode(any(Request.class)))
                .thenReturn(expectedResponse);

        // Act
        Response<WaybillInfoVo> actualResponse = platformStandardCancelServiceRpc.getWaybillCode(waybillCodeRequest, orderNo);

        // Assert
        assertNotNull(actualResponse);
    }

    @Test(expected = InfrastructureException.class) 
    public void testGetWaybillCodeThrowsException() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Arrange
        WaybillCodeRequest waybillCodeRequest = new WaybillCodeRequest();
        String orderNo = "TEST123";
//        platformStandardCancelServiceRpc.setAppName("testApp");

        Mockito.when(platformStandardService.getWaybillCode(any(Request.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        platformStandardCancelServiceRpc.getWaybillCode(waybillCodeRequest, orderNo);
    }

    @Test
    public void testGetWaybillCodeNullRequest() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Arrange
        WaybillCodeRequest waybillCodeRequest = null;
        String orderNo = "TEST123";
//        platformStandardCancelServiceRpc.setAppName("testApp");

        WaybillInfoVo waybillInfoVo = new WaybillInfoVo();
        Response<WaybillInfoVo> expectedResponse = new Response<>("tid1", 200, "success", waybillInfoVo);

        Mockito.when(platformStandardService.getWaybillCode(any(Request.class)))
                .thenReturn(expectedResponse);

        // Act
        Response<WaybillInfoVo> actualResponse = platformStandardCancelServiceRpc.getWaybillCode(waybillCodeRequest, orderNo);

        // Assert
        assertNotNull(actualResponse);
    }

    @Test
    public void testGetWaybillCodeEmptyOrderNo() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Arrange
        WaybillCodeRequest waybillCodeRequest = new WaybillCodeRequest();
        String orderNo = "";
//        platformStandardCancelServiceRpc.setAppName("testApp");

        WaybillInfoVo waybillInfoVo = new WaybillInfoVo();
        Response<WaybillInfoVo> expectedResponse = new Response<>("tid1", 200, "success", waybillInfoVo);

        Mockito.when(platformStandardService.getWaybillCode(any(Request.class)))
                .thenReturn(expectedResponse);

        // Act
        Response<WaybillInfoVo> actualResponse = platformStandardCancelServiceRpc.getWaybillCode(waybillCodeRequest, orderNo);

        // Assert
        assertNotNull(actualResponse);
    }

    @Test
    public void testGetWaybillCodeNullOrderNo() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Arrange
        WaybillCodeRequest waybillCodeRequest = new WaybillCodeRequest();
        String orderNo = null;
//        platformStandardCancelServiceRpc.setAppName("testApp");

        WaybillInfoVo waybillInfoVo = new WaybillInfoVo();
        Response<WaybillInfoVo> expectedResponse = new Response<>("tid1", 200, "success", waybillInfoVo);

        Mockito.when(platformStandardService.getWaybillCode(any(Request.class)))
                .thenReturn(expectedResponse);

        // Act
        Response<WaybillInfoVo> actualResponse = platformStandardCancelServiceRpc.getWaybillCode(waybillCodeRequest, orderNo);

        // Assert
        assertNotNull(actualResponse);
    }

    @Test
    public void testCancelWaybillCodeSuccess() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest waybillRequest = new WaybillCodeCancelRequest();
        waybillRequest.setWaybillCode("123456");
        Request<WaybillCodeCancelRequest> request = new Request<>("operator", waybillRequest);
        Response<Boolean> response = new Response<>("tid", 200, "success", true);

        // Mock static
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock async call
        CompletableFuture<Response<Boolean>> future = CompletableFuture.completedFuture(response);
//        when(rpcContext.asyncCall(any())).thenReturn(future);
        
        // Mock service call
        when(platformStandardService.cancelWaybillCode(any())).thenReturn(response);

        // Execute
        Response<Boolean> result = platformStandardCancelServiceRpc.cancelWaybillCode(request);

        // Verify
        Assert.assertEquals(response, result);
    }

    @Test(expected = InfrastructureException.class) 
    public void testCancelWaybillCodeAsyncCallException() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest waybillRequest = new WaybillCodeCancelRequest();
        Request<WaybillCodeCancelRequest> request = new Request<>("operator", waybillRequest);

        // Mock static
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock async call exception
        when(rpcContext.asyncCall(any())).thenThrow(new RuntimeException());

        // Execute
        platformStandardCancelServiceRpc.cancelWaybillCode(request);
    }

    @Test(expected = InfrastructureException.class)
    public void testCancelWaybillCodeTimeoutException() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest waybillRequest = new WaybillCodeCancelRequest();
        waybillRequest.setWaybillCode("123456");
        Request<WaybillCodeCancelRequest> request = new Request<>("operator", waybillRequest);

        // Mock static
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock timeout
        CompletableFuture<Response<Boolean>> future = new CompletableFuture<>();
//        when(rpcContext.asyncCall(any())).thenReturn(future);
//        future.completeExceptionally(new TimeoutException());

        // Execute
        platformStandardCancelServiceRpc.cancelWaybillCode(request);
    }

    @Test(expected = BusinessDomainException.class)
    public void testCancelWaybillCodeNullResponse() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest waybillRequest = new WaybillCodeCancelRequest();
        Request<WaybillCodeCancelRequest> request = new Request<>("operator", waybillRequest);

        // Mock static
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock null response
        CompletableFuture<Response<Boolean>> future = CompletableFuture.completedFuture(null);
//        when(rpcContext.asyncCall(any())).thenReturn(future);

        // Execute
        platformStandardCancelServiceRpc.cancelWaybillCode(request);
    }

    @Test(expected = BusinessDomainException.class)
    public void testCancelWaybillCodeResponseNotSuccess() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest waybillRequest = new WaybillCodeCancelRequest();
        Request<WaybillCodeCancelRequest> request = new Request<>("operator", waybillRequest);
        Response<Boolean> response = new Response<>("tid", 400, "failed", false);

        // Mock static
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock failed response
        CompletableFuture<Response<Boolean>> future = CompletableFuture.completedFuture(response);
//        when(rpcContext.asyncCall(any())).thenReturn(future);

        // Execute
        platformStandardCancelServiceRpc.cancelWaybillCode(request);
    }

    @Test
    public void testBatchCancelWaybillSuccess() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest request1 = new WaybillCodeCancelRequest();
        request1.setWaybillCode("123");
        Request<WaybillCodeCancelRequest> req1 = new Request<>("op1", request1);

        WaybillCodeCancelRequest request2 = new WaybillCodeCancelRequest(); 
        request2.setWaybillCode("456");
        Request<WaybillCodeCancelRequest> req2 = new Request<>("op2", request2);

        List<Request<WaybillCodeCancelRequest>> requestList = Arrays.asList(req1, req2);

        // Mock RpcContext
//        RpcContext rpcContext = new RpcContext();
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock service response
        Response<Boolean> response1 = new Response<>();
        response1.setCode(200);
        response1.setData(true);
        Response<Boolean> response2 = new Response<>();
        response2.setCode(200); 
        response2.setData(true);

//        Mockito.when(platformStandardService.cancelWaybillCode(any())).thenReturn(response1, response2);

        // Execute
        Map<String, Boolean> result = platformStandardCancelServiceRpc.batchCancelWaybill(requestList);

        // Verify
        Assert.assertEquals(2, result.size());
        Assert.assertTrue(result.get("123"));
        Assert.assertTrue(result.get("456"));
    }

    @Test(expected = InfrastructureException.class) 
    public void testBatchCancelWaybillAsyncCallException() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest request = new WaybillCodeCancelRequest();
        request.setWaybillCode("123");
        Request<WaybillCodeCancelRequest> req = new Request<>("op", request);
        List<Request<WaybillCodeCancelRequest>> requestList = Collections.singletonList(req);

        // Mock RpcContext
//        RpcContext rpcContext = new RpcContext();
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock service throws exception
        Mockito.when(platformStandardService.cancelWaybillCode(any())).thenThrow(new RuntimeException());

        // Execute
        platformStandardCancelServiceRpc.batchCancelWaybill(requestList);
    }

    @Test(expected = InfrastructureException.class)
    public void testBatchCancelWaybillTimeoutException() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest request = new WaybillCodeCancelRequest();
        request.setWaybillCode("123");
        Request<WaybillCodeCancelRequest> req = new Request<>("op", request);
        List<Request<WaybillCodeCancelRequest>> requestList = Collections.singletonList(req);

        // Mock RpcContext
//        RpcContext rpcContext = new RpcContext();
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock service response timeout
        Response<Boolean> response = new Response<>();
        response.setCode(500);
        Mockito.when(platformStandardService.cancelWaybillCode(any())).thenAnswer(invocation -> {
            Thread.sleep(6000);
            return response;
        });

        // Execute
        platformStandardCancelServiceRpc.batchCancelWaybill(requestList);
    }

    @Test
    public void testBatchCancelWaybillNullResponse() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        WaybillCodeCancelRequest request = new WaybillCodeCancelRequest();
        request.setWaybillCode("123");
        Request<WaybillCodeCancelRequest> req = new Request<>("op", request);
        List<Request<WaybillCodeCancelRequest>> requestList = Collections.singletonList(req);

        // Mock RpcContext
//        RpcContext rpcContext = new RpcContext();
//        PowerMockito.mockStatic(RpcContext.class);
//        PowerMockito.when(RpcContext.getContext()).thenReturn(rpcContext);

        // Mock null response
        Mockito.when(platformStandardService.cancelWaybillCode(any())).thenReturn(null);

        // Execute
        Map<String, Boolean> result = platformStandardCancelServiceRpc.batchCancelWaybill(requestList);

        // Verify
        Assert.assertEquals(1, result.size());
        Assert.assertFalse(result.get("123"));
    }

    @Test
    public void testBatchCancelWaybillEmptyRequestList() throws Exception {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Execute
        Map<String, Boolean> result = platformStandardCancelServiceRpc.batchCancelWaybill(Collections.emptyList());

        // Verify
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildCancelWaybillListSuccess() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("TEST001")
                .customer(Customer.builder()
                        .sellerNo("SELLER001")
                        .accountNo("ACCOUNT001")
                        .build())
                .shipment(Shipment.builder()
                        .deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.CAI_NIAO)
                        .shipperNo("SHIPPER001")
                        .build())
                .cancel(Cancel.builder()
                        .requestId("REQ001")
                        .build())
                .build();

        Set<String> waybillSet = new HashSet<>();
        waybillSet.add("WAYBILL001");
        waybillSet.add("WAYBILL002");

        // Mock dependencies
        when(electronicSheetConfigAcl.queryConfigInfo(any())).thenReturn(
                ElectronicSheetInfo.builder().authToken("AUTH001").build());

        // Execute
        List<Request<WaybillCodeCancelRequest>> result = platformStandardCancelServiceRpc.buildCancelWaybillList(model, waybillSet);

        // Verify
        assertEquals(2, result.size());
        
        Request<WaybillCodeCancelRequest> request1 = result.get(0);
        assertEquals("REQ001", request1.getOperator());
    }

    @Test(expected = UnsupportedOperationException.class) 
    public void testBuildCancelWaybillListUnsupportedPlatform() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("TEST001")
                .customer(Customer.builder()
                        .sellerNo("SELLER001")
                        .accountNo("ACCOUNT001")
                        .build())
                .shipment(Shipment.builder()
                        .deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.JD)
                        .shipperNo("SHIPPER001")
                        .build())
                .cancel(Cancel.builder()
                        .requestId("REQ001")
                        .build())
                .build();

        Set<String> waybillSet = new HashSet<>();
        waybillSet.add("WAYBILL001");

        when(electronicSheetConfigAcl.queryConfigInfo(any())).thenReturn(
                ElectronicSheetInfo.builder().authToken("AUTH001").build());

        platformStandardCancelServiceRpc.buildCancelWaybillList(model, waybillSet);
    }

    @Test(expected = InfrastructureException.class)
    public void testBuildCancelWaybillListEmptyAuthToken() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("TEST001")
                .customer(Customer.builder()
                        .sellerNo("SELLER001")
                        .accountNo("ACCOUNT001")
                        .build())
                .shipment(Shipment.builder()
                        .deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.CAI_NIAO)
                        .shipperNo("SHIPPER001")
                        .build())
                .cancel(Cancel.builder()
                        .requestId("REQ001")
                        .build())
                .build();

        Set<String> waybillSet = new HashSet<>();
        waybillSet.add("WAYBILL001");

        when(electronicSheetConfigAcl.queryConfigInfo(any())).thenReturn(
                ElectronicSheetInfo.builder().authToken("").build());

        platformStandardCancelServiceRpc.buildCancelWaybillList(model, waybillSet);
    }

    @Test(expected = InfrastructureException.class)
    public void testBuildCancelWaybillListNullElectronicSheetInfo() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("TEST001")
                .customer(Customer.builder()
                        .sellerNo("SELLER001")
                        .accountNo("ACCOUNT001")
                        .build())
                .shipment(Shipment.builder()
                        .deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.CAI_NIAO)
                        .shipperNo("SHIPPER001")
                        .build())
                .cancel(Cancel.builder()
                        .requestId("REQ001")
                        .build())
                .build();

        Set<String> waybillSet = new HashSet<>();
        waybillSet.add("WAYBILL001");

        when(electronicSheetConfigAcl.queryConfigInfo(any())).thenReturn(null);

        platformStandardCancelServiceRpc.buildCancelWaybillList(model, waybillSet);
    }

    @Test
    public void testBuildCancelWaybillListEmptyWaybillSet() {
        // generated by JoyCoder taskId 3fb4ab6dfa93
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("TEST001")
                .customer(Customer.builder()
                        .sellerNo("SELLER001")
                        .accountNo("ACCOUNT001")
                        .build())
                .shipment(Shipment.builder()
                        .deliveryPerformanceChannel(DeliveryPerformanceChannelEnum.CAI_NIAO)
                        .shipperNo("SHIPPER001")
                        .build())
                .cancel(Cancel.builder()
                        .requestId("REQ001")
                        .build())
                .build();

        Set<String> waybillSet = new HashSet<>();

        when(electronicSheetConfigAcl.queryConfigInfo(any())).thenReturn(
                ElectronicSheetInfo.builder().authToken("AUTH001").build());

        List<Request<WaybillCodeCancelRequest>> result = platformStandardCancelServiceRpc.buildCancelWaybillList(model, waybillSet);

        assertEquals(0, result.size());
    }

}