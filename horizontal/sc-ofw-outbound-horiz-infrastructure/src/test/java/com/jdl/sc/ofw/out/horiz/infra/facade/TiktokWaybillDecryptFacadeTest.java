package com.jdl.sc.ofw.out.horiz.infra.facade;

import com.jd.ldop.center.api.update.dto.WaybillUpdateBeforePickupDTO;
import com.jdl.sc.ofw.out.domain.dto.*;

import com.jdl.sc.ofw.out.horiz.infra.facade.TiktokWaybillDecryptFacade;

import lombok.Builder;
import java.io.Serializable;
import com.jd.ldop.center.api.ResponseDTO;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.time.Instant;
import com.jd.ldop.center.api.receive.dto.*;
import com.jdl.sc.ofw.out.horiz.infra.rpc.WaybillOperatorAcl;
import java.util.Objects;
import com.jd.ldop.center.api.*;
import com.jd.ldop.center.api.update.dto.*;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.dto.EbillInfoGetRequest;
import com.jdl.sc.ofw.out.domain.dto.EdiProcessorTiktokWaybillInfo;
import org.apache.commons.collections4.MapUtils;
import java.time.Duration;
import com.jdl.sc.core.json.JsonUtil;
import java.util.Date;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Value;
import java.util.Map;
import javax.annotation.Resource;
import lombok.NoArgsConstructor;
import com.jdl.sc.ofw.out.horiz.infra.rpc.EdiProcessorAcl;
import com.jd.ldop.tools.validator.*;
import org.springframework.stereotype.Component;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the TiktokWaybillDecryptFacade class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class TiktokWaybillDecryptFacadeTest {

	@InjectMocks
	private TiktokWaybillDecryptFacade tiktokWaybillDecryptFacade;

	@Mock
	private WaybillOperatorAcl waybillOperatorAcl;

	@Mock
	private EdiProcessorAcl ediProcessorAcl;

    @Test
    public void testDecryptWaybillSuccess() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "TEST123";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        EdiProcessorTiktokWaybillInfo waybillInfo = EdiProcessorTiktokWaybillInfo.builder()
                .name("Test Name")
                .phone("12345678")
                .mobile("13800138000")
                .build();

        ResponseDTO<Object> response = new ResponseDTO<>(0, "success");

        // Mock dependencies
//        when(ediProcessorAcl.process(any())).thenReturn(waybillInfo);
//        when(waybillOperatorAcl.updateBeforePickUp(any())).thenReturn(response);

        // Execute
        tiktokWaybillDecryptFacade.decryptWaybill(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testDecryptWaybillUpdateException() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "TEST123";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        EdiProcessorTiktokWaybillInfo waybillInfo = EdiProcessorTiktokWaybillInfo.builder()
                .name("Test Name")
                .phone("12345678")
                .mobile("13800138000")
                .build();

        // Mock dependencies
        when(ediProcessorAcl.process(any())).thenReturn(null);
        when(waybillOperatorAcl.updateBeforePickUp(any())).thenThrow(new RuntimeException());

        // Execute
        tiktokWaybillDecryptFacade.decryptWaybill(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testDecryptWaybillNullResponse() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "TEST123";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        EdiProcessorTiktokWaybillInfo waybillInfo = EdiProcessorTiktokWaybillInfo.builder()
                .name("Test Name")
                .phone("12345678")
                .mobile("13800138000")
                .build();

        // Mock dependencies
        when(ediProcessorAcl.process(any())).thenReturn(null);
        when(waybillOperatorAcl.updateBeforePickUp(any())).thenReturn(null);

        // Execute
        tiktokWaybillDecryptFacade.decryptWaybill(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testDecryptWaybillErrorResponse() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "TEST123";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        EdiProcessorTiktokWaybillInfo waybillInfo = EdiProcessorTiktokWaybillInfo.builder()
                .name("Test Name")
                .phone("12345678")
                .mobile("13800138000")
                .build();

        ResponseDTO<Object> response = new ResponseDTO<>(-1, "error");

        // Mock dependencies
        when(ediProcessorAcl.process(any())).thenReturn(null);
        when(waybillOperatorAcl.updateBeforePickUp(any())).thenReturn(null);

        // Execute
        tiktokWaybillDecryptFacade.decryptWaybill(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class) 
    public void testDecryptWaybillGetInfoException() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "TEST123";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        // Mock dependencies
        when(ediProcessorAcl.process(any())).thenThrow(new RuntimeException());

        // Execute
        tiktokWaybillDecryptFacade.decryptWaybill(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testDecryptWaybillNullInfo() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "TEST123";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        // Mock dependencies
        when(ediProcessorAcl.process(any())).thenReturn(null);

        // Execute
        tiktokWaybillDecryptFacade.decryptWaybill(waybillCode, createTime, existFreightProduct);
    }

    @Test
    public void testGetEdiProcessorTiktokWaybillInfoWithFreightProductSuccess() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        // Mock response data
        Map<String, Object> contact = new HashMap<>();
        contact.put("name", "Test Name");
        contact.put("mobile", "13800138000");
        contact.put("phone", "010-12345678");

        Map<String, Object> address = new HashMap<>();
        address.put("detail_address", "Test Address");

        Map<String, Object> receiver = new HashMap<>();
        receiver.put("contact", contact);
        receiver.put("address", address);

        Map<String, Object> data = new HashMap<>();
        data.put("receiver", receiver);

        Map<String, Object> response = new HashMap<>();
        response.put("data", data);

        when(ediProcessorAcl.process(any(Map.class))).thenReturn(response);

        // Execute
        EdiProcessorTiktokWaybillInfo result = tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);

        // Verify
        assertNotNull(result);
    }

    @Test
    public void testGetEdiProcessorTiktokWaybillInfoWithoutFreightProductSuccess() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = false;

        // Mock response data
        Map<String, Object> contact = new HashMap<>();
        contact.put("name", "Test Name");
        contact.put("mobile", "13800138000");
        contact.put("phone", "010-12345678");

        Map<String, Object> address = new HashMap<>();
        address.put("detailAddress", "Test Address");

        Map<String, Object> receiver = new HashMap<>();
        receiver.put("contact", contact);
        receiver.put("address", address);

        when(ediProcessorAcl.process(any(Map.class))).thenReturn(receiver);

        // Execute
        EdiProcessorTiktokWaybillInfo result = tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);

        // Verify
        assertNotNull(result);
    }

    @Test(expected = PauseException.class)
    public void testGetEdiProcessorTiktokWaybillInfoEmptyResponse() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        when(ediProcessorAcl.process(any(Map.class))).thenReturn(new HashMap<>());

        // Execute
        tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testGetEdiProcessorTiktokWaybillInfoNullResponse() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        when(ediProcessorAcl.process(any(Map.class))).thenReturn(null);

        // Execute
        tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testGetEdiProcessorTiktokWaybillInfoProcessException() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        when(ediProcessorAcl.process(any(Map.class))).thenThrow(new RuntimeException("Process failed"));

        // Execute
        tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class)
    public void testGetEdiProcessorTiktokWaybillInfoEmptyReceiver() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        Map<String, Object> data = new HashMap<>();
        Map<String, Object> response = new HashMap<>();
        response.put("data", data);

        when(ediProcessorAcl.process(any(Map.class))).thenReturn(response);

        // Execute
        tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);
    }

    @Test(expected = PauseException.class) 
    public void testGetEdiProcessorTiktokWaybillInfoEmptyContact() {
        // generated by JoyCoder taskId 8fd21de827ec
        // Prepare test data
        String waybillCode = "123456";
        Date createTime = new Date();
        boolean existFreightProduct = true;

        Map<String, Object> receiver = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        data.put("receiver", receiver);
        Map<String, Object> response = new HashMap<>();
        response.put("data", data);

        when(ediProcessorAcl.process(any(Map.class))).thenReturn(response);

        // Execute
        tiktokWaybillDecryptFacade.getEdiProcessorTiktokWaybillInfo(waybillCode, createTime, existFreightProduct);
    }

}