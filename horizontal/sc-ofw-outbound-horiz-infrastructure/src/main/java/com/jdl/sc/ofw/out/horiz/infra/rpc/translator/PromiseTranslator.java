package com.jdl.sc.ofw.out.horiz.infra.rpc.translator;


import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.etms.vrs.api.cityaging.req.AgingReq;
import com.jd.etms.vrs.api.cityaging.resp.CityAgingInfoExtResp;
import com.jd.etms.vrs.api.common.dto.CommonDto;
import com.jd.fce.dos.service.domain.OrderMarkingRequest;
import com.jd.fce.dos.service.domain.SKU;
import com.jd.fce.dos.service.domain.eclp.OrderMarkingEclpDeliveryRequest;
import com.jd.promise.service.domain.TransferTimeRequest;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeRequest;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.constant.NonstandardProductConstants;
import com.jdl.sc.ofw.out.common.dict.PaymentTypeEnum;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.ProductBusinessLineEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.SolutionAttributeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.JdAddress;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.rpc.enums.OrderControlMode;
import com.jdl.sc.ofw.out.horiz.infra.facade.translator.CustomPresortConvert;
import com.jdl.sc.ofw.out.horiz.infra.rpc.CityAgingNewApiAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.dto.promise.WarehouseAndDeliveryTimeRequestExtend;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WarehouseTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.SiteShipTypeEnum.QING_LONG_DELIVERY;
import static com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum.SiteShipTypeEnum.THIRD_DELIVERY;
import static org.apache.commons.lang3.math.NumberUtils.toInt;

@Slf4j
@Component
@Primary
public class PromiseTranslator {

    @Resource
    private CityAgingNewApiAcl cityAgingNewApiAcl;

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    public OrderMarkingEclpDeliveryRequest modelToForbiddenRequest(OrderFulfillmentModel model, Warehouse warehouse) {
        OrderMarkingEclpDeliveryRequest request = new OrderMarkingEclpDeliveryRequest();
        request.setStartProvinceId(Optional.of(warehouse).map(Warehouse::getWareProvinceId).map(Long::intValue).orElse(0));
        request.setStartCityId(Optional.of(warehouse).map(Warehouse::getWareCityId).map(Long::intValue).orElse(0));
        request.setStartCountyId(Optional.of(warehouse).map(Warehouse::getWareCountyId).map(Long::intValue).orElse(0));
        request.setStartTownId(Optional.of(warehouse).map(Warehouse::getWareTownId).map(Long::intValue).orElse(0));

        request.setProvinceId(Optional.of(model.getConsignee()).map(Consignee::getAddress).map(JdAddress::getProvinceNo).map(NumberUtils::toInt).orElse(0));
        request.setCityId(Optional.of(model.getConsignee()).map(Consignee::getAddress).map(JdAddress::getCityNo).map(NumberUtils::toInt).orElse(0));
        request.setCountyId(Optional.of(model.getConsignee()).map(Consignee::getAddress).map(JdAddress::getCountyNo).map(NumberUtils::toInt).orElse(0));
        request.setTownId(Optional.of(model.getConsignee()).map(Consignee::getAddress).map(JdAddress::getTownNo).map(NumberUtils::toInt).orElse(0));

        request.setTid(System.currentTimeMillis());
        return request;
    }

    public WarehouseAndDeliveryTimeRequest modelToRequest(OrderFulfillmentModel model, Warehouse warehouse, TransportationTypeEnum transportationType) {
        WarehouseAndDeliveryTimeRequest request = new WarehouseAndDeliveryTimeRequest();
        boolean isPOP = model.isPop();

        //京东平台:京东平台订单号 非京东平台：eclp订单号（“ESL”开头）
        request.setOrderId(isPOP ? model.getChannel().getChannelOrderNo() : String.valueOf(model.orderId()));
        //平台类型，pop订单默认京东  非京东平台：2（指天猫、淘宝、当当）
        request.setPlatformType(isPOP ? 1 : 2);
        //ECLP订单类型
        request.setSoType(model.getOrderBusinessType().getCode());
        // 商品信息设置
        request.setSkuQuantity(getSkuQuantity(model.getCargos()));
        // 设置
        request.setPartnerId(isPOP ? (StringUtils.isBlank(model.getShipment().getEndStationNo()) ? -99 : NumberUtils.toInt(model.getShipment().getEndStationNo(), 0)) : -99);
        //青龙业主号
        request.setContractId(model.getShipment().getBdOwnerNo());
        //事业部编码
        request.setDeptNo(model.getCustomer().getAccountNo());
        //销售平台id
        request.setSpId(Long.parseLong(model.getChannel().getChannelNo()));
        // 设置sendPay
        request.setSendPay(getSendPay(model));

        //始发地四级地址
        request.setFromProvinceId(warehouse.getWareProvinceId() == null ? 0 : warehouse.getWareProvinceId().intValue());
        request.setFromCityId(warehouse.getWareCityId() == null ? 0 : warehouse.getWareCityId().intValue());
        request.setFromCountyId(warehouse.getWareCountyId() == null ? 0 : warehouse.getWareCountyId().intValue());
        request.setFromTownId(warehouse.getWareTownId() == null ? 0 : warehouse.getWareTownId().intValue());
        // 四级地址设置
        request.setProvinceId(model.isPop() ? NumberUtils.toInt(model.getConsignee().getAddress().getProvinceNo()) : NumberUtils.toInt(model.getConsignee().getAddress().getProvinceNoGis()));
        request.setCityId(model.isPop() ? NumberUtils.toInt(model.getConsignee().getAddress().getCityNo()) : NumberUtils.toInt(model.getConsignee().getAddress().getCityNoGis()));
        request.setCountyId(model.isPop() ? NumberUtils.toInt(model.getConsignee().getAddress().getCountyNo()) : NumberUtils.toInt(model.getConsignee().getAddress().getCountyNoGis()));
        request.setTownId(model.isPop() ? NumberUtils.toInt(model.getConsignee().getAddress().getTownNo()) : NumberUtils.toInt(model.getConsignee().getAddress().getTownNoGis()));

        // 京东配送中心
        request.setDict(getDict(warehouse));
        // 京东库房号
        request.setStoreId(Integer.valueOf(warehouse.getErpWarehouseNo()));

        request.setFactPrice(ObjectUtils.defaultIfNull(model.getSolution().getAttributeValue(SolutionAttributeEnum.SHOULD_PAY_MONEY.getValue()),
                value -> new BigDecimal(value), BigDecimal.ZERO));

        // 用于区分是预分拣之前或预分拣之后的请求(1: 预分拣之前； 2:预分拣之后)
        request.setSource("2");

        // 判断是否有青龙站点配送 三方站点字段，“1” 表示三方配送站，“2”标示非三方站点
        request.setThirdParty(model.getShipment().getShipperType().getSiteShipType().getCode() == '0' ? "2" : "1");

        // 设置承运商类型
        request.setDeliveryType(model.getShipment().getShipperType().getCode());

        //冷链运输类型
        TransportationTypeEnum transportationTypeNew = getTransportationType(model, warehouse, transportationType);
        if (transportationTypeNew != null) {
            //设置是否支持航空运输
            request.setOrderAviationLine(TransportationTypeEnum.AVIATION.equals(transportationTypeNew));
            //设置是否支持航空填仓
            request.setFlightFilling(transportationTypeNew.isAviationFilling());
        }
        request.setTransportMode(1); // 对应soMark59,默认为零担类型,transportMode赋值为1
        //设置运输方式
        request.setTransportType(model.getShipment().getTransportationType().getPromiseCode());
        //设置商家扩展字段
        request.setSellerExtendParam(buildSellerExtendParam(model));

        request.setCustomPresortList(ObjectUtils.nullIfNull(model.getShipment().getCustomPresortList(), customPresortList -> customPresortList.stream()
                .map(CustomPresortConvert::convert)
                .collect(Collectors.toList())));

        request.setExtendPromise(JsonUtil.toJsonSafe(buildRequestExtend(model)));
        // 配运主产品
        request.setProductCode(model.getProducts().getProducts().stream()
                .filter(product -> ProductTypeEnum.MAIN_PRODUCT.equals(product.getType()))
                .filter(product -> ProductBusinessLineEnum.DISTRIBUTION.equals(product.getBusinessLine())
                        || ProductBusinessLineEnum.TRANSPORT.equals(product.getBusinessLine()))
                .map(Product::getNo)
                .findAny()
                .orElse(null));
        // 是否启用融合产品，固定传否
        request.setMixtureProductMode(0);
        if (switchKeyUtils.isPromiseDeliveryTimeDept(model.getBatrixInfo().getBusinessUnit())) {
            request.setShopNo(model.getChannel().getShopNo());
            request.setCarrierId(model.getShipment().getShipperId());
            request.setSellerNo(model.getCustomer().getSellerNo());
            request.setSellerName(model.getCustomer().getSellerName());
        } else {
            // 设置承运商Id,当为京配的时候不用传
            request.setCarrierId(model.getShipment().getShipperType().getCode() != 1 ? model.getShipment().getShipperId() : null);
        }

        if(switchKeyCommonUtils.slaPromiseNewFieldDeptNoContains(model.getCustomer().getAccountNo())){
            request.setBpWhiteType("SO");
        }

        return request;
    }

    /**
     * 获取配送中心-兼容CLPS仓类型
     */
    private int getDict(Warehouse warehouse) {
        if (WarehouseTypeEnum.isClps(warehouse.getType())) {
            return NumberUtils.toInt(warehouse.getClpsDistributionNo(), 0);
        }
        return NumberUtils.toInt(warehouse.getDistributionNo(), 0);
    }

    private WarehouseAndDeliveryTimeRequestExtend buildRequestExtend(OrderFulfillmentModel model) {
        // promise扩展信息
        WarehouseAndDeliveryTimeRequestExtend extend = new WarehouseAndDeliveryTimeRequestExtend();
        boolean isPOP = model.isPop();
        extend.setWaybillCode(StringUtils.isBlank(model.getShipment().getWayBill()) ? model.getOrderNo() : model.getShipment().getWayBill());
        // 是否使用京东配送,固定为1 京配 ,2 直发三方
        extend.setShipmentType(1);
        // 库存状态:固定值，33 现货
        extend.setStockState(33);
        extend.setAddstr(ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getDetailAddress));

        // 判断是否为货到付款，即后款 //支付方式 1先款 2后款
        extend.setPaymentType(model.getFinance().getPaymentType() == PaymentTypeEnum.CASH_ON_DELIVERY ? 2 : 1);
        // 订单重量   pop订单暂时无法获取，写0  ISV订单暂时无法获取，写0
        extend.setWeight(BigDecimal.ZERO);
        // 订单体积
        extend.setOrderBulk(BigDecimal.ZERO);
        // 商家编号,使用POP的店铺编号 ISV订单商家编号写0
        extend.setVendorId(isPOP ? NumberUtils.toInt(model.getChannel().getChannelShopNo(), 0) : 0);
        // 下单时间
        extend.setOrderTime(model.getChannel().getChannelOrderCreateTime());
        // 配送日期类型
        extend.setCodTimeType(isPOP ? 0 : 3);
        // 订单类型
        extend.setOrderType(isPOP ? 0 : 22);
        return extend;
    }

    private Map<String, String> buildSellerExtendParam(OrderFulfillmentModel model) {
        Map<String, String> param = new HashMap<>();
        Integer orderPriority = model.getFulfillment().getOrderPriority();
        if (orderPriority != null) {
            param.put("orderPriority", String.valueOf(orderPriority));
        }
        param.put("transportType", String.valueOf(model.getShipment().getTransportationType().getPromiseCode()));
        param.put("soType", String.valueOf(model.getOrderBusinessType().getCode()));
        param.put("spId", model.getChannel().getChannelNo() == null ? null : model.getChannel().getChannelNo());
        param.put("transportMode", "1");
        return param;
    }

    /**
     * 构建SKU信息
     */
    protected int getSkuQuantity(List<Cargo> cargos) {
        return cargos.stream()
                .mapToInt(cargo -> cargo.getQuantity() != null ? cargo.getQuantity().getValue().intValue() : 0)
                .sum();
    }


    /**
     * sendpay
     */
    private String getSendPay(OrderFulfillmentModel model) {
        char[] sendPay = String.format("%0500d", 0).toCharArray();
        //冷链B2C订单
        if(OrderBusinessTypeEnum.B2C.equals(model.getOrderBusinessType()) && "01".equals(model.getFulfillment().getProfessionType())){//todo 行业是否转换成枚举类
            log.info("单号：{},为冷链B2C订单，sendPay160=1，调用promise获取时效",model.getOrderNo());
            sendPay[159] = '1';
        }
        //海运违禁订单
        if (model.getNonstandardProducts().isEnrolled(NonstandardProductConstants.MARINE_CONTRABAND)) {
            sendPay[292] = '1';
        }
        return new String(sendPay);
    }

    private TransportationTypeEnum getTransportationType(OrderFulfillmentModel model, Warehouse warehouse, TransportationTypeEnum transportationTypeEnum) {
        // 生鲜特惠 默认陆运
        if (model.getProducts().isEnrolled(ProductEnum.LL_HD)) {
            log.info("单号：{}，生鲜特惠不支持航空运输", model.getOrderNo());
            return TransportationTypeEnum.LAND;
        }
        //POP 生鲜速达，直接赋值航空
        if (ChannelSourceEnum.POP.equals(model.getChannel().getChannelSource()) && model.getProducts().isEnrolled(ProductEnum.LL_SD)) {
            log.info("单号：{}，pop来源生鲜特快直接支持航空运输", model.getOrderNo());
            return TransportationTypeEnum.AVIATION;
        }
        //未开冷链时效升级新流程开关 && 其他生鲜特快，需要调用路由系统，获取路由系统的配置是航空还是陆运
        if (!switchKeyUtils.isColdChainPromiseUpdateNewFlow(model.getCustomer().getAccountNo())
                && ChannelSourceEnum.ISV.equals(model.getChannel().getChannelSource()) && model.getProducts().isEnrolled(ProductEnum.LL_SD)) {
            transportationTypeEnum = getTransportationTypeByCityAging(model, warehouse, transportationTypeEnum);
        }

        return transportationTypeEnum;
    }

    private TransportationTypeEnum getTransportationTypeByCityAging(OrderFulfillmentModel model, Warehouse warehouse, TransportationTypeEnum transportationTypeEnum) {
        //调用路由系统
        log.info("单号：{},ISV来源生鲜特快需要调用路由配置", model.getOrderNo());
        Set<String> productCodeSet = new HashSet<>();
        productCodeSet.add("P9");
        AgingReq agingReq = new AgingReq();
        agingReq.setStartProvinceId(toInt(String.valueOf(warehouse.getProvinceId()), 0));
        agingReq.setStartCityId(toInt(String.valueOf(warehouse.getCityId()), 0));
        agingReq.setStartCountryId(toInt(String.valueOf(warehouse.getCountyId()), 0));
        agingReq.setStartTownId(toInt(String.valueOf(warehouse.getTownId()), 0));
        agingReq.setQueryTime(model.getChannel().getChannelOrderCreateTime());
        //查询时间类型 0下单时间，1揽收时间
        agingReq.setType(0);
        agingReq.setEndProvinceId(toInt(model.getConsignee().getAddress().getProvinceNo(), toInt(model.getConsignee().getAddress().getProvinceNoGis())));
        agingReq.setEndCityId(toInt(model.getConsignee().getAddress().getCityNo(), toInt(model.getConsignee().getAddress().getCityNoGis())));
        agingReq.setEndCountryId(toInt(model.getConsignee().getAddress().getCountyNo(), toInt(model.getConsignee().getAddress().getCountyNoGis())));
        agingReq.setEndTownId(toInt(model.getConsignee().getAddress().getTownNo(), toInt(model.getConsignee().getAddress().getTownNoGis())));
        agingReq.setProductCodes(productCodeSet);
        CommonDto<List<CityAgingInfoExtResp>> listCommonDto = null;
        try {
            listCommonDto = cityAgingNewApiAcl.queryMixtureProductAging(agingReq);
        } catch (Exception e) {
            log.error("单号：{}生鲜速达产品 调用路由系统异常，任务挂起", model.getOrderNo(), e);
            throw new PauseException(PauseReasonEnum.SXSD_AIR_EXCEPTION)
                    .withCustom("单号：" + model.getOrderNo() + "调用路由系统异常" + e);
        }
        //判断路由系统返回的是航空配置还是陆运配置,如果是陆运配置就不需要调用主数据相关的配置
        if (listCommonDto == null || listCommonDto.getCode() != 1 || CollectionUtils.isEmpty(listCommonDto.getData())) {
            log.error("单号：{}记录异常开始 promise返回非航空", model.getOrderNo());
            throw new PauseException(PauseReasonEnum.SXSD_AIR_EXCEPTION)
                    .withCustom("单号：" + model.getOrderNo() + "调用路由接口返回" + JsonUtil.toJsonSafe(listCommonDto));
        }
        //如果AgingNameCode = 2代表陆运  1 代表航空
        if (listCommonDto.getData().get(0).getAgingNameCode() == 2) {
            //陆运直接返回
            return TransportationTypeEnum.LAND;
        } else if (listCommonDto.getData().get(0).getAgingNameCode() == 4) {
            log.error("单号：{}记录异常开始 promise返回非航空 路由接口返回高铁", model.getOrderNo());
            throw new PauseException(PauseReasonEnum.SXSD_AIR_EXCEPTION)
                    .withCustom("单号：" + model.getOrderNo() + "调用路由接口返回高铁");
        }

        if (TransportationTypeEnum.LAND.equals(transportationTypeEnum)) {
            //如果是生鲜速达产品，路由配置是航空，但是实际上返回的是陆运，这里需要卡单，记录10124异常
            log.error("单号：{}记录异常开始 生鲜速达产品，路由配置是航空，但是实际返回的是陆运", model.getOrderNo());
            throw new PauseException(PauseReasonEnum.SXSD_AIR_EXCEPTION)
                    .withCustom("单号：" + model.getOrderNo() + "生鲜速达产品，路由配置是航空，但是实际返回的是陆运");
        }
        return transportationTypeEnum;
    }

    public OrderMarkingRequest modelToCityPromiseRequest(OrderFulfillmentModel model, Warehouse warehouse) {
        OrderMarkingRequest orderMarkingRequest = new OrderMarkingRequest();
        orderMarkingRequest.setOrderId(model.orderId());
        orderMarkingRequest.setOrderType(0);
        orderMarkingRequest.setStockState(33);// 库存状态
        orderMarkingRequest.setDict(Integer.parseInt(warehouse.getDistributionNo()));// 配送中心编码
        orderMarkingRequest.setStoreId(Integer.parseInt(warehouse.getErpWarehouseNo()));// ERP库房号

        JdAddress address = model.getConsignee().getAddress();
        orderMarkingRequest.setProvinceId(StringUtils.isNotBlank(address.getProvinceNo()) ? NumberUtils.toInt(address.getProvinceNo()) : NumberUtils.toInt(address.getProvinceNoGis()));
        orderMarkingRequest.setCityId(StringUtils.isNotBlank(address.getCityNo()) ? NumberUtils.toInt(address.getCityNo()) : NumberUtils.toInt(address.getCityNoGis()));
        orderMarkingRequest.setCountyId(StringUtils.isNotBlank(address.getCountyNo()) ? NumberUtils.toInt(address.getCountyNo()) : NumberUtils.toInt(address.getCountyNoGis()));
        orderMarkingRequest.setTownId(Optional.of(address).map(JdAddress::getTownNo).filter(NumberUtils::isDigits).map(Integer::parseInt).orElse(0));

        if (orderMarkingRequest.getProvinceId() <= 0 && orderMarkingRequest.getCityId() <= 0 && orderMarkingRequest.getCountyId() <= 0) {
            log.error("城配调用promise前四级地址不合法");
            throw new RuntimeException("城配调用promise前四级地址不合法");
        }
        orderMarkingRequest.setPaymentType(model.getFinance().getPaymentType().getCode());// 支付方式
        orderMarkingRequest.setSkuList(model.getCargos()
                .stream()
                .map(o -> {
                    SKU sku = new SKU();
                    sku.setProductId(o.getId());
                    sku.setQuantity(o.getQuantity().getValue().intValue());
                    return sku;
                }).collect(Collectors.toList()));

        orderMarkingRequest.setCodDate(new Date());
        orderMarkingRequest.setCodTimeType(0);
        orderMarkingRequest.setWeight(model.calculateTotalWeight());
        orderMarkingRequest.setOrderBulk(model.calculateTotalVolume());
        orderMarkingRequest.setFactPrice(model.calculateTotalAmount());
        final Integer partnerId = Optional.ofNullable(model.getShipment().getPresortInfo().getSiteNo())
                .filter(StringUtils::isNotBlank)
                .filter(StringUtils::isNumeric)
                .map(Integer::parseInt)
                .orElse(Optional.ofNullable(model.getShipment().getEndStationNo())
                        .filter(StringUtils::isNotBlank)
                        .filter(StringUtils::isNumeric)
                        .map(Integer::parseInt)
                        .orElse(null));
        if (partnerId != null) {
            orderMarkingRequest.setPartnerId(partnerId);
        }

        orderMarkingRequest.setSendpay("00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000");
        orderMarkingRequest.setSource("20"); // 来源 POP城配传入20
        return orderMarkingRequest;
    }

    public TransferTimeRequest translateTransferTimeReq(OrderFulfillmentModel model, Warehouse warehouse) {
        TransferTimeRequest request = new TransferTimeRequest();
        // 京东平台:京东平台订单号 非京东平台：eclp订单号（“ESL”开头）

        boolean isPOP = model.isPop();
        request.setOrderId(isPOP ? model.getChannel().getChannelOrderNo() : model.getOrderNo());
        request.setPlatformType(isPOP ? 1 : 2);

        request.setSoType(model.getOrderBusinessType().getCode());
        request.setControlType(this.isWarehouseAllocation(model) ? 2 : 1);

        request.setControlMode(OrderControlMode.SHUTDOWN_PLUS_AGING.getCode());
        request.setDict(NumberUtils.toInt(warehouse.getDistributionNo(), 0));
        request.setStoreId(NumberUtils.toInt(warehouse.getErpWarehouseNo(), 0));
        request.setSellerNo(model.getCustomer().getSellerNo());
        request.setSellerName(model.getCustomer().getSellerName());
        request.setDeptNo(model.getCustomer().getAccountNo());
        request.setSpId(Long.valueOf(model.getChannel().getChannelNo()));

        // 设置始发地四级地址
        if (warehouse.getWareProvinceId() != null) {
            request.setFromProvinceId(warehouse.getWareProvinceId().intValue());
        }
        if (warehouse.getWareCityId() != null) {
            request.setFromCityId(warehouse.getWareCityId().intValue());
        }
        if (warehouse.getWareCountyId() != null) {
            request.setFromCountyId(warehouse.getWareCountyId().intValue());
        }
        if (warehouse.getWareTownId() != null) {
            request.setFromTownId(warehouse.getWareTownId().intValue());
        }

        JdAddress address = Optional.ofNullable(model.getConsignee().getAddress()).orElse(JdAddress.builder().build());
        if (address.getProvinceNoGis() != null) {
            request.setProvinceId(Integer.parseInt(address.getProvinceNoGis()));
        }
        if (address.getCityNoGis() != null) {
            request.setCityId(Integer.parseInt(address.getCityNoGis()));
        }
        if (address.getCountyNoGis() != null) {
            request.setCountyId(Integer.parseInt(address.getCountyNoGis()));
        }
        if (address.getTownNoGis() != null) {
            request.setTownId(Integer.parseInt(address.getTownNoGis()));
        }
        request.setSkuQuantity(model.calculateTotalCargoQuantity().intValue());

        request.setContractId(model.getShipment().getBdOwnerNo());
        // 默认传1 代表零担
        request.setTransportMode(1);
        // 运输类型
        if (model.getShipment().getTransportationType() != null) {
            request.setTransportType(model.getShipment().getTransportationType().getPromiseCode());
        }
        request.setSellerExtendParam(getSellerExtendParam(model));

        return request;
    }

    private static Map<String, String> getSellerExtendParam(OrderFulfillmentModel model) {
        Map<String, String> sellerExtendParam = new HashMap<>();

        if (model.getFulfillment().getOrderPriority() != null) {
            sellerExtendParam.put("orderPriority", String.valueOf(model.getFulfillment().getOrderPriority()));
        }
        if (model.getShipment().getTransportationType() != null) {
            sellerExtendParam.put("transportType", String.valueOf(model.getShipment().getTransportationType().getPromiseCode()));
        }
        sellerExtendParam.put("soType", String.valueOf(model.getOrderBusinessType().getCode()));
        sellerExtendParam.put("spId", model.getChannel().getChannelNo());
        sellerExtendParam.put("transportMode", "1"); // 默认传1 代表零担
        return sellerExtendParam;
    }

    private boolean isWarehouseAllocation(OrderFulfillmentModel model) {
        return QING_LONG_DELIVERY.equals(model.getShipment().getShipperType().getSiteShipType())
                || THIRD_DELIVERY.equals(model.getShipment().getShipperType().getSiteShipType());
    }

}
