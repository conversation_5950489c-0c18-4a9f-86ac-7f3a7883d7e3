package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jd.csc.channel.intserv.PlatformEnum;
import com.jd.csc.channel.intserv.PlatformStandardService;
import com.jd.csc.channel.intserv.Request;
import com.jd.csc.channel.intserv.Response;
import com.jd.csc.channel.intserv.waybill.AddressInfo;
import com.jd.csc.channel.intserv.waybill.ContactInfo;
import com.jd.csc.channel.intserv.waybill.ExtInfo;
import com.jd.csc.channel.intserv.waybill.PackageInfo;
import com.jd.csc.channel.intserv.waybill.PersonnelInfo;
import com.jd.csc.channel.intserv.waybill.SkuInfo;
import com.jd.csc.channel.intserv.waybill.WaybillCodeRequest;
import com.jd.csc.channel.intserv.waybill.WaybillInfoVo;
import com.jd.csc.channel.intserv.waybill.WaybillPrintInfoRequest;
import com.jd.csc.channel.intserv.waybill.build.BuildWaybillCodeRequest;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.ProductBusinessLineEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.JdAddress;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ThirdPartPlatformCreateAcl;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  <a href="https://joyspace.jd.com/pages/2GCOtYMbAZqKQMuxgFaJ">外平台取号接口</a>
 */
@Slf4j
public abstract class AbstractPlatformStandardCreateServiceRpc implements ThirdPartPlatformCreateAcl {
    @Value("${jone.application.name}")
    private String appName;
    @Resource
    private PlatformStandardService platformStandardService;
    @Resource
    private ElectronicSheetConfigAcl electronicSheetConfigAcl;

    private final static String COUNTRY_NAME = "中国";

    @Override
    public WaybillCodeRequest buildWaybillCodeRequest(OrderFulfillmentModel model) {
        final ElectronicSheetInfo electronicSheetInfo = getElectronicSheetInfo(model);
        return getBuildWaybillCodeRequest(model)
                .basicsInfo(
                        model.getCustomer().getSellerNo(),
                        model.getCustomer().getAccountNo(),
                        getPlatformEnum(),
                        electronicSheetInfo.getAuthToken()
                )
                .orderInfo(
                        model.getOrderNo(),
                        model.getChannel().getChannelOrderNo(),
                        getPackageInfoList(model)
                )
                .logisticsCompanyInfo(
                        model.getShipment().getShipperNo(),
                        electronicSheetInfo.getExpressCompanyCode(),
                        ObjectUtils.nullIfNull(electronicSheetInfo.getCustomExtend(), ElectronicSheetInfo.CustomExtend::getNetSiteCode),
                        ObjectUtils.nullIfNull(electronicSheetInfo.getCustomExtend(), ElectronicSheetInfo.CustomExtend::getSettleAccount),
                        getExpressProduct(model))
                .templateInfo(
                        electronicSheetInfo.getCustomTemplateUrl()
                )
                .personnelInfo(
                        new PersonnelInfo(
                                new ContactInfo(
                                        electronicSheetInfo.getSenderName(),
                                        electronicSheetInfo.getSenderMobile(),
                                        electronicSheetInfo.getSenderMobile()
                                ),
                                new AddressInfo(
                                        COUNTRY_NAME,
                                        electronicSheetInfo.getAddrProvince(),
                                        electronicSheetInfo.getAddrCity(),
                                        electronicSheetInfo.getAddrCounty(),
                                        electronicSheetInfo.getAddrTown(),
                                        electronicSheetInfo.getAddrDetail()
                                )
                        ),
                        new PersonnelInfo(
                                new ContactInfo(
                                        model.getConsignee().getConsigneeNameEnc(),
                                        model.getConsignee().getConsigneeMobileEnc(),
                                        model.getConsignee().getConsigneePhoneEnc()
                                ),
                                new AddressInfo(
                                        model.getConsignee().getOriginProvinceName() != null
                                                ? model.getConsignee().getOriginProvinceName()
                                                : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getProvinceName),
                                        model.getConsignee().getOriginCityName() != null
                                                ? model.getConsignee().getOriginCityName()
                                                : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getCityName),
                                        model.getConsignee().getOriginCountyName() != null
                                                ? model.getConsignee().getOriginCountyName()
                                                : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getCountyName),
                                        model.getConsignee().getOriginTownName() != null
                                                ? model.getConsignee().getOriginTownName()
                                                : ObjectUtils.nullIfNull(model.getConsignee().getAddress(), JdAddress::getTownName),
                                        model.getConsignee().getConsigneeAddrEnc()
                                )
                        )
                )
                .build();
    }

    /**
     * 获取快件产品类别
     */
    private String getExpressProduct(OrderFulfillmentModel model) {
        if (ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())) {
            List<Product> products = model.getProducts().getProducts();
            return products.stream()
                    .filter(product -> ProductTypeEnum.MAIN_PRODUCT.equals(product.getType()))
                    .filter(product -> ProductBusinessLineEnum.DISTRIBUTION.equals(product.getBusinessLine())
                            || ProductBusinessLineEnum.TRANSPORT.equals(product.getBusinessLine()))
                    .map(Product::getNo)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    abstract BuildWaybillCodeRequest getBuildWaybillCodeRequest(OrderFulfillmentModel model);

    /**
     * 查询电子面单配置
     */
    public ElectronicSheetInfo getElectronicSheetInfo(OrderFulfillmentModel model) {
        ElectronicSheetInfo electronicSheetInfo = electronicSheetConfigAcl.queryConfigInfo(electronicSheetConfigAcl.buildElectronicSheetRequest(model));
        if (Objects.isNull(electronicSheetInfo)) {
            log.error("[{}]获取到该商家平台电子面单配置信息为空,orderNo:{}", model.getShipment().getDeliveryPerformanceChannel().getDesc(), model.getOrderNo());
            throw new PauseException(getPauseReasonEnum(PauseReasonTypeEnum.PRINT))
                    .withCustom("获取到该商家平台电子面单配置信息为空，请至商家工作台【平台面单配置】维护");
        }
        if (StringUtils.isBlank(electronicSheetInfo.getAuthToken())) {
            log.error("[{}}]获取到该商家平台电子面单配置授权码为空,orderNo:{}", model.getShipment().getDeliveryPerformanceChannel().getDesc(), model.getOrderNo());
            throw new PauseException(getPauseReasonEnum(PauseReasonTypeEnum.TOKEN))
                    .withCustom("获取到该商家平台电子面单配置授权码为空，请至商家工作台【平台面单配置】维护");
        }
        return electronicSheetInfo;
    }

    abstract PlatformEnum getPlatformEnum();

    @Override
    public WaybillInfoVo getWaybillCode(WaybillCodeRequest waybillCodeRequest, String orderNo) {
        final PauseReasonEnum pauseReasonEnum = getPauseReasonEnum(PauseReasonTypeEnum.WAYBILL);
        final Response<WaybillInfoVo> waybillInfoVoResponse;
        try {
            waybillInfoVoResponse = platformStandardService.getWaybillCode(new Request<>(appName, waybillCodeRequest));
        } catch (Exception e) {
            log.error("[{}]外平台取号接口调用异常:单号:{}", getPlatformEnum().getPlatformName(), orderNo, e);
            throw new InfrastructureException(UnifiedErrorSpec.ACL.CALL_EXCEPTION, e)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取号接口异常: " + e.getMessage());
        }
        if (waybillInfoVoResponse == null) {
            log.error("[{}]调外平台取号接口返回空，单号{}", getPlatformEnum().getPlatformName(), orderNo);
            throw new PauseException(pauseReasonEnum)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取号接口返回空");
        }
        if (!waybillInfoVoResponse.isSuccess()) {
            log.error("[{}]调外平台取号接口返回失败，单号{},异常码:{}, 异常信息:{}", getPlatformEnum().getPlatformName(), orderNo, waybillInfoVoResponse.getCode(), waybillInfoVoResponse.getMessage());
            throw new PauseException(pauseReasonEnum)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取号接口返回失败,接口返回信息: " + JsonUtil.toJsonSafe(waybillInfoVoResponse));
        }
        if (Objects.isNull(waybillInfoVoResponse.getData())) {
            log.error("[{}]调外平台取号接口返回数据为空，单号{},返回数据为空", getPlatformEnum().getPlatformName(), orderNo);
            throw new PauseException(pauseReasonEnum)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取号接口返回数据为空,,接口返回信息:" + JsonUtil.toJsonSafe(waybillInfoVoResponse));
        }
        checkWaybillCode(orderNo, waybillInfoVoResponse);
        checkShipperNo(orderNo, waybillInfoVoResponse);
        return waybillInfoVoResponse.getData();
    }

    @Override
    public WaybillInfoVo getWaybillPrintInfo(WaybillPrintInfoRequest waybillPrintInfoRequest, String orderNo) {
        final PauseReasonEnum pauseReasonEnum = getPauseReasonEnum(PauseReasonTypeEnum.PRINT);
        final Response<WaybillInfoVo> waybillInfoVoResponse;
        try {
            waybillInfoVoResponse = platformStandardService.getWaybillPrintInfo(new Request<>(appName, waybillPrintInfoRequest));
        } catch (Exception e) {
            log.error("[{}]外平台取打印信息接口调用异常:单号:{}", getPlatformEnum().getPlatformName(), orderNo, e);
            throw new InfrastructureException(UnifiedErrorSpec.ACL.CALL_EXCEPTION, e)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取打印信息接口异常: " + e.getMessage());
        }
        if (waybillInfoVoResponse == null) {
            log.error("[{}]调外平台取打印信息接口返回空，单号{}", getPlatformEnum().getPlatformName(), orderNo);
            throw new PauseException(pauseReasonEnum)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取打印信息接口返回空");
        }
        if (!waybillInfoVoResponse.isSuccess()) {
            log.error("[{}]调外平台取打印信息接口返回失败，单号{},异常码:{}, 异常信息:{}", getPlatformEnum().getPlatformName(), orderNo, waybillInfoVoResponse.getCode(), waybillInfoVoResponse.getMessage());
            throw new PauseException(pauseReasonEnum)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取打印信息接口返回失败,接口返回信息: " + JsonUtil.toJsonSafe(waybillInfoVoResponse));
        }
        if (Objects.isNull(waybillInfoVoResponse.getData())) {
            log.error("[{}]调外平台取打印信息接口返回数据为空，单号{},返回数据为空", getPlatformEnum().getPlatformName(), orderNo);
            throw new PauseException(pauseReasonEnum)
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "]调外平台取打印信息接口返回数据为空,,接口返回信息:" + JsonUtil.toJsonSafe(waybillInfoVoResponse));
        }
        checkWaybillPrintInfo(orderNo, waybillInfoVoResponse);
        return waybillInfoVoResponse.getData();
    }

    void checkShipperNo(String orderNo, Response<WaybillInfoVo> waybillInfoVoResponse) {
        // 得物是  订单没有承运商，需要调 得物后 得物返回。这里需要校验
        // 其他平台 都是 咱们带着 承运商 去取号的。不需要校验
    }

    void checkWaybillCode(String orderNo, Response<WaybillInfoVo> waybillInfoVoResponse) {
        if (StringUtils.isBlank(waybillInfoVoResponse.getData().getWaybillCode())) {
            log.error("[{}]调外平台返回运单号为空，单号：{}", getPlatformEnum().getPlatformName(), orderNo);
            throw new PauseException(getPauseReasonEnum(PauseReasonTypeEnum.WAYBILL))
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "}]调外平台返回运单号为空,接口返回信息:" + JsonUtil.toJsonSafe(waybillInfoVoResponse));
        }
    }

    abstract PauseReasonEnum getPauseReasonEnum(PauseReasonTypeEnum pauseReasonTypeEnum);

    @Override
    public WaybillPrintInfoRequest buildWaybillPrintInfoRequest(OrderFulfillmentModel model, String waybillCode) {
        final ElectronicSheetInfo electronicSheetInfo = getElectronicSheetInfo(model);
        WaybillPrintInfoRequest request = new WaybillPrintInfoRequest(
                model.getCustomer().getSellerNo(),
                model.getCustomer().getAccountNo(),
                getPlatformEnum(),
                electronicSheetInfo.getAuthToken());
        request.setSalesPlatformOrderNo(model.getChannel().getChannelOrderNo());
        request.setOrderCode(model.getOrderNo());
        request.setWaybillCode(waybillCode);
        request.setShipperNo(model.getShipment().getShipperNo());
        request.setExpressCompanyCode(electronicSheetInfo.getExpressCompanyCode());
        request.setExtraInfo(getExtInfo(model));
        return request;
    }

    protected ExtInfo getExtInfo(OrderFulfillmentModel model) {
        return null;
    }

    private void checkWaybillPrintInfo(String orderNo, Response<WaybillInfoVo> waybillInfoVoResponse) {
        if (StringUtils.isBlank(waybillInfoVoResponse.getData().getPrintData())) {
            log.error("[{}]调外平台返回打印信息为空，单号：{}", getPlatformEnum().getPlatformName(), orderNo);
            throw new PauseException(getPauseReasonEnum(PauseReasonTypeEnum.PRINT))
                    .withCustom("[" + getPlatformEnum().getPlatformName() + "}]调外平台返回打印信息为空,接口返回信息:" + JsonUtil.toJsonSafe(waybillInfoVoResponse));
        }
    }

    protected List<PackageInfo> getPackageInfoList(OrderFulfillmentModel model) {
        return Collections.singletonList(
                new PackageInfo(
                        CollectionUtils.emptyIfNull(model.getCargos())
                                .stream()
                                .map(cargo -> new SkuInfo(
                                        cargo.getName(),
                                        cargo.getQuantity().getValue().intValue()
                                ))
                                .collect(Collectors.toList())
                )
        );
    }
}
