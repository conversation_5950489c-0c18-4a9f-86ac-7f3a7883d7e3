package com.jdl.sc.ofw.out.horiz.infra.util;


import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.rpc.EclpDictAcl;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TiktokJpUtils {

    @Resource
    private EclpDictAcl eclpDictAcl;

    @Resource
    private SwitchKeyUtils switchKeyUtils;
    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    public boolean isTiktokJpPrintComponent(String orderNo, String deptNo) {
        boolean isTiktokJpPrintComponent = eclpDictAcl.isTiktokJpPrintComponent(deptNo);
        log.info("事业部:{}, 抖音京配订单:{}, 业务开关是否打开:{}", deptNo, orderNo, isTiktokJpPrintComponent);
        return isTiktokJpPrintComponent;
    }

    public boolean supportDyJpMultiPackage(String orderNo, String deptNo, String businessUnit) {
        boolean supportDyJpPackageFlag = switchKeyUtils.supportDyJpPackage(businessUnit);
        if (!supportDyJpPackageFlag) {
            return false;
        }
        boolean isTiktokJpPrintComponent = eclpDictAcl.isTiktokJpPrintComponent(deptNo);
        log.info("业务身份:{}, 事业部:{}, 抖音京配订单:{}, 业务身份开关是否打开：{}，事业部开关是否打开:{}", businessUnit, deptNo, orderNo, supportDyJpPackageFlag, isTiktokJpPrintComponent);
        return isTiktokJpPrintComponent;
    }

    /**
     * 是否抖音即时零售
     */
    public boolean isJpInstant(OrderFulfillmentModel model) {
        String tiktokInstantPrintDeptNo = switchKeyCommonUtils.getTiktokInstantPrintDeptNo();
        boolean flag = StringUtils.contains(tiktokInstantPrintDeptNo, "ALL")
                || StringUtils.contains(tiktokInstantPrintDeptNo, model.getCustomer().getAccountNo());
        if (!flag) {
            log.info("{}, 抖音即时零售，开关状态：false", model.getCustomer().getAccountNo());
            return false;
        }

        flag = DeliveryPerformanceChannelEnum.TIK_TOK.equals(model.getShipment().getDeliveryPerformanceChannel())
                && ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())
                && PlatformConstants.CHANNEL_NO_DY_INSTANT.equals(model.getChannel().getChannelNo());
        log.info("{}, 抖音即时零售，开关状态：true, 业务验证结果：{}", model.getCustomer().getAccountNo(), flag);
        return flag;
    }
}
