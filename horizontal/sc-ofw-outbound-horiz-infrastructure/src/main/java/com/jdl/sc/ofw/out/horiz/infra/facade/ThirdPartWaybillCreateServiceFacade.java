package com.jdl.sc.ofw.out.horiz.infra.facade;

import com.jd.csc.channel.intserv.waybill.WaybillInfoVo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ThirdPartPlatformCreateAcl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ThirdPartWaybillCreateServiceFacade {
    private final List<ThirdPartPlatformCreateAcl> thirdPartPlatformCreateAclList;

    /**
     * 外平台取号:包含承运商、运单号、打印信息
     */
    public WaybillInfoVo getWaybill(OrderFulfillmentModel model) {
        for (ThirdPartPlatformCreateAcl thirdPartPlatformCreateAcl : thirdPartPlatformCreateAclList) {
            if (thirdPartPlatformCreateAcl.support(model.getShipment().getDeliveryPerformanceChannel())) {
                return thirdPartPlatformCreateAcl.getWaybillCode(
                        thirdPartPlatformCreateAcl.buildWaybillCodeRequest(model),
                        model.getOrderNo()
                );
            }
        }
        throw new UnsupportedOperationException("外平台取号::尚未支持" + model.getShipment().getDeliveryPerformanceChannel().getDesc());
    }

    public WaybillInfoVo getWaybillPrintInfo(OrderFulfillmentModel model, String waybillCode) {
        for (ThirdPartPlatformCreateAcl thirdPartPlatformCreateAcl : thirdPartPlatformCreateAclList) {
            if (thirdPartPlatformCreateAcl.support(model.getShipment().getDeliveryPerformanceChannel())) {
                return thirdPartPlatformCreateAcl.getWaybillPrintInfo(
                        thirdPartPlatformCreateAcl.buildWaybillPrintInfoRequest(model, waybillCode),
                        model.getOrderNo()
                );
            }
        }
        throw new UnsupportedOperationException("外平台取打印信息::尚未支持" + model.getShipment().getDeliveryPerformanceChannel().getDesc());
    }

}
