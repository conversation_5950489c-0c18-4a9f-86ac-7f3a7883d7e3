package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.fce.dos.service.contract.OrderMarkingService;
import com.jd.fce.dos.service.domain.OrderMarkingNewResponse;
import com.jd.fce.dos.service.domain.OrderMarkingRequest;
import com.jd.fce.dos.service.domain.SeaShippingRequest;
import com.jd.fce.dos.service.domain.SeaShippingResponse;
import com.jd.fce.dos.service.domain.eclp.OrderMarkingEclpDeliveryRequest;
import com.jd.fce.dos.service.domain.eclp.OrderMarkingEclpDeliveryResponse;
import com.jd.promise.service.contract.PromiseKaService;
import com.jd.promise.service.domain.PromiseControlResponse;
import com.jd.promise.service.domain.TransferTimeRequest;
import com.jd.promise.service.domain.TransferTimeResponse;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeRequest;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeResponse;
import com.jd.promise.service.domain.WarehouseJitTimeRequest;
import com.jd.promise.service.domain.WarehouseJitTimeResponse;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.BusinessDomainException;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.JdAddress;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.domain.rpc.WarehouseServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.enums.OrderControlMode;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ext.PromiseTransportationTypeExt;
import com.jdl.sc.ofw.out.horiz.infra.rpc.translator.PromiseTranslator;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class PromiseRpc implements PromiseAcl {

    @Resource
    private OrderMarkingService orderMarkingService;

    @Resource
    private PromiseKaService promiseKaService;

    @Resource
    private PromiseTranslator promiseTranslator;

    @Resource
    private WarehouseServiceAcl warehouseServiceAcl;

    @Resource
    private PromiseTransportationTypeExt promiseTransportationTypeExt;


    @Override
    public WarehouseAndDeliveryTimeResponse getPromise(WarehouseAndDeliveryTimeRequest request) {
        try {
            return promiseKaService.getWarehouseAndDeliveryTime(request);
        }catch (Exception e) {
            log.warn("Promise接口调用异常，默认返回空", e);
            return null;
        }
    }

    @Override
    public OrderMarkingNewResponse getPromiseNew(OrderFulfillmentModel model) {
        Warehouse warehouse = warehouseServiceAcl.getWarehouseById(model.getWarehouse().getWarehouseId());

        OrderMarkingRequest orderMarkingRequest = promiseTranslator.modelToCityPromiseRequest(model, warehouse);
        try {
            OrderMarkingNewResponse orderMarkingNewResponse = orderMarkingService.orderMarkingAndCodUpdateServiceN(orderMarkingRequest);
            if (orderMarkingNewResponse == null || orderMarkingNewResponse.getResultCode() < 1) {
                log.error("调用Promise获取订单预计妥投时间返回值异常");
                return null;
            }
            return orderMarkingNewResponse;
        } catch (Exception e) {
            log.error("调用Promise获取订单预计妥投时间异常", e);
            return null;
        }
    }

    @Override
    public boolean forbidden(OrderFulfillmentModel model) {
        Warehouse warehouse = warehouseServiceAcl.getWarehouseById(model.getWarehouse().getWarehouseId());
        OrderMarkingEclpDeliveryRequest request = promiseTranslator.modelToForbiddenRequest(model, warehouse);
        try {
            OrderMarkingEclpDeliveryResponse response = orderMarkingService.orderMarkingServiceForEclpDelivery(request);
            return response != null && response.getResultCode() == 1 && !response.isSupportDelivery();
        } catch (Exception e) {
            log.warn("Promise疫情暂停接口调用异常，默认不暂停", e);
            return false;
        }
    }

    @Override
    public SeaShippingResponse isSupportSeaShipping(JdAddress jdAddress) {

        SeaShippingRequest seaShippingRequest = new SeaShippingRequest();
        seaShippingRequest.setProvinceId(ObjectUtils.defaultIfNull(jdAddress.getProvinceNoGis(), Integer::valueOf, 0));
        seaShippingRequest.setCityId(ObjectUtils.defaultIfNull(jdAddress.getCityNoGis(), Integer::valueOf, 0));
        seaShippingRequest.setCountyId(ObjectUtils.defaultIfNull(jdAddress.getCountyNoGis(), Integer::valueOf, 0));
        seaShippingRequest.setTownId(ObjectUtils.defaultIfNull(jdAddress.getTownNoGis(), Integer::valueOf, 0));
        seaShippingRequest.setTid(System.currentTimeMillis());
        seaShippingRequest.setSource("eclp-so");
        final SeaShippingResponse response;
        try {
             response = orderMarkingService.isSupportSeaShipping(seaShippingRequest);
        }catch (Exception e){
            log.error("调用Promise获取是否支持海运异常", e);
            throw new RuntimeException("调用Promise获取是否支持海运异常");
        }

        if (response == null){
            log.error("调用Promise获取是否支持海运,返回结果异常 response:{}", JsonUtil.toJsonSafe(response));
            throw new RuntimeException("调用Promise获取是否支持海运,返回结果异常");
        }
        return response;
    }

    @Override
    public WarehouseAndDeliveryTimeResponse getPromiseColdChain(OrderFulfillmentModel model) {
        try {
            Warehouse warehouse = warehouseServiceAcl.getWarehouseById(model.getWarehouse().getWarehouseId());
            TransportationTypeEnum transportationType = promiseTransportationTypeExt.populateTransportationType(model);
            WarehouseAndDeliveryTimeRequest request = promiseTranslator.modelToRequest(model, warehouse, transportationType);
            boolean orderAviationLine = request.isOrderAviationLine();
            WarehouseAndDeliveryTimeResponse response = promiseKaService.getWarehouseAndDeliveryTime(request);
            processColdChain(model, response, orderAviationLine);
            return response;
        } catch (PauseException e) {
            log.error(e.getPauseReason().getDesc() + e.getMessage());
            throw new PauseException(e.getPauseReason())
                    .withCustom(e.getMessage());
        } catch (Exception e) {
            log.warn("Promise接口调用异常，默认返回空", e);
            throw new InfrastructureException(UnifiedErrorSpec.ACL.CALL_EXCEPTION, e);
        }
    }

    private void processColdChain(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response, boolean orderAviationLine) {
        if (response == null || response.getResultCode() != 1) {
            return;
        }
        if (!model.isPop() && !model.isIsv()) {
            return;
        }
        if (!model.getProducts().isEnrolled(ProductEnum.LL_SD)) {
            return;
        }
        if (orderAviationLine && response.getTransportType() != 1) {
            //如果是生鲜速达产品，路由配置是航空，但是实际上返回的是陆运，这里需要卡单
            log.error("单号 {} 记录生鲜速达订单航空校验异常开始 promise返回非航空", model.getOrderNo());
            throw new PauseException(PauseReasonEnum.SXSD_AIR_EXCEPTION)
                    .withCustom("单号：" + model.getOrderNo() + "路由返回航空，但是promise返回的是非航空导致");
        }
    }

    /**
     * 获取下传时间
     */
    public TransferTimeResponse getTransferTime(TransferTimeRequest transferTimeRequest) {
        TransferTimeResponse transferResponse;
        try {
            transferResponse = promiseKaService.getTransferTime(transferTimeRequest);
        } catch (Exception e) {
            log.warn("获取下传时间异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.ACL.CALL_EXCEPTION, e);
        }
        return transferResponse;
    }

}
