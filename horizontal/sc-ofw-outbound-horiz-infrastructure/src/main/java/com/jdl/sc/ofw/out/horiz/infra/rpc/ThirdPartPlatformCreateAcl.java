package com.jdl.sc.ofw.out.horiz.infra.rpc;

import com.jd.csc.channel.intserv.waybill.WaybillCodeRequest;
import com.jd.csc.channel.intserv.waybill.WaybillInfoVo;
import com.jd.csc.channel.intserv.waybill.WaybillPrintInfoRequest;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;

public interface ThirdPartPlatformCreateAcl {
    boolean support(DeliveryPerformanceChannelEnum deliveryPerformanceChannelEnum);

    WaybillCodeRequest buildWaybillCodeRequest(OrderFulfillmentModel model);

    WaybillInfoVo getWaybillCode(WaybillCodeRequest waybillCodeRequest, String orderNo);

    default WaybillPrintInfoRequest buildWaybillPrintInfoRequest(OrderFulfillmentModel model, String waybillCode) {
        return null;
    }

    default WaybillInfoVo getWaybillPrintInfo(WaybillPrintInfoRequest waybillPrintInfoRequest, String orderNo) {
        return null;
    }
}
