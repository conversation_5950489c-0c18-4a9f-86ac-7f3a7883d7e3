package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jdl.promise.external.front.store.contract.promise.api.PromiseService;
import com.jdl.promise.external.front.store.contract.promise.dto.PromiseData;
import com.jdl.promise.external.front.store.contract.promise.param.PromiseRequest;
import com.jdl.promise.external.front.store.contract.resp.RpcResponse;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.horiz.infra.rpc.PromiseServiceAcl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service
public class PromiseServiceRpc implements PromiseServiceAcl {

    @Resource
    private PromiseService promiseService;

    private static final Integer SUCCESS_CODE = 200;

    @Override
    public RpcResponse<PromiseData> getPromiseInfo(PromiseRequest request) {
        try {
            RpcResponse<PromiseData> response = promiseService.getPromiseInfo(request);
            if (response == null || !SUCCESS_CODE.equals(response.getCode()) || Objects.isNull(response.getData())) {
                log.warn("调用Promise获取外单前置仓时效异常, {}", JsonUtil.toJsonSafe(response));
                return null;
            }
            return response;
        } catch (Exception e) {
            log.error("调用Promise获取外单前置仓时效异常", e);
            return null;
        }
    }
}
