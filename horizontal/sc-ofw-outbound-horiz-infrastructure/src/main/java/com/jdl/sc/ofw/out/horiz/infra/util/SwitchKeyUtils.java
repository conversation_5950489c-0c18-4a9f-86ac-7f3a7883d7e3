package com.jdl.sc.ofw.out.horiz.infra.util;

import com.jd.laf.config.spring.annotation.LafUcc;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.weblab.api.WaybillWebLab;
import com.jdl.sc.weblab.api.dto.LaunchParam;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SwitchKeyUtils {
    // 三方运单切量开关，计划下线时间 618解封后
    @Value("${third.delivery.dept.switch.key:-}")
    private String thirdDeliveryDeptSwitchKey;

    /**
     * pipe灰度分组事业部&业务身份
     */
    @Value("${ducc.pipe.gray.switch.account.and.unit:-}")
    @Getter
    private String pipeGrayAccountNoAndUnit;

    @Value("${address.check.switch.alpha:false}")
    @Getter
    private boolean addressCheckSwitchToAlpha;


    @Value("${tiktok.use.warehouse:false}")
    private boolean tiktokUseWarehouse;
    @Value("${modify.consignee.address.need.check.all.empty:false}")
    private boolean modifyConsigneeAddressNeedCheckAllEmpty;

    /**
     * 得物加密随单指定承运商是自采三方-开关
     * true：执行外平台
     * false：执行EDI
     */
    @Value("${ducc.dewu.zi.csf.platform.switch:false}")
    @Getter
    private boolean dewuZiCsfPlatform;

    /**
     * 判断不允许下发：当前时间延迟X小时后，如果仍然小于计划下发时间，则hold单
     * false：按照36小时计算
     * true：按照0小时计算
     */
    @Value("${ducc.cold.chain.promise.hold.switch:false}")
    @Getter
    private boolean coldChainPromiseHold;

    /**
     * 拼多多大件回传订单运单号开关，true=回传 false：不回传
     */
    @Value("${ducc.las.pdd.callback.waybill.switch:true}")
    @Getter
    private boolean lasPddCallbackWaybill;

    /**
     * 菜鸟拼多多支持京配
     */
    @Value("${cn.pdd.support.jp:false}")
    private String cnPddSupportJp;

    @Value("${ducc.zy.order.switch.key:false}")
    private boolean zyOrder;
    /**
     * TC产品分单上线开关
     */
    @Value("${ducc.tc.product.split.dept:-}")
    @Getter
    private String tcProductSplitDept;

    /**
     * 供应链附加费支持京配转三方上线开关
     */
    @Value("${ducc.attach.fee.jpzsf.dept:-}")
    private String attachFeeJpzsfDept;

    /**
     * 得物虚拟号事业部白名单
     */
    @Value("${ducc.dewu.virtual.mobile.deptNo:-}")
    private String dewuVirtualMobileDeptNo;

    /**
     * 得物京配打印组件事业部白名单
     */
    @Value("${ducc.dewu.jdps.print.deptNo:-}")
    private String dewuJDPSPrintDeptNo;

    @Resource
    private WaybillWebLab waybillWebLab;

    /**
     * 招行合作开关
     */
    @Value("${ducc.order.transfer.detpNos:-}")
    private String orderTransferDetpNos;

    /**
     * 加密场景 获取解密信息新流程 切量KEY
     */
    public static final String ENCRYPT_PROCESS_BY_MQ_DECRYPT = "encrypt_process_by_mq_decrypt";


    @Value("${ducc.cainiao.jp.black.business.unit.switch.key:}")
    private String cainiaoJpBlackListSwitchKey;

    /**
     * 抖音京配支持多包裹回调，业务身份开关
     */
    @Value("${ducc.dyjp.multi.package.businessUnit:-}")
    private String dyJpPackageBusinessUnits;


    /**
     * 签单返还不限制承运商，上线开关
     */
    @Getter
    @Value("${ducc.signBillType.unlimited.switch:false}")
    private boolean signBillTypeUnlimited;

    @Getter
    @Value("${ducc.tiktok.transit.warehouse.key:false}")
    private boolean isTiktokTransitWarehouseOpen;

    /**
     * 菜鸟马甲，事业部开关
     */
    @Value("${ducc.cn.mj.deptNo:-}")
    private String cnMjDeptNo;

    /**
     * 菜鸟马甲，使用真实销售平台编码来获取马甲公司的事业部
     */
    @Value("${ducc.cn.mj.use.real.channelNo.deptNo:-}")
    private String cnMjUseRealChannelNoDeptNo;

    /**
     *  菜鸟马甲，使用oaid赋值新流程:
     *  奇门来源订单（ISV0020000000640）从ediRemark中取oaid，其余订单从CustomField中取oaid
     */
    @Value("${ducc.cn.mj.use.oaid.new.flow:-}")
    private String cnMjUseOaidNewFlowDeptNo;

    /**
     * 抖音代发事业部白名单
     */
    @Value("${ducc.tiktok.df.deptNo:-}")
    private String tikTokDfDeptNo;

    @Getter
    @Value("${ducc.switch.new.update.plaintext:false}")
    private boolean switchNewUpdatePlaintext;

    @Value("${ducc.mj.not.qimen.caid.switch:false}")
    private boolean mjNotQimenCaid;

    @Value("${ducc.not.qimen.caid.switch:false}")
    private boolean notQimenCaid;

    /**
     * 产品分单支持合同物流事业部开关
     */
    @Value("${ducc.contract.logistics.dept:-}")
    private String contractLogisticsDept;
    /**
     * 产品分单使用产品中心校验异常信息，事业部开关
     */
    @Value("${ducc.product.split.use.err.msg.dept:-}")
    private String productSplitUseErrMsgDept;

    /**
     * 冷链使用重货上楼,事业部开关
     */
    @Value("${ducc.cold.chain.use.heavy.goods.dept:-}")
    private String coldChainUseHeavyGoodsDept;


    /**
     * 冷链支持特惠小件和云配的事业部
     */
    @Value("${ducc.cc.support.other.product:-}")
    private String ccSupportOtherProductDept;


    /**
     * 菜鸟使用电子面单获取的承运商Map
     * 例:{ "cn_jdl_sc-isv": "ALL", "cn_jdl_ka-medium": "EBU1,EBU2"}
     */
    private Map<String, String> cnUseEsAuthCodeMap;

    /**
     * 冷链时效升级新流程的事业部
     */
    @Value("${ducc.cold.chain.promise.update.new.flow.deptNo:-}")
    private String coldChainPromiseUpdateNewFlow;
    /**
     * 抖音使用明文的事业部Map
     * 例:{ "cn_jdl_sc-isv": "ALL", "cn_jdl_ka-medium": "EBU1,EBU2"}
     */
    private Map<String, String> tikTokUsePlainTextDeptMap;
    /**
     * 视频号京配持久化model事业部
     */
    @Value("${ducc.video.store.persist.model.deptNo:-}")
    private String videoStorePersistModelDeptNo;

    /**
     * 冷链需要回传温层,事业部开关
     */
    @Value("${ducc.cold.chain.use.warmLayer.dept:-}")
    private String coldChainUseWarmLayerDept;

    /**
     * 抖音供销,事业部开关
     */
    @Value("${ducc.tiktok.supply.distribution.dept:-}")
    private String tiktokSupplyDistributionDept;

    @Value("${ducc.xhs.shipMode.businessUnit.blacklist:ALL}")
    private String xhsShipModeBusinessUnitBlacklist;

    @Value("${ducc.xhs.n.package.deptNo:-}")
    private String xhsNPackageDept;

    /**
     * 冷链使用送货入仓,事业部开关
     */
    @Value("${ducc.cold.chain.use.delivery.into.warehouse:-}")
    private String coldChainUseDeliveryIntoWarehouseDept;

    @Value("${ducc.promise.delivery.time.businessUnit:-}")
    private String promiseDeliveryTimeBusinessUnit;
    @Value("${ducc.cancel.pipe.wms.deptNo:-}")
    private String cancelPipeWmsDeptNo;
    /**
     * 分单接口入参增加订单金额
     */
    @Value("${ducc.product.split.order.amount.deptNo:-}")
    private String productSplitOrderAmountDeptNo;

    /**
     * 使用真实业务策略调产品中心校验
     */
    @Value("${ducc.use.real.business.strategy.deptNo:-}")
    private String useRealBusinessStrategyCheckProductDeptNo;

    /** 合同物流支持clps库房 */
    @Value("${ducc.contract.logistics.clps.warehouse.deptNo:-}")
    private String contractLogisticsClpsWarehouseDeptNo;

    /**
     * 分单接口入参使用预占类型的事业部
     */
    @Value("${ducc.product.split.occupy.type.deptNo:-}")
    private String productSplitOccupyTypeDeptNo;

    /**
     * 国际FOP-上线开关
     */
    @Getter
    @Value("${ducc.i18n.fop.switch:false}")
    private boolean i18nFopSwitchOpen;

    /**
     * 加密流程 MQ解密消息 单号校验逻辑开关
     */
    @Getter
    @Value("${ducc.decrypt.message.valid.order.no.switch:false}")
    private boolean isDecryptMessageValidOrderNoSwitch;



    @Getter
    @Value("${ducc.re.decision.open:false}")
    private boolean reDecisionOpen;

    @Value("${ducc.reuse.add.condition.deptNo:-}")
    private String reuseAddConditionDeptNo;

    /**
     * 下线零跑产品
     */
    @Value("${ducc.down.leapmotor.product.switch:false}")
    private boolean downLeapmotorProduct;

    /**
     * 小红书京配冷链温层根据取号产品判断
     */
    @Value("${ducc.xhs.cold.fresh.deptNo:-}")
    private String xhsColdFreshDeptNo;

    /**
     * 合同物流时效控单，上线开关
     */
    @Value("${ducc.contract.promise.pause.deptNo:-}")
    private String contractPromisePauseDeptNo;

    /**
     * 快手明文流程 业务身份-事业部开关配置
     * key: 业务身份
     * value: 事业部
     */
    private Map<String, String> kwaiPlainTextBusinessUnitMap;


    /**
     * 视频号加密 子母单控量 业务身份事业部
     */
    private HashMap<String, String> videoStoreParentOrderBusinessUnitDeptMap = new HashMap<>();

    @Value("${ducc.zi.cai.jd.no.enc.deptNo:-}")
    private String ziCaiJdNoEncDeptNo;

    @Value("${ducc.video.store.child.and.parent.with.waybill.deptNo:-}")
    private String videoStoreChildAndParentWithWaybillDept;

    /**
     * 修改承运商-切量判断逻辑降级开关
     */
    @Getter
    @Value("${ducc.modify.shipper.cutting:false}")
    private boolean modifyShipperCutting;

    /**
     * 大件勘测服务单上线开关
     */
    @Value("${ducc.las.tcl.survey.deptNo:-}")
    private String lasTclSurveyDeptNo;

    /**
     * 仓运交接BPromise时效控量配置开关
     */
    @Value("${ducc.bpromise.deptNo:-}")
    private String bpromiseDeptNo;

    public boolean isThirdDeliveryDeptSwitch(String deptNo){
        boolean isThirdDeliveryDept = StringUtils.contains(thirdDeliveryDeptSwitchKey, "ALL")
                || StringUtils.contains(thirdDeliveryDeptSwitchKey, deptNo);
        log.info("{},isThirdDeliveryDept is:{}" ,deptNo ,isThirdDeliveryDept);
        return isThirdDeliveryDept;
    }

    public boolean containsTcProductSplitDept(String deptNo) {
        return StringUtils.contains(tcProductSplitDept, "ALL")
                || StringUtils.contains(tcProductSplitDept, deptNo);
    }

    public boolean containsAttachFeeJpzsfDept(String deptNo) {
        return StringUtils.contains(attachFeeJpzsfDept, "ALL")
                || StringUtils.contains(attachFeeJpzsfDept, deptNo);
    }


    public boolean isTiktokUseWarehouse(){
        log.info("抖音使用库房信息,开关状态：{}", tiktokUseWarehouse);
        return tiktokUseWarehouse;
    }
    public boolean isModifyConsigneeAddressNeedCheckAllEmpty(){
        log.info("修改收件人地址时需要校验所有信息非空,开关状态：{}", modifyConsigneeAddressNeedCheckAllEmpty);
        return modifyConsigneeAddressNeedCheckAllEmpty;
    }

    public boolean isCnPddSupportJp(String deptNo){
        return StringUtils.contains(cnPddSupportJp, "ALL")
                || StringUtils.contains(cnPddSupportJp, deptNo);
    }

    public boolean isZyOrder(){
        log.info("是否执行众邮87、88位打标逻辑：{} ",zyOrder);
        return zyOrder;
    }
    /**
     * 判断得物京配打印组件是否包含指定部门编号
     *
     * @param deptNo 部门编号
     * @return ALL或者包含事业部，则返回true；否则返回false
     */
    public boolean dewuJDPSPrintDeptNoContains(String deptNo) {
        boolean isDewuJDPSPrintDept =  StringUtils.contains(dewuJDPSPrintDeptNo, "ALL")
                || StringUtils.contains(dewuJDPSPrintDeptNo, deptNo);
        return isDewuJDPSPrintDept;
    }

    /**
     * 判断得物虚拟号是否包含指定部门编号
     *
     * @param deptNo 部门编号
     * @return ALL或者包含事业部，则返回true；否则返回false
     */
    public boolean dewuVirtualMobileDeptNoContains(String deptNo) {
        boolean isDewuVirtualMobileDept =  StringUtils.contains(dewuVirtualMobileDeptNo, "ALL")
                || StringUtils.contains(dewuVirtualMobileDeptNo, deptNo);
        return isDewuVirtualMobileDept;
    }

    /**
     * 是否 切换到 JMQ 同步明文信息 流程
     * @param model
     * @return
     */
    public boolean isJmqDecrypt(OrderFulfillmentModel model){
        String businessUnit = model.getBatrixInfo().getBusinessUnit();
        String accountNo = model.getCustomer().getAccountNo();
        LaunchParam launchParam = LaunchParam.builder()
                .webLabId(ENCRYPT_PROCESS_BY_MQ_DECRYPT)
                .params4(Collections.singletonList(businessUnit))
                .params5(Collections.singletonList(model.getShipment().getDeliveryPerformanceChannel() == null ? "0" : model.getShipment().getDeliveryPerformanceChannel().getCode()))
                .accountNo(accountNo)
                .productNos(model.getProducts().getProducts().stream()
                        .filter(product -> ProductTypeEnum.MAIN_PRODUCT.equals(product.getType()))
                        .map(Product::getNo)
                        .collect(Collectors.toList()))
                .build();
        boolean isShut = waybillWebLab.isLaunchedByWebLabId(launchParam);
        log.info("JMQ同步明文信息 切量, 事业部:{} 业务身份:{}, isShut:{} orderNo:{}", accountNo, businessUnit, isShut, model.getOrderNo());
        return isShut;
    }

    /**
     * 菜鸟京配-业务身份黑名单
     */
    public boolean isInCainiaoJpBlackList(String businessUnit) {
        if (StringUtils.isEmpty(cainiaoJpBlackListSwitchKey)) {
            return false;
        }
        return StringUtils.contains(cainiaoJpBlackListSwitchKey, businessUnit);
    }

    public boolean isOrderTransfer(String deptNo){
        return StringUtils.contains(orderTransferDetpNos, "ALL")
                || StringUtils.contains(orderTransferDetpNos, deptNo);
    }

    /**
     * 抖音京配支持多包裹回调，业务身份开关
     */
    public boolean supportDyJpPackage(String businessUnit) {
        boolean flag = StringUtils.contains(dyJpPackageBusinessUnits, "ALL")
                || StringUtils.contains(dyJpPackageBusinessUnits, businessUnit);
        log.info("抖音京配支持多包裹回调, 业务身份:{}, 开关状态:{}", businessUnit, flag);
        return flag;
    }
    @LafUcc
    @Value("${ducc.cn.use.es.authCode:[]}")
    public void setCnUseEsAuthCodeMap(String cnUseEsAuthCode) {
        log.info("菜鸟使用电子面单获取承运商,ducc配置为:{}", cnUseEsAuthCode);
        Map<String, String> usedMap = null;
        if (StringUtils.isNotBlank(cnUseEsAuthCode)) {
            try {
                usedMap = JsonUtil.parse(cnUseEsAuthCode, HashMap.class);
            } catch (Exception e) {
                log.warn("菜鸟使用电子面单获取承运商,DUCC配置反序列化异常:{}", cnUseEsAuthCode);
            }
            if (MapUtils.isNotEmpty(usedMap)) {
                cnUseEsAuthCodeMap = usedMap;
            }
        } else {
            log.info("菜鸟使用电子面单获取承运商,DUCC配置为空");
        }
    }

    public boolean isCnUseEsAuthCode(String businessUnit, String accountNo) {
        boolean flag = MapUtils.isNotEmpty(cnUseEsAuthCodeMap)
                && cnUseEsAuthCodeMap.containsKey(businessUnit)
                && (cnUseEsAuthCodeMap.get(businessUnit).contains("ALL") || cnUseEsAuthCodeMap.get(businessUnit).contains(accountNo));
        log.info("菜鸟使用电子面单获取承运商,业务身份:{},事业部:{},开关状态:{}", businessUnit, accountNo, flag);
        return flag;
    }

    public boolean isCnMjDept(String deptNo) {
        boolean flag = StringUtils.contains(cnMjDeptNo, "ALL")
                || StringUtils.contains(cnMjDeptNo, deptNo);
        log.info("{}, 菜鸟马甲事业部电子面单特殊赋值，开关开启状态:{}", deptNo, flag);
        return flag;
    }
    public boolean isCnMjUseRealChannelNoDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(cnMjUseRealChannelNoDeptNo, "ALL")
                || StringUtils.contains(cnMjUseRealChannelNoDeptNo, deptNo);
        log.info("{}, 菜鸟马甲，使用真实销售平台编码来获取马甲公司的事业部，开关开启状态:{}", deptNo, flag);
        return flag;
    }
    public boolean isCnMjUseOaidNewFlowDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(cnMjUseOaidNewFlowDeptNo, "ALL")
                || StringUtils.contains(cnMjUseOaidNewFlowDeptNo, deptNo);
        log.info("{}, 菜鸟马甲，使用oaid赋值新流程的事业部，开关开启状态:{}", deptNo, flag);
        return flag;
    }

    public boolean isTikTokDfDeptNo(String deptNo){
        boolean isTikTokDfDeptNo = StringUtils.contains(tikTokDfDeptNo, "ALL")
                || StringUtils.contains(tikTokDfDeptNo, deptNo);
        log.info("{},抖音代发京配流程，开关状态:{}" ,deptNo ,isTikTokDfDeptNo);
        return isTikTokDfDeptNo;
    }

    public boolean isMjNotQimenCaid(){
        log.info("MJ非奇门caid取值开关：{} ",mjNotQimenCaid);
        return mjNotQimenCaid;
    }

    public boolean isNotQimenCaid(){
        log.info("非奇门caid取值开关：{} ",notQimenCaid);
        return notQimenCaid;
    }

    public boolean contractLogisticsDept(String deptNo) {
        boolean flag = StringUtils.contains(contractLogisticsDept, "ALL")
                || StringUtils.contains(contractLogisticsDept, deptNo);
        log.info("{}, 产品分单支持赋值合同物流,开关状态: {}", deptNo, flag);
        return flag;
    }


    public boolean isVideoStorePersistModelDeptNo(String deptNo){
        boolean flag = StringUtils.contains(videoStorePersistModelDeptNo, "ALL")
                || StringUtils.contains(videoStorePersistModelDeptNo, deptNo);
        log.info("{}, 视频号京配持久化model事业部: {}", deptNo, flag);
        return flag;
    }

    public boolean isProductSplitUseErrMsgDept(String deptNo) {
        boolean flag = StringUtils.contains(productSplitUseErrMsgDept, "ALL")
                || StringUtils.contains(productSplitUseErrMsgDept, deptNo);
        log.info("{}, 产品分单使用产品中心校验异常信息,事业部开关,开关状态: {}", deptNo, flag);
        return flag;
    }

    public boolean isColdChainUseHeavyGoodsDept(String deptNo) {
        boolean flag = StringUtils.contains(coldChainUseHeavyGoodsDept, "ALL")
                || StringUtils.contains(coldChainUseHeavyGoodsDept, deptNo);
        log.info("{}, 冷链使用重货上楼,事业部开关,开关状态: {}", deptNo, flag);
        return flag;
    }

    /**
     * 冷链支持特惠小件和云配的事业部
     */
    public boolean containsCcSupportOtherProductDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(ccSupportOtherProductDept, "ALL")
                || StringUtils.contains(ccSupportOtherProductDept, deptNo);
        log.info("冷链支持特惠小件和云配,事业部编码:{}, 开关状态:{}", deptNo, flag);
        return flag;
    }

    public boolean isColdChainUseWarmLayerDept(String deptNo) {
        boolean flag = StringUtils.contains(coldChainUseWarmLayerDept, "ALL")
                || StringUtils.contains(coldChainUseWarmLayerDept, deptNo);
        log.info("{}, 冷链需要回传温层,事业部开关,开关状态: {}", deptNo, flag);
        return flag;
    }

    public boolean isTiktokSupplyDistributionDept(String deptNo) {
        boolean flag = StringUtils.contains(tiktokSupplyDistributionDept, "ALL")
                || StringUtils.contains(tiktokSupplyDistributionDept, deptNo);
        log.info("抖音供销事业部开关,事业部编码:{}, 开关状态:{}", deptNo, flag);
        return flag;
    }

    public boolean isColdChainPromiseUpdateNewFlow(String deptNo) {
        boolean flag = StringUtils.contains(coldChainPromiseUpdateNewFlow, "ALL")
                || StringUtils.contains(coldChainPromiseUpdateNewFlow, deptNo);
        log.info("{}, 冷链时效升级新流程,开关状态: {}", deptNo, flag);
        return flag;
    }

    public boolean isColdChainUseDeliveryIntoWarehouseDept(String deptNo) {
        boolean flag = StringUtils.contains(coldChainUseDeliveryIntoWarehouseDept, "ALL")
                || StringUtils.contains(coldChainUseDeliveryIntoWarehouseDept, deptNo);
        log.info("{}, 冷链使用送货入仓,事业部开关,开关状态: {}", deptNo, flag);
        return flag;
    }

    public boolean xhsShipModeBusinessUnitBlacklist(String businessUnit){
        boolean flag = StringUtils.contains(xhsShipModeBusinessUnitBlacklist, "ALL")
                || StringUtils.contains(xhsShipModeBusinessUnitBlacklist, businessUnit);
        log.info("业务身份: {}, 小红书新链路, 订单取号模式, 是否在黑名单列表: {}", businessUnit, flag);
        return flag;
    }

    /**
     * 小红书新链路，子母单（不带单号n包裹）
     */
    public boolean xhsNPackage(String deptNo) {
        boolean flag =  StringUtils.contains(xhsNPackageDept, "ALL")
                || StringUtils.contains(xhsNPackageDept, deptNo);
        log.info("{},小红书新链路子母单（不带单号n包裹）,开关状态: {}", deptNo, flag);
        return flag;
    }

    public boolean isPromiseDeliveryTimeDept(String businessUnit) {
        boolean flag = StringUtils.contains(promiseDeliveryTimeBusinessUnit, "ALL")
                || StringUtils.contains(promiseDeliveryTimeBusinessUnit, businessUnit);
        log.info("{}, promise配送时效增加传参，开关状态：{}", businessUnit, flag);
        return flag;
    }

    public boolean isCancelPipeWms(String deptNo){
        boolean flag = StringUtils.contains(cancelPipeWmsDeptNo, "ALL")
                || StringUtils.contains(cancelPipeWmsDeptNo, deptNo);
        return flag;
    }

    @LafUcc
    @Value("${ducc.tiktok.use.plaintext.dept:{}}")
    public void setTikTokUsePlainTextDeptMap(String tikTokUsePlainTextDept) {
        log.info("抖音使用明文的事业部,DUCC配置为:{}", tikTokUsePlainTextDept);
        Map<String, String> usedMap = null;
        if (StringUtils.isNotBlank(tikTokUsePlainTextDept)) {
            try {
                usedMap = JsonUtil.parse(tikTokUsePlainTextDept, HashMap.class);
            } catch (Exception e) {
                log.warn("抖音使用明文的事业部,DUCC配置反序列化异常:{}", tikTokUsePlainTextDept);
            }
            if (MapUtils.isNotEmpty(usedMap)) {
                tikTokUsePlainTextDeptMap = usedMap;
            }
        } else {
            log.info("抖音使用明文的事业部,DUCC配置为空");
        }
    }

    public boolean isTikTokUsePlainTextDept(String businessUnit, String accountNo) {
        boolean flag = MapUtils.isNotEmpty(tikTokUsePlainTextDeptMap)
                && tikTokUsePlainTextDeptMap.containsKey(businessUnit)
                && (tikTokUsePlainTextDeptMap.get(businessUnit).contains("ALL") || tikTokUsePlainTextDeptMap.get(businessUnit).contains(accountNo));
        log.info("抖音使用明文的事业部,业务身份:{},事业部:{},开关状态:{}", businessUnit, accountNo, flag);
        return flag;
    }

    public boolean contractLogisticsClpsWarehouseDeptNo(String deptNo){
        boolean flag = StringUtils.contains(contractLogisticsClpsWarehouseDeptNo, "ALL")
                || StringUtils.contains(contractLogisticsClpsWarehouseDeptNo, deptNo);
        log.info("{},合同物流放开支持clps库房，开关状态:{}", deptNo, flag);
        return flag;
    }


    public boolean isProductSplitOccupyTypeDeptNo(String deptNo){
        boolean flag = StringUtils.contains(productSplitOccupyTypeDeptNo, "ALL")
                || StringUtils.contains(productSplitOccupyTypeDeptNo, deptNo);
        log.info("{},分单接口使用预占类型的事业部，开关状态:{}" ,deptNo ,flag);
        return flag;
    }

    /**
     * 复用运单号增加收件地址比较-上线开关
     */
    public boolean isReuseAddCondition(String deptNo) {
        boolean flag = StringUtils.contains(reuseAddConditionDeptNo, "ALL")
                || StringUtils.contains(reuseAddConditionDeptNo, deptNo);
        log.info("复用运单号增加收件地址比较, 事业部:{}, 开关状态:{}", deptNo, flag);
        return flag;
    }


    public boolean isProductSplitOrderAmount(String deptNo){
        boolean flag = StringUtils.contains(productSplitOrderAmountDeptNo, "ALL")
                || StringUtils.contains(productSplitOrderAmountDeptNo, deptNo);
        log.info("{},分单接口入参增加订单金额，开关状态:{}" ,deptNo ,flag);
        return flag;
    }

    public boolean isUseRealBusinessStrategyCheckProductDeptNo(String deptNo){
        boolean flag = StringUtils.contains(useRealBusinessStrategyCheckProductDeptNo, "ALL")
                || StringUtils.contains(useRealBusinessStrategyCheckProductDeptNo, deptNo);
        log.info("{},使用真实业务策略调产品中心校验，开关状态:{}" ,deptNo ,flag);
        return flag;
    }

    @LafUcc
    @Value("${ducc.kwai.plain.text.business.unit:{}}")
    public void setKwaiPlainTextBusinessUnitMap(String kwaiPlainTextBusinessUnit){
        log.info("快手明文事业部，ducc配置信息:{}", kwaiPlainTextBusinessUnit);
        if (StringUtils.isBlank(kwaiPlainTextBusinessUnit)){
            return;
        }
        HashMap unitMap = null;
        try {
             unitMap = JsonUtil.parse(kwaiPlainTextBusinessUnit, HashMap.class);
        }catch (Exception e){
            log.warn("快手明文事业部 ducc配置解析异常", e);
        }
        if (MapUtils.isNotEmpty(unitMap)){
            kwaiPlainTextBusinessUnitMap = unitMap;
        }
    }

    public boolean isKwaiPlainText(String businessUnit, String accountNo){
        boolean flag = MapUtils.isNotEmpty(kwaiPlainTextBusinessUnitMap)
                && kwaiPlainTextBusinessUnitMap.containsKey(businessUnit)
                && (kwaiPlainTextBusinessUnitMap.get(businessUnit).contains("ALL")
                    || kwaiPlainTextBusinessUnitMap.get(businessUnit).contains(accountNo)
                    );
        log.info("快手明文事业部,业务身份:{},事业部:{},开关状态:{}", businessUnit, accountNo, flag);
        return flag;
    }

    public boolean isDownLeapmotorProduct() {
        log.info("下线零跑产品赋值逻辑, 开关状态:{}", downLeapmotorProduct);
        return downLeapmotorProduct;
    }

    /**
     * 小红书京配冷链温层根据取号产品判断-上线开关
     */
    public boolean xhsColdFresh(String deptNo) {
        boolean flag = StringUtils.contains(xhsColdFreshDeptNo, "ALL")
                || StringUtils.contains(xhsColdFreshDeptNo, deptNo);
        log.info("小红书冷链京配温层逻辑优化, 事业部:{}, 开关状态:{}", deptNo, flag);
        return flag;
    }
    @LafUcc
    @Value("${ducc.video.store.parent.order.business.unit.dept:{}}")
    public void setVideoStoreParentOrderBusinessUnitDeptMap(String businessUnitDeptConf) {
        log.info("视频号子母单控量配置 业务身份事业部,DUCC配置为:{}", businessUnitDeptConf);

        if (StringUtils.isBlank(businessUnitDeptConf)){
            log.info("视频号子母单控量配置 ,DUCC配置为空");
            return;
        }
        HashMap<String, String> usedMap = null;
        try {
            usedMap = JsonUtil.parse(businessUnitDeptConf, HashMap.class);
        } catch (Exception e) {
            log.warn("视频号子母单控量,DUCC配置反序列化异常:{}", businessUnitDeptConf);
        }
        if (MapUtils.isNotEmpty(usedMap)) {
            videoStoreParentOrderBusinessUnitDeptMap = usedMap;
        }
    }

    public boolean isVideoStoreParentOrderBusinessUnitDept(String businessUnit, String accountNo) {
        boolean flag = MapUtils.isNotEmpty(videoStoreParentOrderBusinessUnitDeptMap)
                && videoStoreParentOrderBusinessUnitDeptMap.containsKey(businessUnit)
                && (videoStoreParentOrderBusinessUnitDeptMap.get(businessUnit).contains("ALL")
                    || videoStoreParentOrderBusinessUnitDeptMap.get(businessUnit).contains(accountNo)
                );
        log.info("视频号子母单控量的事业部,业务身份:{},事业部:{},开关状态:{}", businessUnit, accountNo, flag);
        return flag;
    }

    /**
     * 自采京配明文多包裹上线开关
     */
    public boolean ziCaiJdNoEncDeptNoContains(String deptNo) {
        boolean flag =  StringUtils.contains(ziCaiJdNoEncDeptNo, "ALL")
                || StringUtils.contains(ziCaiJdNoEncDeptNo, deptNo);
        log.info("{},自采京配明文多包裹,开关状态: {}", deptNo, flag);
        return flag;
    }

    /**
     * 判断视频号多包裹n-1标位回传订单中心的开关状态是否包含指定部门号。
     * @param deptNo 部门号。
     * @return 如果开关状态包含'ALL'或指定部门号，则返回true；否则返回false。
     */
    public boolean isVideoStoreChildAndParentWithWaybillDept(String deptNo) {
        boolean flag = StringUtils.contains(videoStoreChildAndParentWithWaybillDept, "ALL")
                || StringUtils.contains(videoStoreChildAndParentWithWaybillDept, deptNo);
        log.info("{}, 微信视频号子母单n-1包裹场景，上线开关: {}", deptNo, flag);
        return flag;
    }

    public boolean isLasTclSurveyDeptNo(String deptNo){
        boolean flag = StringUtils.contains(lasTclSurveyDeptNo, "ALL")
                || StringUtils.contains(lasTclSurveyDeptNo, deptNo);
        log.info("{},大件勘测服务上线开关,开关状态: {}", deptNo, flag);
        return flag;
    }

    /**
     * 仓运交接BPromise时效控量配置开关
     *
     * @param deptNo
     * @return
     */
    public boolean containBpromiseDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(bpromiseDeptNo, "ALL")
                || StringUtils.contains(bpromiseDeptNo, deptNo);
        log.info("仓运交接BPromise时效控量配置开关, 事业部:{}, 开关状态:{}", deptNo, flag);
        return flag;
    }

    public boolean isContractPromisePause(String deptNo) {
        boolean flag = StringUtils.contains(contractPromisePauseDeptNo, "ALL")
                || StringUtils.contains(contractPromisePauseDeptNo, deptNo);
        log.info("合同物流时效控单, 事业部:{}, 开关状态:{}", deptNo, flag);
        return flag;
    }
}
