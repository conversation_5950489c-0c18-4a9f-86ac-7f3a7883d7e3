package com.jdl.sc.ofw.out.horiz.infra.rpc.impl;

import com.jd.csc.channel.intserv.PlatformEnum;
import com.jd.csc.channel.intserv.waybill.ExtInfo;
import com.jd.csc.channel.intserv.waybill.PackageInfo;
import com.jd.csc.channel.intserv.waybill.SkuInfo;
import com.jd.csc.channel.intserv.waybill.build.BuildWaybillCodeRequest;
import com.jd.csc.channel.intserv.waybill.build.DyBuildWaybillCodeRequest;
import com.jd.csc.channel.intserv.waybill.ext.DyInstantExtInfo;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetPlatform;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetRequest;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.rpc.ElectronicSheetConfigAcl;
import com.jdl.sc.ofw.out.horiz.infra.rpc.ThirdPartPlatformCreateAcl;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <a href="https://joyspace.jd.com/pages/2GCOtYMbAZqKQMuxgFaJ">外平台取号接口</a>
 */
@Slf4j
@Profile(value = {"!ab"})
@Service("tiktokInstantPlatformStandardServiceRpc")
public class TiktokInstantPlatformStandardServiceRpc extends AbstractPlatformStandardCreateServiceRpc implements ThirdPartPlatformCreateAcl {

    @Resource
    private ElectronicSheetConfigAcl electronicSheetConfigAcl;

    private static final String SALE_PLATFORM_WAREHOUSE_ID = "salePlatformWarehouseId";
    private static final String SALE_PLATFORM_STORE_ID = "salePlatformStoreId";

    @Override
    public boolean support(DeliveryPerformanceChannelEnum deliveryPerformanceChannelEnum) {
        return DeliveryPerformanceChannelEnum.TIK_TOK == deliveryPerformanceChannelEnum;
    }

    @Override
    public PauseReasonEnum getPauseReasonEnum(PauseReasonTypeEnum pauseReasonTypeEnum) {
        if (pauseReasonTypeEnum == null) {
            throw new UnsupportedOperationException("必须指定暂停异常类型");
        }
        switch (pauseReasonTypeEnum) {
            case TOKEN:
                return PauseReasonEnum.TIK_TOK_TOKEN_EXCEPTION;
            case PRINT:
                return PauseReasonEnum.TIK_TOK_PRINT_EXCEPTION;
            case WAYBILL:
                return PauseReasonEnum.TIK_TOK_WAYBILL_EXCEPTION;
            default:
                throw new UnsupportedOperationException("不支持的暂停异常类型");
        }
    }

    @Override
    public BuildWaybillCodeRequest getBuildWaybillCodeRequest(OrderFulfillmentModel model) {
        return new DyBuildWaybillCodeRequest()
                .extraInfo(getExtInfo(model))
                .warehouseInfo(model.getWarehouse().getWarehouseNo());
    }

    @Override
    public ExtInfo getExtInfo(OrderFulfillmentModel model) {
        Map<String, Object> customFieldMap = JsonUtil.parse(model.getFulfillment().getCustomField(), HashMap.class);
        return new DyInstantExtInfo(
                getIntegerValue(customFieldMap, SALE_PLATFORM_WAREHOUSE_ID),
                getIntegerValue(customFieldMap, SALE_PLATFORM_STORE_ID)
        );
    }

    @Override
    public PlatformEnum getPlatformEnum() {
        return PlatformEnum.DY_INSTANT;
    }

    @Override
    public ElectronicSheetInfo getElectronicSheetInfo(OrderFulfillmentModel model) {
        ElectronicSheetInfo electronicSheetInfo = electronicSheetConfigAcl.queryConfigInfo(this.buildElectronicSheetRequest(model));
        if (Objects.isNull(electronicSheetInfo)) {
            log.error("【抖音即时零售】获取到该商家平台电子面单配置信息为空,orderNo:{}", model.getOrderNo());
            throw new PauseException(getPauseReasonEnum(PauseReasonTypeEnum.PRINT))
                    .withCustom("获取到该商家平台电子面单配置信息为空，请至商家工作台【平台面单配置】维护");
        }
        if (StringUtils.isBlank(electronicSheetInfo.getAuthToken())) {
            log.error("【抖音即时零售】获取到该商家平台电子面单配置授权码为空,orderNo:{}", model.getOrderNo());
            throw new PauseException(getPauseReasonEnum(PauseReasonTypeEnum.TOKEN))
                    .withCustom("获取到该商家平台电子面单配置授权码为空，请至商家工作台【平台面单配置】维护");
        }
        return electronicSheetInfo;
    }

    @Override
    public List<PackageInfo> getPackageInfoList(OrderFulfillmentModel model) {
        return Collections.singletonList(
                new PackageInfo(
                        buildPackId(model),
                        CollectionUtils.emptyIfNull(model.getCargos())
                                .stream()
                                .map(cargo -> new SkuInfo(
                                        cargo.getName(),
                                        cargo.getQuantity().getValue().intValue()
                                ))
                                .collect(Collectors.toList())
                )
        );
    }

    private ElectronicSheetRequest buildElectronicSheetRequest(OrderFulfillmentModel model) {
        Map<String, Object> customFieldMap = getCustomFieldMap(model);
        return ElectronicSheetRequest.builder()
                .shipperNo(model.getShipment().getShipperNo())
                .accountNo(model.getCustomer().getAccountNo())
                .warehouseNo(model.getWarehouse().getOriginalWarehouseNo())
                .shopNo(model.getChannel().getShopNo())
                .platform(ElectronicSheetPlatform.DY_INSTANT)
                .platDirectConn(0)
                .storeId(getIntegerValue(customFieldMap, SALE_PLATFORM_STORE_ID))
                .outWarehouseId(getIntegerValue(customFieldMap, SALE_PLATFORM_WAREHOUSE_ID))
                .existFreightProduct(model.getProducts().existTransportProduct())
                .build();
    }

    private Map<String, Object> getCustomFieldMap(OrderFulfillmentModel model) {
        Map<String, Object> customFieldMap = null;
        if (StringUtils.isNotBlank(model.getFulfillment().getCustomField())) {
            customFieldMap = JsonUtil.parse(model.getFulfillment().getCustomField(), HashMap.class);
        }
        return customFieldMap;
    }

    private Integer getIntegerValue(Map<String, Object> customFieldMap, String key) {
        return NumberUtils.isCreatable(MapUtils.getString(customFieldMap, key))
                ? MapUtils.getInteger(customFieldMap, key)
                : null;
    }

    private String buildPackId(OrderFulfillmentModel model) {
        String packId = model.getProducts().existTransportProduct()
                ? model.getOrderNo()
                : String.valueOf(System.nanoTime());
        return StringUtils.right(packId, 15);
    }

}
