package com.jdl.sc.ofw.out.common.dict;

import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.assertEquals;


public class ExpectDeliveryResourceEnumTest {

    @Test
    public void test() {
        Assert.assertEquals(ExpectDeliveryResourceEnum.CONTRACT_LOGISTICS, ExpectDeliveryResourceEnum.fromCode("1"));
        Assert.assertEquals(ExpectDeliveryResourceEnum.NOT_CONTRACT_LOGISTICS, ExpectDeliveryResourceEnum.fromCode("0"));
        Assert.assertEquals(ExpectDeliveryResourceEnum.NOT_CONTRACT_LOGISTICS, ExpectDeliveryResourceEnum.fromCode(""));
        Assert.assertEquals(ExpectDeliveryResourceEnum.NOT_CONTRACT_LOGISTICS, ExpectDeliveryResourceEnum.fromCode(null));
    }
}
