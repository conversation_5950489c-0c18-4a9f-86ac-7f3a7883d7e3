package com.jdl.sc.ofw.out.common.dict;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class SmileWaybillTypeEnumTest {

    @Test
    public void test() {
        SmileWaybillTypeEnum smileType = SmileWaybillTypeEnum.HIDE_NAME_AND_ADDRESS;
        String hiddenContent = "[\"receiveAddress\",\"receiverName\"]";
        assertEquals(hiddenContent, smileType.toHiddenContent());
        assertEquals(smileType, SmileWaybillTypeEnum.fromHiddenContent(hiddenContent));
    }
}
