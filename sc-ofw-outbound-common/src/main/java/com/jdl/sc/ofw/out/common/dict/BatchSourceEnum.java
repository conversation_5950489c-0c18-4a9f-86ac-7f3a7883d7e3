package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BatchSourceEnum {

    RECEIVE("receive", "商家指定批属性"),
    OCCUPY("occupy", "库存预占批属性"),
    CALLBACK("callback", "仓出库批属性");

    @JsonValue
    private String key;
    private String text;

    BatchSourceEnum(String key, String text) {
        this.key = key;
        this.text = text;
    }

    @JsonCreator
    public static BatchSourceEnum fromKey(String key) {
        for (BatchSourceEnum batchSourceEnum : values()) {
            if (batchSourceEnum.getKey().equals(key)) {
                return batchSourceEnum;
            }
        }

        throw new IllegalArgumentException("未知批次信息来源: " + key);
    }

}
