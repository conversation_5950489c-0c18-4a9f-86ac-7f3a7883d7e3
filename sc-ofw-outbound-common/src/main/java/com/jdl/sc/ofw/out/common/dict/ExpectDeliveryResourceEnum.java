package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExpectDeliveryResourceEnum {
    CONTRACT_LOGISTICS("1", "合同物流"),
    NOT_CONTRACT_LOGISTICS("0", "非合同物流"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    @JsonCreator
    public static ExpectDeliveryResourceEnum fromCode(final String code) {
        for (final ExpectDeliveryResourceEnum anEnum : values()) {
            if (anEnum.getCode().equals(code)) {
                return anEnum;
            }
        }
        return ExpectDeliveryResourceEnum.NOT_CONTRACT_LOGISTICS;
    }
}
