package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ThirdShipperEnum {

    SF("shun<PERSON>", "顺风承运"),
    J<PERSON>("jd", "京东承运"),
    JDKY("jing<PERSON><PERSON><PERSON>yun","京东快运"),
    ;

    @JsonValue
    private String shipperNo;
    private String text;

    ThirdShipperEnum(String shipperNo, String text) {
        this.shipperNo = shipperNo;
        this.text = text;
    }

    @JsonCreator
    public static ThirdShipperEnum fromThirdShipperNo(String shipperNo) {
        for (ThirdShipperEnum thirdShipperEnum : values()) {
            if (thirdShipperEnum.getShipperNo().equals(shipperNo)) {
                return thirdShipperEnum;
            }
        }
        return null;
    }


}
