package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Arrays;

@EqualsAndHashCode(callSuper = true)
public abstract class AbstractException extends RuntimeException {
    /**
     * 异常码统一定义返回
     */
    protected OutboundErrorSpec errorSpec;

    protected String[] vars;
    /**
     * (业务前台)个性化消息.
     * <p>
     * <p>例如，业务前台要求它抛出的错误消息，中台不要再加工，要原封不动地输出</p>
     */
    protected String custom;


    public AbstractException(OutboundErrorSpec errorReason) {
        super(errorReason.code() + "(" + errorReason.desc() + ")");
        this.errorSpec = errorReason;
    }

    public AbstractException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason.code() + "(" + errorReason.desc() + ")", exception);
        this.errorSpec = errorReason;
    }

    /**
     * 设置错误消息占位符的变量值.
     * <p>
     * <p>错误消息，通过i18n机制为错误码设定，其中包含占位符的，在抛出异常时需要传递占位符变量的值</p>
     *
     * @param vars 占位符变量的值
     */
    public AbstractException withVars(String... vars) {
        this.vars = vars;
        return this;
    }

    /**
     * 设置(业务前台)个性化消息.
     *
     * @param custom 个性化消息
     */
    public AbstractException withCustom(String custom) {
        this.custom = custom;
        return this;
    }


    /**
     * 是否有个性化信息.
     */
    public boolean hasCustom() {
        return custom != null;
    }

    /**
     * 获取错误码.
     */
    public String code() {
        return errorSpec.code();
    }

    @Override
    public String getMessage() {
        if (vars == null || vars.length == 0) {
            if (hasCustom()) {
                return custom;
            }
            return "(" + code() + ")" + errorSpec.desc();
        }

        if (errorSpec.desc().indexOf("%s") > -1) {
            return String.format(errorSpec.desc(), vars);
        }

        if (hasCustom()) {
            return custom;
        }
        return "(" + code() + ")" + errorSpec.desc() + Arrays.toString(vars);
    }

}
