package com.jdl.sc.ofw.out.common.constant;

public class PlatformConstants {

    /**
     * ISV来源
     */
    public static final String ISV_SOURCE_QIMEN = "ISV0020000000640";

    /**
     * edi来源
     */
    public static final String SYSTEM_CALLER_EDI = "EDI";

    /**
     * 销售平台ID
     */
    public static final String SP_TM_ID = "274";

    /**
     * 无效值
     */
    public static final String INVALID_VALUE = "-128";

    /**
     * 马甲电子面单配置异常话术
     */
    public static final String MJ_CONFIG_ERROR = "未获取到商家的电子面单配置信息，请联系商家配置。操作路径：商家工作台-配置管理-平台电子面单";

    /**
     * p系马甲销售平台ID
     */
    public static final String SP_PDD_ID ="278";
    /**
     * 抖音超市 销售平台编码
     */
    public static final String TIKTOK_SUPERMARKET_CHANNEL_NO= "8041300";

    /**
     * 抖音供销平台  销售平台编码
     */
    public static final String TIKTOK_SUPPLY_DISTRIBUTION_CHANNEL_NO= "8040861";

    /**
     * 抖音超市 产业平台userId
     */
    public static final String TIKTOK_SUPERMARKET_USER_ID = "54507770";
    /**
     * 抖音超市 产业平台orderChannel
     */
    public static final String TIKTOK_SUPERMARKET_ORDER_CHANNEL= "102";
    //  销售平台编码-抖音代发为8040635
    public static final String CHANNEL_NO_DYDF = "8040635";
    /**
     * 抖音供销平台 产业平台orderChannel
     */
    public static final String TIKTOK_SUPPLY_DISTRIBUTION_CHANNEL= "101";

    /**
     * 抖音超市，获取产业平台入参中的UserId
     * ofw中京配，抖音新老流程都在使用
     */
    public static Long getTikTokSuperMarketUserId(String channelNo){
        if (PlatformConstants.TIKTOK_SUPERMARKET_CHANNEL_NO.equals(channelNo)) {
            return  Long.parseLong(PlatformConstants.TIKTOK_SUPERMARKET_USER_ID);
        }
        return null;
    }
    /**
     * 抖音超市，获取产业平台入参中的orderChannel
     * ofw中京配，抖音新老流程都在使用
     */
    public static String getTikTokSuperMarketOrderChannel(String channelNo){
        if (PlatformConstants.TIKTOK_SUPERMARKET_CHANNEL_NO.equals(channelNo)) {
            return PlatformConstants.TIKTOK_SUPERMARKET_ORDER_CHANNEL;
        }
        return null;
    }
}
