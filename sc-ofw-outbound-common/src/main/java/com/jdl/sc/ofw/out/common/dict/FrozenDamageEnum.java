package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum FrozenDamageEnum {

    FROZEN_DAMAGE_EXEMPTION_WHITELIST("39003001", "冻损免赔白名单"),

    FROZEN_DAMAGE_EXEMPTION("39003002", "冻损免赔"),

    PACKAGING_UPGRADE("39003003", "包装升级"),
    ;

    @JsonValue
    private final String code;
    private final String desc;

    @JsonCreator
    public static FrozenDamageEnum fromCode(final String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (final FrozenDamageEnum anEnum : values()) {
            if (anEnum.getCode().equals(code)) {
                return anEnum;
            }
        }
        throw new IllegalArgumentException("未知订单类型: " + code);
    }
}

