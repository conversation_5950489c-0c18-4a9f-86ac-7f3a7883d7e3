package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum CheckGoodsTypeEnum {
    OFF("0", "未开通"),
    CUSTOM_CHECK("1", "随心验(收费)"),
    OPEN_GOODS_PACKAGE("2", "开商品包装验货"),
    OPEN_DELIVERY_PACKAGE("3", "开物流包装验货"),
    NOT_SUPPORT("4", "不支持开箱验货"),
    ;

    @JsonValue
    private final String code;
    private final String desc;

    CheckGoodsTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CheckGoodsTypeEnum fromCode(String code) {
        for (CheckGoodsTypeEnum checkGoodsType : values()) {
            if (Objects.equals(checkGoodsType.getCode(), code)) {
                return checkGoodsType;
            }
        }
        // 默认未开通
        return CheckGoodsTypeEnum.OFF;
    }
}
