package com.jdl.sc.ofw.out.common.specification;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一返回调用通知规范
 */
public class Notification {
    private Map<String, String> errors;


    public static Notification create() {
        return new Notification();
    }

    private Notification() {
    }

    public void addError(String propertyPath, String error) {
        if (error == null || error.trim().isEmpty()) {
            // ignore empty error
            return;
        }

        if (errors == null) {
            errors = new HashMap<>();
        }

        errors.put(propertyPath, error);
    }

    public Map<String, String> getErrors() {
        return errors;
    }

    public boolean hasError() {
        return errors != null && !errors.isEmpty();
    }

    public Map.Entry<String, String> first() {
        if (!hasError()) {
            return null;
        }

        return errors.entrySet().iterator().next();
    }

}
