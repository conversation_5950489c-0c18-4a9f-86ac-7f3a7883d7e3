package com.jdl.sc.ofw.out.common.constant;

public interface DomainConstants {

    /**
     * 销售出库单履约中心
     */
    String OUTBOUND_DOMIAN_CODE = "OUTBOUND_DOMIAN_CODE";
    String OUTBOUND_DOMIAN_NAME = "销售出库单履约中心服务";

    /**
     * 销售出库单履约中心接单V2版
     */
    String OUTBOUND_DOMIAN_CODE_V2 = "OUTBOUND_DOMIAN_CODE_V2";
    String OUTBOUND_DOMIAN_NAME_V2 = "销售出库单履约中心服务V2";

    String OUTBOUND_DOMIAN_RESTART_CODE = "OUTBOUND_DOMIAN_RESTART_CODE";
    String OUTBOUND_DOMIAN_RESTART_NAME = "销售出库单履约中心重跑服务";

    String OUTBOUND_DOMIAN_CANCEL_CODE = "OUTBOUND_DOMIAN_CANCEL_CODE";
    String OUTBOUND_DOMIAN_CANCEL_NAME = "销售出库单履约中心取消服务";

    String OUTBOUND_DOMIAN_PULLBACK_CODE = "OUTBOUND_DOMIAN_PULLBACK_CODE";
    String OUTBOUND_DOMIAN_PULLBACK_NAME = "销售出库单履约中心拉回服务";

    String OUTBOUND_DOMIAN_MODIFY_CODE = "OUTBOUND_DOMIAN_MODIFY_CODE";
    String OUTBOUND_DOMIAN_MODIFY_NAME = "销售出库单履约中心修改服务";

    String OUTBOUND_DOMIAN_MODIFY_STATUS_CODE = "OUTBOUND_DOMIAN_MODIFY_STATUS_CODE";
    String OUTBOUND_DOMIAN_MODIFY_STATUS_NAME = "销售出库单履约中心修改状态服务";

    /**
     * 退货到仓-类型处理（退门店、退商家仓、就近退）扩展点
     */
    String REFUND_WAREHOUSE_TYPE_FLOW_EXT_CODE = "REFUND_WAREHOUSE_TYPE_FLOW_EXT_CODE";
    String REFUND_WAREHOUSE_TYPE_FLOW_EXT_NAME = "退货到仓-类型处理";
    /**
     * 旧-退货到仓-类型处理（退门店、退商家仓、就近退）扩展点
     */
    String OLD_REFUND_WAREHOUSE_TYPE_FLOW_EXT_CODE = "OLD_REFUND_WAREHOUSE_TYPE_FLOW_EXT_CODE";
    String OLD_REFUND_WAREHOUSE_TYPE_FLOW_EXT_NAME = "退货到仓-类型处理";

    /**
     * 履约阶段执行扩展
     */
    String ORDER_ONESELF_EXECUTE_EXT_CODE = "ORDER_ONESELF_EXECUTE_EXT_CODE";
    String ORDER_ONESELF_EXECUTE_EXT_NAME = "履约阶段执行扩展";

    /**
     * 产品服务
     */
    String PRODUCT_SERVICE_FLOW_EXT_CODE = "PRODUCT_SERVICE_FLOW_EXT_CODE";
    String PRODUCT_SERVICE_FLOW_EXT_NAME = "产品服务扩展点";

    /**
     * 出库单下仓能力
     */
    String OUTBOUND_TO_WMS_EXT_CODE = "OUTBOUND_TO_WMS_EXT_CODE";
    String OUTBOUND_TO_WMS = "出库单下仓能力";


    /**
     * 出库单下仓能力
     */
    String OUTBOUND_TRANSPORT_TO_WMS_CODE = "OUTBOUND_TRANSPORT_TO_WMS_CODE";
    String OUTBOUND_TRANSPORT_TO_WMS_NAME = "出库单下仓能力";

    /**
     * 出库单仓取消能力
     */
    String OUTBOUND_CANCEL_TO_WMS_EXT_CODE = "OUTBOUND_CANCEL_TO_WMS_EXT_CODE";
    String OUTBOUND_CANCEL_TO_WMS = "出库单仓取消能力";

    /**
     * 出库单仓拦截能力
     */
    String OUTBOUND_INTERCEPT_TO_WMS_EXT_CODE = "OUTBOUND_INTERCEPT_TO_WMS_EXT_CODE";
    String OUTBOUND_INTERCEPT_TO_WMS = "出库单仓拦截能力";

    /**
     * 出库单仓修改能力
     */
    String OUTBOUND_MODIFY_TO_WMS_EXT_CODE = "OUTBOUND_MODIFY_TO_WMS_EXT_CODE";
    String OUTBOUND_MODIFY_TO_WMS = "出库单仓修改能力";


    /**
     * 出库单仓加急能力
     */
    String OUTBOUND_PREMIUM_PROCESS_TO_WMS_EXT_CODE = "OUTBOUND_PREMIUM_PROCESS_TO_WMS_EXT_CODE";
    String OUTBOUND_PREMIUM_PROCESS_TO_WMS = "出库单仓加急能力";

    /**
     * 下发运单（订单中心）
     */
    String TRANSPORT_TO_TMS_EXT_CODE = "TRANSPORT_TO_TMS_EXT_CODE";
    String TRANSPORT_TO_TMS_EXT_NAME = "下发运单";

    /**
     * 下发运单（订单中心）
     */
    String TRANSPORT_TO_WAYBILL_EXT_CODE = "TRANSPORT_TO_WAYBILL_EXT_CODE";
    String TRANSPORT_TO_WAYBILL_EXT_NAME = "下发运单";

    /**
     * 下发城配系统（城配中心）
     */
    String TRANSPORT_TO_DISPATCH_EXT_CODE = "TRANSPORT_TO_DISPATCH_EXT_CODE";
    String TRANSPORT_TO_DISPATCH_EXT_NAME = "下发城配系统";

    /**
     * 补全配送信息
     */
    String SUPPLY_SHIPMENT_EXT_CODE = "SUPPLY_SHIPMENT_EXT_CODE";
    String SUPPLY_SHIPMENT_EXT_NAME = "补全配送信息";

    /**
     * 补全基础信息
     */
    String SUPPLY_BASIC_EXT_CODE = "SUPPLY_BASIC_EXT_CODE";
    String SUPPLY_BASIC_EXT_NAME = "补全基础信息";

    /**
     * 运单取消
     */
    String TRANSPORT_CANCEL_TO_TMS_EXT_CODE = "TRANSPORT_CANCEL_TO_TMS_EXT_CODE";
    String TRANSPORT_CANCEL_TO_TMS_EXT_NAME = "运单取消";

    /**
     * 运单修改
     */
    String TRANSPORT_MODIFY_TO_TMS_EXT_CODE = "TRANSPORT_MODIFY_TO_TMS_EXT_CODE";
    String TRANSPORT_MODIFY_TO_TMS_EXT_NAME = "运单修改";

    /**
     * 运单加急
     */
    String TRANSPORT_PREMIUM_PROCESS_TO_TMS_EXT_CODE = "TRANSPORT_PREMIUM_PROCESS_TO_TMS_EXT_CODE";
    String TRANSPORT_PREMIUM_PROCESS_TO_TMS_EXT_NAME = "运单加急";

    /**
     * 时效信息，是否执行时效扩展点
     */
    String PROMISE_SHOULD_CALCULATE_CODE = "PROMISE_SHOULD_CALCULATE_CODE";
    String PROMISE_SHOULD_CALCULATE_NAME = "是否需要调用Promise";

    /**
     * 人工预分拣
     */
    String WAYBILL_MODIFY_CODE = "WAYBILL_MODIFY_CODE";
    String WAYBILL_MODIFY_NAME = "人工预分拣结果";

    /**
     * 订单状态校验扩展点
     */
    String ORDER_STATUS_CHECK_EXT_CODE = "ORDER_STATUS_CHECK_EXT_CODE";
    String ORDER_STATUS_CHECK_EXT_NAME = "订单状态校验";

    /**
     * 旧-订单状态校验扩展点
     */
    String OLD_ORDER_STATUS_CHECK_EXT_CODE = "OLD_ORDER_STATUS_CHECK_EXT_CODE";
    String OLD_ORDER_STATUS_CHECK_EXT_NAME = "订单状态校验";

    /**
     * 订单跟踪订阅扩展点
     */
    String ORDER_TRACE_SUBSCRIBE_EXT_CODE = "ORDER_TRACE_SUBSCRIBE_EXT_CODE";
    String ORDER_TRACE_SUBSCRIBE_EXT_NAME = "订单跟踪订阅";

    /**
     * 订单跟踪订阅扩展点
     */
    String OLD_ORDER_TRACE_SUBSCRIBE_EXT_CODE = "OLD_ORDER_TRACE_SUBSCRIBE_EXT_CODE";
    String OLD_ORDER_TRACE_SUBSCRIBE_EXT_NAME = "订单跟踪订阅";

    /**
     * 承运商分单扩展点
     */
    String SHIPPER_SPLIT_EXT_CODE = "SHIPPER_SPLIT_EXT_CODE";
    String SHIPPER_SPLIT_EXT_NAME = "承运商分单";

    /**
     * 创建平台运单扩展点
     */
    String CREATE_PLATFORM_WAYBILL_EXT_CODE = "CREATE_PLATFORM_WAYBILL_EXT_CODE";
    String CREATE_PLATFORM_WAYBILL_EXT_NAME = "创建平台运单";

    /**
     * 订单状态回传扩展点
     */
    String ORDER_STATUS_CALLBACK_EXT_CODE = "ORDER_STATUS_CALLBACK_EXT_CODE";
    String ORDER_STATUS_CALLBACK_EXT_NAME = "订单状态回传";

    String SUPPLY_NON_STANDARD_PRODUCT_EXT_CODE = "SUPPLY_NON_STANDARD_PRODUCT_EXT_CODE";
    String SUPPLY_NON_STANDARD_PRODUCT_EXT_NAME = "非标准产品补全扩展";
    /**
     * 特殊产品补全扩展点
     */
    String SUPPLY_SPECIAL_PRODUCT_EXT_CODE = "SUPPLY_SPECIAL_PRODUCT_EXT_CODE";
    String SUPPLY_SPECIAL_PRODUCT_EXT_NAME = "特殊产品补全扩展点";
    /**
     * 三方产品补全扩展点
     */
    String SUPPLY_THIRD_PRODUCT_EXT_CODE = "SUPPLY_THIRD_PRODUCT_EXT_CODE";
    String SUPPLY_THIRD_PRODUCT_EXT_NAME = "三方产品补全扩展点";

    /**
     * 获取自定义围栏扩展点
     */
    String CUSTOM_PRESORT_EXT_CODE = "CUSTOM_PRESORT_EXT_CODE";
    String CUSTOM_PRESORT_EXT_CODE_NAME = "自定义围栏";

    /**
     * 补齐时效信息扩展点
     */
    String COMPLETE_PROMISE_INFO_EXT_CODE = "COMPLETE_PROMISE_INFO_EXT_CODE";
    String COMPLETE_PROMISE_INFO_EXT_NAME = "补齐时效信息";

    /**
     * 疫情时效暂停扩展点
     */
    String EPIDEMIC_PAUSE_EXT_CODE = "EPIDEMIC_PAUSE_EXT_CODE";
    String EPIDEMIC_PAUSE_EXT_NAME = "疫情时效暂停";


    /**
     * 城配控单能力扩展点
     */
    String CITY_DELIVERY_PAUSE_EXT_CODE = "CITY_DELIVERY_PAUSE_EXT";
    String CITY_DELIVERY_PAUSE_EXT_NAME = "城配控单能力扩展点";

    /**
     * 地址校验扩展点
     */
    String ADDRESS_CHECK_EXT_CODE = "ADDRESS_CHECK_EXT_CODE";
    String ADDRESS_CHECK_EXT_NAME = "地址校验";

    /**
     * 地址解析扩展点
     */
    String ADDRESS_RESOLUTION_EXT_CODE = "ADDRESS_RESOLUTION_EXT_CODE";
    String ADDRESS_RESOLUTION_EXT_NAME = "地址解析";

    /**
     * 青龙业主号补全扩展点
     */
    String SUPPLY_BD_OWNER_NO_EXT_CODE = "SUPPLY_BD_OWNER_NO_EXT_CODE";
    String SUPPLY_BD_OWNER_NO_EXT_NAME = "青龙业主号补全";

    /**
     * 通用流程控制扩展点
     */
    String GENERAL_PROCESS_CONTROL_EXT_CODE = "GENERAL_PROCESS_CONTROL_EXT_CODE";
    String GENERAL_PROCESS_CONTROL_EXT_NAME = "通用流程控制";
    /**
     * 售后信息补全扩展点
     */
    String SUPPLY_AFTER_SALE_INFO_EXT_CODE = "SUPPLY_AFTER_SALE_INFO_EXT_CODE";
    String SUPPLY_AFTER_SALE_INFO_EXT_NAME = "售后信息补全";
    /**
     * 计费模式补全扩展点
     */
    String SUPPLY_BILLING_TYPE_EXT_CODE = "SUPPLY_BILLING_TYPE_EXT_CODE";
    String SUPPLY_BILLING_TYPE_EXT_NAME = "计费模式补全";
    /**
     * 事业部信息补全扩展点
     */
    String SUPPLY_DEPT_INFO_EXT_CODE = "SUPPLY_DEPT_INFO_EXT_CODE";
    String SUPPLY_DEPT_INFO_EXT_NAME = "事业部信息补全";
    /**
     * 收件人信息补全扩展点
     */
    String SUPPLY_CONSIGNEE_EXT_CODE = "SUPPLY_CONSIGNEE_EXT_CODE";
    String SUPPLY_CONSIGNEE_EXT_NAME = "收件人信息补全";
    /**
     * 地址信息补全扩展点
     */
    String SUPPLY_ADDRESS_EXT_CODE = "SUPPLY_ADDRESS_EXT_CODE";
    String SUPPLY_ADDRESS_EXT_NAME = "地址信息补全";
    /**
     * 包裹生产字段补全扩展点
     */
    String SUPPLY_PRODUCT_BY_PACK_EXT_CODE = "SUPPLY_PRODUCT_BY_PACK_EXT_CODE";
    String SUPPLY_PRODUCT_BY_PACK_EXT_NAME = "包裹生产字段补全";
    /**
     * 库房信息补全扩展点
     */
    String SUPPLY_WAREHOUSE_EXT_CODE = "SUPPLY_WAREHOUSE_EXT_CODE";
    String SUPPLY_WAREHOUSE_EXT_NAME = "库房信息补全";
    /**
     * 增值服务补全扩展点
     */
    String VALUE_ADDED_SERVICE_EXT_CODE = "VALUE_ADDED_SERVICE_EXT_CODE";
    String VALUE_ADDED_SERVICE_EXT_NAME = "增值服务补全";
    /**
     * 商品信息补全扩展点
     */
    String SUPPLY_GOODS_INFO_EXT_CODE = "SUPPLY_GOODS_INFO_EXT_CODE";
    String SUPPLY_GOODS_INFO_EXT_NAME = "商品信息补全";

    /**
     * 承运商信息补全扩展点
     */
    String SUPPLY_SHIPPER_INFO_EXT_CODE = "SUPPLY_SHIPPER_INFO_EXT_CODE";
    String SUPPLY_SHIPPER_INFO_EXT_NAME = "承运商信息补全扩展点";

    /**
     * 4PL公司信息补全扩展点
     */
    String SUPPLY_4PL_COMPANY_EXT_CODE = "SUPPLY_4PL_COMPANY_EXT_CODE";
    String SUPPLY_4PL_COMPANY_EXT_NAME = "4PL公司信息补全扩展点";

    /**
     * 4PL运单号补全扩展点
     */
    String SUPPLY_4PL_WAYBILL_EXT_CODE = "SUPPLY_4PL_WAYBILL_EXT_CODE";
    String SUPPLY_4PL_WAYBILL_EXT_NAME = "4PL运单号补全扩展点";

    /**
     * 回传4PL扩展点
     */
    String CALLBACK_4PL_EXT_CODE = "CALLBACK_4PL_EXT_CODE";
    String CALLBACK_4PL_EXT_NAME = "回传4PL扩展点";

    /**
     * 4PL明文信息补全扩展点
     */
    String SUPPLY_4PL_DECRYPT_MESSAGE_EXT_CODE = "SUPPLY_4PL_DECRYPT_MESSAGE_EXT_CODE";
    String SUPPLY_4PL_DECRYPT_MESSAGE_EXT_NAME = "4PL明文信息补全扩展点";

    /**
     * 承运商分单-计算承运商扩展点
     */
    String SHIP_SPLIT_EXT_CODE = "SHIP_SPLIT_EXT_CODE";
    String SHIP_SPLIT_EXT_NAME = "承运商分单-计算承运商扩展点";

    /**
     * 承运商分单-保价扩展点
     */
    String SHIP_SPLIT_INSURE_PRICE_EXT_CODE = "SHIP_SPLIT_INSURE_PRICE_EXT_CODE";
    String SHIP_SPLIT_INSURE_PRICE_EXT_NAME = "承运商分单-保价扩展点";

    /**
     * 承运商分单-青龙业主号扩展点
     */
    String SHIP_SPLIT_BD_OWNER_NO_EXT_CODE = "SHIP_SPLIT_BD_OWNER_NO_EXT_CODE";
    String SHIP_SPLIT_BD_OWNER_NO_EXT_NAME = "承运商分单-青龙业主号扩展点";

    /**
     * 承运商分单-城配打标扩展点
     */
    String MARKING_DISPATCH_EXT_CODE = "MARKING_DISPATCH_EXT_CODE";
    String MARKING_DISPATCH_EXT_NAME = "城配打标";

    /**
     * 承运商分单-产品适配扩展点
     */
    String SHIP_SPLIT_DELIVERY_PRODUCT_EXT_CODE = "SHIP_SPLIT_DELIVERY_PRODUCT_EXT_CODE";
    String SHIP_SPLIT_DELIVERY_PRODUCT_EXT_NAME = "承运商分单-产品适配扩展点";

    /**
     * 运单拦截 扩展点
     */
    String INTERCEPT_TO_WAYBILL_EXT_CODE = "INTERCEPT_TO_WAYBILL_EXT_CODE";
    String INTERCEPT_TO_WAYBILL_EXT_NAME = "运单拦截";

    /**
     * 下仓前暂停扩展点
     */
    String PAUSE_BEFORE_TO_WMS_EXT_CODE = "PAUSE_BEFORE_TO_WMS_EXT";
    String PAUSE_BEFORE_TO_WMS_EXT_NAME = "下仓前暂停扩展点";

    /**
     * 盘点暂停扩展点
     */
    String INVENTORY_PAUSE_EXT_CODE = "INVENTORY_PAUSE_EXT_CODE";
    String INVENTORY_PAUSE_EXT_NAME = "盘点暂停扩展点";

    /**
     * 补齐期望配送资源扩展点
     */
    String SUPPLY_EXPECT_DELIVERY_RESOURCE_EXT_CODE = "SUPPLY_EXPECT_DELIVERY_RESOURCE_EXT";
    String SUPPLY_EXPECT_DELIVERY_RESOURCE_EXT_NAME = "补齐期望配送资源扩展点";

    /**
     * 免赔信息补充节点
     */
    String SUPPLY_DISCLAIMER_INFO_EXT_CODE = "SUPPLY_DISCLAIMER_INFO_EXT_CODE";
    String SUPPLY_DISCLAIMER_INFO_EXT_NAME = "免赔信息补充节点";

    /**
     * 关联单信息补充扩展点
     */
    String SUPPLY_REF_ORDER_INFO_EXT_CODE = "SUPPLY_REF_ORDER_INFO_EXT_CODE";
    String SUPPLY_REF_ORDER_INFO_EXT_NAME = "关联单信息补全扩展点";

    /**
     * 订单持久化扩展点
     */
    String ORDER_PERSISTENCE_EXT_CODE = "ORDER_PERSISTENCE_EXT_CODE";
    String ORDER_PERSISTENCE_EXT_NAME = "订单持久化扩展点";

    /**
     * hold单释放扩展点
     */
    String ORDER_HOLD_RELEASE_EXT_CODE = "ORDER_HOLD_RELEASE_EXT_CODE";
    String ORDER_HOLD_RELEASE_EXT_NAME = "hold单释放扩展点";

    /**
     * 集单扩展点
     */
    public static final String COLLECT_ORDER_EXT_CODE = "COLLECT_ORDER_EXT_CODE";
    public static final String COLLECT_ORDER_EXT_NAME = "集单扩展点";

    /**
     * 清关信息补充扩展点
     */
    String SUPPLY_CUSTOMS_CLEARANCE_EXT_CODE = "SUPPLY_CUSTOMS_CLEARANCE_EXT_CODE";
    String SUPPLY_CUSTOMS_CLEARANCE_EXT_NAME = "清关信息补充扩展点";

    /**
     * 通知扩展点
     */
    String NOTIFY_EXT_CODE = "NOTIFY_FLOW_CODE";
    String NOTIFY_EXT_NAME = "通知扩展点";

    /**
     * 修改运单时效扩展点
     */
    String MODIFY_WAYBILL_PROMISE_EXT_NODE = "MODIFY_WAYBILL_PROMISE_EXT_NODE";
    String MODIFY_WAYBILL_PROMISE_EXT_NAME = "修改运单时效扩展点";

    /**
     * 补全履约信息
     */
    String SUPPLY_FULFILLMENT_EXT_CODE = "SUPPLY_FULFILLMENT_EXT_CODE";
    String SUPPLY_FULFILLMENT_EXT_NAME = "补全配送信息";

    /**
     * 预收款校验扩展点
     */
    String PRE_PAYMENT_VALIDATE_EXT_CODE = "PRE_PAYMENT_VALIDATE_EXT_CODE";
    String PRE_PAYMENT_VALIDATE_EXT_NAME = "预收款校验扩展点";

    /**
     * 运力分单扩展点
     */
    String CAPACITY_SPLIT_ORDER_EXT_CODE = "CAPACITY_SPLIT_ORDER_EXT_CODE";
    String CAPACITY_SPLIT_ORDER_EXT_NAME = "运力分单扩展点";

    /**
     * 校验和补齐明细维度增值服务项扩展点
     */
    String SUPPLY_CARGO_SERVICE_INFO_EXT_CODE = "SUPPLY_CARGO_SERVICE_INFO_EXT_CODE";
    String SUPPLY_CARGO_SERVICE_INFO_EXT_NAME = "校验和补齐明细维度增值服务项扩展点";

    /**
     * 订单同步回传扩展点
     */
    String ORDER_SYNC_CALLBACK_EXT_CODE = "ORDER_SYNC_CALLBACK_EXT_CODE";
    String ORDER_SYNC_CALLBACK_EXT_NAME = "订单同步回传扩展点";

    /**
     * 订单同步回传扩展点
     */
    String ORDER_CALLBACK_EXT_CODE = "ORDER_CALLBACK_EXT_CODE";
    String ORDER_CALLBACK_EXT_NAME = "订单同步回传扩展点";

    /**
     * 预约派送
     */
    String APPOINTMENT_DELIVERY_CODE = "APPOINTMENT_DELIVERY_EXT_CODE";
    String APPOINTMENT_DELIVERY_NAME = "预约派送";

    /**
     * 仓配揽收调度信息补齐扩展点
     */
    String DELIVERY_COLLECTION_EXT_CODE = "DELIVERY_COLLECTION_EXT_CODE";
    String DELIVERY_COLLECTION_EXT_NAME = "仓配揽收调度信息补齐扩展点";

    /**
     * 下仓前阶段hold单扩展点
     */
    String HOLD_BEFORE_WMS_STAGE_EXT_CODE = "HOLD_BEFORE_WMS_STAGE_EXT_CODE";
    String HOLD_BEFORE_WMS_STAGE_EXT_NAME = "下仓前阶段hold单扩展点";

    /**
     * 下配前阶段hold单扩展点
     */
    String HOLD_BEFORE_TMS_STAGE_EXT_CODE = "HOLD_BEFORE_TMS_STAGE_EXT_CODE";
    String HOLD_BEFORE_TMS_STAGE_EXT_NAME = "下配前阶段hold单扩展点";

    /**
     * 主赠单暂停扩展点
     */
    String MAIN_GIFT_PAUSE_EXT_CODE = "MAIN_GIFT_PAUSE_EXT_CODE";
    String MAIN_GIFT_PAUSE_EXT_NAME = "主赠单暂停扩展点";


    /**
     * 后置产品信息补全扩展点
     */
    String SUPPLY_REAR_PRODUCT_INFO_EXT_CODE = "SUPPLY_REAR_PRODUCT_INFO_EXT_CODE";
    String SUPPLY_REAR_PRODUCT_INFO_EXT_NAME = "后置产品信息补全扩展点";

    /**
     * 预制运单号补全扩展点
     */
    String SUPPLY_PRE_WAYBILL_NO_EXT_CODE = "SUPPLY_PRE_WAYBILL_NO_EXT_CODE";
    String SUPPLY_PRE_WAYBILL_NO_EXT_NAME = "预制运单号补全扩展点";


    /**
     * 补齐采购单扩展点
     */
    String SUPPLY_PURCHASE_NO_EXT_CODE = "SUPPLY_PURCHASE_NO_EXT_CODE";
    String SUPPLY_PURCHASE_NO_EXT_NAME = "补齐采购单扩展点";

    /**
     * 附加费信息补全扩展点
     */
    String ATTACH_FEE_INFO_EXT_CODE = "ATTACH_FEE_INFO_EXT_CODE";
    String ATTACH_FEE_INFO_EXT_NAME = "附加费信息补全";

    /**
     * 承运商及产品分单扩展点
     */
    String SHIPMENT_AND_PRODUCT_SPLIT_EXT_CODE = "SHIPMENT_AND_PRODUCT_SPLIT_EXT_CODE";
    String SHIPMENT_AND_PRODUCT_SPLIT_EXT_NAME = "承运商及产品分单扩展点";

    /**
     * 配送网络匹配扩展点
     */
    String DELIVERY_NETWORK_MATCH_EXT_CODE = "DELIVERY_NETWORK_MATCH_EXT_CODE";
    String DELIVERY_NETWORK_MATCH_EXT_NAME = "配送网络匹配扩展点";
    /**
     * 配送信息补全扩展点
     */
    String DELIVERY_INFO_EXT_CODE = "DELIVERY_INFO_EXT_CODE";
    String DELIVERY_INFO_EXT_NAME = "配送信息补全";

    /**
     * 下仓后暂停扩展点
     */
    String PAUSE_AFTER_WMS_EXT_CODE = "PAUSE_AFTER_WMS_EXT_CODE";
    String PAUSE_AFTER_WMS_EXT_NAME = "下仓后暂停扩展点";

    /**
     * 半加工暂停扩展点
     */
    String SEMI_PROCESSED_PAUSE_EXT_CODE = "SEMI_PROCESSED_PAUSE_EXT_CODE";
    String SEMI_PROCESSED_PAUSE_EXT_NAME = "半加工暂停扩展点";

    /**
     * 城配资源补齐扩展点
     */
    String ACTIVE_CITY_DELIVERY_EXT_CODE = "ACTIVE_CITY_DELIVERY_EXT_CODE";
    String ACTIVE_CITY_DELIVERY_EXT_NAME = "城配资源补齐扩展点";

    /**
     * 复用历史运单号扩展点
     */
    String REUSE_HISTORICAL_WAYBILL_EXT_CODE = "REUSE_HISTORICAL_WAYBILL_EXT_CODE";
    String REUSE_HISTORICAL_WAYBILL_EXT_NAME = "复用历史运单号";

    /**
     * 重量体积标准化扩展点
     */
    String WEIGHT_VOLUME_STANDARDIZATION_EXT_CODE = "WEIGHT_VOLUME_STANDARDIZATION_EXT_CODE";
    String WEIGHT_VOLUME_STANDARDIZATION_EXT_NAME = "预制运单号补全扩展点";

    /**
     * 牧童业务集单扩展点
     */
    String MU_TONG_TRANSPORT_EXT_CODE = "MU_TONG_TRANSPORT_EXT_CODE";
    String MU_TONG_TRANSPORT_COLLECT_EXT_NAME = "下发牧童系统扩展点";

    /**
     * 获取平台运单
     */
    String RECEIVE_PLATFORM_WAYBILL_EXT_CODE = "RECEIVE_PLATFORM_WAYBILL_EXT_CODE";
    String RECEIVE_PLATFORM_WAYBILL_EXT_NAME = "平台运单";

    /**
     * 平台运单-切量分流处理扩展点
     */
    String PLATFORM_DIVERSION_SELECTION_PROCESS_EXT_CODE = "PLATFORM_DIVERSION_SELECTION_PROCESS_EXT_CODE";
    String PLATFORM_DIVERSION_SELECTION_PROCESS_EXT_NAME = "切量分流处理扩展点-平台运单";

    /**
     * 送装服务扩展点
     */
    String DELIVERY_INSTALLATION_EXT_CODE = "DELIVERY_INSTALLATION_EXT_CODE";
    String DELIVERY_INSTALLATION_EXT_NAME = "送装服务";

    /**
     * 三方国补信息补全扩展点
     */
    String SUPPLY_THIRD_SUBSIDY_EXT_CODE = "SUPPLY_THIRD_SUBSIDY_EXT_CODE";
    String SUPPLY_THIRD_SUBSIDY_EXT_NAME = "三方国补信息补全扩展点";

    /**
     * 服务单对接
     */
    String SUPPLY_SERVICE_ORDER_EXT_CODE = "SUPPLY_SERVICE_ORDER_EXT_CODE";
    String SUPPLY_SERVICE_ORDER_EXT_NAME = "服务单对接";

}
