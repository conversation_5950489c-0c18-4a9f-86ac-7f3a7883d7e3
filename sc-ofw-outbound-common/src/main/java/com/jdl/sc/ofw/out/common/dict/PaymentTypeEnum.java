package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 支付方式
 */
@Getter
public enum PaymentTypeEnum {
    ONLINE_PAYMENT(0, "在线支付", 2),
    CASH_ON_DELIVERY(1, "货到付款", 1),
    ;
    @JsonValue
    private final int code;
    private final String desc;
    private final int wmsCode;

    PaymentTypeEnum(int code, String desc, int wmsCode) {
        this.code = code;
        this.desc = desc;
        this.wmsCode = wmsCode;
    }

    @JsonCreator
    public static PaymentTypeEnum fromCode(final int code) {
        for (final PaymentTypeEnum anEnum : values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }

        throw new IllegalArgumentException("未知: " + code);
    }
}
