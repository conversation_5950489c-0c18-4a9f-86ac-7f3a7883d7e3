package com.jdl.sc.ofw.out.common.constant;

import java.util.ArrayList;
import java.util.List;

public class ValueAddedServiceConstants {

    /**
     * 主数据增值服务之是否耗材库存管理打标 ：0 否耗材库存管理, 1 是耗材库存管理
     */
    public static final String MATERIAL_STOCK_MANAGEMENT_KEY = "costm";
    public static final String ATERIAL_STOCK_MANAGEMENT_YES = "1";

    /**
     * 主数据增值服务查询订单优先级服务标识
     * 1使用用户下单的优先级
     */
    public static final String ORDER_PRIORITY_KEY = "ordpr";
    public static final String ORDER_PRIORITY_YES = "1";

    // 特安仓配解决方案编码
    public static final String SPECIAL_SECURITY_SOLUTION_CODE = "sc-s-0045";
    // 特安仓配增值服务编码
    public static final String SPECIAL_SECURITY_VALUE_ADDED_SERVICE_CODE = "ed-a-0047";
    // 特安仓配增值服务项 key
    public static final String SPECIAL_SECURITY_OTHER_PARAMS= "guaranteeValueByParticular";
    // 特安服务项-取件强制拍照
    public static final String TE_AN_SERVICE_KEY_PK_FORCE_PHOTO = "pkForcePhoto";
    // 特安服务项-妥投强制拍照
    public static final String TE_AN_SERVICE_KEY_RCV_FORCE_PHOTO = "rcvForcePhoto";
    // 特安服务项-妥投强制电联
    public static final String TE_AN_SERVICE_KEY_FORCE_CONTACT = "forceContact";
    // 特安服务项-短信验证
    public static final String TE_AN_SERVICE_KEY_SMS_VERIFICATION = "smsVerification";
    // 标位对应的char类型，开启标识
    public static final char ENABLED_MARK = '1';
    // 特安服务 trader_sign第143位: 0-否 1-是
    public static final int TRADER_SIGN_143 = 143;
    // 取件强制拍照 trader_sign第145位: 0-否 1-是
    public static final int TRADER_SIGN_145 = 145;
    // 妥投强制拍照 trader_sign第146位: 0-否 1-是
    public static final int TRADER_SIGN_146 = 146;
    // 妥投强制电联 trader_sign第147位: 0-否 1-是
    public static final int TRADER_SIGN_147 = 147;
    // 短信验证 trader_sign第148位: 0-否 1-是
    public static final int TRADER_SIGN_148 = 148;
    // trader_sign 否
    public static final String TRADER_SIGN_DISABLED = "0";
    // trader_sign 是
    public static final String TRADER_SIGN_ENABLED = "1";
    // 特安仓配增值服务 默认保价金额（增值服务维度的保价金额）
    public static final String SPECIAL_SECURITY_DEFAULT_GUARANTEE_AMOUNT = "200";
    // 易损品保价增值服务编码
    public static final String BREAKABLE_VALUE_ADDED_SERVICE_CODE = "ed-a-0002";
    public static List<String> props = new ArrayList<>();

    //重货上楼产品标
    public static final String HEAVY_GOODS_UPSTAIRS_PRODUCT_CODE = "fr-a-0006";
    //增值服务来源 3订单中心
    public static final int PRODUCT_CENTER_VALUE_ADDED_SERVICE_SOURCE = 3;

    //签单返还-快递产品标
    public static final String EXPRESS_VALUE_ADDED_SERVICE_CODE = "ed-a-0010";

    //签单返还-快运产品标
    public static final String FREIGHT_VALUE_ADDED_SERVICE_CODE = "fr-a-0007";

    //指定签收-快递产品标
    public static final String SIGN_TYPE_EXPRESS_VALUE_ADDED_SERVICE_CODE = "ed-a-0026";

    //指定签收-快运产品标
    public static final String SIGN_TYPE_FREIGHT_VALUE_ADDED_SERVICE_CODE = "fr-a-0010";

    //电信卡号操作流程提示
    public static final String TELE_CARD_OPERATION_TIP = "telecomCardPickupTip";

    //电信卡号操作流程提示
    public static final String OPERATION_PROCESS = "operProcess";

    //电信卡号政策宣导
    public static final String PERSUADE_POLICY = "propaPolicy";

    static {
        props.add(MATERIAL_STOCK_MANAGEMENT_KEY);
        props.add(ORDER_PRIORITY_KEY);
    }
}
