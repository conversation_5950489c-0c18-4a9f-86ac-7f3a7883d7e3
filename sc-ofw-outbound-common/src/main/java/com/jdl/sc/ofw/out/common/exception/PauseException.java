package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 暂停，流程结束异常
 */
public class PauseException extends AbstractException {
    @Getter
    private Date resumeTime;

    public PauseException(PauseReasonEnum errorReason, Date resumeTime) {
        super(errorReason);
        this.resumeTime = resumeTime;
    }

    public PauseException(@NotNull PauseReasonEnum errorReason, @NotNull Exception exception, Date resumeTime) {
        super(errorReason, exception);
        this.resumeTime = resumeTime;
    }

    public PauseException(PauseReasonEnum errorReason) {
        super(errorReason);
    }

    public PauseException(@NotNull PauseReasonEnum errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }

    public PauseReasonEnum getPauseReason() {
        return (PauseReasonEnum) errorSpec;
    }
}
