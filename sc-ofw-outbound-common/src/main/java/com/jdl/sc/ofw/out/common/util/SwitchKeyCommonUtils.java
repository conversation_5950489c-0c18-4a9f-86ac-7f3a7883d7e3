package com.jdl.sc.ofw.out.common.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * DUCC开关统一维护在该类中
 */
@Slf4j
@Component
public class SwitchKeyCommonUtils {
    /**
     * 大件仓-小件配，事业部开关
     */
    @Value("${ducc.las.small.shipment.deptNo:-}")
    private String lasSmallShipmentDeptNo;

    /**
     * SLA时效下仓新字段
     */
    @Value("${ducc.sla.promise.new.field.deptNo:-}")
    private String slaPromiseNewFieldDeptNo;

    /**
     * 得物一品多仓事业部
     */
    @Value("${ducc.dewu.multi.warehouse.deptNo:-}")
    private String deWuMultiWarehouseDeptNo;
    /**
     * 耗材时效分层
     */
    @Value("${ducc.material.time.layer.deptNo:-}")
    private String materialTimeLayerDeptNo;

    @Getter
    @Value("${ducc.order.mark.diff.switch:false}")
    private boolean orderMarkDiffSwitch;

    /**
     * 无印DC仓
     */
    @Value("${ducc.muji.hk.dc.deptNo:-}")
    private String mujiHKDCdeptNo;

    @Getter
    @Value("${ducc.batrix.core.log.switch:true}")
    private boolean batrixCoreLogSwitch;

    /**
     * 修改仓信息-事业部开关
     */
    @Value("${ducc.modify.warehouse.deptNo:-}")
    private String modifyWarehouseDeptNo;
    /**
     * 全球购POP直邮
     */
    @Value("${ducc.global.direct.delivery.deptNo:-}")
    private String globalDirectDeliveryDeptNo;
    /**
     * 城配时效接口替换
     */
    @Value("${ducc.city.delivery.interface.change.deptNo:-}")
    private String cityDeliveryInterfaceChangeDeptNo;

    /**
     * 唯品取消流程事业部开关
     */
    @Value("${ducc.vip.cancel.processor.deptNo:-}")
    private String vipCancelProcessorDeptNo;

    @Getter
    @Value("${ducc.cc.checkStationWarehouse.deptNo:-}")
    private String checkStationWarehouseDeptNo;

    /**
     * 中小件时效优先走静态路由时效
     */
    @Value("${ducc.static.route.time.deptNo:-}")
    private String staticRouteTimeDeptNo;

    /**
     * 医冷快递仓配一体
     */
    @Value("${ducc.medicine.common.delivery.deptNos:-}")
    private String medicineCommonDeliveryDeptNos;

    /**
     * 大件仓-小件配，事业部开关
     */
    public boolean lasSmallShipmentDeptNo(String deptNo) {
        boolean flag =  StringUtils.contains(lasSmallShipmentDeptNo, "ALL")
                || StringUtils.contains(lasSmallShipmentDeptNo, deptNo);
        log.info("{},大件仓-小件配，事业部开关,开关状态: {}", deptNo, flag);
        return flag;
    }

    /**
     * SLA时效下仓新字段，事业部开关
     */
    public boolean slaPromiseNewFieldDeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(slaPromiseNewFieldDeptNo, "ALL")
                || StringUtils.contains(slaPromiseNewFieldDeptNo, deptNo);
        log.info("{},SLA时效下仓新字段, 开关状态:{}", deptNo, flag);
        return flag;
    }

    /**
     * 耗材时效分层，事业部开关
     */
    public boolean materialTimeLayerDeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(materialTimeLayerDeptNo, "ALL")
                || StringUtils.contains(materialTimeLayerDeptNo, deptNo);
        log.info("{},耗材时效分层, 开关状态:{}", deptNo, flag);
        return flag;
    }

    /**
     * 无印香港DC仓，事业部开关
     */
    public boolean mujiHKDCdeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(mujiHKDCdeptNo, "ALL")
                || StringUtils.contains(mujiHKDCdeptNo, deptNo);
        log.info("{},无印香港DC仓, 开关状态:{}", deptNo, flag);
        return flag;
    }

    public boolean checkModifyWarehouseDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(modifyWarehouseDeptNo, "ALL")
                || StringUtils.contains(modifyWarehouseDeptNo, deptNo);
        log.info("{},修改仓信息-事业部, 开关状态:{}", deptNo, flag);
        return flag;
    }

    /**
     * 全球售POP直邮，事业部开关
     */
    public boolean globalPopDirectDeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(globalDirectDeliveryDeptNo, "ALL")
                || StringUtils.contains(globalDirectDeliveryDeptNo, deptNo);
        log.info("{},全球售POP直邮, 开关状态:{}", deptNo, flag);
        return flag;
    }

    /**
     * 得物一品多仓，事业部开关
     */
    public boolean isDeWuMultiWarehouseDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(deWuMultiWarehouseDeptNo, "ALL")
                || StringUtils.contains(deWuMultiWarehouseDeptNo, deptNo);
        log.info("{},得物一品多仓,开关状态:{}", deptNo, flag);
        return flag;
    }

    public boolean vipCancelProcessorDeptNo(String deptNo) {
        boolean flag = StringUtils.contains(vipCancelProcessorDeptNo, "ALL")
                || StringUtils.contains(vipCancelProcessorDeptNo, deptNo);
        log.info("{},唯品取消流程事业部开关状态:{}", deptNo, flag);
        return flag;
    }


    public boolean checkStationWarehouseDeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(checkStationWarehouseDeptNo, "ALL")
                || StringUtils.contains(checkStationWarehouseDeptNo, deptNo);
        log.info("{},冷链站仓校验, 开关状态:{}", deptNo, flag);
        return flag;
    }

    public boolean cityDeliveryInterfaceChangeDeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(cityDeliveryInterfaceChangeDeptNo, "ALL")
                || StringUtils.contains(cityDeliveryInterfaceChangeDeptNo, deptNo);
        log.info("{},城配时效接口替换,开关状态:{}", deptNo, flag);
        return flag;
    }

    /**
     * 中小件时效优先走静态路由时效，事业部开关
     */
    public boolean staticRouteTimeDeptNoContains(String deptNo) {
        boolean flag = StringUtils.contains(staticRouteTimeDeptNo, "ALL")
                || StringUtils.contains(staticRouteTimeDeptNo, deptNo);
        log.info("{},中小件时效优先走静态路由时效, 开关状态:{}", deptNo, flag);
        return flag;
    }

    /**
     * 医冷快递仓配一体，事业部开关
     */
    public boolean medicineCommonDeliveryDeptNosContains(String deptNo) {
        boolean flag = StringUtils.contains(medicineCommonDeliveryDeptNos, "ALL")
                || StringUtils.contains(medicineCommonDeliveryDeptNos, deptNo);
        log.info("{}，医冷快递仓配一体, 开关状态:{}", deptNo, flag);
        return flag;
    }

}
