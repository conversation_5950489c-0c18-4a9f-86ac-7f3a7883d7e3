package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GoodsTypeEnum {
    DEFAULT("1", "普通商品"),
    COMBINATION("2", "组套商品")
    ;

    @JsonValue
    private final String value;
    private final String desc;

    @JsonCreator
    public static GoodsTypeEnum fromValue(String value) {
        for (GoodsTypeEnum goodsType : values()) {
            if (goodsType.getValue().equals(value)) {
                return goodsType;
            }
        }
        return GoodsTypeEnum.DEFAULT;
    }
}
