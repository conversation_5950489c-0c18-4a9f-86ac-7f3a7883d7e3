package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

@AllArgsConstructor
public enum CityDeliveryType {
    YES(true, "2", "城配"),
    NO(false, "1", "非城配"),
    ;
    @Getter
    @JsonValue
    private final boolean cityDelivery;
    @Getter
    private final String omsCode;

    private final String desc;

    @JsonCreator
    public static CityDeliveryType fromValue(boolean value) {
        for (CityDeliveryType cityDeliveryType : values()) {
            if (cityDeliveryType.isCityDelivery() == value) {
                return cityDeliveryType;
            }
        }
        throw new IllegalArgumentException("未知的城配类型: " + value);
    }

    public static CityDeliveryType fromOmsValue(String omsValue) {
        if (StringUtils.isBlank(omsValue)){
            return null;
        }
        for (CityDeliveryType cityDeliveryType : values()) {
            if (cityDeliveryType.getOmsCode().equals(omsValue)) {
                return cityDeliveryType;
            }
        }
        throw new IllegalArgumentException("未知的OMS城配类型: " + omsValue);
    }
}
