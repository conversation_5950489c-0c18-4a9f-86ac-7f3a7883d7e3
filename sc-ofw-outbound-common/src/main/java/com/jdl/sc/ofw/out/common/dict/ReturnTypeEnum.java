package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 退仓类型枚举
 */
@Getter
public enum ReturnTypeEnum {

    RETURN_JD(1, "退京东仓", '0', 1),
    RETURN_SELLER(0, "退商家仓", '1', 0),
    RETURN_STORE(6, "退门店", '2', null),
    RETURN_NEARBY(9, "就近退", '3', null),
    RETURN_ON_CREATE(11, "指定地址(正向单下单时指定)", '0', null),
    ;

    @JsonValue
    private final int code;

    private final String desc;
    private final char soMark;
    private final Integer bdCode;

    ReturnTypeEnum(final int code, final String desc, final char soMark, final Integer bdCode) {
        this.code = code;
        this.desc = desc;
        this.soMark = soMark;
        this.bdCode = bdCode;
    }

    @JsonCreator
    public static ReturnTypeEnum fromCode(final Integer code) {

        for (final ReturnTypeEnum anEnum : values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        throw new IllegalArgumentException("未识别的退仓类型");
    }
}
