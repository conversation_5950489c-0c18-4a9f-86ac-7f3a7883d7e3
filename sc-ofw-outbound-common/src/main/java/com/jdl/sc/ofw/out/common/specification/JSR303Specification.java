package com.jdl.sc.ofw.out.common.specification;

import lombok.Setter;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Set;

/**
 * Implementation of JSR303 - Bean Validation.
 */
public class JSR303Specification extends AbstractSpecification<Object> {

    @Setter
    private OutboundValidatorFactory outboundValidatorFactory;

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description
     * <AUTHOR>
     * @createDate
     * @lastModify
     */
    @Override
    public boolean isSatisfiedBy(Object bean, Notification notification, Class... groups) {
        Validator validator = outboundValidatorFactory.getFailFastValidator();
        Set<ConstraintViolation<Object>> result;
        if (groups == null) {
            result = validator.validate(bean);
        } else {
            result = validator.validate(bean, groups);
        }

        if (result.isEmpty()) { // result will never be null
            return true;
        }

        if (notification != null) {
            for (ConstraintViolation<Object> violation : result) {
                String subPath = subRangeString(violation.getPropertyPath() + "", "[", "]");
                notification.addError(subPath, violation.getMessage());
                break;
            }
        }
        return false;
    }

    private String subRangeString(String body, String str1, String str2) {
        while (true) {
            int index1 = body.indexOf(str1);
            if (index1 != -1) {
                int index2 = body.indexOf(str2, index1);
                if (index2 != -1) {
                    String str3 = body.substring(0, index1) + body.substring(index2 + str2.length(), body.length());
                    body = str3;
                } else {
                    return body;
                }
            } else {
                return body;
            }
        }
    }

}
