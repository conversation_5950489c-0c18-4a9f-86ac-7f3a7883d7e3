package com.jdl.sc.ofw.out.common.constant;

/**
 * @Description: 承运商常亮
 */
public interface ShipperConstants {
    /**
     * 京配承运商id
     */
    String JD_SHIPPER_ID = "1";
    /**
     * 京配承运商no
     */
    String JD_SHIPPER_NO = "CYS0000010";
    /**
     * 京配承运商name
     */
    String JD_SHIPPER_NAME = "京东配送";

    /**
     * 京配公司名称
     */
    String JD_SHIPPER_COMPANY = "京东物流";

    /**
     * 京配默认承运国家
     */
    String JD_SHIPPER_COUNTRY = "CN";
    /**
     * 特殊签收要求
     */
    String SPECIAL_SIGN_REQUIREMENTS = "specialSignRequirements";
    /**
     * 快手快运
     */
    String EXPRESS_COMPANY_JDKY = "JDKY";
    /**
     * 快手快递
     */
    String EXPRESS_COMPANY_JD = "JD";
    /**
     * 包装标准
     */
    String PACKAGING_STANDARDS = "packagingStandards";
    /**
     * 京配马甲
     */
    String EXPRESS_COMPANY_JPMJ = "JPMJ";

    /**
     * 京东承运商大写编码——JD
     */
    String SHIPPER_CODE_JD_UPPERCASE = "JD";

    /**
     * 顺丰承运商大写编码——SF
     */
    String SHIPPER_CODE_SF_UPPERCASE = "SF";
}
