package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 无接触收货类型
 */
@Getter
public enum ContactlessReceiveTypeEnum {
    NOT_ENROLLED(0, "未使用", "-1", "0"),
    PROPERTY_MANAGEMENT(1, "物业代收", "9", "1"),
    DOORMAN(2, "门卫代收", "10", "2"),
    SELF_PICK_UP(3, "小区门口自取", "11", "3"),
    APPOINTED_LOCATION(9, "指定地点存放", "12", "4");

    @JsonValue
    private final int omsCode;
    private final String desc;
    private final String bdCodeStr;
    private final String soMark;

    ContactlessReceiveTypeEnum(int omsCode, String desc, String bdCodeStr, String soMark) {
        this.omsCode = omsCode;
        this.desc = desc;
        this.bdCodeStr = bdCodeStr;
        this.soMark = soMark;
    }

    @JsonCreator
    public static ContactlessReceiveTypeEnum fromCode(final Integer code) {
        if (code == null) {
            return NOT_ENROLLED;
        }
        for (final ContactlessReceiveTypeEnum anEnum : values()) {
            if (anEnum.getOmsCode() == code) {
                return anEnum;
            }
        }
        return NOT_ENROLLED;
    }
}
