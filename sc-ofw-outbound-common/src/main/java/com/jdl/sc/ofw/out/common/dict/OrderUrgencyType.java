package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * 订单加急类型
 */
@Getter
public enum OrderUrgencyType {
    OFF("0", "否", '0'),
    LAND("1", "陆运加急", '1'),
    AVIATION("2", "航空加急", '2');
    /**
     * 订单中心存储的订单加急字段
     */
    @JsonValue
    private final String omsCode;
    private final String desc;
    /**
     * orderMark值
     */
    private final char soMark;

    OrderUrgencyType(String omsCode, String desc, char soMark) {
        this.omsCode = omsCode;
        this.desc = desc;
        this.soMark = soMark;
    }

    @JsonCreator
    public static OrderUrgencyType fromOmsCode(String code) {
        for (OrderUrgencyType orderUrgencyType : values()) {
            if (Objects.equals(orderUrgencyType.getOmsCode(), code)) {
                return orderUrgencyType;
            }
        }
        return OFF;
    }
}
