package com.jdl.sc.ofw.out.common.specification;

import org.hibernate.validator.internal.util.privilegedactions.NewInstance;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorFactory;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 校验器工厂
 */
public class OutboundConstraintValidatorFactory implements ConstraintValidatorFactory {

    /**
     * 每个注解对应有多个校验器。
     * 若自定义注解的话，自己可以随便定义属性，并且实现ConstraintValidator接口实现个校验器书写校验逻辑，
     * 写在注解上@Constraint(validatedBy = { })即可生效~~~
     */
    private Map<Class<? extends ConstraintValidator>, Object> constraintValidatorMap = new HashMap();


    // NewInstance的run方法最终就是执行了这句话：clazz.getConstructor().newInstance()而已
    // 因此最终就是创建了一个key的实例而已~  Spring相关的会把它和Bean容器结合起来
    @Override
    public final <T extends ConstraintValidator<?, ?>> T getInstance(Class<T> key) {

        if (null != constraintValidatorMap && !constraintValidatorMap.isEmpty()) {
            Iterator iterator = this.constraintValidatorMap.entrySet().iterator();
            Map.Entry entry;
            do {
                if (!iterator.hasNext()) {
                    return (T) this.run(NewInstance.action(key, "ConstraintValidator"));
                }

                entry = (Map.Entry) iterator.next();
            } while (key != entry.getKey());

            return (T) entry.getValue();
        }
        return run(NewInstance.action(key, "ConstraintValidator"));
    }

    @Override
    public void releaseInstance(ConstraintValidator<?, ?> instance) {
        // noop
    }

    // 入参是个函数式接口:java.security.PrivilegedAction
    private <T> T run(PrivilegedAction<T> action) {
        return System.getSecurityManager() != null ? AccessController.doPrivileged(action) : action.run();
    }

    public Map<Class<? extends ConstraintValidator>, Object> getConstraintValidatorMap() {
        return constraintValidatorMap;
    }

    public void setConstraintValidatorMap(Map<Class<? extends ConstraintValidator>, Object> constraintValidatorMap) {
        this.constraintValidatorMap = constraintValidatorMap;
    }
}
