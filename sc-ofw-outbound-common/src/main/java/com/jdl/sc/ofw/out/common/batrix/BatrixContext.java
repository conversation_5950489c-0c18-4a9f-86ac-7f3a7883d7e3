package com.jdl.sc.ofw.out.common.batrix;

import com.jdl.sc.core.sync.ReentrantLock;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FileName: BatrixContext
 * Description: Batrix流程启动后的上下文对象
 * [@author]:   quhuafeng
 * [@date]:     2023/3/27 11:19 上午
 */
@Slf4j
public class BatrixContext {

    /**
     * Batrix流程处理时的上下文
     */
    private static final ThreadLocal<Map<String, Object>> BATRIX_CONTEXT = new ThreadLocal();

    /**
     * 上下文锁KEY
     */
    private static final String CONTEXT_LOCK_KEY = "lock";

    /**
     * 上下文信息KEY
     */
    private static final String CONTEXT_INFO_KEY = "info";

    /**
     * 当前线程flowNode变更model记录
     */
    private static final String FLOW_NODE_CHANGE_LOG_KEY = "flowNodeChangeLog";


    /**
     * 当前flowNode执行的扩展点。
     */
    private static final String CURRENT_EXECUTE_EXT="current_ext";



    /**
     * 向上下文中写入对象
     *
     * @param object
     */
    private static void writeContext(String key, Object object) {
        Map<String, Object> contextMap = BATRIX_CONTEXT.get();
        if (contextMap == null) {
            Map<String, Object> map = new HashMap<>(8);
            map.put(key, object);
            BATRIX_CONTEXT.set(map);
            return;
        }
        contextMap.put(key, object);
    }

    /**
     * 清完当前线程上下文
     */
    public static void clear() {
        BATRIX_CONTEXT.remove();
    }

    /**
     * 从上下文中获取锁
     *
     * @return
     */
    public static ReentrantLock getLock() {
        Map<String, Object> contextMap = BATRIX_CONTEXT.get();
        if (contextMap == null) {
            return null;
        }
        Object lockObject = contextMap.get(CONTEXT_LOCK_KEY);
        if (lockObject instanceof ReentrantLock) {
            return (ReentrantLock) lockObject;
        }
        return null;
    }

    /**
     * 保存锁
     * @param lock
     */
    public static void saveLock(ReentrantLock lock) {
        writeContext(CONTEXT_LOCK_KEY, lock);
    }

    /**
     * 保存上下文对象
     * @param batrixInfo
     */
    public static void saveBatrixInfo(BatrixInfo batrixInfo) {
        writeContext(CONTEXT_INFO_KEY, batrixInfo);
    }

    public static BatrixInfo getBatrixInfo() {
        Map<String, Object> contextMap = BATRIX_CONTEXT.get();
        if (contextMap == null) {
            return null;
        }
        Object infoObject = contextMap.get(CONTEXT_INFO_KEY);
        if(infoObject == null) {
            return BatrixInfo.builder().build();
        }
        if (infoObject instanceof BatrixInfo) {
            return (BatrixInfo) infoObject;
        }
        return BatrixInfo.builder().build();
    }

    public static String getOrder() {
        if(getBatrixInfo() == null) {
            return null;
        }
        return getBatrixInfo().getOrderNo();
    }

    public static String getBusinessUnit() {
        if(getBatrixInfo() == null) {
            return null;
        }
        return getBatrixInfo().getBusinessUnit();
    }


    public static void putString(String key, String value) {
        writeContext(key, value);
    }

    public static String getString(String key) {
        Map<String, Object> contextMap = BATRIX_CONTEXT.get();
        if (contextMap == null) {
            return null;
        }
        Object str = contextMap.get(key);
        if (str instanceof String) {
            return (String) str;
        }
        return null;
    }

    /**
     * 写入当前线程flowNode变更的LOG
     * @param changeLog
     */
    public static void addModelChangelog(String changeLog) {
        Map<String, Object> contextMap = BATRIX_CONTEXT.get();
        if (contextMap == null) {
            return;
        }
        Object currentLogObject = contextMap.get(FLOW_NODE_CHANGE_LOG_KEY);
        if (currentLogObject == null) {
            List<String> logList = new ArrayList<>();
            logList.add(changeLog);
            contextMap.put(FLOW_NODE_CHANGE_LOG_KEY, logList);
            return;
        }
        if (currentLogObject instanceof List) {
            List<String> currentLogList = (List<String>)currentLogObject;
            currentLogList.add(changeLog);
            contextMap.put(FLOW_NODE_CHANGE_LOG_KEY, currentLogList);
        } else {
            log.warn("写入model变更日志失败");
        }
    }

    public static List<String> getModelChangeLog() {
        Map<String, Object> contextMap = BATRIX_CONTEXT.get();
        Object currentLogObject = contextMap.get(FLOW_NODE_CHANGE_LOG_KEY);
        if(currentLogObject == null) {
            return null;
        }
        if (currentLogObject instanceof List) {
            return (List<String>)currentLogObject;
        }
        return null;
    }


    /**
     * 设置当前batrix的EXT扩展点名称
     * @param currentExtName 当前流程的扩展点
     */
    public static void setCurrentExtName(String currentExtName) {
         putString(CURRENT_EXECUTE_EXT, currentExtName);
    }

    /**
     * 获取最后一个执行当的ext 名称
     */
    public static String getCurrentExtName() {
        return getString(CURRENT_EXECUTE_EXT);
    }
}
