package com.jdl.sc.ofw.out.common.dict;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomsClearanceStatusEnum {
    UNDECLARED(0, "未申报"),
    IN_PROGRESS(1, "申报中"),
    SUCCESS(2, "清关成功"),
    FAILURE(3, "清关失败");

    private final int value;
    private final String desc;

    public static boolean contains(Integer value) {
        for (CustomsClearanceStatusEnum customsClearanceStatusEnum : values()) {
            if (customsClearanceStatusEnum.getValue() == value) {
                return true;
            }
        }
        return false;
    }

}
