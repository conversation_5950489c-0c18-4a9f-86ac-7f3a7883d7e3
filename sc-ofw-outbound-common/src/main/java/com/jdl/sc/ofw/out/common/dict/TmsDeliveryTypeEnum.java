package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 与城配紧密联系，是青龙B2B 则为城配
 */
@Getter
@Slf4j
public enum TmsDeliveryTypeEnum {
    TMS_B2C(0, "2", "青龙B2C", 3, false),
    TMS_B2B(1, "1", "青龙B2B", 1, true),
    TMS_B2B2C(2, "3", "可B可C（青龙自己判断）",2);
    private final int bdCode;
    @JsonValue
    private final String omsCode;
    private final String desc;
    /**
     * 事业部配置信息中，代表B网或C网的标记字段
     */
    private final Integer deptSendType;
    /**
     * 是否支持B2B网络配送
     */
    private final Boolean b2bCityDeliverySupport;

    TmsDeliveryTypeEnum(int bdCode, String omsCode, String desc, Integer deptSendType) {
        this.bdCode = bdCode;
        this.omsCode = omsCode;
        this.desc = desc;
        this.deptSendType = deptSendType;
        b2bCityDeliverySupport = null;
    }

    TmsDeliveryTypeEnum(int bdCode, String omsCode, String desc, Integer deptSendType, Boolean b2bCityDeliverySupport) {
        this.bdCode = bdCode;
        this.omsCode = omsCode;
        this.desc = desc;
        this.deptSendType = deptSendType;
        this.b2bCityDeliverySupport = b2bCityDeliverySupport;
    }

    @JsonCreator
    public static TmsDeliveryTypeEnum fromOmsCode(String omsCode) {
        for (TmsDeliveryTypeEnum operateType : values()) {
            if (operateType.getOmsCode().equals(omsCode)) {
                return operateType;
            }
        }
        // 默认青龙B2C,同somark62位默认为0
        return TMS_B2C;
    }

    public static TmsDeliveryTypeEnum fromBdCode(Integer bdCode) {
        if (bdCode == null) {
            return null;
        }
        for (TmsDeliveryTypeEnum operateType : values()) {
            if (operateType.getBdCode() == bdCode) {
                return operateType;
            }
        }
        // 默认青龙B2C,同somark62位默认为0
        return TMS_B2C;
    }

    public static TmsDeliveryTypeEnum fromDeptSendType(Integer deptSendType) {
        if (deptSendType == null) {
            return null;
        }
        for (TmsDeliveryTypeEnum deliveryTypeEnum : values()) {
            if (deptSendType.equals(deliveryTypeEnum.getDeptSendType())) {
                log.info("获取到配送网络枚举,deptSendType:{},tmsDeliveryType:{}", deptSendType, deliveryTypeEnum);
                return deliveryTypeEnum;
            }
        }
        return null;
    }

    public static TmsDeliveryTypeEnum fromStandardB2bSupport(Boolean b2bCityDeliverySupport) {
        if (b2bCityDeliverySupport == null) {
            log.info("是否支持B网配送服务为空，使用强制C网兜底");
            return TmsDeliveryTypeEnum.TMS_B2C;
        }
        for (TmsDeliveryTypeEnum deliveryTypeEnum : values()) {
            if (b2bCityDeliverySupport.equals(deliveryTypeEnum.getB2bCityDeliverySupport())) {
                log.info("获取到配送网络枚举,b2bCityDeliverySupport:{},tmsDeliveryType:{}", b2bCityDeliverySupport, deliveryTypeEnum);
                return deliveryTypeEnum;
            }
        }
        throw new IllegalArgumentException("未找到青龙配送网络！");
    }
}