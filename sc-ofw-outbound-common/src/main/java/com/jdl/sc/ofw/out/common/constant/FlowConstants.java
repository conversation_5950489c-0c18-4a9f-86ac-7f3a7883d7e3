package com.jdl.sc.ofw.out.common.constant;

public interface FlowConstants {


    /**
     * 出库单接单流程引擎
     */
    String JDL_SC_OUTBOUND_RECEIVE_ENGINE_CODE = "ScOfwOutboundReceiveEngine";
    String JDL_SC_OUTBOUND_RECEIVE_ENGINE_NAME = "仓配出库单接单接口引擎";


    /**
     * 出库单取消流程引擎
     */
    String JDL_SC_OUTBOUND_CANCEL_ENGINE_CODE = "ScOfwOutboundCancelEngine";
    String JDL_SC_OUTBOUND_CANCEL_ENGINE_NAME = "仓配出库单取消接口引擎";

    /**
     * 仓配出库单修改接口引擎
     */
    String JDL_SC_OUTBOUND_MODIFY_ENGINE_CODE = "ScOfwOutboundModifyEngine";
    String JDL_SC_OUTBOUND_MODIFY_ENGINE_NAME = "仓配出库单修改接口引擎";

    /**
     * 基本信息校验能力节点
     */
    String OUTBOUND_ORDER_STATUS_CHECK_FLOW_CODE = "OrderStatusCheckFlowNode";
    String OUTBOUND_ORDER_STATUS_CHECK_FLOW_NAME = "订单状态校验";

    /**
     * 履约阶段执行能力
     */
    String OUTBOUND_ORDER_ONESELF_EXECUTE_FLOW_CODE = "OneselfExecuteFlowNode";
    String OUTBOUND_ORDER_ONESELF_EXECUTE_FLOW_NAME = "履约阶段执行能力";

    /**
     * 基本信息校验能力节点
     */
    String OUTBOUND_BASIC_FLOW_CODE = "OUTBOUND_BASIC_FLOW_CODE";
    String OUTBOUND_BASIC_FLOW_NAME = "基础信息校验";

    /**
     * 出库暂停流程节点
     */
    String OUTBOUND_PAUSE_FLOW_CODE = "OUTBOUND_PAUSE_FLOW_CODE";
    String OUTBOUND_PAUSE_FLOW_NAME = "暂停流程节点";

    /**
     * 基本信息补全能力节点
     */
    String OUTBOUND_SUPPLY_BASIC_FLOW_CODE = "OUTBOUND_SUPPLY_BASIC_FLOW_CODE";
    String OUTBOUND_SUPPLY_BASIC_FLOW_NAME = "基本信息补全能力节点";

    /**
     * 出库下发运单流程节点
     */
    String OUTBOUND_TRANSPORT_TO_TMS_FLOW_CODE = "OUTBOUND_TRANSPORT_TO_TMS_FLOW_CODE";
    String OUTBOUND_TRANSPORT_TO_TMS_FLOW_NAME = "下发运单流程节点";

    /**
     * 出库运单执行流程节点
     */
    String OUTBOUND_TRANSPORT_TO_WAYBILL_FLOW_CODE = "OUTBOUND_TRANSPORT_TO_WAYBILL_FLOW_CODE";
    String OUTBOUND_TRANSPORT_TO_WAYBILL_FLOW_NAME = "运单执行能力";

    /**
     * 出库城配调度执行流程节点
     */
    String OUTBOUND_TRANSPORT_TO_DISPATCH_FLOW_CODE = "OUTBOUND_TRANSPORT_TO_DISPATCH_FLOW_CODE";
    String OUTBOUND_TRANSPORT_TO_DISPATCH_FLOW_NAME = "城配调度执行能力";

    /**
     * 城配控单能力节点
     */
    String CITY_DELIVERY_PAUSE_FLOW_NODE_CODE = "CITY_DELIVERY_PAUSE_FLOW_NODE";
    String CITY_DELIVERY_PAUSE_FLOW_NODE_NAME = "城配控单能力节点";

    /**
     * 出库下发仓流程节点
     */
    String OUTBOUND_TRANSPORT_TO_WMS_FLOW_CODE = "OUTBOUND_TRANSPORT_TO_WMS_FLOW_CODE";
    String OUTBOUND_TRANSPORT_TO_WMS_FLOW_NAME = "下发仓流程节点";


    /**
     * 合同物流 流程节点
     */
    String CONTRAC_TLOGISTICS_FLOW_CODE = "CONTRAC_TLOGISTICS_FLOW_CODE";
    String CONTRAC_TLOGISTICS_FLOW_NAME = "合同物流流程节点";

    /**
     * 物流服务 流程节点
     */
    String LOGISTIC_SERVICE_FLOW_CODE = "LOGISTIC_SERVICE_FLOW_CODE";
    String LOGISTIC_SERVICE_FLOW_NAME = "物流服务流程节点";

    /**
     * 增资服务流程节点
     */

    String VALUE_ADDED_SERVICE_FLOW_CODE = "VALUE_ADDED_SERVICE_FLOW_CODE";
    String VALUE_ADDED_SERVICE_FLOW_NAME = "增资服务流程节点";


    /**
     * 产品中心流程节点
     */

    String PRODUCT_CENTER_FLOW_CODE = "PRODUCT_CENTER_FLOW_CODE";
    String PRODUCT_CENTER_FLOW_NAME = "产品中心流程节点";

    /**
     * 时效hold单流程节点
     */
    String PROMISE_HOLD_ORDER_FLOW_CODE = "PROMISE_HOLD_ORDER_FLOW_CODE";
    String PROMISE_HOLD_ORDER_FLOW_NAME = "时效hold单流程节点";


    /**
     * 时效获取流程节点
     */
    String PROMISE_FLOW_CODE = "PROMISE_FLOW_CODE";
    String PROMISE_FLOW_NAME = "时效获取流程节点";

    /**
     * 状态回传流程节点
     */
    String STATUS_CALLBACK_FLOW_CODE = "STATUS_CALLBACK_FLOW_CODE";
    String STATUS_CALLBACK_FLOW_NAME = "状态回传流程节点";

    /**
     * 出库下发纯配运单流程节点
     */
    String OUTBOUND_TRANSPORT_TO_TMS_APPRAISAL_FLOW_CODE = "OUTBOUND_TRANSPORT_TO_TMS_APPRAISAL_FLOW_CODE";
    String OUTBOUND_TRANSPORT_TO_TMS_APPRAISAL_FLOW_NAME = "下发纯配运单流程节点";


    /**
     * 非标产品信息补齐节点
     */
    String SUPPLY_NON_STANDARD_PRODUCT_INFO_FLOW_CODE = "SUPPLY_NON_STANDARD_PRODUCT_INFO_FLOW_CODE";
    String SUPPLY_NON_STANDARD_PRODUCT_INFO_FLOW_NAME = "非标产品信息补齐节点";

    /**
     * 获取自定义围栏流程节点
     */
    String CUSTOM_PRESORT_FLOW_CODE = "CUSTOM_PRESORT_FLOW_CODE";
    String CUSTOM_PRESORT_FLOW_CODE_NAME = "自定义围栏";

    /**
     * 补齐时效信息流程节点
     */
    String COMPLETE_PROMISE_INFO_FLOW_CODE = "COMPLETE_PROMISE_INFO_FLOW_CODE";
    String COMPLETE_PROMISE_INFO_FLOW_NAME = "补齐时效信息";

    /**
     * 疫情时效暂停流程节点
     */
    String EPIDEMIC_PAUSE_FLOW_CODE = "EPIDEMIC_PAUSE_FLOW_CODE";
    String EPIDEMIC_PAUSE_FLOW_NAME = "疫情时效暂停";

    /**
     * 退仓类型处理流程节点
     * 退仓类型处理（退门店、退商家仓、就近退）
     */
    String OUTBOUND_REFUND_WAREHOUSE_TYPE_FLOW_CODE = "OUTBOUND_REFUND_WAREHOUSE_TYPE_FLOW_CODE";
    String OUTBOUND_REFUND_WAREHOUSE_TYPE_FLOW_NAME = "退仓类型处理流程节点";

    /**
     * 售后信息补全流程节点
     */
    String OUTBOUND_SUPPLY_AFTER_SALE_FLOW_CODE = "OUTBOUND_SUPPLY_AFTER_SALE_FLOW_CODE";
    String OUTBOUND_SUPPLY_AFTER_SALE_FLOW_NAME = "售后信息补全流程节点";

    /**
     * 收件人信息补全流程节点
     */
    String OUTBOUND_SUPPLY_CONSIGNEE_FLOW_CODE = "OUTBOUND_SUPPLY_CONSIGNEE_FLOW_CODE";
    String OUTBOUND_SUPPLY_CONSIGNEE_FLOW_NAME = "收件人信息补全流程节点";

    /**
     * 配送信息补全流程节点
     */
    String OUTBOUND_SUPPLY_SHIPMENT_FLOW_CODE = "OUTBOUND_SUPPLY_SHIPMENT_FLOW_CODE";
    String OUTBOUND_SUPPLY_SHIPMENT_FLOW_NAME = "配送信息补全流程节点";

    /**
     * 青龙业主号补全流程节点
     */
    String OUTBOUND_SUPPLY_BD_OWNER_NO_FLOW_CODE = "OUTBOUND_SUPPLY_BD_OWNER_NO_FLOW_CODE";
    String OUTBOUND_SUPPLY_BD_OWNER_NO_FLOW_NAME = "青龙业主号补全流程节点";

    /**
     * 通用流程控制节点
     */
    String GENERAL_PROCESS_CONTROL_FLOW_CODE = "GENERAL_PROCESS_CONTROL_FLOW_CODE";
    String GENERAL_PROCESS_CONTROL_FLOW_NAME = "通用流程控制节点";

    /**
     * 计费模式补全流程节点
     */
    String OUTBOUND_SUPPLY_BILLING_TYPE_FLOW_CODE = "OUTBOUND_SUPPLY_BILLING_TYPE_FLOW_CODE";
    String OUTBOUND_SUPPLY_BILLING_TYPE_FLOW_NAME = "计费模式补全流程节点";

    /**
     * 四级地址信息补全流程节点
     */
    String OUTBOUND_SUPPLY_CONSIGNEE_ADDRESS_FLOW_CODE = "OUTBOUND_SUPPLY_CONSIGNEE_ADDRESS_FLOW_CODE";
    String OUTBOUND_SUPPLY_CONSIGNEE_ADDRESS_FLOW_NAME = "四级地址信息补全流程节点";

    /**
     * 事业部信息补全流程节点
     */
    String OUTBOUND_SUPPLY_DEPT_INFO_FLOW_CODE = "OUTBOUND_SUPPLY_DEPT_INFO_FLOW_CODE";
    String OUTBOUND_SUPPLY_DEPT_INFO_FLOW_NAME = "事业部信息补全流程节点";

    /**
     * 包裹生产字段补全流程节点
     */
    String OUTBOUND_SUPPLY_PRODUCT_BY_PACK_FLOW_CODE = "OUTBOUND_SUPPLY_PRODUCT_BY_PACK_FLOW_CODE";
    String OUTBOUND_SUPPLY_PRODUCT_BY_PACK_FLOW_NAME = "包裹生产字段补全流程节点";

    /**
     * 店铺信息补全流程节点
     */
    String OUTBOUND_SUPPLY_SHOP_INFO_FLOW_CODE = "OUTBOUND_SUPPLY_SHOP_INFO_FLOW_CODE";
    String OUTBOUND_SUPPLY_SHOP_INFO_FLOW_NAME = "店铺信息补全流程节点";

    /**
     * 库房信息补全流程节点
     */
    String OUTBOUND_SUPPLY_WAREHOUSE_FLOW_CODE = "OUTBOUND_SUPPLY_WAREHOUSE_FLOW_CODE";
    String OUTBOUND_SUPPLY_WAREHOUSE_FLOW_NAME = "库房信息补全流程节点";

    /**
     * 根据事业部id和增值服务key补全增值服务流程节点
     */
    String OUTBOUND_VALUE_ADDED_SERVICE_FLOW_CODE = "OUTBOUND_VALUE_ADDED_SERVICE_FLOW_CODE";
    String OUTBOUND_VALUE_ADDED_SERVICE_FLOW_NAME = "增值服务补全流程节点";

    /**
     * 标记城配流程节点
     */
    String MARKING_DISPATCH_FLOW_CODE = "MARKING_DISPATCH_FLOW_CODE";
    String MARKING_DISPATCH_FLOW_NAME = "标记城配信息节点";


    /**
     * 地址信息校验节点
     */
    String ADDRESS_CHECK_FLOW_CODE = "AddressCheckFlowNode";
    String ADDRESS_CHECK_FLOW_NAME = "地址信息校验节点";

    /**
     * 地址信息解析节点
     */
    String ADDRESS_RESOLUTION_FLOW_CODE = "ADDRESS_RESOLUTION_FLOW_CODE";
    String ADDRESS_RESOLUTION_FLOW_NAME = "地址解析节点";

    /**
     * 承运商基础信息补全节点
     */
    String SUPPLY_SHIPPER_INFO_FlOW_CODE = "SUPPLY_SHIPPER_INFO_FlOW_CODE";
    String SUPPLY_SHIPPER_INFO_FlOW_NAME = "承运商基础信息补全节点";

    /**
     * 特殊产品补全节点
     */
    String SUPPLY_SPECIAL_PRODUCT_FLOW_CODE = "SUPPLY_SPECIAL_PRODUCT_FLOW_CODE";
    String SUPPLY_SPECIAL_PRODUCT_FLOW_NAME = "特殊产品补全节点";

    /**
     * 三方产品补全节点
     */
    String SUPPLY_THIRD_PRODUCT_FLOW_CODE = "SUPPLY_THIRD_PRODUCT_FLOW_CODE";
    String SUPPLY_THIRD_PRODUCT_FLOW_NAME = "三方产品补全节点";

    /**
     * 4PL运单号补全流程节点
     */
    String SUPPLY_4PL_WAYBILL_FLOW_CODE = "SUPPLY_4PL_WAYBILL_FLOW_CODE";
    String SUPPLY_4PL_WAYBILL_FLOW_NAME = "4PL运单号补全流程节点";

    /**
     * 商品信息补全节点
     */
    String OUTBOUND_SUPPLY_GOODS_INFO_FLOW_CODE = "OUTBOUND_SUPPLY_GOODS_INFO_FLOW_CODE";
    String OUTBOUND_SUPPLY_GOODS_INFO_FLOW_NAME = "商品信息补全节点";

    /**
     * 4PL公司信息补全流程节点
     */
    String SUPPLY_4PL_COMPANY_FLOW_CODE = "SUPPLY_4PL_COMPANY_FLOW_CODE";
    String SUPPLY_4PL_COMPANY_FLOW_NAME = "4PL公司信息补全流程节点";

    /**
     * 回传4PL流程节点
     */
    String CALLBACK_4PL_FLOW_CODE = "CALLBACK_4PL_FLOW_CODE";
    String CALLBACK_4PL_FLOW_NAME = "回传4PL节点";

    /**
     * 订单跟踪订阅节点
     */
    String ORDER_TRACE_SUBSCRIBE_FLOW_CODE = "ORDER_TRACE_SUBSCRIBE_FLOW_CODE";
    String ORDER_TRACE_SUBSCRIBE_FLOW_NAME = "订单跟踪订阅能力";

    /**
     * 4PL明文信息补全流程节点
     */
    String SUPPLY_4PL_DECRYPT_MESSAGE_FLOW_CODE = "SUPPLY_4PL_DECRYPT_MESSAGE_FLOW_CODE";
    String SUPPLY_4PL_DECRYPT_MESSAGE_FLOW_NAME = "4PL明文信息补全流程节点";

    /**
     * 承运商分单-计算承运商节点
     */
    String SHIP_SPLIT_FLOW_CODE = "SHIP_SPLIT_FLOW_CODE";
    String SHIP_SPLIT_FLOW_NAME = "承运商分单-计算承运商节点";

    /**
     * 承运商分单-保价扩展点
     */
    String SHIP_SPLIT_INSURE_PRICE_FLOW_CODE = "SHIP_SPLIT_INSURE_PRICE_FLOW_CODE";
    String SHIP_SPLIT_INSURE_PRICE_FLOW_NAME = "承运商分单-保价扩展点";

    /**
     * 承运商分单-产品适配扩展点
     */
    String SHIP_SPLIT_DELIVERY_PRODUCT_FLOW_CODE = "SHIP_SPLIT_DELIVERY_PRODUCT_FLOW_CODE";
    String SHIP_SPLIT_DELIVERY_PRODUCT_FLOW_NAME = "承运商分单-产品适配扩展点";

    /**
     * 承运商分单-青龙业主号扩展点
     */
    String SHIP_SPLIT_BD_OWNER_NO_FLOW_CODE = "SHIP_SPLIT_BD_OWNER_NO_FLOW_CODE";
    String SHIP_SPLIT_BD_OWNER_NO_FLOW_NAME = "承运商分单-青龙业主号扩展点";

    /**
     * 业务拦截流程 节点
     */
    String INTERCEPT_TO_WAYBILL_FLOW_CODE = "INTERCEPT_TO_WAYBILL_FLOW_CODE";
    String INTERCEPT_TO_WAYBILL_FLOE_NAME = "运单拦截流程节点";

    /**
     * 订单暂停-执行为完成节点
     */
    String ORDER_PAUSE_DONE_FLOW_CODE = "ORDER_PAUSE_DONE_FLOW_CODE";
    String ORDER_PAUSE_DONE_FLOW_NAME = "订单暂停-执行为完成节点";

    /**
     * 通用订单暂停节点
     */
    String GENERAL_PAUSE_FLOW_CODE = "GENERAL_PAUSE_FLOW_CODE";
    String GENERAL_PAUSE_FLOW_NAME = "通用订单暂停节点";

    /**
     * 零售台账节点 节点
     */
    String RETAIL_FINANCE_FLOW_CODE = "RETAIL_FINANCE_FLOW_CODE";
    String RETAIL_FINANCE_FLOW_NAME = "零售台账节点";

    /**
     * 下仓前订单暂停节点
     */
    String PAUSE_BEFORE_TO_WMS_FLOW_CODE = "PAUSE_BEFORE_TO_WMS_FLOW_CODE";
    String PAUSE_BEFORE_TO_WMS_FLOW_NAME = "下仓前订单暂停节点";

    /**
     * 盘点暂停节点
     */
    String INVENTORY_PAUSE_FLOW_CODE = "INVENTORY_PAUSE_FLOW_CODE";
    String INVENTORY_PAUSE_FLOW_NAME = "盘点暂停节点";

    /**
     * 补齐期望配送资源节点
     */
    String SUPPLY_EXPECT_DELIVERY_RESOURCE_FLOW_NODE_CODE = "SupplyExpectDeliveryResource";
    String SUPPLY_EXPECT_DELIVERY_RESOURCE_FLOW_NODE_NAME = "补齐期望配送资源节点";

    /**
     * 免赔信息补充节点
     */
    String SUPPLY_DISCLAIMER_INFO_FLOW_CODE = "SUPPLY_DISCLAIMER_INFO_FLOW_CODE";
    String SUPPLY_DISCLAIMER_INFO_FLOW_NAME = "免赔信息补充节点";

    /**
     * 关联单号信息补充节点
     */
    String SUPPLY_REF_ORDER_INFO_FLOW_CODE = "SUPPLY_REF_ORDER_INFO_FLOW_CODE";
    String SUPPLY_REF_ORDER_INFO_FLOW_NAME = "关联单信息补充节点";

    /**
     * 订单持久化节点
     */
    String ORDER_PERSISTENCE_FLOW_CODE = "ORDER_PERSISTENCE_FLOW_CODE";
    String ORDER_PERSISTENCE_FLOW_NAME = "订单持久化节点";


    /**
     * hold单释放节点
     */
    String ORDER_HOLD_RELEASE_FLOW_CODE = "ORDER_HOLD_RELEASE_FLOW_CODE";
    String ORDER_HOLD_RELEASE_FLOW_NAME = "hold单释放节点";

    /**
     * 集单流程节点
     */
    String COLLECT_ORDER_FLOW_CODE = "COLLECT_ORDER_FLOW_CODE";
    String COLLECT_ORDER_FLOW_NAME = "集单流程节点";

    /**
     * 清关信息补充节点
     */
    String SUPPLY_CUSTOMS_CLEARANCE_FLOW_CODE = "SUPPLY_CUSTOMS_CLEARANCE_FLOW_CODE";
    String SUPPLY_CUSTOMS_CLEARANCE_FLOW_NAME = "清关信息补充节点";

    /**
     * 通知节点
     */
    String NOTIFY_FLOW_CODE = "NOTIFY_FLOW_CODE";
    String NOTIFY_FLOW_NAME = "通知节点";

    /**
     * 修改运单时效节点
     */
    String MODIFY_WAYBILL_PROMISE_FLOW_NODE = "MODIFY_WAYBILL_PROMISE_FLOW_NODE";
    String MODIFY_WAYBILL_PROMISE_FLOW_NAME = "修改运单时效节点";

    /**
     * 履约信息补全流程节点
     */
    String OUTBOUND_SUPPLY_FULFILLMENT_FLOW_CODE = "OUTBOUND_SUPPLY_FULFILLMENT_FLOW_CODE";
    String OUTBOUND_SUPPLY_FULFILLMENT_FLOW_NAME = "履约信息补全流程节点";

    /**
     * 预分捡校验节点
     */
    String PRE_PAYMENT_VALIDATE_FLOW_CODE = "PRE_PAYMENT_VALIDATE_FLOW_CODE";
    String PRE_PAYMENT_VALIDATE_FLOW_NAME = "预收款校验节点";

    /**
     * 运力分单节点
     */
    String CAPACITY_SPLIT_ORDER_FLOW_CODE = "CAPACITY_SPLIT_ORDER_FLOW_CODE";
    String CAPACITY_SPLIT_ORDER_FLOW_NAME = "运力分单节点";

    /**
     * 校验和补齐明细维度增值服务项节点
     */
    String SUPPLY_CARGO_SERVICE_INFO_FLOW_CODE = "SUPPLY_CARGO_SERVICE_INFO_FLOW_CODE";
    String SUPPLY_CARGO_SERVICE_INFO_FLOW_NAME = "明细维度增值服务项补全节点";

    /**
     * 订单信息同步回传节点
     */
    String OUTBOUND_ORDER_SYNC_CALLBACK_FLOW_NAME = "订单信息同步回传节点";
    String OUTBOUND_ORDER_SYNC_CALLBACK_FLOW_CODE = "OUTBOUND_ORDER_SYNC_CALLBACK_FLOW_CODE";

    /**
     * 订单信息同步回传节点
     */
    String OUTBOUND_ORDER_CALLBACK_FLOW_NAME = "订单信息同步回传节点";
    String OUTBOUND_ORDER_CALLBACK_FLOW_CODE = "OUTBOUND_ORDER_CALLBACK_FLOW_CODE";

    /**
     * 预约派送节点
     */
    String APPOINTMENT_DELIVERY_FLOW_CODE = "PRE_PAYMENT_VALIDATE_FLOW_CODE";
    String APPOINTMENT_DELIVERY_FLOW_NAME = "预约派送节点";

    /**
     * 仓配揽收调度信息补齐
     */
    String DELIVERY_COLLECTION_FLOW_CODE = "DELIVERY_COLLECTION_FLOW_CODE";
    String DELIVERY_COLLECTION_FLOW_NAME = "仓配揽收调度信息补齐节点";

    /**
     * 下仓前阶段hold单节点
     */
    String HOLD_BEFORE_WMS_STAGE_FLOW_CODE = "HOLD_BEFORE_WMS_STAGE_FLOW_CODE";
    String HOLD_BEFORE_WMS_STAGE_FLOW_NAME = "下仓前阶段hold单节点";

    /**
     * 下配前阶段hold单节点
     */
    String HOLD_BEFORE_TMS_STAGE_FLOW_CODE = "HOLD_BEFORE_TMS_STAGE_FLOW_CODE";
    String HOLD_BEFORE_TMS_STAGE_FLOW_NAME = "下配前阶段hold单节点";

    /**
     * 后置产品信息补全节点
     */
    String SUPPLY_REAR_PRODUCT_INFO_FLOW_CODE = "SUPPLY_REAR_PRODUCT_INFO_FLOW_CODE";
    String SUPPLY_REAR_PRODUCT_INFO_FLOW_NAME = "后置产品信息补全节点";
    /**
     * 补齐预制运单号节点
     */
    String SUPPLY_PRE_WAYBILL_NO_FLOW_CODE = "SUPPLY_PRE_WAYBILL_NO_FLOW_CODE";
    String SUPPLY_PRE_WAYBILL_NO_FLOW_NAME = "补齐预制运单号节点";

    /**
     * 补齐采购单号节点
     */
    String SUPPLY_PURCHASE_NO_FLOW_CODE = "SUPPLY_PURCHASE_NO_FLOW_CODE";
    String SUPPLY_PURCHASE_NO_FLOW_NAME = "补齐采购单号节点";

    /**
     * 附加费信息补全节点
     */
    String ATTACH_FEE_INFO_FLOW_CODE = "ATTACH_FEE_INFO_FLOW_CODE";
    String ATTACH_FEE_INFO_FLOW_NAME = "附加费信息补全节点";

    /**
     * 承运商及产品分单节点
     */
    String SHIPMENT_AND_PRODUCT_SPLIT_FLOW_CODE = "SHIPMENT_AND_PRODUCT_SPLIT_FLOW_CODE";
    String SHIPMENT_AND_PRODUCT_SPLIT_FLOW_NAME = "承运商及产品分单节点";

    /**
     * 配送网络匹配节点
     */
    String DELIVERY_NETWORK_MATCH_FLOW_CODE = "DELIVERY_NETWORK_MATCH_FLOW_CODE";
    String DELIVERY_NETWORK_MATCH_FLOW_NAME = "配送网络匹配节点";
    /**
     * 配送信息补全节点
     */
    String DELIVERY_INFO_FLOW_CODE = "DELIVERY_INFO_FLOW_CODE";
    String DELIVERY_INFO_FLOW_NAME = "配送信息补全";

    /**
     * 下仓后订单暂停节点
     */
    String PAUSE_AFTER_WMS_FLOW_CODE = "PAUSE_AFTER_WMS_FLOW_CODE";
    String PAUSE_AFTER_WMS_FLOW_NAME = "下仓后订单暂停节点";

    /**
     * 城配资源补齐节点
     */
    String ACTIVE_CITY_DELIVERY_FLOW_CODE = "ACTIVE_CITY_DELIVERY_FLOW_CODE";
    String ACTIVE_CITY_DELIVERY_FLOW_NAME = "城配资源补齐节点";

    /**
     * 复用历史运单号节点
     */
    String REUSE_HISTORICAL_WAYBILL_FLOW_CODE = "REUSE_HISTORICAL_WAYBILL_FLOW_CODE";
    String REUSE_HISTORICAL_WAYBILL_FLOW_NAME = "复用历史运单号";

    /**
     * 重量体积标准化节点
     */
    String WEIGHT_VOLUME_STANDARDIZATION_FLOW_CODE = "WEIGHT_VOLUME_STANDARDIZATION_FLOW_CODE";
    String WEIGHT_VOLUME_STANDARDIZATION_FLOW_NAME = "重量体积标准化节点";

    /**
     * 牧童集单节点
     */
    String MU_TONG_TRANSPORT_FLOW_CODE = "MU_TONG_TRANSPORT_FLOW_CODE";
    String MU_TONG_TRANSPORT_FLOW_NAME = "下发牧童系统节点";

    /**
     * 获取平台运单执行流程节点
     */
    String OUTBOUND_RECEIVE_PLATFORM_WAYBILL_FLOW_CODE = "OUTBOUND_RECEIVE_PLATFORM_WAYBILL_FLOW_CODE";
    String OUTBOUND_RECEIVE_PLATFORM_WAYBILL_FLOW_NAME = "获取平台运单能力";

    /**
     * 获取平台运单执行流程节点
     */
    String OUTBOUND_DIVERSION_SELECTION_PROCESS_FLOW_CODE = "OUTBOUND_DIVERSION_SELECTION_PROCESS_FLOW_CODE";
    String OUTBOUND_DIVERSION_SELECTION_PROCESS_FLOW_NAME = "切量分流处理能力-平台运单";

    /**
     * 送装服务流程节点
     */
    String DELIVERY_INSTALLATION_FLOW_CODE = "DELIVERY_INSTALLATION_FLOW_CODE";
    String DELIVERY_INSTALLATION_FLOW_NAME = "送装服务流程节点";

    /**
     * 三方国补信息补全
     */
    String SUPPLY_THIRD_SUBSIDY_FLOW_CODE = "SUPPLY_THIRD_SUBSIDY_FLOW_CODE";
    String SUPPLY_THIRD_SUBSIDY_FLOW_NAME = "三方国补信息补全";

    /**
     * 勘测服务单
     */
    String SURVEY_SERVICE_ORDER_FLOW_CODE = "SURVEY_SERVICE_ORDER_FLOW_CODE";
    String SURVEY_SERVICE_ORDER_FLOW_NAME = "勘测服务单";
}
