package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 三方承运商类型
 */
@Getter
public enum ThirdShipTypeEnum {

    EXPRESS((byte) 1, "快递"),
    FREIGHT((byte) 2, "快运"),
    INSTALL((byte) 3, "安装"),
    FRESH((byte) 4, "生鲜"),
    ;

    @JsonValue
    private final byte type;
    private final String desc;

    ThirdShipTypeEnum(byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }


}
