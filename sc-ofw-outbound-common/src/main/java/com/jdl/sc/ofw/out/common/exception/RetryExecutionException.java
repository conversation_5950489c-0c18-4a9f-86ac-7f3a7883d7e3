package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

import javax.validation.constraints.NotNull;

/**
 * FileName: RetryExecutionException
 * Description: 异步消息重试异常
 * [@author]:   quh<PERSON><PERSON>
 * [@date]:     2024/9/916:19
 */
public class RetryExecutionException extends AbstractException {

    public RetryExecutionException(OutboundErrorSpec errorReason) {
        super(errorReason);
    }
    public RetryExecutionException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }
}
