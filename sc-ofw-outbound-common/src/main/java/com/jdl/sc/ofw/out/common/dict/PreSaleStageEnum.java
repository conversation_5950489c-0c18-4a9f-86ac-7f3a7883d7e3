package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PreSaleStageEnum {
    DEFAULT(0, "非预售单", true),
    FRONT_MONEY(1, "定金阶段", false),
    REST_MONEY(2, "尾款阶段", true),
    ;
    private final String name;
    @JsonValue
    private final int code;

    private final boolean canChangeToMe;

    PreSaleStageEnum(int code, String name, boolean canChangeToMe) {
        this.name = name;
        this.code = code;
        this.canChangeToMe = canChangeToMe;
    }

    @JsonCreator
    public static PreSaleStageEnum fromCode(final Integer code) {
        if (code == null) {
            return DEFAULT;
        }
        for (final PreSaleStageEnum anEnum : values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return DEFAULT;
    }

    /**
     * 根据枚举判断是预售单
     *
     * @param preSaleStageEnum
     * @return
     */
    public static boolean isPreSale(PreSaleStageEnum preSaleStageEnum) {
        if (preSaleStageEnum == null) {
            return false;
        }
        return !PreSaleStageEnum.DEFAULT.equals(preSaleStageEnum);
    }

}
