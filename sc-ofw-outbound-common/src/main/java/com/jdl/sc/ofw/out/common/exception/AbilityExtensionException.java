package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
public class AbilityExtensionException extends AbstractException {
    public AbilityExtensionException(OutboundErrorSpec errorReason) {
        super(errorReason);
    }

    public AbilityExtensionException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }
}
