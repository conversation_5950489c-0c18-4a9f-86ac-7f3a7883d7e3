package com.jdl.sc.ofw.out.common.dict;

import lombok.Getter;

import java.util.StringJoiner;

/**
 * 微笑面单类型。3类不同的选择，共计8种组合方式。每个二进制位代表一种选择。
 * 2位       1位       0位
 * 隐藏地址 隐藏电话 隐藏姓名
 */
@Getter
public enum SmileWaybillTypeEnum {
    HIDE_NONE(0, "不隐藏"),
    HIDE_NAME(1, "隐藏姓名"),
    HIDE_PHONE(2, "隐藏电话"),
    HIDE_NAME_AND_PHONE(3, "隐藏姓名和电话"),
    HIDE_ADDRESS(4, "隐藏地址"),
    HIDE_NAME_AND_ADDRESS(5, "隐藏姓名和地址"),
    HIDE_PHONE_AND_ADDRESS(6, "隐藏电话和地址"),
    HIDE_ALL(7, "隐藏全部"),
    NO_REQUIREMENT(8, "无诉求")
    ;

    /**
     * 青龙对应的面单类型码
     */
    private final int bdCode;
    private final String desc;

    SmileWaybillTypeEnum(int bdCode, String desc) {
        this.bdCode = bdCode;
        this.desc = desc;
    }

    public static SmileWaybillTypeEnum fromCode(int code) {
        for (SmileWaybillTypeEnum smileType : values()) {
            if (smileType.getBdCode() == code) {
                return smileType;
            }
        }

        throw new IllegalArgumentException("未识别的微笑面单类型" + code);
    }

    public static SmileWaybillTypeEnum fromHiddenContent(String hiddenContent) {
        if (hiddenContent.equals(ProductCenterConstants.NO_REQUIREMENT_ATTRIBUTE)) {
            return NO_REQUIREMENT;
        }

        int bdCode = 0;
        if (hiddenContent.contains(ProductCenterConstants.HIDE_RECEIVE_NAME)) {
            bdCode = bdCode | 1;
        }

        if (hiddenContent.contains(ProductCenterConstants.HIDE_MOBILE_ATTRIBUTE)) {
            bdCode = bdCode | (1 << 1);
        }

        if (hiddenContent.contains(ProductCenterConstants.HIDE_ADDRESS_ATTRIBUTE)) {
            bdCode = bdCode | (1 << 2);
        }

        return fromCode(bdCode);
    }

    /**
     * 是否隐藏姓名
     */
    public boolean shouldHideName() {
        return (this.bdCode & (1 << 0)) > 0;
    }

    /**
     * 是否隐藏电话
     */
    public boolean shouldHidePhone() {
        return (this.bdCode & (1 << 1)) > 0;
    }

    /**
     * 是否隐藏地址
     */
    public boolean shouldHideAddress() {
        return (this.bdCode & (1 << 2)) > 0;
    }


    public String toHiddenContent() {
        if (this == NO_REQUIREMENT) {
            return ProductCenterConstants.NO_REQUIREMENT_ATTRIBUTE;
        }

        StringJoiner joiner = new StringJoiner(",");
        if (shouldHideAddress()) {
            joiner.add("\"" + ProductCenterConstants.HIDE_ADDRESS_ATTRIBUTE + "\"");
        }

        if (shouldHidePhone()) {
            joiner.add("\"" + ProductCenterConstants.HIDE_MOBILE_ATTRIBUTE + "\"");
        }

        if (shouldHideName()) {
            joiner.add("\"" + ProductCenterConstants.HIDE_RECEIVE_NAME + "\"");
        }

        return ProductCenterConstants.LEFT_BRACKET + joiner.toString() + ProductCenterConstants.RIGHT_BRACKET;
    }

    /**
     * 产品中心标准要素格式：https://cf.jd.com/pages/viewpage.action?pageId=465557380
     */
    private static class ProductCenterConstants {
        public static final String NO_REQUIREMENT_ATTRIBUTE = "noRequirement"; //不属于标准产品定义，为了兼容不确定微笑要求，需要外单计算的场景
        public static final String HIDE_ADDRESS_ATTRIBUTE = "receiveAddress";
        public static final String HIDE_MOBILE_ATTRIBUTE = "receiverMobile";
        public static final String HIDE_RECEIVE_NAME = "receiverName";
        public static final String LEFT_BRACKET = "[";
        public static final String RIGHT_BRACKET = "]";
    }


}
