package com.jdl.sc.ofw.out.common.exception;


import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

import javax.validation.constraints.NotNull;

/**
 * 领域服务异常定义统一输出
 */
public class DomainServiceException extends AbstractException {

    public DomainServiceException(OutboundErrorSpec errorReason) {
        super(errorReason);
    }

    public DomainServiceException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }
}
