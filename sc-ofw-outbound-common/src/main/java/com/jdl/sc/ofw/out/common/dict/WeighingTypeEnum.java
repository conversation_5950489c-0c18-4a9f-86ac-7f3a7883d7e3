package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @RefSo("soMain.somark123")
 * 0： 不需要采集实际称重  1： 需要采集实际称重  2： 系统自动称重
 * @date 2023-04-18 13:09
 */
@Getter
public enum WeighingTypeEnum {
    NOT_WEIGHING(0, "不需要采集实际称重"),
    WEIGHING_COLLECT(1, "需要采集实际称重"),
    AUTO_WEIGHING_COLLECT(2, "系统自动称重");

    private final String name;
    @JsonValue
    private final Integer code;

    WeighingTypeEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    @JsonCreator
    public static WeighingTypeEnum fromCode(Integer code){
        if(code == null){
            return NOT_WEIGHING;
        }
        for(final WeighingTypeEnum anEnum : values()){
            if(anEnum.getCode() == code){
                return anEnum;
            }
        }
        return NOT_WEIGHING;
    }
}
