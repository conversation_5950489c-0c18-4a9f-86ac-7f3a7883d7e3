package com.jdl.sc.ofw.out.common.dict;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

public final class UnifiedErrorSpec {
    public enum BasisOrder implements OutboundErrorSpec {
        INTERNAL_ERROR("10000", "系统内部错误"),
        ;

        private String code;
        private String desc;

        BasisOrder(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @Override
        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum ACL implements OutboundErrorSpec {
        RETURN_EXCEPTION("20000", "外部调用返回错误"),
        EMPTY_RESULT("20001", "外部调用返回为空"),
        CALL_EXCEPTION("20003", "外部调用接口异常"),
        /**
         * 远程接口调用超时，此异常映射到OFW后可以自动重试
         */
        RPC_TIMEOUT_EXCEPTION("20004", "外部调用接口超时，需要重试"),
        ;
        private String code;
        private String desc;

        ACL(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @Override
        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum Business implements OutboundErrorSpec {
        /**
         * 主业务异常级别
         */
        ORDER_ADAPT_EXCEPTION("30000", "订单适配异常"),
        LOCK_ACQUIRE_EXCEPTION("30010", "订单获取锁异常"),
        OUT_LOCK_ACQUIRE_EXCEPTION("30011", "外部服务订单获取锁异常"),
        ORDER_BUSINESS_EXCEPTION("30020", "订单业务异常"),
        ORDER_SYSTEM_EXCEPTION("30030", "订单系统异常"),
        RESOLUTION_EXCEPTION("30040", "四级地址未解析"),
        PERSISTENCE_EXCEPTION("30050", "持久化异常"),
        HOLD_PERSISTENCE_EXCEPTION("30060", "hold单，持久化异常"),
        HOLD_PERSISTENCE_STATUS_EXCEPTION("30070", "hold单，状态持久化异常"),
        ORDER_EXISTS("30080", "订单已存在"),
        ORDER_NO_EXISTS("30081", "订单不存在"),
        STATUS_PERSISTENCE_EXCEPTION("30090", "状态更新，持久化异常"),
        ORDER_CONCURRENT_CANCEL("30100", "订单正在取消中，不可以并发取消"),
        PRINT_TEMPLATE_EXCEPTION("30110", "打印模板处理异常"),
        PRINT_TEMPLATE_PLACEHOLDER_EXCEPTION("30111", "打印模板占位符处理异常"),

        ORDER_CANCELLED("30112", "订单已是取消状态"),
        ORDER_CANCEL_WMS_FAILED("30113", "WMS取消失败"),

        AB_MOCK_EXCEPTION("30114", "AB环境异常"),
        WAYBILL_NO_EXISTS("30115", "运单号不存在"),
        MODIFY_FAILED("30116", "修改失败"),
        STOP_EXCEPTION("30117", "流程停止异常"),
        WAYBILL_NOT_CANCEL("30118", "运单取消失败，请稍后重试"),
        ;

        private String code;
        private String desc;

        Business(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @Override
        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }
}
