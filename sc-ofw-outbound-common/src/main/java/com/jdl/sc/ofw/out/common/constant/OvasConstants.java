package com.jdl.sc.ofw.out.common.constant;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 产品代码
 */
public class OvasConstants {

    /**
     * @Description: 产品中心调用成功code
     */
    public static final int RPC_RESPONSE_SUCCESS = 1;

    /**
     * 特殊运营保障
     */
    public static final String SPECIAL_SAFEGUARD = "specialSafeguards";

    /**
     * 根据事业部查询POP订单京东配送(青龙和大件等)是否打印出库交接单
     * somark107
     * SO_MARK_HUNDRED_SEVEN_0(107, '0', "不打印"),
     * SO_MARK_HUNDRED_SEVEN_1(107, '1', "打印"),
     */
    public static final String PRINT_OUT_STORAGE_BILLS = "deliverylistForJD";

    /**
     * 开通耗材服务
     * 1 开通
     * 0 未开通
     */
    public static final String OPEN_MATERIAL_SERVICE = "conre";

    // 是否走云打印2.0配置中心的配置项
    public static final String CLOUD_PRINT_II_PARAM_KEY = "SoCloudPrint";

    /**
     * 包裹半收
     * 1 开通
     */
    public static final String PACKAGE_HALF = "package";

    public static final String GAIN_MAJOR_PART = "gainMajorPart";

    /**
     * 是否hold单不下发wms配置项
     */
    public static final String ECLP_HOLD_ORDER = "eclpHoldOrder";

    /**
     * 是否信任商家的运输方式配置
     */
    public static final String PRODUCT_SELLER_TRANSPORT = "Sellertransport";

    /**
     * 拼多多一单多包裹配置项
     */
    public static final String PDD_MULTIPLE_PKG_KEY = "pddMultiplePkg";

    /**
     * 拼多多特殊京配商家配置
     */
    public static final String PDD_JD_SERVICE = "PddJdService";

    /**
     * 婴贝儿B2C 京东大网配送
     * 1开启
     */
    public static final String IS_YBR_B2C_BIGSITE = "isB2CBigSiteDelivery";

    /**
     * 运力分单
     * 1 开启
     */
    public static final String CAPACITY = "isCapacitySplitting";

    /**
     * 签单返还读取规则
     * 0-不使用；1-使用
     */
    public static final String USE_SIGN_BILL_BACK_READ_RULES_KEY = "isReceiptModel";

    /**
     * 是否信任商家指定预计送达时间
     */
    public static final String TRUST_CUSTOMER_AGING_KEY = "trustCustomerAging";

    /**
     * 出库计费商品成品/原材料 标识
     */
    public static final String OUT_CHARGE_PRODUCT_OR_MATERIAL = "outChargeProductOrMateral";

    /**
     * 使用明文获取运单号的事业部
     * 0-否；1-是
     */
    public static final String PLAINTEXT_DEPT = "DYplaintext";
    public static final String PLAINTEXT_DEPT_YES = "1";
    public static final String PLAINTEXT_DEPT_KWAI = "2";

    /**
     * 仓运协同模式
     * 0 - 先运调度后仓生产
     * 1 - 先仓生产后运调度
     */
    public static final String WMS_DISTRIBUTION_SYNERGY_MODEL = "wmsDistributionSynergyModel";
    public static final String DISTRIBUTION_GO_FIRST = "0";
    public static final String WMS_GO_FIRST = "1";

    /**
     * 送装服务模式配置
     * 0-无安装，1-送装分离，2-送装一体
     */
    public static final String DELIVERY_INSTALLATION_MODE = "deliveryInstallationMode";
    public static final String NO_INSTALLATION = "0";
    public static final String DELIVERY_OR_INSTALLATION = "1";
    public static final String DELIVERY_AND_INSTALLATION = "2";

    public static final String CHOOSE_TIME_DELIVERY_SERVICE = "chooseTimeDeliveryService";

    public static List<String> props = new ArrayList<>();

    static {
        props.add(PRINT_OUT_STORAGE_BILLS);
        props.add(OPEN_MATERIAL_SERVICE);
        props.add(SPECIAL_SAFEGUARD);
        props.add(PACKAGE_HALF);
        props.add(GAIN_MAJOR_PART);
        props.add(CAPACITY);
    }

    public static final String ENROLLED = "1";

    public static boolean isEnrolled(Map<String, String> values, String key) {
        return values != null &&
                values.containsKey(key) &&
                OvasConstants.ENROLLED.equals(values.get(key));
    }

    public static boolean isEnrolled(String value) {
        return value != null &&
                OvasConstants.ENROLLED.equals(value);
    }
}
