package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum HalfReceiveEnum {
    NOT_ALLOWED(1, "不允许半收", 0, '0'),
    NORMAL(2, "允许半收", 1, '7'),
    PACKAGE(3, "包裹半收", 2, '8'),
    ITEM(4, "明细半收", 3, '9');

    @JsonValue
    private final int code;
    private final String desc;
    private final int bdCode;
    private final char soMark;


    HalfReceiveEnum(final int code, final String desc, final int bdCode, final char soMark) {
        this.code = code;
        this.desc = desc;
        this.bdCode = bdCode;
        this.soMark = soMark;
    }

    @JsonCreator
    public static HalfReceiveEnum fromCode(final Integer code) {
        if (code == null) {
            return NOT_ALLOWED;
        }
        for (final HalfReceiveEnum anEnum : values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return NOT_ALLOWED;
    }
}
