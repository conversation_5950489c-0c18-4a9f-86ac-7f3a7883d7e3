package com.jdl.sc.ofw.out.common.dict;

import lombok.Getter;

@Getter
public enum JitTypeEnum {
    JIT_OUT((byte)0, "0或null：按JIT出库"),
    PLAN_TIME_OUT((byte)1, "按计划出库时间出库"),
    ;
    private final byte code;
    private final String desc;

    JitTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static JitTypeEnum fromCode(byte code) {
        for (JitTypeEnum jitType : values()) {
            if (jitType.getCode() == code) {
                return jitType;
            }
        }
        throw new IllegalArgumentException("未知JIT类型: " + code);
    }
}