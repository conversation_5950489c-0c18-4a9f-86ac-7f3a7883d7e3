package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

import javax.validation.constraints.NotNull;


public class InfrastructureException extends AbstractException {

    public InfrastructureException(OutboundErrorSpec errorReason) {
        super(errorReason);
    }

    public InfrastructureException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }
}
