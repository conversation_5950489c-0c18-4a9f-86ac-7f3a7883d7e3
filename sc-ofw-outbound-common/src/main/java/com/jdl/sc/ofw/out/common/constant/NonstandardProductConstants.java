package com.jdl.sc.ofw.out.common.constant;

//TODO: 转换成枚举
public interface NonstandardProductConstants {

    /**
     * 为空则任务没有，不允许增加 0 属性
     */
    String ENROLLED = "1";

    /**
     * 加急优先配送
     */
    String URGENT_SERVICE = "urgentService";

    /**
     * 保障标：SafeguardTypeEnum  类型：boolean
     * somark126
     * SO_MARK_SAFEGUARD_MARK_0(126,'0',"默认的商家保障"),
     * SO_MARK_SAFEGUARD_MARK_1(126,'1',"KA商家运营特殊保障"),
     */
    String SPECIAL_SAFEGUARD = "specialSafeguard";
    /**
     * 是否菜鸟模板  类型：boolean
     * somark38
     * SOMARK_THREETYEIGHT_0(38, '0', "非菜鸟模板"),
     * SOMARK_THREETYEIGHT_1(38, '1', "菜鸟模板"),
     */
    String CAINIAO_TEMPLATE = "cainiao_template";

    /**
     * 海运违禁订单标志位
     * true 海运违禁订单 false 非海运违禁订单（默认）
     * somark133
     * SOMARK_HUNDRED_THIRTY_THREE_0(133,'0',"非海运违禁订单"),
     * SOMARK_HUNDRED_THIRTY_THREE_1(133,'1',"海运违禁订单"),
     */
    String MARINE_CONTRABAND = "marineContraband";

    /**
     * somark132 次日达时效提升
     * SOMARK_HUNDRED_THIRTY_TWO_0(132,'0',"无次日达时效提升"),
     * SOMARK_HUNDRED_THIRTY_TWO_1(132,'1',"0-11:10下单类订单"),
     * SOMARK_HUNDRED_THIRTY_TWO_2(132,'2',"11:10-18点下单类订单"),
     * SOMARK_ORDER_TYPE_SUFFIX_5(116,'5',"京喜订单"),
     */
    String NEXT_DAY_AGING_PROMOTION = "nextDayAgingPromotion";

    /**
     * somark139
     * SOMARK_SOP_ZT_HUNDRED_THIRTY_NINE_ZERO(139,'0',"sop自提柜"),
     * SOMARK_SOP_ZT_HUNDRED_THIRTY_NINE_SEVEN(139,'7',"sop自提店"),
     */
    String SELF_PICKUP_TYPE = "selfPickupType";

    /**
     * somark111,跨配送中心订单
     * SO_MARK_ACROSS_DC_ORDER_NO(111,'0',"非跨配送中心订单"),
     * SO_MARK_ACROSS_DC_ORDER_YES(111,'1',"跨配送中心订单"),
     */
    String ACROSS_DISTRIBUTION_CENTER_ORDER = "acrossDc";

    /**
     * 211时效 = somrk7+8+9
     * somark7 = somark39
     * somark8 = somark30
     * somark9 = somark35
     * <p>
     * //211时效 39 30 35组合使用   枚举值存储在orderMark39
     * //1 211 2 次日达 3 隔日达 4 4日达 5 5日达 6 6日达 7 7日达 8 8+日达 9 411极速达
     * SOMARK_THIRTY_NINE(39, '0', "211时效"),
     * //211时效 39 30 35组合使用: 39:1, 30:1, 35:0,代表当日达
     * SOMARK_THIRTY_NINE_1(39, '1', "211时效"),
     * //1 在211范围（去除库存逻辑） 2 +（32-2）=次日达（唐久） 9 +（32-2）=一小时达（唐久）
     * SOMARK_THIRTY(30, '0', "211时效"),
     * //211时效 39 30 35组合使用: 39:1, 30:1, 35:0,代表当日达
     * SOMARK_THIRTY_1(30, '1', "211时效"),
     * //0 用户未选择时效，默认第一天 1 定时达-早班次 2 定时达-午班次 3 定时达-夜间配 4 次日达/隔日达全天配送（目前只能选择全天）
     * SOMARK_THIRTY_FIVE(35, '0', "211时效"),
     */
    String PROMISE_TIME_211 = "promiseTime211";

    /**
     * somark49
     * //49.代表闪购
     * SOMARK_FORTYNINE_0(49, '0', "非闪购订单"),
     * SOMARK_FORTYNINE_1(49, '1', "闪购订单"),
     */
    String FLASH_SALE = "flashSale";

    /**
     * somark48
     * //48.代表闪购商品类型
     * SOMARK_FORTYEIGHT_0(48, '0', "京东自有"),
     * SOMARK_FORTYEIGHT_1(48, '1', "闪购自有"),
     */
    String FLASH_SALE_GOODS_TYPE = "flashSaleGoodsType";
    /**
     * 根据事业部查询POP订单京东配送(青龙和大件等)是否打印出库交接单
     * somark107
     * SO_MARK_HUNDRED_SEVEN_0(107, '0', "不打印"),
     * SO_MARK_HUNDRED_SEVEN_1(107, '1', "打印"),
     */
    String PRINT_OUTBOUND_DELIVERY_ORDER = "printOutboundDeliveryOrder";

    /**
     * 预售暂存位置
     * soMainParam.getExtendedField().getCustomField().getString("preSalePoint")
     */
    String ORDER_TEMPORARY_STORAGE = "orderTemporaryStorage";

    /**
     * 开通耗材服务
     * 1 开通
     * 0 未开通
     */
    String OPEN_MATERIAL_SERVICE = "conre";


    /**
     * 本人签收服务 soMain.somark14
     * 1 本人签收
     * 0 非本人签收
     */
    String SELF_SIGN = "selfSign";

    /**
     * 购物清单模板
     * somark41
     * somark42
     * somark43
     * //41.42.43三位
     * // 注意：用来联合表示购物清单模板类型，41=1,42=0,43=0代表通用购物清单，41=0,42=0,43=0代表无购物清单
     * SOMARK_SHOPPING_LIST_000(41, '0', "购物清单标识第一位，000代表无购物清单"),
     * SOMARK_SHOPPING_LIST_100(41, '1', "购物清单标识第一位，100代表通用购物清单"),
     * SOMARK_SHOPPING_LIST_200(41, '2', "购物清单标识第一位，200代表安利购物清单"),
     * SOMARK_SHOPPING_LIST_CXC(42, '0', "购物清单标识第二位，待扩展"),
     * SOMARK_SHOPPING_LIST_CCX(43, '0', "购物清单标识第三位，待扩展"),
     */
    String SHOPPING_TEMPLATE = "shopping_Template";

    /**
     * 包裹半收
     * 1 开通
     */
    String PACKAGE_HALF = "package";

    /**
     * GIS解析
     */
    String GAIN_MAJOR_PART = "gainMajorPart";

    /**
     * 拼多多接口切换开关,需要废弃的开关;
     * 1 为切换为新接口
     *
     * @RefSo("soMainParam.extendedField.switchPddNewInterface")
     */

    @Deprecated
    String SWITCH_PDD_NEW_INTERFACE = "pddNewInterface";

    /**
     * 运力分单
     */
    String CAPACITY = "isCapacitySplitting";
}
