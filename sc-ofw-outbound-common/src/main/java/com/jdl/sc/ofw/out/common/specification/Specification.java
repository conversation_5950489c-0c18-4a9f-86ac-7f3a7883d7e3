package com.jdl.sc.ofw.out.common.specification;

import javax.validation.groups.Default;

/**
 * Specification interface，归约模式(Specification Pattern)就是一种约束条件.
 * <p>
 * <p>Specifications are small, single‐purpose classes(SRP), similar to policies.</p>
 */
public interface Specification<T> {

    /**
     * Check if {@code t} is satisfied by the specification.
     *
     * @param t      Object to test.
     * @param groups the group or list of groups targeted for validation (defaults to {@link Default})
     * @return {@code true} if {@code t} satisfies the specification.
     */
    boolean isSatisfiedBy(T t, Class... groups);

    /**
     * Same as {@link #isSatisfiedBy(Object, Class[])} except that it accepts {@link Notification} to pass more detailed error info.
     *
     * @param t            Object to test.
     * @param notification an object that collects error messages.
     * @param groups       the group or list of groups targeted for validation (defaults to {@link Default})
     * @return if {@code t} satisfies the specification.
     */
    boolean isSatisfiedBy(T t, Notification notification, Class... groups);
}
