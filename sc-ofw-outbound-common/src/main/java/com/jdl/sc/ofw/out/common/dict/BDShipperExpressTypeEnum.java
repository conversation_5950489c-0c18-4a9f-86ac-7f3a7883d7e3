package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

@Getter
public enum BDShipperExpressTypeEnum {
    T4("T4", "1", "顺丰标快"),
    T6("T6", "2", "顺丰标快(陆运)"),
    T77("T77", "208", "特惠专配"),
    T29("T29", "155", "标准零担"),
    T15("T15","201","冷运标快"),
    T6_231("T6_231","231","陆运包裹");

    /**
     * 承运商时效代码
     */
    private final String code;

    /**
     * 青龙ExpressType
     * https://cf.jd.com/pages/viewpage.action?pageId=109419365
     */
    private final String expressType;

    /**
     * 承运商时效名称
     */
    private final String name;

    @JsonCreator
    public static BDShipperExpressTypeEnum fromCode(final String code) {
        if (code == null) {
            return null;
        }
        for (final BDShipperExpressTypeEnum anEnum : values()) {
            if (anEnum.getCode().equals(code)) {
                return anEnum;
            }
        }
        return null;
    }

    BDShipperExpressTypeEnum(String code, String expressType, String name) {
        this.code = code;
        this.expressType = expressType;
        this.name = name;
    }

}
