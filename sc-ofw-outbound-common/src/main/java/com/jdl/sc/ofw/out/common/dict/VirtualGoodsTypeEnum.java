package com.jdl.sc.ofw.out.common.dict;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 是否虚拟品
 */
@Getter
@AllArgsConstructor
public enum VirtualGoodsTypeEnum {

    NON_VIRTUAL_GOODS("0", 1, "非虚拟品"),

    VIRTUAL_GOODS("1", 2, "是虚拟品"),

    ;

    private String omsType;

    private Integer wmsType;

    private String desc;


    public static VirtualGoodsTypeEnum fromOmsType(String omsType){
        return Arrays.stream(VirtualGoodsTypeEnum.values())
                .filter(virtualGoodsTypeEnum -> virtualGoodsTypeEnum.getOmsType().equals(omsType))
                .findFirst()
                .orElse(null);
    }

}
