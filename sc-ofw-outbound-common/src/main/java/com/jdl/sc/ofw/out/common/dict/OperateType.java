package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum OperateType {
    CREATE(1, "新建"),
    CANCEL(2, "取消"),
    INTERCEPT(3, "拦截"),
    MODIFY(4, "修改"),
    MODIFY_STATUS(5, "修改状态"),
    PULLBACK(6,"拉回"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    OperateType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static OperateType fromCode(int code) {
        for (OperateType operateType : values()) {
            if (operateType.getCode() == code) {
                return operateType;
            }
        }

        throw new IllegalArgumentException("未知操作类型: " + code);
    }
}