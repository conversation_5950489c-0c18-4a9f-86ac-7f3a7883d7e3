package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

import javax.validation.constraints.NotNull;

/**
 * FileName: RpcException
 * Description: 调用远程接口异常
 * [@author]:   quh<PERSON><PERSON>
 * [@date]:     2024/9/919:42
 */
public class RpcTimeOutException extends InfrastructureException {

    public RpcTimeOutException(OutboundErrorSpec errorReason) {
        super(errorReason);
    }

    public RpcTimeOutException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }
}
