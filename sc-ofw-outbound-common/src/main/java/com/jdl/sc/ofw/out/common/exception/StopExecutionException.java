package com.jdl.sc.ofw.out.common.exception;

import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;

import javax.validation.constraints.NotNull;

public class StopExecutionException extends AbstractException {

    public StopExecutionException(OutboundErrorSpec errorReason) {
        super(errorReason);
    }

    public StopExecutionException(@NotNull OutboundErrorSpec errorReason, @NotNull Exception exception) {
        super(errorReason, exception);
    }
}
