package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PlaceholderLevelEnum {
    MAIN(0, "主档"),
    ITEM(1, "明细"),
    UNKNOWN(-1, "未知的，模版不处理");

    private final String name;
    @JsonValue
    private final int code;

    PlaceholderLevelEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    @JsonCreator
    public static PlaceholderLevelEnum fromCode(final Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        for (final PlaceholderLevelEnum anEnum : values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return UNKNOWN;
    }
}
