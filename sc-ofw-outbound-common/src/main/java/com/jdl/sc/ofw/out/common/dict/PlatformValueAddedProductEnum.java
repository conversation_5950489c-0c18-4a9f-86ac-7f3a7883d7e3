package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 外平台增值服务产品
 *
 * <AUTHOR>
 */
@Getter
public enum PlatformValueAddedProductEnum {
    INSURE("SVC-INSURE", "保价金额", null),
    PAYMENT_TYPE("SVC-PAYMENT-TYPE", "结算方式", "1"),
    BIZ_TYPE("BUSINESS-TYPE", "业务类型","ECOMMERCE"),
    PRODUCT_TYPE("PRODUCT-TYPE", "配运主产品",null),
    DELIVERY_TYPE("SVC-DELIVERY-TYPE", "重货上楼", "SEND"),
    SVC_FRESH("SVC-FRESH", "温层", null),
    ;

    /**
     * 增值服务产品编码
     */
    @JsonValue
    private final String code;
    /**
     * 增值服务产品名称
     */
    private final String name;
    /**
     * 默认值
     */
    private final String defaultValue;


    PlatformValueAddedProductEnum(String code, String name, String defaultValue) {
        this.code = code;
        this.name = name;
        this.defaultValue = defaultValue;
    }

}
