package com.jdl.sc.ofw.out.common.dict;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PackagePrintSourceEnum {
    DY("DY", "抖音", "tiktokPrintInfoProcessor"),
    PDD("PDD", "拼多多", "pddPrintInfoProcessor"),
    ;

    private final String type;
    private final String desc;
    private final String processor;

    private static final Map<String, PackagePrintSourceEnum> MAP =
            Stream.of(PackagePrintSourceEnum.values()).collect(Collectors.toMap(PackagePrintSourceEnum::getType, Function.identity()));

    public static boolean contains(String type) {
        return MAP.containsKey(type);
    }

    public static PackagePrintSourceEnum parse(String type) {
        return contains(type) ? PackagePrintSourceEnum.valueOf(type) : null;
    }
}
