package com.jdl.sc.ofw.out.common.batrix;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * FileName: BatrixInfo
 * Description: 流程运行时上下文对象
 * [@author]:   quh<PERSON><PERSON>
 * [@date]:     2023/3/27 3:58 下午
 */
@Getter
@AllArgsConstructor
@Builder
public class BatrixInfo {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 场景
     */
    private String businessScene;

    /**
     * 请求唯一编码
     */
    private String traceId;

    /**
     * batrix当前异步事件
     */
    private String eventAction;

}
