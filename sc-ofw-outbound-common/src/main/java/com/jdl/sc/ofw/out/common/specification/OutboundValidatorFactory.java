package com.jdl.sc.ofw.out.common.specification;

import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintValidatorFactory;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * OutboundValidatorFactory
 */
public class OutboundValidatorFactory {

    private ValidatorFactory failFastValidatorFactory;
    private ValidatorFactory notFailFastValidatorFactory;
    private ConstraintValidatorFactory constraintValidatorFactory;


    private Validator failFastValidator;
    private Validator notFailFastValidator;

    public OutboundValidatorFactory() {
    }

    public void init() {
        this.failFastValidatorFactory = Validation.byProvider(HibernateValidator.class).configure().failFast(true).constraintValidatorFactory(this.constraintValidatorFactory).buildValidatorFactory();
        this.notFailFastValidatorFactory = Validation.byProvider(HibernateValidator.class).configure().failFast(false).constraintValidatorFactory(this.constraintValidatorFactory).buildValidatorFactory();
    }

    public Validator getValidator() {
        if (notFailFastValidator != null) {
            return notFailFastValidator;
        }
        notFailFastValidator = this.notFailFastValidatorFactory.getValidator();
        return notFailFastValidator;
    }

    public Validator getFailFastValidator() {
        if (failFastValidator != null) {
            return failFastValidator;
        }
        failFastValidator = this.failFastValidatorFactory.getValidator();
        return failFastValidator;
    }

    public void setConstraintValidatorFactory(OutboundConstraintValidatorFactory constraintValidatorFactory) {
        this.constraintValidatorFactory = constraintValidatorFactory;
    }


}
