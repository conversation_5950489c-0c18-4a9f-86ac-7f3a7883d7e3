package com.jdl.sc.ofw.out.common.dict;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 派送方式
 */
@Getter
public enum DeliveryTypeEnum {
    HOME_DELIVERY(1, "送货上门", '0'),
    SELF_PICK_UP(2, "自提", '1'),
    ;
    @JsonValue
    private final Integer code;
    private final String name;
    private final char soMark;

    DeliveryTypeEnum(Integer key, String name, char soMark) {
        this.name = name;
        this.code = key;
        this.soMark = soMark;
    }

    @JsonCreator
    public static DeliveryTypeEnum fromCode(final Integer code) {
        if (code == null) {
            return HOME_DELIVERY;
        }
        for (final DeliveryTypeEnum anEnum : values()) {
            if (anEnum.getCode().equals(code)) {
                return anEnum;
            }
        }
        return HOME_DELIVERY;
    }

}
