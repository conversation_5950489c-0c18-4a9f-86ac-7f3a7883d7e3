package com.jdl.sc.ofw.out.common.dict;

import lombok.Getter;

/**
 *  承运商编码枚举
 */
@Getter
public enum ShipmentCodeEnum {

    YUNDA("CYS4418046511208","YUNDA",1327,"韵达快递"),
    YTO("CYS4418046511289","YTO",463,"圆通快递"),

    ;

    ShipmentCodeEnum(String shipperNo, String jdlShipperCode, int jdlShipperId, String shipperName) {
        this.shipperNo = shipperNo;
        this.jdlShipperCode = jdlShipperCode;
        this.jdlShipperId = jdlShipperId;
        this.shipperName = shipperName;
    }

    /**
     * CYS 承运商编码
     */
    private String shipperNo;

    /**
     * 物流开发平台承运商编码
     * https://cloud.jdl.com/#/open-business-document/access-guide/207/54463
     */
    private String jdlShipperCode;

    /**
     * 物流开发平台承运商ID
     * https://cloud.jdl.com/#/open-business-document/access-guide/207/54463
     */
    private int jdlShipperId;

    /**
     *承运商名称
     */
    private String shipperName;


    public static ShipmentCodeEnum fromShipperNo(final String shipperNo) {

        for (final ShipmentCodeEnum anEnum : values()) {
            if (anEnum.getShipperNo().equals(shipperNo)) {
                return anEnum;
            }
        }
        throw new IllegalArgumentException("承运商编码");
    }
}
