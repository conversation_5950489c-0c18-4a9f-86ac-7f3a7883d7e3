package com.jdl.sc.ofw.out.common.dict;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 订单类型
 * 100-销售出库单，101-送取同步售后换新出库单
 */
@Getter
public enum OrderTypeEnum {
    DEFAULT("100", "销售出库单"),
    //"soMain.soMark.115-116"
    DELIVERY_PICK_SYNC_AFTER_SALE("101", "送取同步售后换新出库单"),
    CRETURN("201", "退货入库单"),
    APPRAISAL("800", "鉴定单"),
    DUMP("107", "同仓转储单"),
    ;
    private final String name;
    @JsonValue
    private final String code;

    OrderTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    @JsonCreator
    public static OrderTypeEnum fromCode(final String code) {
        for (final OrderTypeEnum anEnum : values()) {
            if (anEnum.getCode().equals(code)) {
                return anEnum;
            }
        }

        throw new IllegalArgumentException("未知订单类型: " + code);
    }

}
