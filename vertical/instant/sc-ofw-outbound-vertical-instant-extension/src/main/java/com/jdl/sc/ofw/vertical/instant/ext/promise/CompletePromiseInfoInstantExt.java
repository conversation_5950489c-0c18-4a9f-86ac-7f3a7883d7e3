package com.jdl.sc.ofw.vertical.instant.ext.promise;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.promise.external.front.store.contract.calendar.dto.Batch;
import com.jdl.promise.external.front.store.contract.promise.dto.BatchInfo;
import com.jdl.promise.external.front.store.contract.promise.dto.ConfigInfo;
import com.jdl.promise.external.front.store.contract.promise.dto.PromiseData;
import com.jdl.promise.external.front.store.contract.promise.param.PromiseRequest;
import com.jdl.promise.external.front.store.contract.resp.RpcResponse;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.extension.promise.ICompletePromiseInfoExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductBusinessLineEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductTypeEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.rpc.PromiseServiceAcl;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Extension(code = OutboundProduct.CODE)
public class CompletePromiseInfoInstantExt implements ICompletePromiseInfoExt {

    @Resource
    private PromiseServiceAcl promiseServiceAcl;

    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    private static final String SOURCE = "external-front-store";
    /**
     * 默认生产耗时5min
     */
    private static final long DEFAULT_PRODUCTION_DURATION = 5L;
    /**
     * 默认在途时长40min
     */
    private static final long DEFAULT_TRAVEL_DURATION = 40L;
    /**
     * 默认下传提前量10min
     */
    private static final long DEFAULT_DOWNLOAD_ADVANCE = 10L;

    @Override
    public void execute(OrderFulfillmentContext context) {
        OrderFulfillmentModel model = context.getModel();

        if (switchKeyCommonUtils.instantPromiseDeptNoContains(model.getCustomer().getAccountNo())
                && ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())) {
            getPromiseInfo(model);
        }
    }

    private void getPromiseInfo(OrderFulfillmentModel model) {
        RpcResponse<PromiseData> promiseResponse = promiseServiceAcl.getPromiseInfo(buildPromiseRequest(model));
        if (Objects.nonNull(promiseResponse)) {
            log.info("前置仓-订单[{}]，获取时效返回结果正常:{}", model.getOrderNo(), JsonUtil.toJsonSafe(promiseResponse));
            PromiseData promiseData = promiseResponse.getData();
            model.getShipment().assignExpectDeliveryStartTime(promiseData.getDeliveryTime());
            model.getShipment().assignExpectDeliveryEndTime(promiseData.getDeliveryTime());
            model.getFulfillment().assignEstimatedDeliveryTimePeriod(getEstimatedDeliveryTimePeriod(promiseData.getDeliveryTime()));
            model.getFulfillment().assignProductionStartTime(promiseData.getProductStartTime());
            model.getFulfillment().assignProductionEndTime(promiseData.getProductEndTime());
            model.getFulfillment().assignPreProductionMinutes(Optional.ofNullable(promiseData.getConfigInfo()).map(ConfigInfo::getAdvancePushDuration).orElse(null));
            model.getFulfillment().assignProductionDuration(Optional.ofNullable(promiseData.getConfigInfo()).map(ConfigInfo::getProductDuration).orElse(null));
            model.getFulfillment().assignTransitDuration(Optional.ofNullable(promiseData.getConfigInfo()).map(ConfigInfo::getDeliveryDuration).orElse(null));
            model.getFulfillment().assignWarehouseArrivalTime(Optional.ofNullable(promiseData.getTransferTime()).map(DateUtil::dateToString).orElse(null));
        }
        // 兜底逻辑
        else {
            // 预约单
            if (PlatformConstants.INSTANT_DELIVERY_TYPE_APPOINTMENT.equals(model.getShipment().getInstantDeliveryType())) {
                String warehouseArrivalTime = getWarehouseArrivalTime(model);
                log.info("前置仓-预约单[{}]，执行兜底逻辑，计算下仓时间为:{}", model.getOrderNo(), warehouseArrivalTime);
                model.getFulfillment().assignWarehouseArrivalTime(warehouseArrivalTime);
                model.getFulfillment().assignProductionStartTime(getProductionStartTimeOfAppointment(model));
                model.getFulfillment().assignProductionEndTime(getProductionEndTimeOfAppointment(model));
            }
        }
    }

    private PromiseRequest buildPromiseRequest(OrderFulfillmentModel model) {
        int promiseType = NumberUtils.toInt(model.getShipment().getInstantDeliveryType(), 0);
        return PromiseRequest.builder()
                .traceId(UuidUtil.getTerseUuid())
                .source(SOURCE)
                .orderId(model.getOrderNo())
                .requestTime(new Date())
                .storeId(model.getWarehouse().getWarehouseNo())
                .fenceId(model.getShipment().getGisFenceId())
                .productId(getProductCode(model))
                .promiseType(promiseType)
                .batchInfo(buildBatchInfo(model))
                .build();
    }

    private String getProductCode(OrderFulfillmentModel model) {
        return model.getProducts().getProducts().stream()
                .filter(product -> ProductTypeEnum.MAIN_PRODUCT.equals(product.getType())
                        && ProductBusinessLineEnum.STORAGE.equals(product.getBusinessLine()))
                .findFirst()
                .map(Product::getNo)
                .orElse(null);
    }

    private BatchInfo buildBatchInfo(OrderFulfillmentModel model) {
        return BatchInfo.builder()
                .day(model.getShipment().getExpectDeliveryEndTime())
                .batch(new Batch(model.getShipment().getExpectDeliveryStartTime(), model.getShipment().getExpectDeliveryEndTime()))
                .build();
    }

    /**
     * 获取下仓时间，公式：下仓时间 = 预计送达时间 - 下传提前量（10min） - 生产时间（5min） - 在途时间（40min）
     */
    private String getWarehouseArrivalTime(OrderFulfillmentModel model) {
        if (Objects.isNull(model.getShipment().getExpectDeliveryEndTime())) {
            return DateUtil.dateToString(new Date(), DateUtil.DATE_TIME_PATTERN);
        }
        LocalDateTime localDateTime = DateUtil.dateToLocalDateTime(model.getShipment().getExpectDeliveryEndTime());
        LocalDateTime expectDeliveryTime = localDateTime.minusMinutes(DEFAULT_DOWNLOAD_ADVANCE + DEFAULT_PRODUCTION_DURATION + DEFAULT_TRAVEL_DURATION);
        return DateUtil.localDateTimeToString(expectDeliveryTime, DateUtil.DATE_TIME_PATTERN);
    }

    /**
     * 预约单，仓生产开始时间 = 预计送达时间 - 下传提前量 - 生产耗时
     */
    private Date getProductionStartTimeOfAppointment(OrderFulfillmentModel model) {
        LocalDateTime productionStartTime = DateUtil.dateToLocalDateTime(getExpectDeliveryEndTime(model))
                .minusMinutes(DEFAULT_DOWNLOAD_ADVANCE + DEFAULT_PRODUCTION_DURATION);
        return DateUtil.localDateTimeToDate(productionStartTime);
    }

    /**
     * 预约单，仓生产结束时间 = 预计送达时间 - 下传提前量
     */
    private Date getProductionEndTimeOfAppointment(OrderFulfillmentModel model) {
        LocalDateTime productionEndTime = DateUtil.dateToLocalDateTime(getExpectDeliveryEndTime(model))
                .minusMinutes(DEFAULT_DOWNLOAD_ADVANCE);
        return DateUtil.localDateTimeToDate(productionEndTime);
    }

    /**
     * 获取预约单的“预计送达结束时间”（如果为null则用当前时间）
     */
    private Date getExpectDeliveryEndTime(OrderFulfillmentModel model) {
        return Optional.ofNullable(model.getShipment().getExpectDeliveryEndTime())
                .orElseGet(Date::new);
    }

    /**
     * 获取预计送达时间区间，将 2025-05-29 17:00:00 转成 2025-05-29 17:00-17:00
     */
    private String getEstimatedDeliveryTimePeriod(Date deliveryTime) {
        if (Objects.isNull(deliveryTime)) {
            return null;
        }
        String datePart = DateUtil.dateToString(deliveryTime, DateUtil.DATE_PATTERN);
        String timePart = DateUtil.dateToString(deliveryTime, DateUtil.PATTERN_HH_MM);
        return datePart + " " + timePart + "-" + timePart;
    }

}
