package com.jdl.sc.ofw.vertical.instant.ext.pause;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.extension.pause.IPauseBeforeToWmsExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Extension(code = OutboundProduct.CODE)
public class PauseBeforeToWmsInstantExt implements IPauseBeforeToWmsExt {

    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DateUtil.DATE_TIME_PATTERN);

    @Override
    public void execute(OrderFulfillmentContext context) {
        OrderFulfillmentModel model = context.getModel();

        // 预约单，未到下仓时间，hold 单
        if (switchKeyCommonUtils.instantPromiseDeptNoContains(model.getCustomer().getAccountNo())
                && ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())) {
            holdIfNecessary(model);
        }
    }

    private void holdIfNecessary(OrderFulfillmentModel model) {
        if (!PlatformConstants.INSTANT_DELIVERY_TYPE_APPOINTMENT.equals(model.getShipment().getInstantDeliveryType())) {
            return;
        }

        // 到达下仓时间-直接下仓
        if (isArrivalTimeToWMS(model.getFulfillment().getWarehouseArrivalTime())) {
            log.info("前置仓到达下仓时间，直接下仓，订单号:{}, 下仓时间:{}",
                    model.getOrderNo(),
                    model.getFulfillment().getWarehouseArrivalTime());
            return;
        }
        log.info("前置仓未到达下仓时间，hold单，订单号:{}, 下仓时间:{}",
                model.getOrderNo(),
                model.getFulfillment().getWarehouseArrivalTime());
        throw new PauseException(PauseReasonEnum.STUCK_ORDER_EXCEPTION,
                DateUtil.stringToDate(model.getFulfillment().getWarehouseArrivalTime(), DateUtil.DATE_TIME_PATTERN));

    }

    /**
     * 是否到下仓时间，当下仓时间为空 或 下仓时间 <= 当前日期时，返回 true，其他返回 false
     */
    private boolean isArrivalTimeToWMS(String warehouseArrivalTime) {
        return StringUtils.isBlank(warehouseArrivalTime)
                || isDateLessThanOrEqualToCurrent(LocalDateTime.parse(warehouseArrivalTime, DATE_TIME_FORMATTER));
    }

    /**
     * 判断下仓时间是否 <= 当前日期
     */
    private boolean isDateLessThanOrEqualToCurrent(LocalDateTime warehouseArrivalTime) {
        LocalDateTime currentDate = LocalDateTime.now();
        return warehouseArrivalTime.isBefore(currentDate) || warehouseArrivalTime.isEqual(currentDate);
    }

}
