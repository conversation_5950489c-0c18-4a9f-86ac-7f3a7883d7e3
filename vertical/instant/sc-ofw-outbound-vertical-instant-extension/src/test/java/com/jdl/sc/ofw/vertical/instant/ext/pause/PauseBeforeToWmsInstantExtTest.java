package com.jdl.sc.ofw.vertical.instant.ext.pause;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.outbound.spec.enums.InvoiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackDetailsCollectEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PauseBeforeToWmsInstantExtTest {

    @InjectMocks
    private PauseBeforeToWmsInstantExt pauseBeforeToWmsInstantExt;

    @Mock
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    private OrderFulfillmentContext context;

    @Before
    public void setUp() {
        when(switchKeyCommonUtils.instantPromiseDeptNoContains(any())).thenReturn(true);
        context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity())
                .model(OrderFulfillmentModel.builder()
                        .basicInfo(BasicInfo.builder().build())
                        .orderNo("ESL000001")
                        .orderStatus(OrderStatusEnum.INIT)
                        .batrixInfo(BatrixInfo.builder().build())
                        .warehouse(Warehouse.builder().warehouseNo("2910293").build())
                        .consignee(Consignee.builder().build())
                        .consignor(Consignor.builder().build())
                        .channel(Channel.builder().channelCode("1").build())
                        .orderBusinessType(OrderBusinessTypeEnum.B2C)
                        .nonstandardProducts(new NonstandardProducts())
                        .fulfillment(aFulfillment())
                        .customer(Customer.builder().accountNo("EBU001").build())
                        .shipment(Shipment.builder().shipperType(ShipperTypeEnum.JDPS).instantDeliveryType(PlatformConstants.INSTANT_DELIVERY_TYPE_APPOINTMENT).build())
                        .ediRemark(EdiRemarkInfo.builder().deliveryOrder(DeliveryOrderInfo.builder().build()).build())
                        .build())
                .build();
    }

    @Test
    public void test_execute_when_warehouseArrivalTimeIsNullOrEmpty() {
        pauseBeforeToWmsInstantExt.execute(context);
    }

    @Test
    public void test_execute_when_warehouseArrivalTimeIsLessNow() {
        Fulfillment fulfillment = aFulfillment();
        fulfillment.assignWarehouseArrivalTime("2025-05-29 09:00:00");
        ReflectionTestUtils.setField(context.getModel(), "fulfillment", fulfillment);

        pauseBeforeToWmsInstantExt.execute(context);
    }

    @Test(expected = PauseException.class)
    public void test_execute_when_warehouseArrivalTimeIsGreaterNow() {
        Fulfillment fulfillment = aFulfillment();
        fulfillment.assignWarehouseArrivalTime("2028-05-29 10:00:00");
        ReflectionTestUtils.setField(context.getModel(), "fulfillment", fulfillment);

        pauseBeforeToWmsInstantExt.execute(context);
    }

    private Fulfillment aFulfillment() {
        return Fulfillment.builder()
                .occupyResult(true)
                .packDetailsCollect(PackDetailsCollectEnum.PACK_DETAILS_COLLECT)
                .invoiceEnumFlag(InvoiceEnum.fromCode("1"))
                .orderUrgencyType(OrderUrgencyType.LAND)
                .orderPriority(1)
                .trustJDMetrics(false)
                .customField("{\"qiaqiaOriginWarehouseNo\":\"110000180\",\"sku_EMG4418274909556100af448cfc-2292-496d-ab39-830b88eb1e8e_remark\":\"\",\"carBusinessLine\":\"0\",\"isDyJdKd\":\"3\",\"oaid\":\"32\"}")
                .professionType("00")
                .fopFlowPathFlag(Boolean.TRUE)
                .warehouseArrivalTime(null)
                .build();
    }

}