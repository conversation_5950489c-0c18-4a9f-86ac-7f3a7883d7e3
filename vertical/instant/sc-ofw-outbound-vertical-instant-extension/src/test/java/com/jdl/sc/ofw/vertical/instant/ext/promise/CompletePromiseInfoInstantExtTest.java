package com.jdl.sc.ofw.vertical.instant.ext.promise;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.promise.external.front.store.contract.promise.dto.ConfigInfo;
import com.jdl.promise.external.front.store.contract.promise.dto.PromiseData;
import com.jdl.promise.external.front.store.contract.resp.RpcResponse;
import com.jdl.sc.ofw.out.common.constant.PlatformConstants;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.model.BatrixInfo;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.out.horiz.infra.rpc.PromiseServiceAcl;
import com.jdl.sc.ofw.outbound.spec.enums.InvoiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackDetailsCollectEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CompletePromiseInfoInstantExtTest {

    @InjectMocks
    private CompletePromiseInfoInstantExt completePromiseInfoInstantExt;

    @Mock
    private PromiseServiceAcl promiseServiceAcl;
    @Mock
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    private OrderFulfillmentContext context;

    @Before
    public void setUp() {
        when(switchKeyCommonUtils.instantPromiseDeptNoContains(any())).thenReturn(true);
        context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity())
                .model(OrderFulfillmentModel.builder()
                        .basicInfo(BasicInfo.builder().build())
                        .orderNo("ESL000001")
                        .orderStatus(OrderStatusEnum.INIT)
                        .batrixInfo(BatrixInfo.builder().build())
                        .warehouse(Warehouse.builder().warehouseNo("2910293").build())
                        .consignee(Consignee.builder().build())
                        .consignor(Consignor.builder().build())
                        .channel(Channel.builder().channelCode("1").build())
                        .orderBusinessType(OrderBusinessTypeEnum.B2C)
                        .nonstandardProducts(new NonstandardProducts())
                        .fulfillment(aFulfillment())
                        .shipment(aShipment())
                        .ediRemark(EdiRemarkInfo.builder().deliveryOrder(DeliveryOrderInfo.builder().build()).build())
                        .products(new ProductCollection())
                        .createTime(new Date())
                        .customer(Customer.builder().accountNo("EBU001").build())
                        .build())
                .build();
    }

    @Test
    public void testExecuteWithPromiseResponseIsNotNull() {
        when(promiseServiceAcl.getPromiseInfo(any())).thenReturn(aResponse());

        completePromiseInfoInstantExt.execute(context);
    }

    @Test
    public void testExecuteWithPromiseResponseIsNull_Appointment() {
        when(promiseServiceAcl.getPromiseInfo(any())).thenReturn(null);

        completePromiseInfoInstantExt.execute(context);
    }

    private Fulfillment aFulfillment() {
        return Fulfillment.builder()
                .occupyResult(true)
                .packDetailsCollect(PackDetailsCollectEnum.PACK_DETAILS_COLLECT)
                .invoiceEnumFlag(InvoiceEnum.fromCode("1"))
                .orderUrgencyType(OrderUrgencyType.LAND)
                .orderPriority(1)
                .trustJDMetrics(false)
                .customField("{\"qiaqiaOriginWarehouseNo\":\"*********\",\"sku_EMG4418274909556100af448cfc-2292-496d-ab39-830b88eb1e8e_remark\":\"\",\"carBusinessLine\":\"0\",\"isDyJdKd\":\"3\",\"oaid\":\"32\"}")
                .professionType("00")
                .fopFlowPathFlag(Boolean.TRUE)
                .warehouseArrivalTime(null)
                .build();
    }

    private Shipment aShipment() {
        return Shipment.builder()
                .shipperType(ShipperTypeEnum.JDPS)
                .expectDeliveryStartTime(new Date())
                .expectDeliveryEndTime(new Date())
                .instantDeliveryType(PlatformConstants.INSTANT_DELIVERY_TYPE_APPOINTMENT)
                .gisFenceId("100")
                .build();
    }

    private RpcResponse<PromiseData> aResponse() {
        PromiseData promiseData = PromiseData.builder()
                .orderId("ESL000001")
                .transferTime(new Date())
                .productStartTime(new Date())
                .productEndTime(new Date())
                .pickupTime(new Date())
                .deliveryTime(new Date())
                .configInfo(ConfigInfo.builder().advancePushDuration(10).deliveryDuration(10).productDuration(30).totalDuration(60).build())
                .build();
        return RpcResponse.success(promiseData, "success");
    }
}