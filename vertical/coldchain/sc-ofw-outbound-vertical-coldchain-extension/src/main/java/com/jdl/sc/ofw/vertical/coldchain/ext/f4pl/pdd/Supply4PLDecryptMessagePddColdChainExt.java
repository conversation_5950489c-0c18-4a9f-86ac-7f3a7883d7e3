package com.jdl.sc.ofw.vertical.coldchain.ext.f4pl.pdd;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLDecryptMessageExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.pojo.WaybillInfo;
import com.jdl.sc.ofw.out.horiz.JDLApp;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.extension.pdd.Supply4PLDecryptMessagePddExt;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 冷链拼多多-获取运单明文信息
 * <AUTHOR>
 * @Date 2023-08-30 14:52
 **/
@Slf4j
@Extension(code = OutboundProduct.CODE)
public class Supply4PLDecryptMessagePddColdChainExt extends Supply4PLDecryptMessagePddExt implements ISupply4PLDecryptMessageExt {
    @Override
    public void execute(OrderFulfillmentContext model) {
        super.execute(model);
    }
    public void signPddProductType(OrderFulfillmentModel model, WaybillInfo waybillInfo) {
        log.info("订单:{},冷链订单somark148位不更新，且不更新配产品", model.getOrderNo());
    }
}
