package com.jdl.sc.ofw.vertical.coldchain.ext.f4pl.xhs;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.extension.f4pl.xhs.Supply4PLXhsWaybillExt;
import com.jdl.sc.ofw.out.horiz.infra.rpc.impl.XhsPlatformStandardCreateServiceRpc;
import com.jdl.sc.ofw.vertical.coldchain.infra.rpc.impl.XhsColdChainPlatformStandardServiceRpc;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@Extension(code = OutboundProduct.CODE)
public class Supply4PLXhsColdChainWaybillExt extends Supply4PLXhsWaybillExt {

    @Resource(name = "xhsColdChainPlatformStandardServiceRpc")
    private XhsColdChainPlatformStandardServiceRpc xhsColdChainPlatformStandardServiceRpc;

    @Override
    public void execute(OrderFulfillmentContext context) throws Exception {
        super.execute(context);
    }

    protected XhsPlatformStandardCreateServiceRpc getXhsPlatformStandardCreateServiceRpc() {
        return xhsColdChainPlatformStandardServiceRpc;
    }
}
