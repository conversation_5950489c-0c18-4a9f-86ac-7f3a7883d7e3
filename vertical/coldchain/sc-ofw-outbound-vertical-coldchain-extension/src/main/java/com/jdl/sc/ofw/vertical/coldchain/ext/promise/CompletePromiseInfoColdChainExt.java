package com.jdl.sc.ofw.vertical.coldchain.ext.promise;

import com.jd.matrix.sdk.annotation.Extension;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeResponse;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.ofw.out.common.constant.NonstandardProductConstants;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.DomainServiceException;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.extension.promise.ICompletePromiseInfoExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.MaterialTimeLayerEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 接单-冷链
 * 时效信息补齐
 */
@Slf4j
@Extension(code = OutboundProduct.CODE)
public class CompletePromiseInfoColdChainExt implements ICompletePromiseInfoExt {
    @Resource
    private PromiseAcl promiseAcl;

    @Resource
    private ProductCenterServiceAcl productCenterServiceAcl;

    @Resource
    private SwitchKeyUtils switchKeyUtils;

    @Resource
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    /**
     * 京准达 解析标位和PromiseMsg中的时效
     * 校验标位对应的时效和PromiseMsg中的时效，如果不一致抛异常
     * 117位=1，118位=0，2H
     * 117位=1，118位=2，2H
     * 117位=1，118位=3，1H
     * 117位=1，118位=5，1H
     * 117位=1，118位=6，0.5H
     * 117位=1，118位=8，0.5H
     * 117位=2，118位=0，2H
     * 117位=2，118位=2，2H
     * 117位=2，118位=3，1H
     * 117位=2，118位=5，1H
     * 117位=2，118位=6，0.5H
     * 117位=2，118位=8，0.5H
     * 117位=4，118位=0，5H
     * 117位=3，118位=8，5H
     */
    private static final Map<String, Long> sendPayPromise = new HashMap<>(14);

    static {
        sendPayPromise.put("10", 120L);
        sendPayPromise.put("12", 120L);
        sendPayPromise.put("13", 60L);
        sendPayPromise.put("15", 60L);
        sendPayPromise.put("16", 30L);
        sendPayPromise.put("18", 30L);
        sendPayPromise.put("20", 120L);
        sendPayPromise.put("22", 120L);
        sendPayPromise.put("23", 60L);
        sendPayPromise.put("25", 60L);
        sendPayPromise.put("26", 30L);
        sendPayPromise.put("28", 30L);
        sendPayPromise.put("40", 300L);
        sendPayPromise.put("38", 300L);
    }



    @Override
    public void execute(OrderFulfillmentContext context) {
        //获取时效信息
        OrderFulfillmentModel model = context.getModel();
        //冷链/医药订单 && 冷链城配/冷链专车/冷链卡班/医药零担/医药整车，不调promise
        if (model.getProducts().isEnrolled(ProductEnum.LL_KB) || model.getProducts().isEnrolled(ProductEnum.LL_ZC) || model.getProducts().isEnrolled(ProductEnum.LL_CP)
                || model.getProducts().isEnrolled(ProductEnum.LL_YYLD) || model.getProducts().isEnrolled(ProductEnum.LL_YYZC)) {
            return;
        }
        WarehouseAndDeliveryTimeResponse response = promiseAcl.getPromiseColdChain(model);
        if (response == null || response.getResultCode() != 1) {
            log.error("Promise返回为空或返回失败");
            throw new DomainServiceException(UnifiedErrorSpec.ACL.RETURN_EXCEPTION).withCustom(model.getOrderNo() + "  获取时效信息为空或者ResultCode != 1");
        }
        //处理耗材时效分层
        if(switchKeyCommonUtils.materialTimeLayerDeptNoContains(model.getCustomer().getAccountNo())){
            String materialTimeLayer = processMaterialTimeLayer(response.getDeliveryDate(), response.getOutStoreTime(), response.getSendpay());
            model.getFinance().assignMaterialTimeLayer(materialTimeLayer);
        }
        //处理时效标位
        processPromiseMark(model, response.getSendpay());
        //给妥投时间赋值
        processDeliveryTime(model, response);
        // 判断是否为需要时效的订单,不需要时效则将运输类型设置为NO_PROMISE
        if (notNeedCodDate(model)) {
            model.getShipment().assignTransportationType(TransportationTypeEnum.NO_PROMISE);
            return;
        }
        //基本信息补齐
        processBaseInfo(model, response);
        if (response.getCoddate() == null) {
            return;
        }
        //精准达预计时间
        processAccurateDelivery(model, response);
        //极速达预计时间
        processLightningDelivery(model, response);
    }



    /**
     * 极速达处理
     */
    private void processLightningDelivery(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        if (!model.getProducts().isEnrolled(ProductEnum.LIGHTNING_DELIVERY)) {
            return;
        }
        // 预计送达时间，格式为xx年xx月xx日 xx:xx-xx:xx送达
        Date[] dates;
        try {
            dates = DateUtil.processDateRange(response.getCoddate(), response.getPromiseMsg());
        } catch (Exception e) {
            log.error("promise返回结果-->{} - {} promise{}解析时效,response.promiseMsg:{}错误",
                    model.orderNo(), model.getChannel().getChannelOrderNo(), ProductEnum.LIGHTNING_DELIVERY.getDesc(), response.getPromiseMsg());
            throw new DomainServiceException(UnifiedErrorSpec.Business.ORDER_BUSINESS_EXCEPTION, e).withCustom(
                    "promise返回结果-->" + model.orderNo() + " - " +
                            model.getChannel().getChannelOrderNo() + " promise" +
                            ProductEnum.LIGHTNING_DELIVERY.getDesc() + "解析时效错误" + e.getMessage()
            );
        }
        //处理成功
        model.getShipment().assignExpectDeliveryStartTime(dates[0]);
        model.getShipment().assignExpectDeliveryEndTime(dates[1]);
    }

    /**
     * 精准达处理
     */
    private void processAccurateDelivery(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        if (!model.getProducts().isEnrolled(ProductEnum.ACCURATE_DELIVERY)) {
            return;
        }
        //获取时长
        Long promiseMark = sendPayPromise.get(new String(new char[]{response.getSendpay().charAt(116), response.getSendpay().charAt(117)}));
        if (promiseMark == null) {
            log.info("soNo:{},sendPay标位超出京准达业务范围,京准达降级处理,sendPay:{}",
                    model.getOrderNo(), response.getSendpay());
            //移除精准达产品
            model.getProducts().remove(ProductEnum.ACCURATE_DELIVERY);
            return;
        }
        // 预计送达时间，格式为xx年xx月xx日 xx:xx-xx:xx送达
        Date[] dates;
        try {
            dates = DateUtil.processDateRange(response.getCoddate(), response.getPromiseMsg());
        } catch (Exception e) {
            log.error("promise返回结果-->{} - {} promise{}解析时效,response.promiseMsg:{}错误",
                    model.orderNo(), model.getChannel().getChannelOrderNo(), ProductEnum.ACCURATE_DELIVERY.getDesc(), response.getPromiseMsg());
            throw new DomainServiceException(UnifiedErrorSpec.Business.ORDER_BUSINESS_EXCEPTION, e).withCustom(
                    "promise返回结果-->" + model.orderNo() + " - " +
                            model.getChannel().getChannelOrderNo() + " promise" +
                            ProductEnum.ACCURATE_DELIVERY.getDesc() + "解析时效错误" + e.getMessage()
            );
        }

        //时长比较判断
        Duration duration = new Duration(dates[0].getTime(), dates[1].getTime());
        if (promiseMark != duration.getStandardMinutes()) {
            log.info("soNo:{},sendPay标位和返回的时效不符合,京准达降级处理,promiseMsg:{},promiseMark:{},sendPay:{}",
                    model.getOrderNo(), response.getPromiseMsg(), promiseMark, response.getSendpay());

            //移除精准达产品
            model.getProducts().remove(ProductEnum.ACCURATE_DELIVERY);
            return;
        }
        //处理成功
        model.getShipment().assignExpectDeliveryStartTime(dates[0]);
        model.getShipment().assignExpectDeliveryEndTime(dates[1]);
    }
    /**
     * 时效打标
     *
     * @param model
     * @param sendPay
     */
    public void processPromiseMark(OrderFulfillmentModel model, String sendPay) {
        if (StringUtils.isEmpty(sendPay)) {
            return;
        }

        // Sendpay第17位=2时，异地生成订单（跨配送中心订单）
        char seventeen = sendPay.charAt(16);
        char[] promiseTime211 = {sendPay.charAt(0), sendPay.charAt(29), sendPay.charAt(34)};
        if ('2' == seventeen) {
            model.getNonstandardProducts().enroll(NonstandardProductConstants.ACROSS_DISTRIBUTION_CENTER_ORDER);
        }
        model.getNonstandardProducts().enroll(NonstandardProductConstants.PROMISE_TIME_211, String.valueOf(promiseTime211));
    }

    /**
     * 处理妥投时间
     */
    private void processDeliveryTime(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        model.getShipment().assignDeliveryDate(response.getDeliveryDate());
        model.getShipment().assignBatchStartTime(response.getBatchStartTime());
        model.getShipment().assignBatchEndTime(response.getBatchEndTime());
    }

    /**
     * 不需要时效的订单 只需要跨区标 TODO 目前POP没有
     */
    private boolean notNeedCodDate(OrderFulfillmentModel model) {
        if (ShipperTypeEnum.ZI_CSF == model.getShipment().getShipperType()
                || ShipperTypeEnum.SELF_PICKUP == model.getShipment().getShipperType()
                || ShipperTypeEnum.JPZSF == model.getShipment().getShipperType()) {
            log.info("orderNo:{}承运商类型{}，不需要时效", model.orderNo(), model.getShipment().getShipperType().getName());
            return true;
        }
        return false;
    }

    /**
     * 补齐基本信息
     *
     * @param model
     * @param response
     */
    private void processBaseInfo(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        // 如果信任商家的运输方式,则使用商家建单时候传入的运输方式,否则使用promise推断运输方式
        if (!productCenterServiceAcl.isTrustSellerTransport(model.getCustomer().getAccountNo())) {
            model.getShipment().assignTransportationType(deduceTransportTypeFromPromise(model, response));
        }
        if (response.getCoddate() == null) {
            model.getShipment().assignIgnorePromise(true);
            return;
        } else {
            model.getShipment().assignIgnorePromise(false);
        }
        // 时效信息更新赋值
        model.getShipment().assignExpectDeliveryStartTime(response.getCoddate());
        model.getShipment().assignExpectDeliveryEndTime(response.getCoddate());
        if (switchKeyUtils.containBpromiseDeptNo(model.getCustomer().getAccountNo())) {
            processCollectTime(model, response);
        }
    }

    /**
     * 揽收时间处理
     * 若只获取到“仓配交接时间”，则使用【仓配交接时间-1h】作为“取件开始时间”下传配运。
     * 若未获取到“仓配交接时间”，或“预计打包时间”小于当前时间 或 【仓配交接时间-1h】小于当前时间，则“取件开始/结束时间”均不下传。
     *
     * @param model
     * @param response
     */
    private void processCollectTime(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        if (response.getStoreDeliveryHandoverTime() == null) {
            return;
        }
        Date collectStartTime = response.getExpectPackageDate() == null ? DateUtil.plusMinutes(response.getStoreDeliveryHandoverTime(), -60) : response.getExpectPackageDate();
        Date collectEndTime = response.getStoreDeliveryHandoverTime();
        Date now = new Date();
        if (collectStartTime.before(now)) {
            log.warn("揽收开始时间早于当前时间，取件开始/结束时间均不下传，orderNo:{}", model.orderNo());
            return;
        }
        model.getShipment().assignCollectStartTime(collectStartTime);
        model.getShipment().assignCollectEndTime(collectEndTime);
    }

    /**
     * 根据promise返回结果返回最终运输方式类型
     */
    private TransportationTypeEnum deduceTransportTypeFromPromise(OrderFulfillmentModel model, WarehouseAndDeliveryTimeResponse response) {
        // 陆运
        if (com.jd.fce.dos.service.enums.TransportationTypeEnum.LAND.getValue() == response.getTransportType()) {
            return TransportationTypeEnum.LAND;
        }
        // 航空
        if (com.jd.fce.dos.service.enums.TransportationTypeEnum.AVIATION.getValue() == response.getTransportType()) {
            // 当promise返回的运输方式为航空,但是商家的期望运输方式为陆运时,使用商家期望运输方式
            if (model.getShipment().getSellerExpectTransport() == TransportationTypeEnum.LAND) {
                return TransportationTypeEnum.LAND;
            } else {
                return TransportationTypeEnum.AVIATION;
            }
        }
        // 陆运填仓
        if (com.jd.fce.dos.service.enums.TransportationTypeEnum.FLIGHT_FILLING.getValue() == response.getTransportType()) {
            return TransportationTypeEnum.LAND_WITH_FILLING;
        }
        log.warn("Promise返回未识别的transportType，重置运输类型，跳过时效处理，流程继续");
        return TransportationTypeEnum.NO_PROMISE;
    }

    private String processMaterialTimeLayer(Date deliverTime, Date outStoreTime, String sendPay){
        if(deliverTime == null || outStoreTime == null){
            //若预计送达时间和计划出库时间有空值，则根据sendPay第1位对应的Promise211时效判断时效分层
            if (StringUtils.isEmpty(sendPay)) {
                return null;
            }
            log.info("根据时效标判断时效分层");
            char promiseTime211 = sendPay.charAt(0);

            MaterialTimeLayerEnum materialTimeLayer = MaterialTimeLayerEnum.findTimeLayerByPromiseTime211(promiseTime211);
            return materialTimeLayer == null ? null : materialTimeLayer.getTimeLayer();

        } else{
            long timeDiffer = deliverTime.getTime() - outStoreTime.getTime();
            //比较预计送达时间和计划出库时间的差值，根据所落区间的不同返回不同时效分层层级
            if (timeDiffer >= 0 && timeDiffer <= Duration.standardHours(18).getMillis()) {
                return MaterialTimeLayerEnum.EIGHTEEN_HOURS.getTimeLayer();
            } else if (timeDiffer > Duration.standardHours(18).getMillis() && timeDiffer <= Duration.standardHours(24).getMillis()) {
                return MaterialTimeLayerEnum.TWENTY_FOUR_HOURS.getTimeLayer();
            } else {
                return MaterialTimeLayerEnum.FOURTY_EIGHT_HOURS.getTimeLayer();
            }
        }
    }
}
