package com.jdl.sc.ofw.vertical.coldchain.ext.f4pl.pdd;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.domain.dto.BizPlatformRequest;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLWaybillExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.horiz.JDLApp;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.extension.pdd.Supply4PLPddWaybillExt;
import com.jdl.sc.ofw.vertical.coldchain.infra.rpc.translator.PddPlatformParamColdChainTranslator;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Description 冷链拼多多-调产业平台获取运单号
 * <AUTHOR>
 * @Date 2023-08-30 14:46
 **/
@Slf4j
@Extension(code = OutboundProduct.CODE)
public class Supply4PLWaybillPddColdChainExt extends Supply4PLPddWaybillExt implements ISupply4PLWaybillExt {
    @Resource
    private PddPlatformParamColdChainTranslator pddPlatformParamColdChainTranslator;
    @Override
    public void execute(OrderFulfillmentContext model) {
        super.execute(model);
    }
    public BizPlatformRequest buildBizPlatformRequest(OrderFulfillmentModel model) {
        return pddPlatformParamColdChainTranslator.buildBizPlatformRequest(model);
    }
}
