package com.jdl.sc.ofw.vertical.coldchain.ext.hold;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.common.exception.InfrastructureException;
import com.jdl.sc.ofw.out.domain.extension.hold.IOrderHoldReleaseExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.domain.service.WorkflowControlService;
import com.jdl.sc.ofw.out.horiz.JDLApp;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.order.hold.api.dto.request.CloseRequest;
import com.jdl.sc.order.hold.api.dto.request.QueryRequest;
import com.jdl.sc.order.hold.api.dto.response.OrderHoldRecord;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 冷链-修改场景hold单释放 扩展实现
 */
@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.MODIFY + JDLApp.SEPARATOR + JDLApp.COLD_CHAIN})
public class OrderHoldReleaseColdChainExt implements IOrderHoldReleaseExt {

    @Resource
    private OrderHoldAcl orderHoldAcl;

    @Resource
    private WorkflowControlService workflowControlService;

    @Override
    public void execute(OrderFulfillmentContext context) throws Exception {

        final OrderFulfillmentModel model = context.getModel();

        // 存在异常Hold单记录
        QueryRequest queryRequest = QueryRequest.builder()
                .orderNo(model.getOrderNo())
                .code(PauseReasonEnum.VIRTUAL_OUT_BOUND_HOLD.getCode())
                .build();
        OrderHoldRecord orderHoldRecord = orderHoldAcl.queryOneHoldRecord(queryRequest);
        if (orderHoldRecord == null) {
            throw new InfrastructureException(UnifiedErrorSpec.ACL.EMPTY_RESULT)
                    .withCustom("修改场景-虚出销售出库单等待采购订单，单号:" + model.getOrderNo() + ",无控单记录");
        }

        workflowControlService.resume(model, orderHoldRecord.getCustomizeSnapshot(), false);
        final CloseRequest closeRequest = CloseRequest.builder()
                .id(queryRequest.getId())
                .orderNo(model.getOrderNo())
                .operator(model.getAudit().getOperator())
                .build();
        orderHoldAcl.orderHoldClose(closeRequest);
    }
}
