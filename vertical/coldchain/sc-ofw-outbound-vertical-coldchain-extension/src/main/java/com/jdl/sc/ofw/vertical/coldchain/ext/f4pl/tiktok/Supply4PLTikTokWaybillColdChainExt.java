package com.jdl.sc.ofw.vertical.coldchain.ext.f4pl.tiktok;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLWaybillExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderExtendProps;
import com.jdl.sc.ofw.out.domain.model.vo.DeliveryOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import com.jdl.sc.ofw.out.horiz.JDLApp;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.extension.f4pl.tiktok.Supply4PLTikTokWaybillExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.GENERATE + JDLApp.SEPARATOR + JDLApp.COLD_CHAIN + JDLApp.SCENARIO_TIKTOK})
public class Supply4PLTikTokWaybillColdChainExt extends Supply4PLTikTokWaybillExt implements ISupply4PLWaybillExt {


    // 抖音供销
    private static final String TIKTOK_SUPPLY_CHAIN_CHANNEL_NO = "8040861";
    private static final String TIKTOK_SUPPLY_CHAIN_ORDER_CHANNEL = "101";

    /**
     * 场景条件：
     * ShipperTypeEnum.JDPS.equals(model.getShipment().getShipperType())
     * && DeliveryPerformanceChannelEnum.TIK_TOK.equals(model.getShipment().getDeliveryPerformanceChannel())
     */
    @Override
    public void execute(OrderFulfillmentContext context) throws Exception {
        super.execute(context);
    }


    @Override
    protected String getOrderChannel(OrderFulfillmentModel model) {
        String channelNo = Optional.ofNullable(model)
                .map(OrderFulfillmentModel::getChannel)
                .map(Channel::getChannelNo).orElse(null);
        // 冷链抖音供销指定order_channel
        if (TIKTOK_SUPPLY_CHAIN_CHANNEL_NO.equals(channelNo)) {
            return TIKTOK_SUPPLY_CHAIN_ORDER_CHANNEL;
        }
        return super.getOrderChannel(model);
    }

    @Override
    protected Long getElectronicUserId(OrderFulfillmentModel model) {
        Optional<OrderFulfillmentModel> modelOptional = Optional.ofNullable(model);
        String channelNo = modelOptional
                .map(OrderFulfillmentModel::getChannel)
                .map(Channel::getChannelNo).orElse(null);
        String userId = modelOptional.map(OrderFulfillmentModel::getEdiRemark)
                .map(EdiRemarkInfo::getDeliveryOrder)
                .map(DeliveryOrderInfo::getExtendProps)
                .map(DeliveryOrderExtendProps::getUserId)
                .orElse(null);
        //冷链抖音供销useID
        if (TIKTOK_SUPPLY_CHAIN_CHANNEL_NO.equals(channelNo) && StringUtils.isNumeric(userId)) {
            return Long.parseLong(userId);
        }
        return super.getElectronicUserId(model);
    }
}
