package com.jdl.sc.ofw.vertical.coldchain.ext.order;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.dto.ModifyCommand;
import com.jdl.sc.ofw.out.domain.extension.order.ISupplyRefOrderInfoExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.horiz.JDLApp;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description 冷链-修改关联单信息
 * <AUTHOR>
 * @Date 2023-06-05 16:09
 **/

@Slf4j
@Extension(code = OutboundProduct.CODE, scenario = {BusinessScene.MODIFY + JDLApp.SEPARATOR + JDLApp.COLD_CHAIN})
public class ModifyRefOrderInfoColdChainExt implements ISupplyRefOrderInfoExt {

    @Override
    public void execute(OrderFulfillmentContext context) {
        OrderFulfillmentModel model = context.getModel();
        ModifyCommand command = context.getCommand();

        // 订单类型-同仓转储单
        if (!OrderTypeEnum.DUMP.equals(model.getOrderType())) {
            return;
        }

        // 关联采购单号不为空
        if (StringUtils.isBlank(command.getPurchaseOrderNo())) {
            throw new IllegalArgumentException("同仓转储修改，关联采购单号为空");
        }

        model.getRefOrderInfo().assignPurchaseOrderNo(command.getPurchaseOrderNo());
    }
}
