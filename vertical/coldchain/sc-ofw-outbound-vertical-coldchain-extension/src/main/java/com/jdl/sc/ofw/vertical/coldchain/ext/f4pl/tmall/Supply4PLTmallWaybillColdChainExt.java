package com.jdl.sc.ofw.vertical.coldchain.ext.f4pl.tmall;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdl.sc.ofw.out.domain.dto.ElectronicSheetInfo;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLWaybillExt;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.horiz.OutboundProduct;
import com.jdl.sc.ofw.out.horiz.extension.f4pl.tmall.Supply4PLTmallWaybillExt;
import com.jdl.sc.ofw.out.horiz.infra.rpc.dto.bizplatform.cn.CreateCainiaoWaybillRequest;
import com.jdl.sc.ofw.vertical.coldchain.infra.rpc.translator.TmallRequestColdChainTranslator;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@Extension(code = OutboundProduct.CODE)
public class Supply4PLTmallWaybillColdChainExt extends Supply4PLTmallWaybillExt implements ISupply4PLWaybillExt {

    @Resource
    private TmallRequestColdChainTranslator tmallRequestColdChainTranslator;

    @Override
    public void execute(OrderFulfillmentContext context) {
        super.execute(context);
    }

    @Override
    protected CreateCainiaoWaybillRequest buildCreateCainiaoWaybillRequest(OrderFulfillmentModel model, ElectronicSheetInfo sheetInfo) {
        return tmallRequestColdChainTranslator.buildCreateCainiaoWaybillRequest(model, sheetInfo);
    }
}
