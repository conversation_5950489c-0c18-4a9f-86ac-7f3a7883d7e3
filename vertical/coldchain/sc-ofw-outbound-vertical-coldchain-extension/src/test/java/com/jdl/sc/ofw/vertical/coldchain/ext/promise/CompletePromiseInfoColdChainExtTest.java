package com.jdl.sc.ofw.vertical.coldchain.ext.promise;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeResponse;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.util.SwitchKeyCommonUtils;
import com.jdl.sc.ofw.out.domain.model.Make;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.domain.rpc.PromiseAcl;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-04-17 17:25
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CompletePromiseInfoColdChainExtTest {

    @InjectMocks
    private CompletePromiseInfoColdChainExt completePromiseInfoColdChainExt;

    @Mock
    private PromiseAcl promiseAcl;

    @Mock
    private ProductCenterServiceAcl productCenterServiceAcl;

    @Mock
    private SwitchKeyUtils switchKeyUtils;

    @Mock
    private SwitchKeyCommonUtils switchKeyCommonUtils;

    private static OrderFulfillmentModel model;

    public static ProductCollection productCollection;

    @Before
    public void setUp(){
        productCollection = new ProductCollection();
    }

    @Test
    public void t1(){
        WarehouseAndDeliveryTimeResponse response = new WarehouseAndDeliveryTimeResponse();
        response.setResultCode(1);
        response.setDeliveryDate(new Date());
        response.setBatchEndTime("");
        response.setBatchStartTime("");
        response.setSendpay(String.format("%0200d", 0));
        response.setCoddate(new Date());
        when(promiseAcl.getPromiseColdChain(any())).thenReturn(response);
        when(productCenterServiceAcl.isTrustSellerTransport(any())).thenReturn(false);
        productCollection.enroll(ProductEnum.LL_HD.toProduct());
        productCollection.enroll(ProductEnum.LL_SD.toProduct());
        productCollection.enroll(ProductEnum.QUALITY_SERVICE.toProduct());
        //productCollection.enroll(ProductEnum.LIGHTNING_DELIVERY.toProduct());
        OrderFulfillmentModel model1 = OrderFulfillmentModel.builder()
                .products(productCollection)
                .nonstandardProducts(new NonstandardProducts())
                .customer(Customer.builder()
                        .accountNo("EBU2992929")
                        .build())
                .shipment(Shipment.builder()

                        .build())
                .channel(Channel.builder().channelNo("uuu").build())
                .build();
        OrderFulfillmentContext context =
                new OrderFulfillmentContext(new BusinessIdentity("cn_jdl_sc", "outbound_sale_sc_ofc", BusinessScene.GENERATE), model1, null);
        completePromiseInfoColdChainExt.execute(context);
    }

    @Test
    public void t2(){
        productCollection.enroll(ProductEnum.LL_CP.toProduct());
        OrderFulfillmentModel model1 = OrderFulfillmentModel.builder()
                .products(productCollection)
                .build();
        OrderFulfillmentContext context =
                new OrderFulfillmentContext(new BusinessIdentity("cn_jdl_sc", "outbound_sale_sc_ofc", BusinessScene.GENERATE),model1, null);
        completePromiseInfoColdChainExt.execute(context);
    }

    @Test
    public void t3(){
        try {
            when(promiseAcl.getPromiseColdChain(any())).thenReturn(null);
            //productCollection.enroll(ProductEnum.LL_CP.toProduct());
            OrderFulfillmentModel model1 = OrderFulfillmentModel.builder()
                    .products(productCollection)
                    .build();
            OrderFulfillmentContext context =
                    new OrderFulfillmentContext(new BusinessIdentity("cn_jdl_sc", "outbound_sale_sc_ofc", BusinessScene.GENERATE), model1, null);
            completePromiseInfoColdChainExt.execute(context);
        }catch(Exception e){
            log.error(e.getMessage());
        }
    }

    @Test
    public void execute() {
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity())
                .model(Make.anOutboundModel_B2C_JDPS(new ProductCollection()))
                .build();
        when(productCenterServiceAcl.isTrustSellerTransport(any())).thenReturn(true);
        when(switchKeyUtils.containBpromiseDeptNo(any())).thenReturn(true);
        when(switchKeyCommonUtils.materialTimeLayerDeptNoContains(any())).thenReturn(true);
        WarehouseAndDeliveryTimeResponse response = new WarehouseAndDeliveryTimeResponse();
        response.setResultCode(1);
        response.setCoddate(new Date());
        response.setSendpay("100000000000000000000000000000000000000000000000000");
        when(promiseAcl.getPromiseColdChain(any())).thenReturn(response);
        completePromiseInfoColdChainExt.execute(context);
        response.setDeliveryDate(new Date());
        response.setOutStoreTime(new Date());
        //s1
        response.setStoreDeliveryHandoverTime(new Date());
        completePromiseInfoColdChainExt.execute(context);
        //s2
        response.setExpectPackageDate(new Date());
        completePromiseInfoColdChainExt.execute(context);
    }
}
