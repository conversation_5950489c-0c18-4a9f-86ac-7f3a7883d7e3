package com.jdl.sc.ofw.outbound.dto.vo;

import com.jdl.sc.ofw.outbound.spec.enums.InvoiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackDetailsCollectEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PrintMedicinePriceBalanceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ProductSplitResultSourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WarehouseShipmentModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 履约信息
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Fulfillment implements Serializable {

    /**
     * 预占结果
     */
    public boolean occupyResult;

    /**
     * 库存不足详情
     */
    public List<StockDetail> stockDetailList;

    /**
     * 货品履约方式
     */
    private boolean cargoFulfillmentWay;

    /**
     * 是否允许缺量履约
     */
    private boolean partialFulfillment;
    /**
     * 商家包装类型编码
     */
    private String sellerPackTypeNo;
    /**
     * 商家包装类型名称
     */
    private String sellerPackTypeName;
    /**
     * 商家指定包装类型,只有soMark22=5时会传值5
     */
    private String sellerAssignPackType;
    /**
     * 商家个性化打印信息
     * 对应的数据格式为: "{orderExtendInfo:{},skuPrintInfoList:[]}"
     */
    public String sellerIndividuationPrintInfo;

    /**
     * 	是否三方保价
     */
    public String thirdInsured;
    /**
     * 三方价保金额
     */
    public String thirdGuaranteeMoney;

    /**
     * 货票同行标识枚举
     * 0-不开发票，1-普通发票，3-增值发票，4-电子发票
     */
    private InvoiceEnum invoiceEnumFlag;
    /**
     * 订单优先级
     */
    private Integer orderPriority;
    /**
     * 订单加急类型 soMark84
     */
    private String orderUrgencyType;

    /**
     * 是否跳过手机号解密
     */
    private boolean skipMobileDecrypt;

    /**
     * 信任京东出库体积和重量
     */
    private boolean trustJDMetrics;
    /**
     * 是否使用抖音组件打印面单
     */
    private String printComponentType;

    /**
     * 客户自定义属性
     */
    private String customField;
    /**
     * 天猫订单京广流程
     */
    private boolean tmDeliveryByJgFlag;

    /**
     * 是否使用云仓菜鸟电子面单
     */
    private boolean clpsCainiaoElecFlag;
    /**
     * 天猫订单类型
     */
    private String tmOrderFlag;
    /**
     * 是否大件包裹打印仓配黑名单
     */
    private boolean packageSignFlag;

    /**
     * 是否有动物检疫正
     */
    private boolean quarantineCert;

    /**
     * 流通监管码
     */
    private String supervisionCode;
    /**
     * 开票员
     */
    private String invoiceChecker;
    /**
     * 付款方式
     */
    private String paymentType;
    /**
     * 销售类型
     */
    private String saleType;

    /**
     * 存储温层
     */
    private String warmLayer;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次数
     */
    private String batchQuantity;

    /**
     * 库位码
     */
    private String stationCode;

    /**
     * 要求送达时间
     */
    private String requireConsigneeTimeTo;

    /**
     * 商家自定义图片链接地址（好评卡信息）
     */
    private String sellerCustomPictureUrl;

    /**
     * 越库类型 0-非越库、1-计划型越库、2-机会型越库
     */
    private String crossDockType;

    /**
     * 是否商家下发打印信息 联通华盛使用
     */
    private boolean printInfoFromSeller;

    /**
     * 马甲公司编码（众邮订单）
     */
    private String mjCompanyCode;

    /**
     * 临期安全天数
     */
    private String adventSafeDays;

    /**
     * 包裹明细管理
     */
    private PackDetailsCollectEnum packDetailsCollect;

    /**
     * 逻辑因子,来源于 extendfield.logicParam
     */
    private String logicParam;

    /**
     * 业务员+业务员电话 打印占位符使用 今麦郎新增
     */
    private String salesmanAttribute;

    /*
     * 妥投特殊要求（枚举： 1 - 必须妥投/ 2 - 必须拒收/ 3 - 必须半收，下单默认传必须妥投）
     * 今麦郎新增
     */
    private String customerSignforAttributes;

    /**
     * KA扩展点
     */
    private Integer kaExtensionPoint;

    /**
     * 平台业务模式: 1-唯品会JIT 2-唯品会JITX
     */
    private String platformBusinessModel;

    /**
     * 商家指定箱明细
     */
    private String boxInfos;

    /**
     * 创维及大件云打印使用,JFS 传递给大件中间件 自行处理
     */
    private String skyWorthWaybill;

    /**
     * 库存预占结果类型，包括： 1- 零库存预占；2- 缺量预占；3- 全量预占
     */
    private Integer occupyResultType;

    /**
     * 平台要求出库时间
     * 时间格式：YYYY-MM-DD HH:mm:ss
     */
    private String platformExpectWmsDeliveryTime;

    /**
     * 平台要求揽收时间
     * 时间格式：YYYY-MM-DD HH:mm:ss
     */
    private String platformExpectPickupTime;

    /**
     * 包裹限制
     */
    private Map<String, String> packageRestrictions;


    /**
     * 电商订单发货策略
     * 1: 一物一检
     */
    private String ecpOrderDeliveryStrategy;

    /**
     * 对应零售拆分的两段单据中的第一段含金额信息的消费者下单的单据的订单号
     */
    private String ecpFirstOrderNo;

    /**
     * 电商订单销售策略，“1”为指数商品订单，不传或为空，无策略
     */
    private String ecpOrderSalesStrategy;

    /**
     * 39003001 冻损免赔白名单
     * 39003002 冻损免赔
     * 39003003 包装升级
     */
    private String frozenDamageMethod;

    /**
     * 产品分单的结果来源
     */
    private ProductSplitResultSourceEnum productSplitResultSource;
    /**
     * 是否复用运单号
     * 0-否 1-是
     */
    private String reusedHistoryWaybillType;

    /**
     * 是否打印医药冲价单
     */
    private PrintMedicinePriceBalanceEnum isPrintMedicinePriceBalance;

    /**
     * 序列号校验来源 1-京东平台国补
     */
    private String serialNumberCheckChannel;

    /**
     *  商家会员客户的会员卡
     */
    private String memberCardId;
    /**
     *  会员名称
     */
    private String memberName;

    /**
     *  商家子单（原始订单)(接收订单后转换成dtcjimkv透传给wms)
     */
    private String customizeOrderPrintInfo;

    /**
     * <a href="https://joyspace.jd.com/pages/fdgypS2ILBIZWuVBmJyn">分批次出</a>
     *  batchOutbound-分批次出库
     */
    private String outboundStrategy;

    /**
     * 电商订单类型 jingxipingou（京喜拼购）
     */
    private String ecpOrderType;

    /**
     * 大小件同仓配送模式
     * LARGE_WAREHOUSE_SMALL_SHIPMENT-大件仓小件配；
     * 默认无；
     */
    private WarehouseShipmentModeEnum warehouseShipmentMode;

    /**
     * 货品主增关系
     */
    private String cargoGiftRelationInfo;

    /**
     * jit时效
     */
    private String jitPromise;

    /**
     * 单据生产时效类型
     */
    private String productionPromiseType;

    /**
     * 缺货处理方式
     */
    private String stockShortsProcessWay;

    /**
     * 七鲜门店名称
     */
    private String sevenFreshStoreName;

    /**
     * 预估送达时间段
     */
    private String estimatedDeliveryTimePeriod;

    /**
     * 包装费
     */
    private String packagingFee;

    /**
     * 加工类型
     * 1-服饰定制
     */
    private String processingWay;
    /**
     *  商家箱明细信息dtc
     */
    private String customerBoxInfos;

    /**
     * 扩展信息
     */
    private Map<String,String> extendProps;
    /**
     *商家客户订单号
     */
    private String sellerExternalOrderNo;
    /**
     * 仓运交接提货要求
     */
    private String handoverPickupRequirements;


    /**
     * 订单主辅标识
     * MAIN_ORDER-主单，SUB_ORDER-辅单
     */
    private String orderMainSubFlag;
}
