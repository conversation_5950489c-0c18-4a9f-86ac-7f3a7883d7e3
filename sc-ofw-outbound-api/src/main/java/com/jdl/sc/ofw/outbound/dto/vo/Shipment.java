package com.jdl.sc.ofw.outbound.dto.vo;

import com.jdl.sc.ofw.outbound.spec.enums.BillingTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.DeliveryServiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.GoodsIsolateTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackageProduceModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PickupTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.SignBillTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 配送信息
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Shipment implements Serializable {
    /**
     * 承运商编码
     */
    private String shipperNo;

    /**
     * 承运商类型
     */
    private Integer shipperType;

    /**
     * 承运商名称
     */
    private String shipperName;

    /**
     * 预期送货开始时间
     */
    private Date expectDeliveryStartTime;

    /**
     * 预期送货结束时间
     */
    private Date expectDeliveryEndTime;

    /**
     * 站点编号
     */
    private String endStationNo;

    /**
     * 站点名称
     */
    private String endStationName;

    /**
     * 非接触收货类型
     */
    private Integer contactlessReceiveType;

    /**
     * 非接触收货类型-指定地点
     */
    private String assignedAddress;

    /**
     * 半收类型
     */
    private Integer halfReceive;
    /**
     * 派送方式
     * 1：送货上门
     * <p>
     * 2：自提
     */
    private Integer deliveryType;

    /**
     * 计费模式
     */
    private BillingTypeEnum billingType;

    /**
     * 原包标识
     */
    private Integer logisticsOriginalPackage;

    /**
     * 包装免赔标识，多个的情况用 , 分隔
     * 枚举值为1：全部免赔，2：破损免赔，3：外包完好免赔，4：冻损免赔
     */
    private String deductibleCompensation;

    /**
     * 城配标识
     */
    private boolean cityDeliveryType;

    /**
     * 期望的城配标识
     */
    private boolean expectCityDeliveryType;

    /**
     * 青龙订单类型
     */
    private String tmsDeliveryType;

    /**
     * 售后联系人
     */
    private String contactPerson;

    /**
     * 售后联系电话
     */
    private String contactPhone;

    /**
     * 售后联系地址
     */
    private String contactAddress;

    /**
     * 开箱验货类型
     */
    private String checkGoodsType;

    /**
     * 重货上楼标识
     */
    private boolean heavyGoodsUpstairs;

    /**
     * 隐私通话标识
     */
    private boolean hidePrivacyType;

    /**
     * 运输模式
     */
    private String transportMode;

    /**
     * 预计送达开始时间
     */
    private Date expectDeliveryStartDate;

    /**
     * 城配卡单开始时间
     */
    private Date cityDeliveryStartDate;

    /**
     * 城配卡单结束时间
     */
    private Date cityDeliveryEndDate;

    /**
     * 城配完成时间
     */
    private String cityDeliveryDeadline;

    /**
     * 城配完成时间类型
     */
    private Integer cityDeadlineType;

    /**
     * 时效名称
     */
    private String agingName;

    /**
     * 城配-派车单号
     */
    private String dispatchNo;
    /**
     * 城配-运输单数
     */
    private String transportCount;
    /**
     * 城配-装车顺序
     */
    private String loadSequence;
    /**
     * 城配-卡位号
     */
    private String cardNo;
    /**
     * 计划始发进场时间
     */
    private String planBeginEnterTime;
    /**
     * 运输类型
     */
    private Integer transportType;
    /**
     * 青龙业主号
     */
    private String bdOwnerNo;
    /**
     * 商家意向运输类型
     * isv场景: 建单时传入商家意向运输类型
     */
    private Integer expectTransportType;
    /**
     * 配送履约渠道
     *  1-京东
     *  2-抖音
     *  3-拼多多电子面单
     *  4-菜鸟电子面单
     *  5-唯品会电子面单
     *  6-快手电子面单
     *  7-美团电子面单
     *  8-得物电子面单
     *  9-京配马甲（来源=EDI、ISVSOURCE=640、销售平台=274、仓库类型=中小件）
     */
    private String deliveryPerformanceChannel;

    /**
     * 预分拣结果 站点编码
     */
    private String preSiteNo;

    /**
     * 预分拣结果 站点名称
     */
    private String preSiteName;

    /**
     * 部分收款
     */
    private boolean partialPayment;

    /**
     * 三方运单号,仅转三方场景使用
     */
    private String thirdWaybill;

    /**
     * T&P随单运单号
     */
    private String tpWaybill;

    /**
     * 三方快递方式
     */
    private String thirdExpressType;

    /**
     * 是否菜鸟包裹
     */
    private Integer CNTemplate;

    /**
     * 只需要三方运单号
     */
    private Integer needExternalWaybillOnly;

    /**
     * 打印信息
     */
    private PrintExtendInfo printExtendInfo;
    /*
    * 签单返还类型
    * 0-无签单 1-纸质单 3-电子单 4-纸质单+电子单
     * */
    private SignBillTypeEnum signBillTypeEnum;

    /**
     * 签单返还日期
     */
    private String reReceiveDate;

    /**
     * 签单返还收件人姓名
     */
    private String reReceiveName;

    /**
     * 签单返还收件人手机
     */
    private String reReceiveMobile;

    /**
     * 签单返还收件人电话
     */
    private String reReceivePhone;

    /**
     * 签单返还收件人地址
     */
    private String reReceiveAdress;

    /**
     * 三方产品类型
     */
    private String thirdExpressProduct;

    /**
     * 车型 (订单中心下发)
     */
    private String vehicleType;

    /**
     * 仓配揽收标识
     */
    private String express;

    /**
     * 激活卡业务
     */
    private String activationCardService;

    /**
     *如果是身份证签收，则需要保留6到18位的数字字母的签收
     */
    private String signIDCode;

    /**
     * 签收类型
     */
    private String signType;

    /**
     * 运单号，随单指定的运单号，非转三方场景使用
     */
    private String waybillNo;

    /**
     * 包裹生产模式
     */
    private PackageProduceModeEnum packageProduceMode;

    /**
     * 首个包裹下发
     */
    private Boolean firstWaybillIssue;

    /**
     * 配送温层
     */
    private String warmLayer;

    /**
     * 隔离运输（对应原清真和易污染）
     */
    private GoodsIsolateTypeEnum goodsIsolateType;

    /**
     * 计划投递时间
     */
    private Date planDeliveryTime;

    /**
     * 城配-串点数
     */
    private String bunchQty;

    /**
     * 城配-公里数
     */
    private String kilometers;

    /**
     * 商家承运商编码
     */
    private String sellerShipperNo;

    /**
     * 商家承运商名称
     */
    private String sellerShipperName;

    /**
     * 时效有效性标识
     */
    private String expectDeliveryTimeFlag;

    /**
     * 预分拣结果路区
     */
    private String road;

    /**
     * 是否送货入仓
     */
    private Boolean deliveryIntoWarehouse;

    /**
     * 入仓预约码
     */
    private String warehousingAppointmentCode;
    /**
     * 入仓预约开始时间
     */
    private String warehousingAppointmentStartTime;
    /**
     * 入仓预约结束时间
     */
    private String warehousingAppointmentEndTime;
    /**
     * 入仓备注
     */
    private String warehousingAppointmentRemark;

    /**
     * 用于回传商家
     * 司机
     */
    private String deliveryStaffName;

    /**
     * 用于回传商家
     * 车牌号
     */
    private String vehicleNumber;

    /**
     * 用于回传商家
     * 操作时间
     */
    private String operateTime;

    /**
     * 是否开启奢侈品保证服务
     */
    private Boolean luxurySecurity;

    /**
     * 是否妥投强制拍照
     */
    private Boolean deliveryForcePhoto;

    /**
     * 是否妥投强制电联
     */
    private Boolean forceContact;

    /**
     * 是否允许拒收 0 允许拒收、1 不允许拒收、2 必须拒收 
     */
    private String refusalAllowed;

    /**
     * 期望配送资源 1=合同物流
     */
    private String expectDeliveryResource;

    /**
     * 运单号获取方式:
     * <p>
     * 1-无运单号
     * 2-从履约获取
     * 3-由客户下发
     * 4-从客户获取
     * <p>
     * 5-首单号客户下发，其余履约获取
     * 6-首单号客户下发，后续增量从客户获取
     * 7-首单号客户下发，后续全量从客户获取
     * 8-仓储发货从承运商获取
     */
    private Integer waybillNoFetchType;


    /**
     * 分批派送
     * 多包裹是否允许先到先送
     */
    private Boolean deliveryInBatches;

    /**
     * 电商平台物流服务，如抖音的"音需达"
     */
    private String platformLogisticsService;


    /**
     * 派送服务标识 0-默认，1-预约派送
     */
    private DeliveryServiceEnum deliveryService;
    /**
     * 非本人签收 是否强制外呼标识，枚举值：0-否 1-是
     */
    private String nonSelfSignCall;

    /**
     * 托寄物管控规则：false-不校验、true-校验
     */
    private Boolean consignmentControlRules;


    /**
     * 包装标准，1-等于JDL标准 2-高于JDL标准 3-低于JDL标准
     */
    private String packagingStandards;
    /**
     * 数据传输要求
     * 1-不需要(或者不传此服务标识key)
     * 2-收货人信息加密
     * 3-发货人信息加密
     * 4-收发货人信息全加密
     */
    private String dataTransferRequirements;

    /**
     * 托寄物类型: 书籍、电子产品、服装
     */
    private String consignmentType;
    /**
     * 揽收类型 1-上门揽件；2-自送
     */
    private PickupTypeEnum pickupType;
    /**
     * TC专用预计揽收开始时间
     */
    private Date tcExpectPickupStartTime;
    /**
     * TC专用预计揽收结束时间
     */
    private Date tcExpectPickupEndTime;

    /**
     * 商家运输方式编码
     */
    private String customerTransportType;

    /**
     * 产品分单使用货品数量类型 0计划数量，1预占数量
     */
    private String productSplitCargoQuantityType;

    /**
     * 商家运输方式名称
     */
    private String customerTransportName;

    /**
     * 服务要求
     */
    private Map<String, String> serviceRequirements;

    /**
     * 流向类型，0或空为跳过流向处理过程（按大陆处理），1为不跳过（目前只涉及港澳）
     */
    private String flowType;

    /**
     * 卸货道口
     */
    private String unloadingDock;

    /**
     * 客户自定义无接触收货方式
     */
    private String customerContactlessType;

    /**
     * 即时配送方式
     */
    private String instantDeliveryType;

    /**
     * GIS围栏ID
     */
    private String gisFenceId;
}
