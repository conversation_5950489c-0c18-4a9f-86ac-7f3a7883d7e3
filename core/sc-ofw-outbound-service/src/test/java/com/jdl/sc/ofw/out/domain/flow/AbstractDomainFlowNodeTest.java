package com.jdl.sc.ofw.out.domain.flow;

import com.jdl.sc.ofw.out.domain.extension.disclaimer.ISupplyDisclaimerInfoExt;
import com.jdl.sc.ofw.out.domain.rpc.OrderHoldAcl;
import com.jdl.sc.ofw.out.domain.ability.mutong.MuTongTransportAbility;
import com.jdl.sc.ofw.out.domain.ability.order.OrderSyncCallbackAbility;
import com.jdl.sc.ofw.out.domain.ability.order.DefaultOrderStatusCallbackAbility;
import com.jdl.sc.ofw.out.domain.dto.*;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import lombok.Builder;
import com.jdl.sc.ofw.out.domain.extension.wms.ITransportToWMSExt;
import com.jdl.sc.ofw.out.domain.ability.shipper.*;
import com.jdl.sc.ofw.out.domain.model.*;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import java.math.BigDecimal;
import com.jdl.sc.ofw.out.domain.ability.hold.OrderHoldReleaseAbility;
import com.jdl.sc.ofw.out.domain.ability.weightvolume.WeightVolumeStandardizationAbility;
import com.jdl.sc.ofw.out.domain.ability.promise.DefaultEpidemicPauseAbility;
import com.jdl.sc.ofw.out.common.dict.GoodsTypeEnum;
import com.jdl.sc.ofw.out.domain.ability.basic.DefaultDeliveryAbility;
import com.jdl.sc.ofw.out.domain.flow.activecitydelivery.*;
import com.jdl.sc.ofw.out.domain.extension.collect.ICollectOrderExt;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.ofw.out.domain.extension.validate.IPrePaymentValidateExt;
import com.jdl.sc.ofw.out.domain.ability.finance.*;
import com.jdl.sc.ofw.out.domain.extension.shipper.IShipSplitBdOwnerNoExt;
import com.jdl.sc.ofw.out.domain.ability.validate.PrePaymentValidateAbility;
import com.jdl.sc.ofw.out.domain.flow.delivery.*;
import com.jdl.sc.ofw.out.domain.ability.pause.*;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.out.domain.ability.customsclearance.*;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import javax.validation.constraints.NotNull;
import com.jdl.sc.ofw.out.domain.ability.pause.OrderHoldBeforeTmsStageAbility;
import com.jdl.sc.ofw.out.domain.ability.product.ShipSplitDeliveryProductAbility;
import com.jdl.sc.ofw.out.domain.ability.purchase.*;
import com.jdl.sc.ofw.out.domain.extension.wms.IDeliveryCollectionDispatchInfoExt;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.exec.*;
import com.jdl.sc.ofw.out.domain.ability.insureprice.*;
import com.jd.matrix.core.domain.flow.InputMessage;
import cn.jdl.batrix.sdk.base.BDomainModel;
import com.jdl.sc.ofw.out.domain.ability.product.DefaultSupplyProductByPackAbility;
import com.jdl.sc.ofw.out.domain.extension.api.dto.wms.TransportToWMSApiDto;
import com.jdl.sc.ofw.out.common.dict.DecryptCommandEnum;
import com.jdl.sc.ofw.out.domain.ability.waybill.DiversionSelectionProcessAbility;
import com.jdl.sc.ofw.out.domain.extension.product.ISupplyNonStandardProductInfoExt;
import com.jdl.sc.ofw.out.domain.extension.address.IAddressCheckExt;
import com.jdl.sc.ofw.out.domain.flow.validate.*;
import com.jdl.sc.ofw.out.domain.ability.customsclearance.SupplyCustomsClearanceAbility;
import com.jdl.sc.ofw.out.domain.extension.weightvolume.IWeightVolumeStandardizationExt;
import com.jdl.sc.ofw.out.domain.ability.product.SupplyNonStandardProductInfoAbility;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import com.jdl.sc.ofw.out.domain.ability.promise.DefaultCompletePromiseInfoAbility;
import com.jdl.sc.ofw.out.domain.extension.oneself.IOneselfExecuteExt;
import com.jdl.sc.ofw.out.domain.ability.intercept.InterceptToWaybillAbility;
import com.jdl.sc.ofw.out.domain.extension.product.ISupplyThirdProductExt;
import com.jdl.sc.ofw.out.domain.extension.notify.INotifyExt;
import com.jdl.sc.ofw.out.domain.ability.pause.ForbidSalesPauseAbility;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.out.domain.ability.notify.NotifyAbility;
import java.util.List;
import com.jdl.sc.ofw.out.domain.ability.consignee.SupplyConsigneeAbility;
import com.jdl.sc.ofw.out.domain.extension.fulfillment.ISupplyFulfillmentExt;
import com.jdl.sc.ofw.out.domain.ability.wms.CancelTransportToWMSRpcAbility;
import com.jdl.sc.ofw.out.domain.extension.delivery.IDeliveryNetworkMatchExt;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import java.util.Objects;
import com.jdl.sc.ofw.out.domain.flow.basic.*;
import com.jdl.sc.ofw.out.domain.extension.capacity.ICapacitySplitOrderExt;
import com.jdl.sc.ofw.out.domain.ability.intercept.*;
import com.jdl.sc.ofw.out.domain.ability.goods.*;
import com.jdl.sc.ofw.out.domain.extension.valueadded.ISupplyCargoServiceInfoExt;
import com.jdl.sc.ofw.out.domain.extension.warehouse.ISupplyWarehouseExt;
import com.jdl.sc.ofw.out.domain.ability.address.*;
import com.jdl.sc.ofw.out.domain.ability.weightvolume.*;
import com.jdl.sc.ofw.out.domain.extension.pause.IOrderHoldBeforeWmsStageExt;
import cn.jdl.batrix.core.flow.domain.*;
import com.jd.matrix.sdk.base.DomainModel;
import com.jdl.sc.ofw.out.domain.flow.product.*;
import com.jdl.sc.ofw.out.domain.ability.delivery.DeliveryNetworkMatchAbility;
import java.util.Map;
import com.jdl.sc.ofw.out.domain.ability.oneself.DefaultOneselfExecuteAbility;
import com.jdl.sc.ofw.out.domain.flow.dispatch.*;
import com.jdl.sc.ofw.out.domain.ability.capacity.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusPhaseEnum;
import com.jdl.sc.ofw.out.domain.ability.order.*;
import com.jdl.sc.ofw.out.domain.extension.order.IOrderSyncCallbackExt;
import com.jdl.sc.ofw.out.domain.flow.weightvolume.*;
import com.jdl.sc.ofw.out.domain.ability.appointment.AppointmentDeliveryAbility;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.APPOINTMENT_DELIVERY_FLOW_CODE;
import com.jdl.sc.ofw.out.domain.ability.dept.DefaultSupplyDeptInfoAbility;
import org.springframework.stereotype.Component;
import com.jdl.sc.ofw.out.domain.ability.waybill.ReceivePlatformWaybillAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.extension.product.ISupplySpecialProductExt;
import com.jdl.sc.ofw.out.domain.extension.finance.IAttachFeeInfoExt;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.domain.extension.serviceplus.IDeliveryInstallationExt;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.ability.mutong.*;
import com.jdl.sc.ofw.out.common.constant.FlowConstants;
import com.jdl.sc.ofw.out.domain.extension.pause.IPauseAfterWmsExt;
import com.jdl.sc.ofw.out.domain.rpc.RetailOrderQueryAcl;
import com.jdl.sc.ofw.out.domain.extension.order.IOrderPersistenceExt;
import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.ofw.out.domain.flow.oneself.*;
import com.jdl.sc.ofw.out.domain.extension.goods.ISupplyGoodsInfoExt;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultSupplyBdOwnerNoAbility;
import com.jdl.sc.ofw.out.domain.pojo.RetailFinanceResponse;
import com.jdl.sc.ofw.out.domain.ability.purchase.SupplyPurchaseNoAbility;
import cn.jdl.batrix.core.flow.domain.BOutputMessage;
import com.jdl.sc.ofw.out.domain.flow.shipper.*;
import com.jdl.sc.ofw.out.domain.ability.disclaimer.*;
import cn.jdl.batrix.core.base.*;
import com.jdl.sc.ofw.out.domain.extension.product.ISupplyRearProductInfoExt;
import java.util.Optional;
import com.jdl.sc.ofw.out.domain.flow.wms.*;
import com.jdl.sc.ofw.outbound.spec.enums.CancelResultStateEnum;
import cn.jdl.batrix.sdk.base.BDomainAsyncModel;
import com.jdl.sc.ofw.out.domain.ability.basic.DefaultGeneralProcessControlAbility;
import com.jdl.sc.ofw.out.domain.extension.aftersale.ISupplyAfterSaleInfoExt;
import com.jd.pfinder.profiler.sdk.metric.Histogram;
import com.jdl.sc.ofw.out.domain.ability.subsidy.*;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.ability.activecitydelivery.*;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLDecryptMessageExt;
import com.jdl.sc.ofw.out.domain.ability.pause.InventoryPauseAbility;
import com.jdl.sc.ofw.out.domain.ability.delivery.*;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultReuseHistoricalWaybillAbility;
import com.jdl.sc.ofw.out.domain.flow.notify.*;
import com.jdl.sc.ofw.out.domain.extension.basic.ISupplyBasicExt;
import com.jdl.sc.ofw.out.domain.ability.aftersale.*;
import com.jdl.sc.ofw.out.domain.extension.address.ISupplyAddressExt;
import com.jdl.sc.ofw.out.domain.ability.finance.DefaultAttachFeeAbility;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.domain.extension.order.IOrderStatusCheckExt;
import com.jdl.sc.ofw.out.domain.extension.waybill.IModifyWaybillPromiseExt;
import com.jdl.sc.ofw.out.domain.ability.serviceOrder.SurveyServiceOrderAbility;
import com.jdl.sc.ofw.out.domain.extension.promise.IEpidemicPauseExt;
import com.jdl.sc.ofw.out.domain.ability.goods.DefaultSupplyGoodsInfoAbility;
import com.jdl.sc.ofw.out.domain.flow.collect.*;
import cn.jdl.batrix.core.BatrixInvoker;
import com.jdl.sc.ofw.out.domain.ability.dispatch.*;
import com.jdl.sc.ofw.out.domain.flow.purchase.*;
import java.io.Serializable;
import com.jdl.sc.ofw.out.domain.flow.*;
import com.jdl.sc.ofw.out.domain.ability.validate.*;
import com.jdl.sc.ofw.out.domain.ability.activecitydelivery.ActiveCityDeliveryAbility;
import com.jdl.sc.ofw.out.domain.extension.pause.ISemiProcessedPauseExt;
import lombok.AllArgsConstructor;
import com.jdl.sc.ofw.out.domain.extension.waybill.IDiversionSelectionProcessExt;
import com.jdl.sc.ofw.out.domain.extension.shipment.ISupplyShipmentExt;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.ability.address.AddressResolutionAbility;
import com.jdl.sc.ofw.out.domain.service.adapter.GrayscaleAdapter;
import com.jdl.sc.ofw.out.domain.extension.address.IAddressResolutionExt;
import com.jdl.sc.ofw.out.domain.ability.oneself.*;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.ability.valueadded.*;
import com.jdl.sc.ofw.out.domain.ability.serviceplus.*;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.ability.order.DefaultOrderStatusCheckAbility;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultModifyWaybillPromiseAbility;
import com.jd.pfinder.profiler.sdk.metric.*;
import com.jdl.sc.ofw.out.domain.flow.finance.*;
import com.jdl.sc.ofw.out.domain.ability.address.DefaultSupplyConsigneeAddressAbility;
import com.jdl.sc.ofw.out.domain.ability.shipper.SupplyShipperInfoAbility;
import java.util.Date;
import com.jdl.sc.ofw.out.domain.ability.warehouse.*;
import com.jdl.sc.ofw.out.domain.ability.shipper.ShipSplitBdOwnerNoAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import lombok.experimental.SuperBuilder;
import com.jdl.sc.ofw.out.domain.extension.dept.ISupplyDeptInfoExt;
import com.jdl.sc.ofw.out.domain.flow.serviceOrder.*;
import com.jdl.sc.ofw.out.domain.flow.insureprice.*;
import com.jdl.sc.ofw.out.domain.extension.split.IShipmentAndProductSplitExt;
import com.jdl.sc.ofw.out.domain.ability.promise.DefaultCustomPresortAbility;
import com.jdl.sc.ofw.out.domain.extension.waybill.ISupplyBdOwnerNoExt;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import com.jdl.sc.ofw.out.common.constant.DomainConstants;
import com.jdl.sc.ofw.out.domain.extension.promise.ICompletePromiseInfoExt;
import com.jdl.sc.ofw.out.domain.extension.product.ISupplyProductByPackExt;
import java.util.ArrayList;
import com.jdl.sc.ofw.out.domain.ability.pause.OrderHoldBeforeWmsStageAbility;
import com.jdl.sc.ofw.out.domain.extension.refund.IRefundWarehouseTypeExt;
import com.jdl.sc.ofw.out.domain.flow.customsclearance.*;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.out.domain.extension.purchase.ISupplyPurchaseNoExt;
import com.jdl.sc.ofw.out.domain.ability.address.AddressCheckAbility;
import org.apache.commons.lang3.StringUtils;
import com.jdl.sc.ofw.out.domain.ability.collect.DefaultCollectOrderAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import cn.jdl.batrix.core.base.BIDomainAbilityExtension;
import com.jdl.sc.ofw.out.domain.ability.serviceplus.DeliveryInstallationAbility;
import com.jdl.sc.ofw.out.domain.flow.address.*;
import com.jdl.sc.ofw.out.domain.ability.collect.*;
import com.jdl.sc.ofw.out.domain.model.annotation.AbilityScene;
import com.jdl.sc.ofw.out.domain.flow.subsidy.*;
import com.jdl.sc.ofw.out.domain.ability.fulfillment.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import cn.jdl.batrix.core.flow.node.adapt.*;
import com.jd.pfinder.profiler.sdk.PfinderContext;
import com.jdl.sc.ofw.out.domain.ability.shipper.ShipSplitAbility;
import com.jdl.sc.ofw.out.domain.ability.order.SupplyRefOrderInfoAbility;
import com.jdl.sc.ofw.out.domain.extension.api.dto.wms.TransportToWMSApiRequest;
import com.jdl.sc.ofw.out.domain.extension.intercept.IInterceptToWaybillExt;
import cn.jdl.batrix.core.base.BBaseDomainAbility;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import com.jdl.sc.ofw.out.domain.ability.pause.hanlder.OrderHoldJudgeHandler;
import org.slf4j.MDC;
import com.jdl.sc.order.hold.api.dto.response.HoldResult;
import com.jdl.sc.ofw.out.domain.extension.shipper.IShipSplitExt;
import com.jdl.sc.ofw.out.domain.extension.subsidy.ISupplyThirdSubsidyExt;
import com.jdl.sc.ofw.out.domain.extension.IEmptyExt;
import com.jdl.sc.ofw.out.domain.extension.pause.ICityDeliveryPauseExt;
import com.jdl.sc.ofw.out.domain.extension.basic.IGeneralProcessControlExt;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.APPOINTMENT_DELIVERY_FLOW_NAME;
import com.jdl.sc.ofw.out.domain.extension.basic.IDeliveryInfoExt;
import lombok.Setter;
import com.jdl.sc.ofw.out.domain.ability.product.*;
import com.jdl.sc.ofw.out.domain.ability.valueadded.SupplyCargoServiceInfoAbility;
import com.jdl.sc.ofw.outbound.spec.response.Response;
import com.jdl.sc.ofw.out.domain.ability.f4pl.Supply4PLDecryptMessageAbility;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.domain.ability.basic.*;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.ability.appointment.*;
import com.jdl.sc.ofw.out.domain.extension.pause.IOrderHoldBeforeTmsStageExt;
import com.jdl.sc.ofw.out.domain.flow.appointment.*;
import com.jdl.sc.ofw.out.domain.flow.hold.*;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.GENERAL_PAUSE_FLOW_NAME;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLWaybillExt;
import com.jdl.sc.ofw.out.domain.ability.valueadded.DefaultValueAddedServiceAbility;
import com.jdl.sc.ofw.out.domain.ability.order.OrderCallbackAbility;
import com.jdl.sc.ofw.out.domain.ability.insureprice.ShipSplitInsurePriceAbility;
import com.jdl.sc.ofw.out.domain.extension.serviceOrder.ISupplyServiceOrderExt;
import com.jd.matrix.core.func.IReducer;
import com.jdl.sc.ofw.out.common.exception.*;
import com.jdl.sc.ofw.out.domain.ability.disclaimer.SupplyDisclaimerInfoAbility;
import com.jdl.sc.ofw.out.domain.extension.activecitydelivery.IActiveCityDeliveryExt;
import com.jdl.sc.ofw.out.domain.extension.shipper.ISupplyShipperInfoExt;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import cn.jdl.batrix.core.flow.domain.BDomainAsyncFlowNode;
import com.jdl.sc.ofw.out.domain.model.orderclob.ClobTypeEnums;
import com.jdl.sc.ofw.out.domain.extension.waybill.IReuseHistoricalWaybillExt;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.domain.extension.waybill.ISupplyPreWaybillNoExt;
import com.jdl.sc.ofw.out.domain.ability.trace.*;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.outbound.spec.enums.CancelTypeEnum;
import com.jdl.sc.ofw.out.domain.extension.waybill.ITransportToWaybillExt;
import com.jdl.sc.ofw.out.domain.ability.subsidy.SupplyThirdSubsidyAbility;
import com.jdl.sc.ofw.outbound.spec.enums.ShipModeEnum;
import com.jdl.sc.ofw.out.domain.extension.mutong.IMuTongTransportExt;

import com.jdl.sc.ofw.out.domain.flow.AbstractDomainFlowNode;

import com.jdl.sc.ofw.out.domain.extension.trace.IOrderTraceSubscribeExt;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ICallbackTo4PLExt;
import com.jd.matrix.core.SimpleReducer;
import com.jdl.sc.ofw.out.domain.extension.dispatch.ITransportToDispatchExt;
import com.jdl.sc.ofw.out.domain.ability.*;
import com.jdl.sc.ofw.out.domain.extension.order.IOrderStatusCallbackExt;
import com.jdl.sc.ofw.out.domain.extension.pause.IInventoryPauseExt;
import lombok.extern.jackson.Jacksonized;
import com.jdl.sc.ofw.out.domain.ability.wms.DefaultTransportToWMSAbility;
import com.jdl.sc.ofw.out.domain.ability.warehouse.DefaultSupplyWarehouseAbility;
import com.jdl.sc.ofw.out.domain.flow.order.*;
import com.jdl.sc.ofw.out.domain.ability.pause.PauseBeforeToWmsAbility;
import com.jdl.sc.ofw.out.domain.ability.billing.*;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.ability.split.DefaultShipmentAndProductSplitAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.extension.api.dto.vo.*;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.ability.f4pl.*;
import com.jdl.sc.ofw.out.domain.ability.f4pl.Supply4PLCompanyAbility;
import com.jd.pfinder.profiler.sdk.metric.MethodExecutionWatcher;
import com.jdl.sc.ofw.out.domain.extension.consignee.ISupplyConsigneeExt;
import com.jdl.sc.ofw.out.domain.ability.pause.PauseAfterWmsAbility;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jdl.sc.ofw.out.domain.ability.hold.*;
import com.jdl.sc.ofw.out.domain.ability.consignee.*;
import com.jdl.sc.ofw.out.domain.extension.product.IShipSplitDeliveryProductExt;
import com.jdl.sc.ofw.out.domain.ability.AbstractDomainAbility;
import com.jdl.sc.ofw.out.domain.model.exec.Command;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.domain.flow.AbstractDomainFlowNode;
import com.jdl.sc.ofw.out.domain.model.vo.Cancel;
import com.jdl.sc.ofw.out.domain.ability.dispatch.DefaultMarkingDispatchAbility;
import com.jdl.sc.ofw.out.domain.extension.appointment.IAppointmentDeliveryExt;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import com.jdl.sc.ofw.out.domain.ability.waybill.SupplyPreWaybillNoAbility;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultTransportToWaybillAbility;
import com.jdl.sc.ofw.out.domain.flow.promise.*;
import com.jdl.sc.ofw.out.domain.flow.intercept.*;
import com.jdl.sc.ofw.out.domain.extension.pause.IPauseBeforeToWmsExt;
import com.jdl.sc.ofw.out.domain.ability.shipment.SupplyShipmentAbility;
import com.jdl.sc.ofw.out.domain.model.bc.*;
import org.apache.commons.collections4.CollectionUtils;
import com.jdl.sc.ofw.out.domain.ability.pause.CityDeliveryPauseAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jdl.sc.ofw.out.domain.ability.finance.DefaultRetailFinanceAbility;
import com.jdl.sc.ofw.out.domain.ability.refund.RefundWarehouseTypeAbility;
import com.jdl.sc.ofw.out.domain.extension.delivery.ISupplyExpectDeliveryResourceExt;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import cn.jdl.batrix.sdk.base.*;
import com.jdl.sc.ofw.out.domain.ability.waybill.*;
import com.jdl.sc.ofw.out.domain.flow.serviceplus.*;
import com.jdl.sc.ofw.out.domain.flow.mutong.*;
import javax.annotation.Resource;
import com.jdl.sc.ofw.out.domain.ability.dept.*;
import com.jdl.sc.ofw.out.domain.flow.split.*;
import cn.jdl.batrix.event.spec.EventAction;
import com.jdl.sc.ofw.out.domain.ability.pause.SemiProcessedPauseAbility;
import com.jdl.sc.ofw.out.domain.extension.f4pl.ISupply4PLCompanyExt;
import com.jdl.sc.ofw.out.domain.flow.trace.*;
import com.jdl.sc.ofw.out.domain.ability.capacity.DefaultCapacitySplitOrderAbility;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.ability.basic.SupplyBasicAbility;
import com.jdl.sc.ofw.out.domain.ability.trace.DefaultOrderTraceSubScribeAbility;
import com.jdl.sc.ofw.out.domain.ability.order.OrderPersistenceAbility;
import com.jdl.sc.ofw.out.domain.ability.f4pl.Supply4PLWaybillAbility;
import com.jdl.sc.ofw.out.domain.ability.wms.DefaultDeliveryCollectionDispatchInfoAbility;
import com.jdl.sc.ofw.out.domain.ability.f4pl.DefaultCallbackTo4PLAbility;
import com.jdl.sc.ofw.out.domain.extension.dispatch.IMarkingDispatchExt;
import com.jdl.sc.ofw.out.common.exception.AbilityExtensionException;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.out.domain.extension.insureprice.IShipSplitInsurePriceExt;
import com.jdl.sc.ofw.out.domain.flow.capacity.*;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.out.domain.ability.promise.*;
import com.jdl.sc.ofw.out.domain.extension.pause.IMainGiftPauseExt;
import com.jdl.sc.ofw.out.domain.ability.aftersale.DefaultSupplyAfterSaleInfoAbility;
import com.jdl.sc.ofw.out.domain.ability.split.*;
import com.jdl.sc.ofw.out.domain.extension.waybill.IReceivePlatformWaybillExt;
import com.jdl.sc.ofw.out.domain.flow.waybill.*;
import com.jdl.sc.ofw.out.domain.model.product.ExpressProductEnum;
import com.jdl.sc.ofw.out.domain.flow.f4pl.*;
import com.jdl.sc.ofw.out.domain.model.vo.Money;
import com.jdl.sc.ofw.out.domain.extension.billing.ISupplyBillingTypeExt;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.flow.pause.*;
import com.jdl.sc.ofw.out.domain.ability.delivery.SupplyExpectDeliveryResourceAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.flow.disclaimer.*;
import com.jd.matrix.core.domain.flow.*;
import com.jdl.sc.ofw.out.domain.model.product.SolutionAttributeEnum;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.domain.ability.shipment.*;
import com.jdl.sc.ofw.out.domain.extension.valueadded.IValueAddedServiceExt;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.domain.ability.wms.*;
import com.jdl.sc.ofw.out.common.exception.BusinessDomainException;
import com.jdl.sc.ofw.out.domain.extension.hold.IOrderHoldReleaseExt;
import com.jdl.sc.ofw.out.domain.extension.api.wms.ITransportToWMSApiExt;
import com.jdl.sc.ofw.out.domain.ability.product.SupplyRearProductInfoAbility;
import com.jdl.sc.ofw.out.domain.ability.serviceOrder.*;
import com.jdl.sc.ofw.out.domain.ability.product.SupplyThirdProductAbility;
import com.jdl.sc.ofw.out.domain.extension.order.ISupplyRefOrderInfoExt;
import com.jdl.sc.ofw.out.domain.extension.promise.ICustomPresortExt;
import com.jdl.sc.ofw.out.domain.ability.pause.MainGiftPauseAbility;
import com.jdl.sc.ofw.out.domain.extension.order.IOrderCallbackExt;
import com.jdl.sc.ofw.out.domain.ability.billing.DefaultSupplyBillingTypeAbility;
import com.jdl.sc.ofw.out.domain.ability.dispatch.DefaultTransportToDispatchAbility;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.GENERAL_PAUSE_FLOW_CODE;
import com.jdl.sc.ofw.out.domain.ability.refund.*;
import com.jdl.sc.ofw.out.domain.ability.fulfillment.SupplyFulfillmentAbility;
import com.jdl.sc.ofw.out.domain.extension.customsclearance.ISupplyCustomsClearanceExt;
import com.jdl.sc.ofw.out.domain.ability.notify.*;
import com.jdl.sc.ofw.out.domain.ability.product.SupplySpecialProductAbility;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the AbstractDomainFlowNode class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractDomainFlowNodeTest {

	@Mock
	private AbstractDomainFlowNodeHandle abstractDomainFlowNodeHandle;

	@Mock
	private AbstractDomainAbility abstractDomainAbility;

	@Mock
	private BIDomainAbilityExtension bIDomainAbilityExtension;

	@Mock
	private MethodExecutionWatcher methodExecutionWatcher;

	@Mock
	private AbilityScene abilityScene;

	@Mock
	private Histogram histogram;

	@Mock
	private BusinessIdentity businessIdentity;

	@Mock
	private OrderFulfillmentContext orderFulfillmentContext;

    @Test
    public void testExecuteNullAbility() throws Exception {
        // generated by JoyCoder taskId a4b730f471ae
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        OrderFulfillmentContext orderFulfillmentContext = OrderFulfillmentContext.builder().build();
//        FlowNodeJSFConsumer flowNode = new FlowNodeJSFConsumer("test", new RunDataConfigDucc.RpcFlowNodeConfig());
//
//        node.execute(null, orderFulfillmentContext, flowNode);
    }

    @Test
    public void testExecuteValidAbility() throws Exception {
        // generated by JoyCoder taskId a4b730f471ae
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility ability = mock(AbstractDomainAbility.class);
        OrderFulfillmentContext orderFulfillmentContext = OrderFulfillmentContext.builder().build();
        FlowNodeJSFConsumer flowNode = new FlowNodeJSFConsumer("test", null);
        
        node.execute(ability, orderFulfillmentContext, flowNode);
        
    }

    @Test(expected = Exception.class) 
    public void testExecuteThrowsException() throws Exception {
        // generated by JoyCoder taskId a4b730f471ae
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility ability = mock(AbstractDomainAbility.class);
        OrderFulfillmentContext orderFulfillmentContext = OrderFulfillmentContext.builder().build();
        FlowNodeJSFConsumer flowNode = new FlowNodeJSFConsumer("test", null);
        
        doThrow(new RuntimeException()).when(ability).execute(orderFulfillmentContext, flowNode);
        
        node.execute(ability, orderFulfillmentContext, flowNode);
    }

    @Test
    public void testConstructor() {
        // generated by JoyCoder taskId a4b730f471ae
        // Mock static method
//        PowerMockito.mockStatic(PfinderContext.class);
        
        // Mock registry
        PfinderMetricRegistry mockRegistry = mock(PfinderMetricRegistry.class);
//        PowerMockito.when(PfinderContext.getMetricRegistry()).thenReturn(mockRegistry);
        
        // Mock histogram builder
        MetricBuilder.HistogramBuilder mockHistogramBuilder = mock(MetricBuilder.HistogramBuilder.class);
        when(mockRegistry.histogram(anyString())).thenReturn(mockHistogramBuilder);
        
        // Mock histogram
        Histogram mockHistogram = mock(Histogram.class);
        when(mockHistogramBuilder.build()).thenReturn(mockHistogram);

        // Create instance
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        // Verify interactions
    }

    @Test
    public void testConstructorWithNullRegistry() {
        // generated by JoyCoder taskId a4b730f471ae
        // Mock static method to return null
//        PowerMockito.mockStatic(PfinderContext.class);
//        PowerMockito.when(PfinderContext.getMetricRegistry()).thenReturn(null);

        // Should handle null registry gracefully
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
    }

    @Test
    public void testConstructorWithNullHistogramBuilder() {
        // generated by JoyCoder taskId a4b730f471ae
        // Mock static method
//        PowerMockito.mockStatic(PfinderContext.class);
        
        // Mock registry
        PfinderMetricRegistry mockRegistry = mock(PfinderMetricRegistry.class);
//        PowerMockito.when(PfinderContext.getMetricRegistry()).thenReturn(mockRegistry);
        
        // Return null histogram builder
        when(mockRegistry.histogram(anyString())).thenReturn(null);

        // Should handle null histogram builder
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
    }

    @Test
    public void testCallSyncFlow() {
        // generated by JoyCoder taskId a4b730f471ae
        // Arrange
        InputMessage inputMessage = new InputMessage("test");
        BOutputMessage expectedOutput = new BOutputMessage();
        
        when(histogram.watcher()).thenReturn(methodExecutionWatcher);
        when(abstractDomainFlowNodeHandle.execute(any(InputMessageDomain.class), any(AbstractDomainFlowNode.class)))
            .thenReturn(expectedOutput);

        // Act
//        BOutputMessage result = abstractDomainFlowNode.call(inputMessage);

        // Assert
    }

    @Test
    public void testCallAsyncFlow() {
        // generated by JoyCoder taskId a4b730f471ae
        // Arrange
        InputMessage inputMessage = new InputMessage("test");
        BOutputMessage expectedOutput = new BOutputMessage();

        when(histogram.watcher()).thenReturn(methodExecutionWatcher);
//        when(aabstractDomainFlowNodeHandle.execute(any(InputMessageDomain.class), any(AbstractDomainFlowNode.class)))
//            .thenReturn(expectedOutput);
//
        AbstractDomainFlowNode abstractDomainFlowNode = new ActiveCityDeliveryFlowNode();
//        // Act
        BOutputMessage result = abstractDomainFlowNode.call(inputMessage);

        // Assert
    }

    @Test(expected = RuntimeException.class)
    public void testCallWithException() {
        // generated by JoyCoder taskId a4b730f471ae
        // Arrange
        InputMessage inputMessage = new InputMessage("test");
        
        when(histogram.watcher()).thenReturn(methodExecutionWatcher);
        when(abstractDomainFlowNodeHandle.execute(any(InputMessageDomain.class), any(AbstractDomainFlowNode.class)))
            .thenThrow(new RuntimeException("Test exception"));

        // Act
        AbstractDomainFlowNode abstractDomainFlowNode = new ActiveCityDeliveryFlowNode();
        abstractDomainFlowNode.call(inputMessage);

        // Assert
    }

    @Test
    public void testCallWithNullMessage() {
        // generated by JoyCoder taskId a4b730f471ae
        // Arrange
        InputMessage inputMessage = new InputMessage(null);
        BOutputMessage expectedOutput = new BOutputMessage();

        when(histogram.watcher()).thenReturn(methodExecutionWatcher);
        when(abstractDomainFlowNodeHandle.execute(any(InputMessageDomain.class), any(AbstractDomainFlowNode.class)))
            .thenReturn(expectedOutput);

        // Act
        AbstractDomainFlowNode abstractDomainFlowNode = new ActiveCityDeliveryFlowNode();
        BOutputMessage result = abstractDomainFlowNode.call(inputMessage);

        // Assert
    }

    @Test
    public void testCallWithEmptyHeaderMessage() {
        // generated by JoyCoder taskId a4b730f471ae
        // Arrange
        InputMessage inputMessage = new InputMessage("test", new HashMap<>());
        BOutputMessage expectedOutput = new BOutputMessage();

        when(histogram.watcher()).thenReturn(methodExecutionWatcher);
        when(abstractDomainFlowNodeHandle.execute(any(InputMessageDomain.class), any(AbstractDomainFlowNode.class)))
            .thenReturn(expectedOutput);

        // Act
        AbstractDomainFlowNode abstractDomainFlowNode = new ActiveCityDeliveryFlowNode();
        BOutputMessage result = abstractDomainFlowNode.call(inputMessage);

        // Assert
    }

    @Test
    public void testGetDomainAbilityNullBusinessIdentity() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(null);

        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        try {
            node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
            fail("Should throw BusinessDomainException");
        } catch (BusinessDomainException e) {
            assertEquals(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR, e.code());
        }
    }

    @Test
    public void testGetDomainAbilityNullAbilities() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);

        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, (AbstractDomainAbility[])null);
        assertNull(result);
    }

    @Test
    public void testGetDomainAbilityEmptyAbilities() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, new AbstractDomainAbility[]{});
        assertNull(result);
    }

    @Test
    public void testGetDomainAbilityNullAbility() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, null, abstractDomainAbility);
        assertEquals(abstractDomainAbility, result);
    }

    @Test
    public void testGetDomainAbilityNoAnnotation() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        Mockito.when(abstractDomainAbility.getClass().getAnnotation(AbilityScene.class)).thenReturn(null);
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
        assertNull(result);
    }

    @Test
    public void testGetDomainAbilityNoMatchingScene() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        Mockito.when(abstractDomainAbility.getClass().getAnnotation(AbilityScene.class)).thenReturn(abilityScene);
        Mockito.when(abilityScene.businessScenes()).thenReturn(new String[]{"scene1"});
        Mockito.when(businessIdentity.getBusinessScene()).thenReturn("scene2");
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
        assertNull(result);
    }

    @Test
    public void testGetDomainAbilityDefaultAbility() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        Mockito.when(abstractDomainAbility.getClass().getAnnotation(AbilityScene.class)).thenReturn(abilityScene);
        Mockito.when(abilityScene.businessScenes()).thenReturn(new String[]{"scene1"});
        Mockito.when(businessIdentity.getBusinessScene()).thenReturn("scene1");
        Mockito.when(abstractDomainAbility.getAbilityExtensionClass()).thenReturn(null);
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
        assertEquals(abstractDomainAbility, result);
    }

    @Test
    public void testGetDomainAbilityNoExtensions() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        Mockito.when(abstractDomainAbility.getClass().getAnnotation(AbilityScene.class)).thenReturn(abilityScene);
        Mockito.when(abilityScene.businessScenes()).thenReturn(new String[]{"scene1"});
        Mockito.when(businessIdentity.getBusinessScene()).thenReturn("scene1");
        Mockito.when(abstractDomainAbility.getAbilityExtensionClass()).thenReturn(BIDomainAbilityExtension.class);
//        Mockito.when(BatrixInvoker.getNodeExtensionConfigList(orderFulfillmentContext, node)).thenReturn(null);
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
        assertNull(result);
    }

    @Test
    public void testGetDomainAbilityMatchingExtension() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        Mockito.when(abstractDomainAbility.getClass().getAnnotation(AbilityScene.class)).thenReturn(abilityScene);
        Mockito.when(abilityScene.businessScenes()).thenReturn(new String[]{"scene1"});
        Mockito.when(businessIdentity.getBusinessScene()).thenReturn("scene1");
        Mockito.when(abstractDomainAbility.getAbilityExtensionClass()).thenReturn(BIDomainAbilityExtension.class);
//        Mockito.when(BatrixInvoker.getNodeExtensionConfigList(orderFulfillmentContext, node)).thenReturn(Arrays.asList(bIDomainAbilityExtension));
        Mockito.when(bIDomainAbilityExtension.getClass().isAssignableFrom(BIDomainAbilityExtension.class)).thenReturn(true);
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        AbstractDomainAbility result = node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
        assertEquals(abstractDomainAbility, result);
    }

    @Test
    public void testGetDomainAbilityException() {
        // generated by JoyCoder taskId a4b730f471ae
        Mockito.when(orderFulfillmentContext.getBusinessIdentity()).thenReturn(businessIdentity);
        Mockito.when(abstractDomainAbility.getClass().getAnnotation(AbilityScene.class)).thenThrow(new RuntimeException());
        
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();
        
        try {
            node.getDomainAbility(orderFulfillmentContext, abstractDomainAbility);
            fail("Should throw RuntimeException");
        } catch (RuntimeException e) {
            // Expected
        }
    }

    @Test
    public void testExecuteWithNullContext() throws Exception {
        // generated by JoyCoder taskId a4b730f471ae
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();

        
        OrderFulfillmentContext orderFulfillmentContext = null;
        BOutputMessage result = node.execute(orderFulfillmentContext);
        assertNotNull(result);
    }

    @Test
    public void testExecuteWithValidContext() throws Exception {
        // generated by JoyCoder taskId a4b730f471ae
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();

        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
            .batrixInfo(null)
            .orderNo("123")
            .build();

        OrderFulfillmentContext orderFulfillmentContext = OrderFulfillmentContext.builder()
            .businessIdentity(null)
            .model(model)
            .command(null)
            .build();

        BOutputMessage result = node.execute(orderFulfillmentContext);
        assertNotNull(result);
    }

    @Test(expected = Exception.class) 
    public void testExecuteThrowsException1() throws Exception {
        // generated by JoyCoder taskId a4b730f471ae
        AbstractDomainFlowNode node = new ActiveCityDeliveryFlowNode();

        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
            .batrixInfo(null)
            .orderNo("123")
            .build();

        OrderFulfillmentContext orderFulfillmentContext = OrderFulfillmentContext.builder()
            .businessIdentity(null)
            .model(model)
            .command(null)
            .build();

        node.execute(orderFulfillmentContext);
    }

}