package com.jdl.sc.ofw.out.domain.flow;

import com.jdl.sc.ofw.out.domain.ability.mutong.MuTongTransportAbility;
import com.jdl.sc.ofw.out.domain.ability.order.OrderSyncCallbackAbility;
import com.jdl.sc.ofw.out.domain.ability.order.DefaultOrderStatusCallbackAbility;
import com.jdl.sc.ofw.out.domain.dto.*;
import com.jdl.sc.ofw.out.domain.monitor.enums.BizMarkerEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import lombok.Builder;
import com.jdl.sc.ofw.out.domain.model.*;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import java.math.BigDecimal;
import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import com.jdl.sc.ofw.out.domain.ability.hold.OrderHoldReleaseAbility;
import com.jdl.sc.ofw.out.domain.ability.weightvolume.WeightVolumeStandardizationAbility;
import com.jdl.sc.ofw.out.domain.ability.promise.DefaultEpidemicPauseAbility;
import com.jdl.sc.ofw.out.common.dict.GoodsTypeEnum;
import com.jdl.sc.ofw.out.domain.ability.basic.DefaultDeliveryAbility;
import com.jdl.sc.ofw.out.domain.flow.activecitydelivery.*;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.ofw.out.domain.ability.validate.PrePaymentValidateAbility;
import com.jdl.sc.ofw.out.domain.flow.delivery.*;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import javax.validation.constraints.NotNull;
import com.jdl.sc.ofw.out.domain.ability.pause.OrderHoldBeforeTmsStageAbility;
import com.jdl.sc.ofw.out.domain.ability.product.ShipSplitDeliveryProductAbility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.exec.*;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jdl.sc.ofw.out.domain.ability.product.DefaultSupplyProductByPackAbility;
import cn.jdl.batrix.sdk.base.BDomainModel;
import com.jdl.sc.ofw.out.common.dict.DecryptCommandEnum;
import com.jdl.sc.ofw.out.domain.ability.waybill.DiversionSelectionProcessAbility;
import com.jdl.sc.ofw.out.domain.flow.validate.*;
import com.jdl.sc.ofw.out.domain.ability.customsclearance.SupplyCustomsClearanceAbility;
import com.jdl.sc.ofw.out.domain.ability.product.SupplyNonStandardProductInfoAbility;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import com.jdl.sc.ofw.out.domain.ability.promise.DefaultCompletePromiseInfoAbility;
import com.jdl.sc.ofw.out.domain.ability.intercept.InterceptToWaybillAbility;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.out.domain.ability.pause.ForbidSalesPauseAbility;
import com.jdl.sc.ofw.out.domain.ability.notify.NotifyAbility;
import com.jdl.sc.core.cache.jimdb.JimClient;
import java.util.List;
import com.jdl.sc.ofw.out.domain.ability.consignee.SupplyConsigneeAbility;
import com.jdl.sc.ofw.out.domain.ability.wms.CancelTransportToWMSRpcAbility;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import java.util.Objects;
import com.jdl.sc.ofw.out.domain.flow.basic.*;
import com.jdl.sc.ofw.out.domain.model.RefSo;
import cn.jdl.batrix.core.flow.domain.*;
import com.jdl.sc.ofw.out.domain.flow.product.*;
import com.jdl.sc.ofw.out.domain.ability.delivery.DeliveryNetworkMatchAbility;
import java.util.Map;
import com.jdl.sc.ofw.out.domain.ability.oneself.DefaultOneselfExecuteAbility;
import com.jdl.sc.ofw.out.domain.model.vo.*;
import com.jdl.sc.ofw.out.domain.flow.dispatch.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusPhaseEnum;
import com.jdl.sc.ofw.out.domain.flow.weightvolume.*;
import com.jdl.sc.ofw.out.domain.ability.appointment.AppointmentDeliveryAbility;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.APPOINTMENT_DELIVERY_FLOW_CODE;
import com.jdl.sc.ofw.out.domain.ability.dept.DefaultSupplyDeptInfoAbility;
import org.springframework.stereotype.Component;
import com.jdl.sc.ofw.out.domain.ability.waybill.ReceivePlatformWaybillAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.common.constant.FlowConstants;
import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.ofw.out.domain.flow.oneself.*;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultSupplyBdOwnerNoAbility;
import com.jdl.sc.ofw.out.domain.ability.purchase.SupplyPurchaseNoAbility;
import cn.jdl.batrix.core.flow.domain.BOutputMessage;
import com.jdl.sc.ofw.out.domain.flow.shipper.*;
import java.util.Optional;
import com.jdl.sc.ofw.out.domain.flow.wms.*;
import cn.jdl.batrix.sdk.base.BDomainAsyncModel;
import com.jdl.sc.ofw.out.domain.ability.basic.DefaultGeneralProcessControlAbility;
import com.jd.pfinder.profiler.sdk.metric.Histogram;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.*;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.ability.pause.InventoryPauseAbility;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultReuseHistoricalWaybillAbility;
import com.jdl.sc.ofw.out.domain.flow.notify.*;
import com.jdl.sc.ofw.out.domain.ability.finance.DefaultAttachFeeAbility;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.domain.ability.serviceOrder.SurveyServiceOrderAbility;
import com.jdl.sc.ofw.out.domain.ability.goods.DefaultSupplyGoodsInfoAbility;
import com.jdl.sc.ofw.out.domain.flow.collect.*;
import cn.jdl.batrix.core.BatrixInvoker;
import com.jdl.sc.ofw.out.domain.flow.purchase.*;
import java.io.Serializable;
import com.jdl.sc.ofw.out.domain.flow.*;
import com.jdl.sc.ofw.out.domain.ability.activecitydelivery.ActiveCityDeliveryAbility;
import lombok.AllArgsConstructor;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.out.domain.ability.address.AddressResolutionAbility;
import com.jdl.sc.ofw.out.domain.service.adapter.GrayscaleAdapter;
import com.jdl.sc.ofw.out.domain.monitor.MonitorLogger;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;

import com.jdl.sc.ofw.out.domain.flow.AsyncDomainFlowNodeHandle;

import com.jdl.sc.ofw.out.domain.ability.order.DefaultOrderStatusCheckAbility;
import com.jdl.sc.ofw.out.common.exception.PauseException;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultModifyWaybillPromiseAbility;
import com.jdl.sc.ofw.out.domain.flow.finance.*;
import com.jdl.sc.ofw.out.domain.ability.address.DefaultSupplyConsigneeAddressAbility;
import com.jdl.sc.ofw.out.domain.ability.shipper.SupplyShipperInfoAbility;
import com.jdl.sc.ofw.out.common.dict.OperateType;
import java.util.Date;
import com.jdl.sc.ofw.out.domain.ability.shipper.ShipSplitBdOwnerNoAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import lombok.experimental.SuperBuilder;
import com.jdl.sc.ofw.out.domain.flow.serviceOrder.*;
import com.jdl.sc.ofw.out.domain.flow.insureprice.*;
import com.jdl.sc.ofw.out.domain.ability.promise.DefaultCustomPresortAbility;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import java.util.ArrayList;
import com.jdl.sc.ofw.out.domain.ability.pause.OrderHoldBeforeWmsStageAbility;
import com.jdl.sc.ofw.out.domain.flow.customsclearance.*;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.out.domain.ability.address.AddressCheckAbility;
import org.apache.commons.lang3.StringUtils;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import com.jdl.sc.ofw.out.domain.ability.collect.DefaultCollectOrderAbility;
import cn.jdl.batrix.core.base.BIDomainAbilityExtension;
import com.jdl.sc.ofw.out.domain.ability.serviceplus.DeliveryInstallationAbility;
import com.jdl.sc.ofw.out.domain.flow.address.*;
import com.jdl.sc.ofw.out.domain.model.annotation.AbilityScene;
import com.jdl.sc.ofw.out.domain.flow.subsidy.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jd.pfinder.profiler.sdk.PfinderContext;
import com.jdl.sc.ofw.out.domain.ability.shipper.ShipSplitAbility;
import com.jdl.sc.ofw.out.domain.ability.order.SupplyRefOrderInfoAbility;
import com.jdl.sc.ofw.outbound.spec.utils.UuidUtil;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import org.slf4j.MDC;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.APPOINTMENT_DELIVERY_FLOW_NAME;
import com.jdl.sc.ofw.out.domain.ability.valueadded.SupplyCargoServiceInfoAbility;
import lombok.Setter;
import com.jdl.sc.ofw.out.domain.ability.f4pl.Supply4PLDecryptMessageAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.jdl.sc.ofw.out.domain.flow.appointment.*;
import com.jdl.sc.ofw.out.domain.flow.hold.*;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.GENERAL_PAUSE_FLOW_NAME;
import com.jdl.sc.ofw.out.domain.ability.valueadded.DefaultValueAddedServiceAbility;
import com.jdl.sc.ofw.out.domain.ability.order.OrderCallbackAbility;
import com.jdl.sc.ofw.out.domain.ability.insureprice.ShipSplitInsurePriceAbility;
import com.jdl.sc.ofw.out.common.exception.*;
import com.jdl.sc.ofw.out.domain.ability.disclaimer.SupplyDisclaimerInfoAbility;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import cn.jdl.batrix.core.flow.domain.BDomainAsyncFlowNode;
import com.jdl.sc.ofw.out.domain.model.orderclob.ClobTypeEnums;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.outbound.spec.enums.ShipModeEnum;
import com.jdl.sc.ofw.out.domain.ability.subsidy.SupplyThirdSubsidyAbility;
import lombok.extern.jackson.Jacksonized;
import com.jdl.sc.ofw.out.domain.ability.wms.DefaultTransportToWMSAbility;
import com.jdl.sc.ofw.out.domain.ability.warehouse.DefaultSupplyWarehouseAbility;
import com.jdl.sc.ofw.out.domain.model.utils.OrderClobJsonUtils;
import com.jdl.sc.ofw.out.domain.flow.order.*;
import com.jdl.sc.ofw.out.domain.ability.pause.PauseBeforeToWmsAbility;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import com.jdl.sc.ofw.out.domain.ability.split.DefaultShipmentAndProductSplitAbility;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.ability.f4pl.Supply4PLCompanyAbility;
import com.jd.pfinder.profiler.sdk.metric.MethodExecutionWatcher;
import com.jdl.sc.ofw.out.domain.ability.pause.PauseAfterWmsAbility;
import com.jdl.sc.ofw.outbound.spec.enums.OutboundErrorSpec;
import com.jdl.sc.ofw.out.domain.ability.AbstractDomainAbility;
import com.jdl.sc.ofw.out.domain.model.exec.Command;
import com.jdl.sc.ofw.out.domain.model.vo.Cancel;
import com.jdl.sc.ofw.out.domain.flow.AbstractDomainFlowNode;
import com.jdl.sc.ofw.out.domain.ability.dispatch.DefaultMarkingDispatchAbility;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import java.time.Instant;
import com.jdl.sc.ofw.out.domain.ability.waybill.SupplyPreWaybillNoAbility;
import com.jdl.sc.ofw.out.domain.ability.waybill.DefaultTransportToWaybillAbility;
import com.jdl.sc.ofw.out.domain.flow.promise.*;
import com.jdl.sc.ofw.out.domain.flow.intercept.*;
import com.jdl.sc.ofw.out.domain.ability.shipment.SupplyShipmentAbility;
import com.jdl.sc.ofw.out.domain.model.bc.*;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.collections4.CollectionUtils;
import com.jdl.sc.ofw.out.domain.ability.pause.CityDeliveryPauseAbility;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jdl.sc.ofw.out.domain.ability.finance.DefaultRetailFinanceAbility;
import com.jdl.sc.ofw.out.domain.ability.refund.RefundWarehouseTypeAbility;
import java.time.Duration;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.domain.flow.serviceplus.*;
import com.jdl.sc.ofw.out.domain.flow.mutong.*;
import javax.annotation.Resource;
import com.jdl.sc.ofw.out.domain.flow.split.*;
import cn.jdl.batrix.event.spec.EventAction;
import com.jdl.sc.ofw.out.domain.ability.pause.SemiProcessedPauseAbility;
import com.jdl.sc.ofw.out.domain.flow.trace.*;
import com.jdl.sc.ofw.out.domain.ability.capacity.DefaultCapacitySplitOrderAbility;
import com.jdl.sc.ofw.out.domain.hold.OrderHoldHandler;
import com.jdl.sc.ofw.out.domain.ability.basic.SupplyBasicAbility;
import com.jdl.sc.ofw.out.domain.ability.trace.DefaultOrderTraceSubScribeAbility;
import com.jdl.sc.ofw.out.domain.ability.order.OrderPersistenceAbility;
import com.jdl.sc.ofw.out.domain.ability.f4pl.Supply4PLWaybillAbility;
import com.jdl.sc.ofw.out.domain.ability.wms.DefaultDeliveryCollectionDispatchInfoAbility;
import com.jdl.sc.ofw.out.domain.ability.f4pl.DefaultCallbackTo4PLAbility;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.out.domain.flow.capacity.*;
import com.jdl.sc.ofw.out.common.exception.AbilityExtensionException;
import lombok.SneakyThrows;
import com.jdl.sc.ofw.out.domain.ability.aftersale.DefaultSupplyAfterSaleInfoAbility;
import cn.jdl.batrix.event.spec.*;
import com.jdl.sc.ofw.out.domain.flow.waybill.*;
import com.jdl.sc.ofw.out.domain.model.product.ExpressProductEnum;
import com.jdl.sc.ofw.out.domain.flow.f4pl.*;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.flow.pause.*;
import com.jdl.sc.ofw.out.domain.ability.delivery.SupplyExpectDeliveryResourceAbility;
import com.jdl.sc.ofw.out.domain.flow.disclaimer.*;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jd.matrix.core.domain.flow.*;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.domain.cache.CacheKeyEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;
import com.jdl.sc.ofw.out.common.exception.BusinessDomainException;
import com.jdl.sc.ofw.out.domain.ability.product.SupplyRearProductInfoAbility;
import com.jdl.sc.ofw.out.domain.ability.product.SupplyThirdProductAbility;
import com.jdl.sc.ofw.out.domain.ability.pause.MainGiftPauseAbility;
import com.jdl.sc.ofw.out.domain.ability.billing.DefaultSupplyBillingTypeAbility;
import com.jdl.sc.ofw.out.domain.ability.dispatch.DefaultTransportToDispatchAbility;
import static com.jdl.sc.ofw.out.common.constant.FlowConstants.GENERAL_PAUSE_FLOW_CODE;
import com.jdl.sc.ofw.out.domain.ability.fulfillment.SupplyFulfillmentAbility;
import com.jdl.sc.ofw.out.domain.ability.product.SupplySpecialProductAbility;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the AsyncDomainFlowNodeHandle class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class AsyncDomainFlowNodeHandleTest {

	@InjectMocks
	private AsyncDomainFlowNodeHandle asyncDomainFlowNodeHandle;

	@Mock
	private OrderHoldHandler orderHoldHandler;

	@Mock
	private AbstractDomainFlowNode abstractDomainFlowNode;

	@Mock
	private JimClient jimClient;

    @Test
    public void testProcessSuccessWithNullAudit() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Arrange
        AsyncDomainFlowNodeHandle handle = new AsyncDomainFlowNodeHandle();
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(null)
                .orderNo("123")
                .orderStatus(null)
                .audit(null)
                .build();

        // Act
        handle.processSuccess(model);

        // Assert
        assertNotNull(model.getAudit());
    }

    @Test
    public void testProcessSuccessWithExistingAudit() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Arrange
        AsyncDomainFlowNodeHandle handle = new AsyncDomainFlowNodeHandle();
        Audit existingAudit = Audit.builder()
                .operateTime(new Date())
                .operator("existing-operator") 
                .operateType(OperateType.MODIFY)
                .build();
        
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(null)
                .orderNo("123")
                .orderStatus(null)
                .audit(existingAudit)
                .build();

        // Act
        handle.processSuccess(model);

        // Assert
        assertNotNull(model.getAudit());
    }

    @Test
    public void testProcessSuccessWithOrderStatus() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Arrange
        AsyncDomainFlowNodeHandle handle = new AsyncDomainFlowNodeHandle();
        OrderStatusEnum status = OrderStatusEnum.INIT;
        
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(null)
                .orderNo("123")
                .orderStatus(status)
                .audit(null)
                .build();

        // Act
        handle.processSuccess(model);

        // Assert
        assertNotNull(model.getAudit());
    }

    @Test
    public void testProcessFailWhenRetriesLessThanMax() {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        
        InputMessageDomain inputMessageDomain = new InputMessageDomain(new InputMessage(""));
        Exception e = new RuntimeException("test error");
        String errorId = "error123";
        TestDomainFlowNode abstractDomainFlowNode = new TestDomainFlowNode();

        // Mock dependencies
        Mockito.when(jimClient.get(Mockito.anyString())).thenReturn("2");
        
        // Execute
        asyncDomainFlowNodeHandle.processFail(model, inputMessageDomain, e, errorId, abstractDomainFlowNode);
    }

    @Test
    public void testProcessFailWhenRetriesExceedMax() {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        
        InputMessageDomain inputMessageDomain = new InputMessageDomain(new InputMessage(""));
        Exception e = new RuntimeException("test error");
        String errorId = "error123";
        TestDomainFlowNode abstractDomainFlowNode = new TestDomainFlowNode();

        // Mock dependencies
        Mockito.when(jimClient.get(Mockito.anyString())).thenReturn("6");
        Mockito.doNothing().when(orderHoldHandler).pause(
            Mockito.any(OrderFulfillmentModel.class),
            Mockito.any(PauseReasonEnum.class),
            Mockito.anyString(),
            Mockito.any(Date.class),
            Mockito.anyString(),
            Mockito.any(EventAction.class)
        );

        // Execute
        asyncDomainFlowNodeHandle.processFail(model, inputMessageDomain, e, errorId, abstractDomainFlowNode);
    }

    @Test(expected = AbstractException.class) 
    public void testProcessFailWhenAbstractException() {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        
        InputMessageDomain inputMessageDomain = new InputMessageDomain(new InputMessage(""));
        AbstractException e = new AcquireLockException(PauseReasonEnum.CAINIAO_DECRYPT_ERROR_EXCEPTION);
        String errorId = "error123";
        TestDomainFlowNode abstractDomainFlowNode = new TestDomainFlowNode();

        // Mock dependencies
        Mockito.when(jimClient.get(Mockito.anyString())).thenReturn("2");

        // Execute
        asyncDomainFlowNodeHandle.processFail(model, inputMessageDomain, e, errorId, abstractDomainFlowNode);
    }

    @Test(expected = DomainServiceException.class)
    public void testProcessFailWhenNonAbstractException() {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        
        InputMessageDomain inputMessageDomain = new InputMessageDomain(new InputMessage(""));
        Exception e = new RuntimeException("test error");
        String errorId = "error123";
        TestDomainFlowNode abstractDomainFlowNode = new TestDomainFlowNode();

        // Mock dependencies
        Mockito.when(jimClient.get(Mockito.anyString())).thenReturn("2");

        // Execute
        asyncDomainFlowNodeHandle.processFail(model, inputMessageDomain, e, errorId, abstractDomainFlowNode);
    }

    @Test
    public void testProcessFailWhenRetryExecutionException() {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        
        InputMessageDomain inputMessageDomain = new InputMessageDomain(new InputMessage(""));
        RetryExecutionException e = new RetryExecutionException(PauseReasonEnum.CAINIAO_DECRYPT_ERROR_EXCEPTION);
        String errorId = "error123";
        TestDomainFlowNode abstractDomainFlowNode = new TestDomainFlowNode();

        // Mock dependencies
        Mockito.when(jimClient.get(Mockito.anyString())).thenReturn("6");
        Mockito.doNothing().when(orderHoldHandler).pause(
            Mockito.any(OrderFulfillmentModel.class),
            Mockito.any(PauseReasonEnum.class),
            Mockito.anyString(),
            Mockito.any(Date.class),
            Mockito.anyString(),
            Mockito.any(EventAction.class)
        );

        // Execute
        asyncDomainFlowNodeHandle.processFail(model, inputMessageDomain, e, errorId, abstractDomainFlowNode);
    }

    

    private static class TestDomainFlowNode extends AbstractDomainFlowNode {
        @Override
        public BOutputMessage execute(OrderFulfillmentContext context) throws Exception {
            return null;
        }

        @Override
        public String getCode() {
            return "";
        }

        @Override
        public String getName() {
            return "";
        }
        // Concrete implementation for testing
    }
    @Test
    public void testProcessNormalCase() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        context.assignModel(model);
        
        InputMessage inputMessage = new InputMessage(context);
        InputMessageDomain inputMessageDomain = new InputMessageDomain(inputMessage);
        
        BOutputMessage expectedOutput = new BOutputMessage();
        expectedOutput.setBody(context);
        
        // Mock
        Mockito.when(abstractDomainFlowNode.execute(context)).thenReturn(expectedOutput);
        
        // Execute
        BOutputMessage result = asyncDomainFlowNodeHandle.process(inputMessageDomain, abstractDomainFlowNode);
        
        // Verify
        Assert.assertFalse(result.isBlock());
        Assert.assertEquals(context, result.getBody());
    }

    @Test
    public void testProcessWithNullOutput() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        context.assignModel(model);
        
        InputMessage inputMessage = new InputMessage(context);
        InputMessageDomain inputMessageDomain = new InputMessageDomain(inputMessage);
        
        // Mock
        Mockito.when(abstractDomainFlowNode.execute(context)).thenReturn(null);
        
        // Execute
        BOutputMessage result = asyncDomainFlowNodeHandle.process(inputMessageDomain, abstractDomainFlowNode);
        
        // Verify
        Assert.assertFalse(result.isBlock());
        Assert.assertEquals(context, result.getBody());
    }

    @Test
    public void testProcessWithStopExecutionException() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        context.assignModel(model);
        
        InputMessage inputMessage = new InputMessage(context);
        InputMessageDomain inputMessageDomain = new InputMessageDomain(inputMessage);
        
        // Mock
        Mockito.when(abstractDomainFlowNode.execute(context)).thenThrow(new StopExecutionException(PauseReasonEnum.ADDRESS_MAPPING_EXCEPTION));
        
        // Execute
        BOutputMessage result = asyncDomainFlowNodeHandle.process(inputMessageDomain, abstractDomainFlowNode);
        
        // Verify
        Assert.assertTrue(result.isBlock());
        Assert.assertEquals(context, result.getBody());
    }

    @Test
    public void testProcessWithPauseException() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        context.assignModel(model);
        
        InputMessage inputMessage = new InputMessage(context);
        InputMessageDomain inputMessageDomain = new InputMessageDomain(inputMessage);
        EventAction eventAction = new EventAction();
//        inputMessageDomain.(eventAction);
//
        PauseException pauseException = new PauseException(PauseReasonEnum.OTHER);
        
        // Mock
        Mockito.when(abstractDomainFlowNode.execute(context)).thenThrow(pauseException);
        Mockito.doNothing().when(orderHoldHandler).pause(any(OrderFulfillmentModel.class), 
            any(PauseReasonEnum.class), any(String.class), any(Date.class), any(String.class), any(EventAction.class));
        
        // Execute
        BOutputMessage result = asyncDomainFlowNodeHandle.process(inputMessageDomain, abstractDomainFlowNode);
        
        // Verify
        Assert.assertTrue(result.isBlock());
        Assert.assertEquals(context, result.getBody());
    }

    @Test
    public void testProcessWithGeneralException() throws Exception {
        // generated by JoyCoder taskId 89af1071df1a
        // Prepare test data
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .orderNo("123")
                .build();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        context.assignModel(model);
        
        InputMessage inputMessage = new InputMessage(context);
        InputMessageDomain inputMessageDomain = new InputMessageDomain(inputMessage);
        
        // Mock
        Mockito.when(abstractDomainFlowNode.execute(context)).thenThrow(new RuntimeException("error"));
        
        // Execute
        BOutputMessage result = asyncDomainFlowNodeHandle.process(inputMessageDomain, abstractDomainFlowNode);
        
        // Verify
        Assert.assertTrue(result.isBlock());
        Assert.assertTrue(result.isException());
        Assert.assertEquals(context, result.getBody());
    }

}