package com.jdl.sc.ofw.out.domain.flow;

import com.jdl.sc.ofw.out.domain.model.vo.Channel;
import com.jdl.sc.ofw.out.common.constant.ShipperConstants;
import com.jdl.sc.ofw.out.domain.model.vo.Finance;
import com.jdl.sc.ofw.out.domain.dto.*;
import com.jdl.sc.ofw.out.domain.model.exec.Command;
import com.jdl.sc.ofw.out.common.dict.OrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Invoice;
import lombok.Builder;
import com.jdl.sc.ofw.out.domain.model.vo.Cancel;
import com.jdl.sc.ofw.out.domain.model.*;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import com.jdl.sc.ofw.out.domain.model.vo.FulfillmentCommand;
import java.math.BigDecimal;
import java.util.ArrayList;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.PauseReasonEnum;
import com.jdl.sc.ofw.out.common.dict.GoodsTypeEnum;
import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.ofw.out.domain.model.vo.Consignee;
import lombok.extern.slf4j.Slf4j;
import com.jdl.sc.ofw.out.domain.model.bc.*;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import java.util.Optional;
import com.jdl.sc.ofw.out.domain.model.vo.Goods;
import com.jdl.sc.ofw.outbound.spec.enums.OrderBusinessTypeEnum;
import cn.jdl.batrix.sdk.base.BDomainAsyncModel;
import com.jdl.sc.ofw.out.domain.model.vo.Intercept;
import com.jdl.sc.ofw.out.common.dict.UnifiedErrorSpec;
import com.jdl.sc.ofw.out.domain.model.vo.Warehouse;
import cn.jdl.batrix.sdk.base.*;
import lombok.Setter;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.EdiRemarkInfo;
import javax.validation.constraints.NotNull;
import com.jdl.sc.ofw.out.domain.model.vo.Shipment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jdl.sc.ofw.out.domain.model.vo.Consignor;
import com.jdl.sc.ofw.out.domain.model.exec.*;
import cn.jdl.batrix.event.spec.EventAction;
import com.jd.matrix.core.domain.flow.InputMessage;
import cn.jdl.batrix.sdk.base.BDomainModel;
import org.apache.commons.lang.StringUtils;
import com.jdl.sc.ofw.out.common.dict.DecryptCommandEnum;
import com.jdl.sc.ofw.out.domain.model.vo.OrderSign;
import com.jdl.sc.ofw.out.domain.model.product.ProductCollection;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.out.common.exception.AbilityExtensionException;
import java.io.Serializable;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ChannelSourceEnum;
import com.jdl.sc.ofw.out.domain.model.orderclob.ClobTypeEnums;
import java.util.List;
import cn.jdl.batrix.event.spec.*;
import com.jdl.sc.core.utils.ObjectUtils;
import com.jdl.sc.ofw.out.domain.model.product.ExpressProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.RefOrderInfo;
import com.jdl.sc.ofw.out.domain.model.vo.BasicInfo;
import com.jdl.sc.ofw.outbound.spec.enums.OrderClobEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import com.jdl.sc.ofw.outbound.spec.enums.ShipModeEnum;
import com.jdl.sc.ofw.out.domain.service.adapter.GrayscaleAdapter;
import java.util.Objects;
import com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts;
import com.jdl.sc.ofw.out.domain.model.vo.Fulfillment;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jd.matrix.core.domain.flow.*;
import com.jdl.sc.ofw.out.domain.model.product.FreightProductEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Audit;

import com.jdl.sc.ofw.out.domain.flow.InputMessageDomain;

import com.jdl.sc.ofw.out.common.exception.BusinessDomainException;
import lombok.extern.jackson.Jacksonized;
import java.util.Date;
import lombok.Getter;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.model.vo.Cargo;
import java.util.Map;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusPhaseEnum;
import com.jdl.sc.ofw.out.domain.model.vo.Solution;
import lombok.experimental.SuperBuilder;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.stream.*;
import java.util.*;
import java.math.*;
/**
 * This class is used for unit testing the InputMessageDomain class.(Generated by JoyCoder)
 * @author: quhuafeng
 * @date: 2025-06-03
 */
@RunWith(MockitoJUnitRunner.class)
public class InputMessageDomainTest {

	@InjectMocks
	private InputMessageDomain inputMessageDomain;

	@Mock
	private InputMessage inputMessage;

	@Mock
	private OrderFulfillmentContext orderFulfillmentContext;

    @Test
    public void testIsSyncWhenHeaderIsNull() {
        // generated by JoyCoder taskId 106648ea8978
        InputMessage inputMessage = new InputMessage("");
        inputMessage.setHeader("BRATIX_FLOW_TYPE_HEADER", null);
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        assertTrue(domain.isSync());
    }

    @Test
    public void testIsSyncWhenHeaderHasValue() {
        // generated by JoyCoder taskId 106648ea8978
        InputMessage inputMessage = new InputMessage("");
        inputMessage.setHeader("BRATIX_FLOW_TYPE_HEADER", "someValue");
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        assertFalse(domain.isSync());
    }

    @Test
    public void testIsSyncWhenHeaderIsEmpty() {
        // generated by JoyCoder taskId 106648ea8978
        InputMessage inputMessage = new InputMessage("");
        inputMessage.setHeader("BRATIX_FLOW_TYPE_HEADER", "");
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        assertFalse(domain.isSync());
    }

    @Test
    public void testIsSyncWhenHeaderNotSet() {
        // generated by JoyCoder taskId 106648ea8978
        InputMessage inputMessage = new InputMessage("");
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        assertTrue(domain.isSync());
    }

    @Test
    public void testValidateInputMessageSuccess() {
        // generated by JoyCoder taskId 106648ea8978
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(new BatrixInfo())
                .orderNo("123")
                .build();
        
        Mockito.when(orderFulfillmentContext.getModel()).thenReturn(model);
        
        inputMessageDomain.validateInputMessage(orderFulfillmentContext);
    }

    @Test(expected = AbilityExtensionException.class) 
    public void testValidateInputMessageNullContext() {
        // generated by JoyCoder taskId 106648ea8978
        inputMessageDomain.validateInputMessage(null);
    }

    @Test(expected = BusinessDomainException.class)
    public void testValidateInputMessageNullModel() {
        // generated by JoyCoder taskId 106648ea8978
        Mockito.when(orderFulfillmentContext.getModel()).thenReturn(null);
        
        inputMessageDomain.validateInputMessage(orderFulfillmentContext);
    }

    @Test
    public void testGetContextWithValidContext() {
        // generated by JoyCoder taskId 106648ea8978
        // Arrange
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(new BatrixInfo())
                .orderNo("123")
                .build();
                
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity())
                .model(model)
                .build();
                
        InputMessage inputMessage = new InputMessage("");
        inputMessage.setBody(context);
        
        InputMessageDomain domain = new InputMessageDomain(inputMessage);

        // Act
        OrderFulfillmentContext result = domain.getContext();

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testGetContextWithNullContext() {
        // generated by JoyCoder taskId 106648ea8978
        // Arrange
        InputMessage inputMessage = new InputMessage("");
        inputMessage.setBody(null);
        
        InputMessageDomain domain = new InputMessageDomain(inputMessage);

        // Act
        OrderFulfillmentContext result = domain.getContext();

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetMessageWithBodyOnly() {
        // generated by JoyCoder taskId 106648ea8978
        // Create input message with body only
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        InputMessage inputMessage = new InputMessage(context);
        
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        
        // Verify returned message matches input
        InputMessage result = domain.getMessage();
        assertNotNull(result);
    }

    @Test 
    public void testGetMessageWithBodyAndHeader() {
        // generated by JoyCoder taskId 106648ea8978
        // Create input message with body and header
        OrderFulfillmentContext context = OrderFulfillmentContext.builder().build();
        Map<String, Object> header = new HashMap<>();
        header.put("key1", "value1");
        header.put("key2", "value2");
        
        InputMessage inputMessage = new InputMessage(context, header);
        
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        
        // Verify returned message matches input
        InputMessage result = domain.getMessage();
        assertNotNull(result);
    }

    @Test
    public void testGetMessageWithNullBody() {
        // generated by JoyCoder taskId 106648ea8978
        // Create input message with null body
        InputMessage inputMessage = new InputMessage(null);
        
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
        
        // Verify returned message matches input
        InputMessage result = domain.getMessage();
        assertNotNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetMessageWithNullInputMessage() {
        // generated by JoyCoder taskId 106648ea8978
        InputMessageDomain domain = new InputMessageDomain(null);
        domain.getMessage();
    }

    @Test
    public void testGetCurrentEventSuccess() {
        // generated by JoyCoder taskId 106648ea8978
        // Setup
        Mockito.when(inputMessage.getHeader("eventAction", String.class)).thenReturn("TEST_ACTION");
        Mockito.when(inputMessage.getHeader("eventBizId", String.class)).thenReturn("TEST_BIZ_ID");
        Mockito.when(inputMessage.getHeader("eventSource", String.class)).thenReturn("TEST_SOURCE");
        Mockito.when(inputMessage.getHeader("traceId", String.class)).thenReturn("TEST_TRACE_ID");

        // Execute
        EventAction result = inputMessageDomain.getCurrentEvent();

        // Verify
        assertNotNull(result);
    }

    @Test
    public void testGetCurrentEventNullHeaders() {
        // generated by JoyCoder taskId 106648ea8978
        // Setup
        Mockito.when(inputMessage.getHeader(any(String.class), any())).thenReturn(null);

        // Execute
        EventAction result = inputMessageDomain.getCurrentEvent();

        // Verify
        assertNotNull(result);
    }

    @Test
    public void testGetCurrentEventEmptyHeaders() {
        // generated by JoyCoder taskId 106648ea8978
        // Setup
        Mockito.when(inputMessage.getHeader(any(String.class), any())).thenReturn("");

        // Execute
        EventAction result = inputMessageDomain.getCurrentEvent();

        // Verify
        assertNotNull(result);
    }

    @Test
    public void testGetCurrentEventHeaderException() {
        // generated by JoyCoder taskId 106648ea8978
        // Setup
        Mockito.when(inputMessage.getHeader(any(String.class), any())).thenThrow(new RuntimeException());

        // Execute & Verify
        try {
            inputMessageDomain.getCurrentEvent();
            fail("Should throw RuntimeException");
        } catch (RuntimeException e) {
            // Expected exception
        }
    }

    @Test
    public void testConstructorWithValidContext() {
        // generated by JoyCoder taskId 106648ea8978
        // Prepare test data
        BatrixInfo batrixInfo = BatrixInfo.builder().build();
        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .batrixInfo(batrixInfo)
                .orderNo("123")
                .build();
                
        BusinessIdentity businessIdentity = new BusinessIdentity();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(businessIdentity)
                .model(model)
                .build();

        // Mock behavior
        when(inputMessage.getBody(OrderFulfillmentContext.class)).thenReturn(context);

        // Execute
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
    }

    @Test(expected = AbilityExtensionException.class) 
    public void testConstructorWithNullContext() {
        // generated by JoyCoder taskId 106648ea8978
        // Mock behavior
        when(inputMessage.getBody(OrderFulfillmentContext.class)).thenReturn(null);

        // Execute
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
    }

    @Test(expected = BusinessDomainException.class)
    public void testConstructorWithNullModel() {
        // generated by JoyCoder taskId 106648ea8978
        // Prepare test data

        BusinessIdentity businessIdentity = new BusinessIdentity();
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(businessIdentity)
                .model(null)
                .build();

        // Mock behavior
        when(inputMessage.getBody(OrderFulfillmentContext.class)).thenReturn(context);

        // Execute
        InputMessageDomain domain = new InputMessageDomain(inputMessage);
    }

    @Test(expected = NullPointerException.class)
    public void testConstructorWithNullInputMessage() {
        // generated by JoyCoder taskId 106648ea8978
        // Execute
        InputMessageDomain domain = new InputMessageDomain(null);
    }

}