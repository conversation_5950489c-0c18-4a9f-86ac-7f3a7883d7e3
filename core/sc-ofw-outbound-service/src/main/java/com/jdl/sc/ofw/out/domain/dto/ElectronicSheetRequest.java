package com.jdl.sc.ofw.out.domain.dto;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

@Builder
@Getter
public class ElectronicSheetRequest implements Serializable {
    /**
     * 商家事业部编码
     */
    private final String accountNo;

    /**
     * 承运商编码
     */
    private String shipperNo;

    /**
     * 库房编码
     */
    private String warehouseNo;

    /**
     * 店铺编号
     */
    private final String shopNo;

    /**
     * 电子面单平台
     */
    private final ElectronicSheetPlatform platform;

    /**
     * 平台直连
     * 0-默认值，无意义
     * 1-PDD加密直连
     */
    private Integer platDirectConn;

    /**
     * 存在快运产品
     */
    private boolean existFreightProduct;

    /**
     * 平台物流编码
     */
    private String expressCompanyCode;

    /**
     * 是否一品多仓
     */
    private boolean multiWarehouse;

    /**
     * 外仓 id
     */
    private Integer outWarehouseId;

    /**
     * 门店 id
     */
    private Integer storeId;

    public void assignExistFreightProduct(boolean existFreightProduct) {
        this.existFreightProduct = existFreightProduct;
    }

    public void assignExpressCompanyCode(String expressCompanyCode) {
        this.expressCompanyCode = expressCompanyCode;
    }
}