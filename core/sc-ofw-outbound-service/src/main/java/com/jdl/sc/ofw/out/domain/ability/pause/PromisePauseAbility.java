package com.jdl.sc.ofw.out.domain.ability.pause;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.common.constant.DomainConstants;
import com.jdl.sc.ofw.out.domain.ability.AbstractDomainAbility;
import com.jdl.sc.ofw.out.domain.extension.finance.IAttachFeeInfoExt;
import com.jdl.sc.ofw.out.domain.extension.pause.IPromisePauseExt;
import com.jdl.sc.ofw.out.domain.model.annotation.AbilityScene;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@DomainAbility(name = "时效控单能力", parent = DomainConstants.OUTBOUND_DOMIAN_CODE)
@AbilityScene(businessScenes = {BusinessScene.GENERATE})
public class PromisePauseAbility extends AbstractDomainAbility<OrderFulfillmentContext, IPromisePauseExt> {
    @Override
    public void execute(OrderFulfillmentContext context, BDomainFlowNode bDomainFlowNode) throws Exception {
        IPromisePauseExt ext = this.getMiddleExtensionFast(
                IPromisePauseExt.class,
                context,
                SimpleReducer.firstOf(r -> true),
                bDomainFlowNode);
        ext.execute(context);
    }
}
