package com.jdl.sc.ofw.out.domain.rpc;

import com.jd.fce.dos.service.domain.OrderMarkingNewResponse;
import com.jd.fce.dos.service.domain.SeaShippingResponse;
import com.jd.promise.service.domain.TransferTimeRequest;
import com.jd.promise.service.domain.TransferTimeResponse;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeRequest;
import com.jd.promise.service.domain.WarehouseAndDeliveryTimeResponse;
import com.jd.promise.service.domain.WarehouseJitTimeRequest;
import com.jd.promise.service.domain.WarehouseJitTimeResponse;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.vo.JdAddress;

public interface PromiseAcl {
    WarehouseAndDeliveryTimeResponse getPromise(WarehouseAndDeliveryTimeRequest request);

    OrderMarkingNewResponse getPromiseNew(OrderFulfillmentModel model);

    boolean forbidden(OrderFulfillmentModel model);

    /**
     * 是否支持海运
     */
    SeaShippingResponse isSupportSeaShipping(JdAddress jdAddress);

    WarehouseAndDeliveryTimeResponse getPromiseColdChain(OrderFulfillmentModel model);

    /**
     * 获取下传时间
     */
    TransferTimeResponse getTransferTime(TransferTimeRequest transferTimeRequest);

}
