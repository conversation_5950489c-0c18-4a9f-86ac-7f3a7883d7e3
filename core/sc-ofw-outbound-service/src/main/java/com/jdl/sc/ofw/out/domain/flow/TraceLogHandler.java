package com.jdl.sc.ofw.out.domain.flow;

import com.alibaba.fastjson.JSONObject;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jdl.sc.ofw.out.common.batrix.BatrixContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.skyscreamer.jsonassert.FieldComparisonFailure;
import org.skyscreamer.jsonassert.JSONCompare;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.JSONCompareResult;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * FileName: TraceHandler
 * Author:   huafeng.qu
 * Date:     2023/3/6 10:12 上午
 * Description: 全链路操作信息处理--处理每个flowNode节点处理mode产生的差异信息-，后续日志扩展从里开始，例如存储供运维用
 */
@Component
@Slf4j
public class TraceLogHandler {
    private static final CustomComparator COMPARATOR = new CustomComparator(JSONCompareMode.NON_EXTENSIBLE);

    public void execute(String inputMessage, String outputMessage, String flowNodeName,
                        InputMessageDomain inputMessageDomain, long executeTime) {
        if(inputMessage == null || outputMessage == null) {
            log.error("能力处理结果：输入参数异常，inputMessage is null:{}, outputMessage is null：{}",
                    StringUtils.isBlank(inputMessage), StringUtils.isBlank(outputMessage));
        }
        if(inputMessage.length() > 50000) {
            log.warn("大报文订单，不做输入输出对比");
            return;
        }
        String orderNo = inputMessageDomain.getContext().getModel().getOrderNo();
        try {
            JSONCompareResult result = JSONCompare.compareJSON(inputMessage, outputMessage, COMPARATOR);
            if(result!= null && result.passed()) {
                log.debug("flowNode:{},能力处理结果：未修改。orderNo:{},处理时长（ms）:{}", flowNodeName, orderNo, executeTime);
                return;
            } else {
                String changeMessage = formatFailMessage(result, inputMessage, outputMessage);
                log.info("flowNode:{},能力处理结果：有修改。orderNo:{},处理时长(ms):{},变更内容{}", flowNodeName, orderNo,
                        executeTime, changeMessage);
                BatrixContext.addModelChangelog(flowNodeName + "-" + BatrixContext.getCurrentExtName() + ":" + changeMessage);
            }
        } catch (Exception e) {
            log.error("JSON对比失败,单号：{}", orderNo, e);
        }
    }

    /**
     * 格式化失败内容
     * @return
     */
    private static String formatFailMessage(JSONCompareResult result, String inputMessage, String outputMessage) {
        if (result.getFieldFailures().isEmpty() && StringUtils.isNotBlank(result.getMessage())) {
            return parseArrayErrorMessage(result.getMessage(), inputMessage, outputMessage);
        }
        if(!result.getFieldFailures().isEmpty()) {
            return parseObjectErrorMessage(result.getFieldFailures());
        }
        return "";
    }

    /**
     * 解析JSON数组异常
     * @param errorMessage 对比结果
     * @param inputMessage 调用flowNode前model信息
     * @param outputMessage 调用flowNode后model信息
     * @return
     */
    private static String parseArrayErrorMessage(String errorMessage, String inputMessage, String outputMessage) {
        List<String> difArrayKeyList = getAllArrayKey(errorMessage);
        if(difArrayKeyList.isEmpty()) {
            return "";
        }
        Object inDocument = Configuration.defaultConfiguration().jsonProvider().parse(inputMessage);
        Object outDocument = Configuration.defaultConfiguration().jsonProvider().parse(outputMessage);
        StringBuilder difMsg = new StringBuilder();
        for(String difArrayKey: difArrayKeyList) {
            List<String> inJsonValues = JsonPath.read(inDocument, getJsonPathFullKey(difArrayKey));
            List<String> outJsonValues = JsonPath.read(outDocument, getJsonPathFullKey(difArrayKey));
            String inStrValues = JSONObject.toJSONString(inJsonValues);
            String outStrValues = JSONObject.toJSONString(outJsonValues);
            difMsg.append(diffMsgFormat(difArrayKey, inStrValues, outStrValues));
        }
        return difMsg.toString();
    }

    /**
     * 从对比结果信息中获取所有的数组类型的Key
     * @param diffMessage
     * @return  List<String>  所有数组类型的Key
     */
    private static List<String> getAllArrayKey(String diffMessage){
        String[] difArr = diffMessage.split(";");
        List<String> difArrayKeyList = new ArrayList<>();
        for(String difItem : difArr) {
            String[] errorItemArr = difItem.split(":");
            if (errorItemArr[0].contains("[]")) {
                difArrayKeyList.add(errorItemArr[0].substring(0, errorItemArr[0].lastIndexOf("[]")).trim());
            }
        }
        return difArrayKeyList;
    }



    /**
     * 格式化变更日志
     * @param key
     * @param sourceMsg
     * @param targetMsg
     * @return
     */
    private static StringBuilder diffMsgFormat(String key, String sourceMsg, String targetMsg) {
        StringBuilder diffSb = new StringBuilder();
        diffSb.append("[").append(key).append(":")
                .append(sourceMsg).append("->")
                .append(targetMsg).append("]").append(",");
        return diffSb;
    }

    /**
     * 获取用于jsonPath工具类的json属性全路径
     * @param key
     * @return
     */
    private static String getJsonPathFullKey(String key) {
        return "$." + key;
    }

    /**
     * 解析JSON对象异常
     * @return
     */
    private static String parseObjectErrorMessage(List<FieldComparisonFailure> fieldComparisonFailureList) {
        StringBuilder difMsg = new StringBuilder();
        for(FieldComparisonFailure fieldComparisonFailure : fieldComparisonFailureList) {
            difMsg.append(diffMsgFormat(fieldComparisonFailure.getField(),
                    String.valueOf(fieldComparisonFailure.getExpected()),
                    String.valueOf(fieldComparisonFailure.getActual())));
        }
        return difMsg.toString();
    }
}
