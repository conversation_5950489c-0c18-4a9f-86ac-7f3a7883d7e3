package com.jdl.sc.ofw.out.domain.flow.pause;

import cn.jdl.batrix.core.flow.domain.BOutputMessage;
import com.jdl.sc.ofw.out.common.constant.FlowConstants;
import com.jdl.sc.ofw.out.domain.ability.AbstractDomainAbility;
import com.jdl.sc.ofw.out.domain.ability.pause.PromisePauseAbility;
import com.jdl.sc.ofw.out.domain.flow.AbstractDomainFlowNode;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 时效控单节点
 */
@Component
@Slf4j
public class PromisePauseFlowNode extends AbstractDomainFlowNode {

    @Resource
    private PromisePauseAbility promisePauseAbility;

    @Override
    public BOutputMessage execute(OrderFulfillmentContext context) throws Exception {
        AbstractDomainAbility ability = super.getDomainAbility(context, promisePauseAbility);
        execute(ability, context, this);
        return null;
    }

    @Override
    public String getCode() {
        return FlowConstants.PROMISE_PAUSE_FLOW_CODE;
    }

    @Override
    public String getName() {
        return FlowConstants.PROMISE_PAUSE_FLOW_NAME;
    }
}
