package com.jdl.sc.ofw.out.domain.dto;

import lombok.Getter;

@Getter
public enum ElectronicSheetPlatform {
    PDD("1", "拼多多平台"),
    CN("2", "菜鸟平台"),
    DY("3", "抖音平台"),
    MT("4", "美团平台"),
    VIP("5", "唯品会平台"),
    KS("6", "快手平台"),
    DW("7", "得物平台"),
    JD_BOUNDLESS("8", "京东无界"),
    XHS("10", "小红书"),
    WX("11", "微信视频号"),
    AKC("12", "爱库存平台"),
    HW("13", "华为"),
    DY_INSTANT("16", "抖音即时零售"),
    ;

    private final String code;
    private final String desc;

    ElectronicSheetPlatform(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
