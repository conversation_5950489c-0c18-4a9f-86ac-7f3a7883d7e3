package com.jdl.sc.ofw.out.domain.extension.pause;

import cn.jdl.batrix.core.base.BIDomainAbilityExtension;
import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jdl.sc.ofw.out.common.constant.DomainConstants;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;

import javax.validation.constraints.NotNull;

public interface IPromisePauseExt extends BIDomainAbilityExtension {
    @DomainAbilityExtension(
            code = DomainConstants.PROMISE_PAUSE_EXT_CODE,
            name = DomainConstants.PROMISE_PAUSE_EXT_NAME
    )
    void execute(@NotNull OrderFulfillmentContext context);
}
