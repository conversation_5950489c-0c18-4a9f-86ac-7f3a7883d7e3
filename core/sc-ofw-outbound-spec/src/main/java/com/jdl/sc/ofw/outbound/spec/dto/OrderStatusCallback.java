package com.jdl.sc.ofw.outbound.spec.dto;

import com.jdl.sc.ofw.outbound.spec.enums.OrderStatusEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackageProduceModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 状态回传
 */
@Jacksonized
@Builder
@Getter
public class OrderStatusCallback implements Serializable {
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 商家单号
     */
    private String channelOrderNo;

    /**
     * 状态
     *
     * @see OrderStatusEnum
     */
    private Integer status;
    /**
     * 产品列表
     */
    private List<Product> products;
    /**
     * 操作时间
     */
    private Date operateTime;
    /**
     * 操作人
     */
    private String operator;

    private Shipment shipment;
    /**
     * orderMark eclp订单标记位
     * preSaleStage 预售订单拉回时需清空该属性  该字段拉回时触发回传订单中心
     */
    private Map<String, String> extendProps;

    private Solution solution;

    /**
     * 订单金额
     */
    private Money orderAmount;

    /**
     * 收件人
     */
    private Consignee consignee;

    /**
     * 渠道信息
     */
    private Channel channel;

    /**
     * 履约信息
     */
    private Fulfillment fulfillment;

    /**
     * 货品信息
     */
    private List<Cargo> cargos;

    /**
     * 关联单号信息
     */
    private RefOrderInfo refOrderInfo;

    /**
     * 财务信息
     */
    private Finance finance;


    /**
     * 虚拟号码过期时间
     */
    private String virtualNumberExpirationDate;

    /**
     * EDI将解密信息同步给oms时，加密模式，是必传字段
     * （0-明文，1-加密，2-虚拟号）
     * 有虚拟号过期时间 ——> 2-虚拟号
     * 其他           ——> 1-加密
     * 注：（由于抖音未区分明文和脱敏，京东侧只能按照加密，后续终端会把明文和脱敏信息，统一进行解密处理）
     */
    private String encryptMode;

    @Builder
    @Getter
    @Jacksonized
    public static class Channel implements Serializable {
        /**
         * 渠道承运商编码(PDD,快手等内部的承运商编码)
         */
        private String channelShipmentNo;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Consignee implements Serializable {
        private String name;
        private String phone;
        private String mobile;
        private JdAddress address;
    }

    @Getter
    @Builder
    @Jacksonized
    public static class JdAddress implements Serializable {
        private String detailAddress;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Product implements Serializable {
        private String no;
        private String name;
        private Integer type;
        private String billingType;
        private String billingTypeName;
        private Map<String, String> attributes;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Shipment implements Serializable {
        /**
         * 运单号
         */
        private String wayBill;
        /**
         * 承运商编码
         */
        private String shipperNo;
        /**
         * 承运商名称
         */
        private String shipperName;


        /**
         * 站点名称
         */
        private String endStationName;

        /**
         * 预期送货开始时间
         */
        private Date expectDeliveryStartTime;
        /**
         * 预期送货结束时间
         */
        private Date expectDeliveryEndTime;

        /**
         * 售后联系人
         */
        private String contactPerson;

        /**
         * 售后联系电话
         */
        private String contactPhone;

        /**
         * 售后联系地址
         */
        private String contactAddress;

        /**
         * 首个包裹运单下发
         */
        private Boolean firstWaybillIssue;
        /**
         *
         */
        private String thirdWaybill;

        /**
         * 母单号
         */
        private String parentWaybillNo;

        /**
         * 三方承运商编码
         */
        private String externalCarrierCode;

        /**
         * 扩展信息
         */
        private Map<String, String> extendProps;

        /**
         * 承运商类型
         */
        private Integer shipperType;

        /**
         * 站点编号
         */
        private String endStationNo;
        /**
         * 配送方式
         */
        private TransportationTypeEnum transportationType;

        /**
         * 包裹生产模式
         */
        private PackageProduceModeEnum packageProduceModeEnum;

        /**
         * 马甲公司编码
         */
        private String mjCompanyCode;

        /**
         * 马甲运单号
         */
        private String cainiaoPlatformWaybill;
        /**
         * 期望配送运营资源  1-合同物流
         */
        private String expectDeliveryResource;
        /**
         * 妥投日期
         */
        private Date deliveryDate;
        /**
         * 波次开始时间
         */
        private String batchStartTime;
        /**
         * 波次结束时间
         */
        private String batchEndTime;
        /**
         * 目的中心编码
         */
        private String endCenterNo;

        /**
         * 目的中心名称
         */
        private String endCenterName;

        /**
         * 城配-卡位号
         */
        private String cardNo;

        /**
         * 城配-串点数
         */
        private String bunchQty;

        /**
         * 城配-公里数
         */
        private String kilometers;

        /**
         * 预分拣结果
         */
        private PresortInfo presortInfo;
        /**
         * 打印信息
         */
        private PrintExtendInfo printExtendInfo;

        /**
         * 合同物流-用于回传商家
         * 司机
         */
        private String deliveryStaffName;

        /**
         * 合同物流-用于回传商家
         * 车牌号
         */
        private String vehicleNumber;

        /**
         * 合同物流-用于回传商家
         * 操作时间
         */
        private String operateTime;

        /**
         * 城配-派车单号
         */
        private String dispatchNo;

        /**
         * 是否开启奢侈品保证服务
         */
        private Boolean luxurySecurity;

        /**
         * 是否妥投强制拍照
         */
        private Boolean deliveryForcePhoto;

        /**
         * 是否妥投强制电联
         */
        private Boolean forceContact;

        /**
         * 三方快递类型
         */
        private String thirdPartExpressType;

        /**
         * 三方快递产品名称
         */
        private String thirdPartExpressProduct;

        /**
         * 青龙业主号
         */
        private String bdOwnerNo;

        /**
         * 配送温层
         */
        private String warmLayer;
        /**
         * 配送履约渠道
         */
        private String deliveryPerformanceChannel;
        /**
         * 高值
         */
        private String highValue;
        /**
         * 冷冻品
         */
        private String frozen;
        /**
         * 冷藏品
         */
        private String refrigerate;
        /**
         * 易碎品
         */
        private String frangible;
        /**
         * 生鲜品
         */
        private String fresh;
        /**
         * 超重 超过30kg
         */
        private String overweight;

        /**
         * 站仓标识
         */
        private String distributionWarehouse;
    }

    @Getter
    @Builder
    @Jacksonized
    public static class PresortInfo implements Serializable {
        private String siteName;
        private String siteNo;
        private String roadArea;
        private Integer siteType;
        private String aoiCode;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Solution implements Serializable {
        private final String no;

        private final String name;

        private Map<String, String> attributes;

        public Map<String, String> getAttributes() {
            return Collections.unmodifiableMap(attributes);
        }

        public String getAttributeValue(String key) {
            return this.attributes.get(key);
        }
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Money implements Serializable {
        private BigDecimal amount;
        private String currencyCode;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Fulfillment implements Serializable {

        /**
         * 签署免赔含字段
         */
        private String signDisclaimer;

        /**
         * 信任京东出库体积和重量
         */
        private boolean trustJDMetrics;

        /**
         * 强制下发标识
         */
        private String forceIssueProduction;

        /**
         * 按商家约定耗材计费
         */
        private String isConsumableBilling;

        /**
         * 耗材方案id
         */
        private List<Long> materialSolutionIds;

        /**
         * 1-等待出库
         */
        private String pickCallback;
        /**
         * 时效降级订单标识
         */
        private Integer timelinessDegradeType;

        /**
         *  商家线路名称
         */
        private String merchantRoute;

        /**
         *  仓运协同模式
         */
        private String warehouseDeliveryCollaborationMode;

        /**
         *  送装服务模式配置(京东服务+的服务单)
         *  0-无安装，1-送装分离，2-送装一体
         */
        private String deliveryInstallationMode;

        /**
         * 下传提前量
         */
        private Integer preProductionMinutes;

        /**
         * 生产耗时
         */
        private Integer productionDuration;

        /**
         * 在途时长
         */
        private Integer transitDuration;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Cargo implements Serializable {

        /**
         * 货品编码
         */
        private String cargoNo;

        /**
         * 货品行号
         */
        private String cargoLineNo;

        /**
         * 增值服务项
         */
        private List<CargoServiceInfo> cargoServiceInfo;

        /**
         * 货品等级
         */
        private String cargoLevel;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class CargoServiceInfo implements Serializable {
        /**
         * 增值服务项编码
         */
        private String serviceCode;

        /**
         * 增值服务项名称
         */
        private String serviceName;

        /**
         * 业务条线 1-仓储 | 2-大件配送 | 3-中小件配送 | 4-通用配送
         */
        private String businessLine;

        /**
         * 服务要求
         */
        private String serviceRequirement;

        /**
         * 操作说明
         */
        private String remark;

        /**
         * 扩展字段
         */
        private Map<String, String> extendProps;

        /**
         * 服务标识
         */
        private Map<String, String> serviceSign;
    }

    @Getter
    @Builder
    @Jacksonized
    public static class PrintExtendInfo implements Serializable {
        private String codingMappingOut;
        private String twoDimensionCode;
        private String destTransferCode;
        private String destRouteLabel;
        private String codingMapping;
        private String destTeamCode;
        private String proCode;
        private String alreadyDoDecryptAddressMj;
    }

    @Getter
    @Builder
    @Jacksonized
    public static class RefOrderInfo implements Serializable {
        /**
         * 纯配订单号
         */
        private String deliveryOrderNo;
        /**
         * 关联采购单号
         */
        private String purchaseOrderNo;
        /**
         * 越库入库单号
         */
        private String targetWarehouseCustomerPurchaseOrderNo;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class Finance implements Serializable {
        /**
         * 附加费用
         */
        private List<CostInfo> attachFees;
        /**
         * 耗材时效分层层级
         */
        private String materialTimeLayer;
    }

    @Builder
    @Getter
    @Jacksonized
    public static class CostInfo implements Serializable {

        /**
         * 费用编号
         */
        private String costNo;

        /**
         * 费用名称
         */
        private String costName;
    }
}
