package com.jdl.sc.ofw.out.domain.model.product;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.jdl.sc.ofw.out.domain.model.vo.Product;
import lombok.Getter;

import java.util.List;

/**
 * 产品线枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ProductLineEnum {
    SC("sc", "供应链"),
    ED("ed", "快递"),
    FR("fr", "快运"),
    LQ("lq", "大件"),
    LL("ll", "冷链"),
    ISC("isc", "国际"),
    MK("mk", "广告"),
    CL("cl", "云仓"),
    TC("tc", "到仓服务"),
    SP("sp", "服务+"),
    JXD("jxd", "同城"),
    QZ("qz", "清铢"),
    MD("md", "医药"),
    T<PERSON>("tn","运力"),
    J<PERSON>("jh", "京慧"),
    QH("qh","汽车"),
    YUTU("yutu","数智创新业务"),
    BB("bb","B2B供应链"),
    IS("is", "秒送仓配"),
    ;

    private final String name;
    @JsonValue
    private final String code;

    private ProductLineEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    @JsonCreator
    public static ProductLineEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProductLineEnum productTypeEnum : values()) {
            if (productTypeEnum.getCode().equals(code)) {
                return productTypeEnum;
            }
        }

        throw new IllegalArgumentException("未知产品线: " + code);
    }

    /**
     * 用于判断快递快运场景，根据主产品的产品线判断所属业务场景
     * @param productList 产品编码
     * @return 主产品产品线，费快递快运主产品不返回产品线
     */
    public static ProductLineEnum getMainExpressOrFreightProductLine(List<Product> productList) {
        for (Product product : productList) {
            if (product.getType() == ProductTypeEnum.MAIN_PRODUCT
                    && ProductLineEnum.ED.equals(product.getProductLine())) {
                return ProductLineEnum.ED;
            }
            if (product.getType() == ProductTypeEnum.MAIN_PRODUCT
                    && ProductLineEnum.FR.equals(product.getProductLine())) {
                return ProductLineEnum.FR;
            }
        }
        return null;
    }
}
