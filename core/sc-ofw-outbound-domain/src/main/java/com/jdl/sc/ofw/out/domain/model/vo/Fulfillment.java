package com.jdl.sc.ofw.out.domain.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.jdl.sc.ofw.out.common.dict.FrozenDamageEnum;
import com.jdl.sc.ofw.out.common.dict.OrderUrgencyType;
import com.jdl.sc.ofw.out.common.dict.WeighingTypeEnum;
import com.jdl.sc.ofw.out.domain.model.RefSo;
import com.jdl.sc.ofw.out.domain.model.product.CrossDockTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.PrintComponentTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.TmOrderTypeEnum;
import com.jdl.sc.ofw.out.domain.model.trigger.FieldExtendProcess;
import com.jdl.sc.ofw.outbound.spec.enums.InvoiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackDetailsCollectEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PlatformBusinessModelEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PrintMedicinePriceBalanceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ReusedHistoryWaybillTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WarehouseShipmentModeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 履约信息
 */
@Builder
@Getter
@Jacksonized
public class Fulfillment implements Serializable {

    private static Map<String, FieldExtendProcess> fieldExtendProcessMap = new HashMap<>();

    public static void assignFieldProcessMap(String fieldName, FieldExtendProcess fieldExtendProcess) {
        Fulfillment.fieldExtendProcessMap.put(fieldName, fieldExtendProcess);
    }

    //fastJson 不序列化此字段
    @JSONField(serialize = false)
    public String getCustomField() {
        FieldExtendProcess<String> process = fieldExtendProcessMap.get("customField");
        if (process == null) {
            return this.customField;
        }
        return process.doProcess(this.customField);
    }

    /**
     * 预占结果
     */
    public boolean occupyResult;

    /**
     * 包裹明细管理
     */
    @RefSo("soMain.soMark22")
    private PackDetailsCollectEnum packDetailsCollect;

    @RefSo("somain.somark57")
    private boolean productionByPack;

    /**
     * 计划箱数
     */
    private Quantity boxQuantity;

    /**
     * 是否允许缺量履约
     */
    private boolean partialFulfillment;

    /**
     * 部分收款
     */
    private boolean partialPayment;

    /**
     * 回传方式 1-按单回传，2-按板回传
     */
    private Integer backWay;

    /**
     * 等级方式 1-按单据等级收货，2-按实物等级收货
     */
    private Integer gradingCriteria;

    /**
     * 关单超期日期
     */
    private Date closingDate;

    /**
     * 库存不足详情
     */
    public List<StockDetail> stockDetailList;

    /**
     * 货品履约方式 true 计划量出库 false 预占量出库
     */
    private final boolean cargoFulfillmentWay;

    /**
     * 商家个性化打印信息
     * 对应的数据格式为: "{orderExtendInfo:{},skuPrintInfoList:[]}"
     */
    public String sellerIndividuationPrintInfo;

    /**
     * 	是否三方保价
     */
    @RefSo("soExtend.insuredPriceFlag")
    public String thirdInsured;
    /**
     * 三方价保金额
     */
    @RefSo("soExtend.insuredValue")
    public String thirdGuaranteeMoney;

    /**
     * 商家包装类型编码
     */
    private String sellerPackTypeNo;
    /**
     * 商家包装类型名称
     */
    private String sellerPackTypeName;
    /**
     * 商家指定包装类型
     */
    private String sellerAssignPackType;

    /**
     * 货票同行标识枚举
     * 0-不开发票，1-普通发票，3-增值发票，4-电子发票
     */
    private InvoiceEnum invoiceEnumFlag;

    /**
     * 行业标识 根据somark78、somark79位标位赋值
     */
    private String professionType;
    /**
     * 是否跳过手机号解密
     */
    @RefSo("soMainParam.extendedField.customField().skipMobileDecrypt")
    private boolean skipMobileDecrypt;
    /**
     * 订单优先级
     */
    @RefSo("soExtend.orderPriority")
    private Integer orderPriority;

    /**
     * 订单加急类型
     */
    @RefSo("soMain.somark84")
    private OrderUrgencyType orderUrgencyType;

    /**
     * 信任京东出库体积和重量
     */
    @RefSo("soMain.somark158")
    private boolean trustJDMetrics;

    /**
     * 是否使用抖音组件打印面单
     */
    @RefSo("soMain.customField.printComponentType")
    private PrintComponentTypeEnum printComponentType;

    /**
     * 客户自定义属性
     */
    @RefSo("soMainParam.extendedField.customField")
    private String customField;
    /**
     * 天猫订单京广流程
     */
    @RefSo("soMain.somark163")
    private boolean tmDeliveryByJgFlag;

    /**
     * 是否使用云仓菜鸟电子面单
     */
    @RefSo("soMain.somark140")
    private boolean clpsCainiaoElecFlag;
    /**
     * 天猫订单类型
     */
    @RefSo("soMain.somark88")
    private TmOrderTypeEnum tmOrderFlag;
    /**
     * 是否大件包裹打印仓配黑名单
     */
    @RefSo("soMain.somark87")
    private boolean packageSignFlag;

    /**
     * 是否调过菜鸟产业平台
     */
    private boolean invokedCainiaoPlatform;

    /**
     * 菜鸟运单号
     */
    private String cainiaoPlatformWaybill;

    /**
     * 马甲公司编码（众邮订单）
     */
    private String mjCompanyCode;

    /**
     * 标识耗材是否需要采集唯一码(0否 1是)
     */
    @RefSo("soMain.somark122")
    private boolean collectUniqueCodeFlag;

    /**
     * 是否有动物检疫证
     */
    @RefSo("soMainParam.extendedField.quarantineCert")
    private boolean quarantineCert;

    /**
     * 流通监管码
     */
    @RefSo("soMain.supervisionCode")
    private String supervisionCode;
    /**
     * 开票员
     */
    @RefSo("soMain.invoiceChecker")
    private String invoiceChecker;
    /**
     * 付款方式
     */
    @RefSo("soMain.paymentType")
    private String paymentType;
    /**
     * 销售类型
     */
    @RefSo("soMain.saleType")
    private String saleType;

    /**
     * 存储温层
     */
    @RefSo("soMark92")
    private String warmLayer;

    /**
     * 批次号
     */
    @RefSo("soMain.batchNo")
    private String batchNo;

    /**
     * 批次数
     */
    @RefSo("soMain.batchQty")
    private String batchQuantity;

    /**
     * 0： 不需要采集实际称重  1： 需要采集实际称重  2： 系统自动称重
     */
    @RefSo("soMark123")
    private WeighingTypeEnum weighingType;

    @RefSo("soMainParam.extendedField.signDisclaimer")
    private String signDisclaimer;

    /**
     *库位码
     */
    @RefSo("soMain.ediRemark.locationCode")
    private String stationCode;

    /**
     *要求送达时间
     */
    @RefSo("soMain.ediRemark.requireConsigneeTimeTo")
    private String requireConsigneeTimeTo;

    /**
     * 清关数据
     */
    private CustomsClearance customsClearance;

    /**
     * 商家自定义图片链接地址（好评卡信息）
     */
    private String sellerCustomPictureUrl;

    /**
     * 越库类型 0-非越库、1-计划型越库、2-机会型越库
     */
    @RefSo("soMark98")
    private CrossDockTypeEnum crossDockType;


    /**
     * 是否商家下发打印信息 联通华盛使用
     */
    @RefSo("somain.customField.printInfoFromSeller")
    private boolean printInfoFromSeller;

    /**
     * 临期安全天数
     */
    private String adventSafeDays;

    /**
     * 逻辑因子,来源于 extendfield.logicParam
     */
    private String logicParam;


    /**
     * 业务员+业务员电话 打印占位符使用 今麦郎新增
     */
    private String salesmanAttribute;

    /*
     * 妥投特殊要求（枚举： 1 - 必须妥投/ 2 - 必须拒收/ 3 - 必须半收，下单默认传必须妥投）
     * 今麦郎新增
     */
    private String customerSignforAttributes;

    /**
     * KA扩展点
     */
    private Integer kaExtensionPoint;

    /**
     * 平台业务模式: 1-唯品会JIT 2-唯品会JITX
     */
    private PlatformBusinessModelEnum platformBusinessModel;


    /**
     * 商家指定箱明细
     */
    private String boxInfos;

    private String vipPlatformTemplateUrl;

    /**
     * 按商家约定耗材计费
     */
    private String consumableBilling;

    /**
     * 耗材方案id
     */
    private List<Long> materialSolutionIds;

    /**
     * 创维及大件云打印使用,JFS 传递给大件中间件 自行处理
     */
    private String skyWorthWaybill;

    /**
     * 1-等待出库
     */
    private String pickCallback;

    /**
     * 库存预占结果类型，包括： 1- 零库存预占；2- 缺量预占；3- 全量预占
     */
    private Integer occupyResultType;
    /**
     * 时效降级订单标识
     */
    private Integer timelinessDegradeType;

    /**
     * 平台要求出库时间
     * 时间格式：YYYY-MM-DD HH:mm:ss
     */
    private String platformExpectWmsDeliveryTime;

    /**
     * 平台要求揽收时间
     * 时间格式：YYYY-MM-DD HH:mm:ss
     */
    private String platformExpectPickupTime;

    /**
     * 包裹限制
     */
    private Map<String, String> packageRestrictions;


    /**
     * 电商订单发货策略
     * 1: 一物一检
     */
    private String ecpOrderDeliveryStrategy;

    /**
     * 对应零售拆分的两段单据中的第一段含金额信息的消费者下单的单据的订单号
     */
    private String ecpFirstOrderNo;

    /**
     * 电商订单销售策略，“1”为指数商品订单，不传或为空，无策略
     */
    private String ecpOrderSalesStrategy;

    /**
     * 39003001 冻损免赔白名单
     * 39003002 冻损免赔
     * 39003003 包装升级
     */
    private FrozenDamageEnum frozenDamageMethod;

    /**
     * 是否复用运单号
     * 0-否 1-是
     */
    private ReusedHistoryWaybillTypeEnum reusedHistoryWaybillType;

    /**
     * 是否打印医药冲价单,1：打印，其他：不打印
     */
    private PrintMedicinePriceBalanceEnum isPrintMedicinePriceBalance;

    /**
     * 序列号校验来源 1-京东平台国补
     */
    private String serialNumberCheckChannel;

    /**
     *  商家会员客户的会员卡
     */
    private String memberCardId;

    /**
     *  会员名称
     */
    private String memberName;

    /**
     *  商家子单（原始订单)(dtcjimkv)
     */
    private String customizeOrderPrintInfo;

    /**
     *  商家线路名称
     */
    private String merchantRoute;

    /**
     * 下发库房时间（目前仅用于FOP)
     */
    private String warehouseArrivalTime;

    /**
     * 国际FOP创建运单结果标识
     */
    private Boolean fopFlowPathFlag;

    /**
     * 货品主增关系
     */
    private String cargoGiftRelationInfo;

    /**
     * jit时效
     */
    private String jitPromise;

    /**
     * 单据生产时效类型
     */
    private String productionPromiseType;

    /**
     * 缺货处理方式
     */
    private String stockShortsProcessWay;

    /**
     * 七鲜门店名称
     */
    private String sevenFreshStoreName;

    /**
     * 预估送达时间段
     */
    private String estimatedDeliveryTimePeriod;

    /**
     * <a href="https://joyspace.jd.com/pages/fdgypS2ILBIZWuVBmJyn">分批次出</a>
     *  batchOutbound-分批次出库
     */
    private String outboundStrategy;

    /**
     * 电商订单类型
     */
    private String ecpOrderType;

    /**
     * 大小件同仓配送模式
     * LARGE_WAREHOUSE_SMALL_SHIPMENT-大件仓小件配；
     * 默认无；
     */
    private WarehouseShipmentModeEnum warehouseShipmentMode;

    /**
     * 包装费
     */
    private String packagingFee;

    /**
     * 加工类型
     * 1-服饰定制
     */
    private String processingWay;

    /**
     *  送装服务模式配置(京东服务+的服务单)
     *  0-无安装，1-送装分离，2-送装一体
     */
    private String deliveryInstallationMode;

    /**
     *  商家箱明细信息dtc
     */
    private String customerBoxInfos;

    /**
     * 订单主辅标识
     * MAIN_ORDER-主单，SUB_ORDER-辅单
     */
    private String orderMainSubFlag;

    /**
     * 下传提前量
     */
    private Integer preProductionMinutes;

    /**
     * 生产耗时
     */
    private Integer productionDuration;

    /**
     * 在途时长
     */
    private Integer transitDuration;

    /**
     * 前置仓-生产开始时间
     */
    private Date productionStartTime;

    /**
     * 前置仓-生产结束时间
     */
    private Date productionEndTime;

    public void assignDeliveryInstallationMode(String haveServicePlusOrder) {
        this.deliveryInstallationMode = haveServicePlusOrder;
    }

    /**
     *商家客户订单号
     */
    private String sellerExternalOrderNo;

    /**
     * 仓运交接提货要求
     */
    private String handoverPickupRequirements;

    public void assignWeighingType(WeighingTypeEnum weighingType) {
        this.weighingType = weighingType;
    }


    public void assignOccupyResult(boolean occupyResult) {
        this.occupyResult = occupyResult;
    }

    public void assignPackDetailsCollect(PackDetailsCollectEnum packDetailsCollect) {
        this.packDetailsCollect = packDetailsCollect;
    }

    public void assignProductionByPack(boolean productionByPack) {
        this.productionByPack = productionByPack;
    }

    public void assignOrderPriority(Integer orderPriority) {
        this.orderPriority = orderPriority;
    }

    public void assignOrderUrgentType(OrderUrgencyType orderUrgencyType) {
        this.orderUrgencyType = orderUrgencyType;
    }

    public void assignInvokedCainiaoPlatform(boolean invokedCainiaoPlatform) {
        this.invokedCainiaoPlatform = invokedCainiaoPlatform;
    }

    public void assignCainiaoPlatformWaybill(String cainiaoPlatformWaybill) {
        this.cainiaoPlatformWaybill = cainiaoPlatformWaybill;
    }

    public void assignMjCompanyCode(String mjCompanyCode) {
        this.mjCompanyCode = mjCompanyCode;
    }

    public void assignCollectUniqueCodeFlag(boolean collectUniqueCodeFlag) {
        this.collectUniqueCodeFlag = collectUniqueCodeFlag;
    }

    public void assignSignDisclaimer(String signDisclaimer) {
        this.signDisclaimer = signDisclaimer;
    }

    public void assignCustomsClearance(CustomsClearance customsClearance) {
        this.customsClearance = customsClearance;
    }

    public void assginSellerIndividuationPrintInfo(String sellerIndividuationPrintInfo) {
        this.sellerIndividuationPrintInfo = sellerIndividuationPrintInfo;
    }

    public void assignCrossDockType(CrossDockTypeEnum crossDockType) {
        this.crossDockType = crossDockType;
    }

    public void assignTrustJDMetrics(boolean trustJDMetrics) {
        this.trustJDMetrics = trustJDMetrics;
    }

    public void assignVipPlatformTemplateUrl(String vipPlatformTemplateUrl) {
        this.vipPlatformTemplateUrl = vipPlatformTemplateUrl;
    }

    public void assignConsumableBilling(String consumableBilling) {
        this.consumableBilling = consumableBilling;
    }

    public void assignMaterialSolutionIds(List<Long> materialSolutionIds) {
        this.materialSolutionIds = materialSolutionIds;
    }

    public void assignPickCallback(String pickCallback) {
        this.pickCallback = pickCallback;
    }

    public void assignTimelinessDegradeType(Integer timelinessDegradeType) {
        this.timelinessDegradeType = timelinessDegradeType;
    }

    public void assignPackageSignFlag(boolean packageSignFlag) {
        this.packageSignFlag = packageSignFlag;
    }

    public void assignTmOrderFlag(TmOrderTypeEnum tmOrderFlag) {
        this.tmOrderFlag = tmOrderFlag;
    }

    public void assignMerchantRoute(String merchantRoute) {
        this.merchantRoute = merchantRoute;
    }

    public void assignWarehouseArrivalTime(String warehouseArrivalTime) {
        this.warehouseArrivalTime = warehouseArrivalTime;
    }

    public void assignFopFlowPathFlag(Boolean fopFlowPathFlag) {
        this.fopFlowPathFlag = fopFlowPathFlag;
    }

    public void assignPreProductionMinutes(Integer preProductionMinutes) {
        this.preProductionMinutes = preProductionMinutes;
    }

    public void assignProductionDuration(Integer productionDuration) {
        this.productionDuration = productionDuration;
    }

    public void assignTransitDuration(Integer transitDuration) {
        this.transitDuration = transitDuration;
    }

    public void assignProductionStartTime(Date productionStartTime) {
        this.productionStartTime = productionStartTime;
    }

    public void assignProductionEndTime(Date productionEndTime) {
        this.productionEndTime = productionEndTime;
    }

    public void assignEstimatedDeliveryTimePeriod(String estimatedDeliveryTimePeriod) {
        this.estimatedDeliveryTimePeriod = estimatedDeliveryTimePeriod;
    }
}
