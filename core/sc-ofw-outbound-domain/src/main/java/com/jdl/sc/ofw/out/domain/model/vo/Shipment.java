package com.jdl.sc.ofw.out.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.jdl.sc.ofw.out.common.dict.CheckGoodsTypeEnum;
import com.jdl.sc.ofw.out.common.dict.ContactlessReceiveTypeEnum;
import com.jdl.sc.ofw.out.common.dict.DeliveryTypeEnum;
import com.jdl.sc.ofw.out.common.dict.ExpectDeliveryResourceEnum;
import com.jdl.sc.ofw.out.common.dict.HalfReceiveEnum;
import com.jdl.sc.ofw.out.common.dict.ReturnTypeEnum;
import com.jdl.sc.ofw.out.common.dict.TmsDeliveryTypeEnum;
import com.jdl.sc.ofw.out.domain.model.RefSo;
import com.jdl.sc.ofw.out.domain.model.product.CityDeliveryTypeEnum;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryCollectionEnum;
import com.jdl.sc.ofw.out.domain.model.product.DeliveryPerformanceChannelEnum;
import com.jdl.sc.ofw.out.domain.model.product.ProductLineEnum;
import com.jdl.sc.ofw.outbound.spec.enums.BillingTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.DeliveryServiceEnum;
import com.jdl.sc.ofw.outbound.spec.enums.GoodsIsolateTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.OperationModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PackageProduceModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.PickupTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.ShipperTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.SignBillTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportModeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.TransportationTypeEnum;
import com.jdl.sc.ofw.outbound.spec.enums.WaybillNoFetchTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 配送信息
 */
@Builder
@Getter
@Jacksonized
@JsonAutoDetect(setterVisibility = JsonAutoDetect.Visibility.ANY, fieldVisibility = JsonAutoDetect.Visibility.ANY)
public class Shipment implements Serializable {
    /**
     * 承运商名称
     */
    @RefSo("soMain.shipperId")
    private Long shipperId;
    /**
     * 承运商编码
     */
    @RefSo("soMain.shipperNo")
    private String shipperNo;

    /**
     * 是否随单指定的承运商 1:是 0:否
     */
    private Integer designatedShipper;

    /**
     * 承运商类型
     */
    @RefSo("soMain.shipperType")
    private ShipperTypeEnum shipperType;

    /**
     * 承运商名称
     */
    @RefSo("soMain.shipperName")
    private String shipperName;

    /**
     * 预期送货开始时间
     */
    @RefSo("soMain.expectDeliveryDate")
    private Date expectDeliveryStartTime;

    /**
     * 预期送货结束时间
     */
    @RefSo("soMain.expectDate")
    private Date expectDeliveryEndTime;

    /**
     * promise返回预期送货开始时间 //TODO  和 @Param soMain.expectDeliveryDate 关系
     */
    @RefSo("soExtendParam.expectDeliveryStartDate")
    private Date expectDeliveryStartDate;

    @RefSo("soExtendParam.cityDeliveryStartDate")
    private Date cityDeliveryStartDate;

    @RefSo("soExtendParam.cityDeliveryStartDate")
    private Date cityDeliveryEndDate;
    /**
     * 退仓类型
     * somark44
     * <p>
     * SOMARK_FORTYFOUR_0(44, '0', "退京东仓"),
     * SOMARK_FORTYFOUR_1(44, '1', "退商家仓"),
     * SOMARK_FORTYFOUR_2(44, '2', "退门店"),
     * SOMARK_FORTYFOUR_3(44, '3', "就近退"),
     */
    @RefSo("soMain.somark")
    private ReturnTypeEnum returnType;

    /**
     * 期望配送资源，原合同物流标:
     * somark142
     * 是否B2B合同物流订单 0：否1：合同物流
     */
    @RefSo("soMain.somark")
    private ExpectDeliveryResourceEnum expectDeliveryResource;

    /**
     * 青龙业主号，一般为商家传值，根据这个获取青龙业主的具体信息
     * <ul>
     * <li>POP来源:从店铺获取京东平台的青龙业主号</li>
     * <li>JOS来源:开启无店铺下单,从事业部取,否则从店铺取,区分销售平台,获取不同的青龙业主号</li>
     * <li>EDI来源:大部分会检查,开启无店铺下单,从事业部取,否则从店铺取,区分销售平台,获取不同的青龙业主号</li>
     * </ul>
     */
    @RefSo(value = {"soMain.bdOwnerNo"})
    private String bdOwnerNo;


    /**
     * 青龙运单号，一般是在青龙三合一接口返回的接口中，菜鸟转三方的时候，会填ESL单号soNo
     * <ul>
     * <li>京配转三方商家可能会带运单号</li>
     * <li>卡宾调用商家接口获取运单号</li>
     * <li>安踏调用顺丰的接口获取运单号</li>
     * </ul>
     */
    @RefSo(value = {"soMain.wayBill"})
    private String wayBill;

    /**
     * 站点编号
     */
    @RefSo(value = {"soMainParam.stationNo"})
    private String endStationNo;

    /**
     * 站点名称
     */
    @RefSo(value = {"soMainParam.stationName"})
    private String endStationName;

    /**
     * 是否忽略promise时效，下传青龙会用到。默认不忽略，如果需要调用promise会被重新计算
     */
    @RefSo("OrderConfigEnum.ORDER_EXPECT_DATE_VALID")
    @Builder.Default
    private boolean ignorePromise = false;

    /**
     * 非接触收货类型
     */
    @RefSo("soMain.somark134")
    private final ContactlessReceiveTypeEnum contactlessReceiveType;

    @RefSo("oMainParam.getExtendedField().getPopExt.OrderPayShipmentInfo.tagContent")
    private final String assignedAddress;

    /**
     * 半收类型
     */
    @RefSo("soMain.somark136")
    private HalfReceiveEnum halfReceive;

    /**
     * 自提订单
     */
    @RefSo("soMain.somark137")
    private final DeliveryTypeEnum deliveryType;

    /**
     * 计费模式
     */
    @RefSo("soMain.somark11")
    private BillingTypeEnum billingType;

    /**
     * 预分拣结果
     */
    private PresortInfo presortInfo;

    /**
     * 运输类型：取自promise或商家指定
     */
    @RefSo("soMain.transType|SoTransTypeEnum")
    @Builder.Default
    private TransportationTypeEnum transportationType = TransportationTypeEnum.NO_PROMISE;

    /**
     * isv商家期望运输类型
     */
    @RefSo("soMain.extendedField.expecTransport")
    private TransportationTypeEnum sellerExpectTransport;


    @RefSo("soMain.soMark51")
    private TransportModeEnum transportModeEnum;
    /**
     * 运单组号
     */
    private String groupNo;

    /**
     * 原包标识
     */
    @RefSo("soMainParam.extendedField.logisticsOriginalPackage")
    private Integer logisticsOriginalPackage;

    /**
     * 包装免赔标识，多个的情况用 , 分隔
     * 枚举值为1：全部免赔，2：破损免赔，3：外包完好免赔，4：冻损免赔
     */
    @RefSo("soMainParam.extendedField.deductible")
    private String deductibleCompensation;

    /**
     * 自定义外部围栏
     */
    private List<CustomPresort> customPresortList;

    /**
     * 城配标识
     */
    @RefSo("soMain.somark37")
    private boolean cityDeliveryType;
    /**
     * 初始的城配标识
     */
    private boolean initialCityDeliveryType;

    /**
     * 期望的城配标识
     */
    private boolean expectCityDeliveryType;

    /**
     * 卸货道口
     */
    private String unloadingDock;

    /**
     * 城配调度再生产
     */
    private Boolean dispatchReproduce;

    /**
     * 青龙订单类型
     */
    @RefSo("soMain.somark62")
    private TmsDeliveryTypeEnum tmsDeliveryType;

    @RefSo("soMain.somark119")
    private final CheckGoodsTypeEnum checkGoodsTypeEnum;

    /**
     * 三方运单类型
     */
    @RefSo("extendedField.expressType")
    private String thirdPartExpressType;

    /**
     * 自采京配产品线（限自采京配使用）
     */
    private ProductLineEnum ziCaiJDProductLine;

    /**
     * 只需要三方运单号
     */
    private boolean needExternalWaybillOnly;

    /**
     * 首个包裹运单下发
     */
    @RefSo("soMark114")
    private Boolean firstWaybillIssue;

    /**
     * 三方承运商
     */
    private String externalCarrierCode;
    /**
     * 打印信息
     */
    private PrintExtendInfo printExtendInfo;

    /**
     * 售后联系人
     */
    private String contactPerson;

    /**
     * 售后联系电话
     */
    private String contactPhone;

    /**
     * 售后联系地址
     */
    private String contactAddress;

    /**
     * 修改重货上楼 0:非重货上楼 1:重货上楼
     */
    @RefSo("soMark67")
    private boolean heavyGoodsUpstairs;

    /**
     * 隐私通话标识 0:不隐藏 1:隐藏
     */
    @RefSo("soMark86")
    private boolean hidePrivacyType;

    @RefSo("soExtendParam.cityDeliveryDeadline")
    private String cityDeliveryDeadline;

    @RefSo("soExtendParam.cityDeadlineType")
    private CityDeliveryTypeEnum cityDeadlineType;

    /**
     * 时效名称
     */
    @RefSo("soExtendParam.agingName")
    private String agingName;

    /**
     * 城配-派车单号
     */
    @RefSo("soExtend.dispatchNo")
    private String dispatchNo;
    /**
     * 城配-运输单数
     */
    @RefSo("soExtend.transportCount")
    private String transportCount;
    /**
     * 城配-装车顺序
     */
    @RefSo("soExtend.loadSequence")
    private String loadSequence;
    /**
     * 城配-卡位号
     */
    @RefSo("soExtend.cardNo")
    private String cardNo;

    /**
     * 计划始发进场时间
     */
    private String planBeginEnterTime;
    /**
     * 三方运单号,仅转三方场景使用
     */
    @RefSo("soMain.thirdWayBill")
    private String thirdWayBill;


    /**
     * 签单返还日期
     */
    @RefSo("soMainParam.extendedField.signBillBackReturnDate")
    private String reReceiveDate;

    /**
     * 签单返还收件人姓名
     */
    @RefSo("soExtend.grossReturnName")
    private String reReceiveName;

    /**
     * 签单返还收件人手机
     */
    @RefSo("soExtend.grossReturnMobile")
    private String reReceiveMobile;

    /**
     * 签单返还收件人电话
     */
    @RefSo("soExtend.grossReturnPhone")
    private String reReceivePhone;

    /**
     * 签单返还收件人地址
     */
    @RefSo("soExtend.grossReturnAddress")
    private String reReceiveAdress;

    /*
     * 签单返还类型
     * 0-无签单 1-纸质单 3-电子单 4-纸质单+电子单
     * */
    @RefSo("soMain.somark4")
    private SignBillTypeEnum signBillTypeEnum;

    /**
     * 母单号
     */
    @RefSo("soExtend.motherNo")
    private String parentWaybillNo;


    /**
     * 是否菜鸟包裹
     */
    @RefSo("soMark38")
    private boolean CNTemplate;

    /**
     * 妥投日期
     */
    private Date deliveryDate;

    /**
     * 波次开始时间
     */
    private String batchStartTime;

    /**
     * 波次结束时间
     */
    private String batchEndTime;

    /**
     * 包裹生产模式
     */
    @RefSo("soMark113")
    private PackageProduceModeEnum packageProduceMode;

    /**
     * 配送履约渠道  1-京东  2-抖音
     */
    private DeliveryPerformanceChannelEnum deliveryPerformanceChannel;

    /**
     * 三方配送信息
     */
    private ThirdShipment thirdShipment;

    /**
     * 三方产品类型
     */
    @RefSo("extendedField.customField.getString(\"thirdWayBillType\")")
    private String thirdExpressProduct;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 仓配揽收业务标识
     */
    private DeliveryCollectionEnum express;

    /**
     * 激活卡业务
     */
    @RefSo("soMainParam.extendedField.activationCardService")
    private String activationCardService;

    /**
     * 如果是身份证签收，则需要保留6到18位的数字字母的签收
     */
    @RefSo("soMainParam.soExtendParam.signIDCode")
    private String signIDCode;

    /**
     * 是否指定签收 0、不需要此服务 2、短信验证 3、身份证验证   针对isv订单有前面的三个选项 京配订单只有0 ,2
     */
    @RefSo("soMainParam.soExtendParam.signType")
    private String signType;

    /**
     * 是否下发指定预置运单号
     */
    private boolean transportPresetNo;

    @RefSo("soMark92")
    private String warmLayer;

    /**
     * 隔离运输类型
     */
    @RefSo("soMark93、soMark94")
    private GoodsIsolateTypeEnum goodsIsolateType;

    /**
     * 目的中心编码-订单中心新增
     */
    private String endCenterNo;

    /**
     * 目的中心名称
     */
    @RefSo("soMainParam.extendedField.destSiteName")
    private String endCenterName;

    /**
     * 计划出库时间
     */
    @RefSo("soExtend.planDeliveryTime")
    private Date planDeliveryTime;

    /**
     * 城配-串点数
     */
    @RefSo("SoChargeWork.bunchQty")
    private String bunchQty;

    /**
     * 城配-公里数
     */
    @RefSo("SoChargeWork.kilometers")
    private String kilometers;

    /**
     * 特快送运营模式
     */
    private OperationModeEnum operationMode;

    /**
     * 商家承运商编码
     */
    @RefSo("soMainParam.extendedField.isvShipperNo")
    private String sellerShipperNo;

    /**
     * 商家承运商名称
     */
    @RefSo("soMainParam.extendedField.isvShipperName")
    private String sellerShipperName;

    /**
     * 时效有效性标识
     */
    private String expectDeliveryTimeFlag;

    /**
     * 是否送货入仓
     */
    private Boolean deliveryIntoWarehouse;

    /**
     * 入仓预约码
     */
    private String warehousingAppointmentCode;
    /**
     * 入仓预约开始时间
     */
    private String warehousingAppointmentStartTime;
    /**
     * 入仓预约结束时间
     */
    private String warehousingAppointmentEndTime;
    /**
     * 入仓备注
     */
    private String warehousingAppointmentRemark;

    /**
     * 用于回传商家
     * 司机
     */
    private String deliveryStaffName;

    /**
     * 用于回传商家
     * 车牌号
     */
    private String vehicleNumber;

    /**
     * 用于回传商家
     * 操作时间
     */
    private String operateTime;


    /**
     * 是否开启奢侈品保证服务
     */
    private Boolean luxurySecurity;

    /**
     * 是否妥投强制拍照
     */
    private Boolean deliveryForcePhoto;

    /**
     * 是否妥投强制电联
     */
    private Boolean forceContact;

    /**
     * 运单号获取方式
     */
    private WaybillNoFetchTypeEnum waybillNoFetchType;

    /**
     * 预约派送标识
     */
    private DeliveryServiceEnum deliveryService;
    /**
     * 是否为城配B网
     */
    public boolean isCityDelivery2B() {
        return isCityDeliveryType() && getTmsDeliveryType() != null && TmsDeliveryTypeEnum.TMS_B2B == getTmsDeliveryType();
    }

    /**
     * 是否允许拒收 0 允许拒收、1 不允许拒收、2 必须拒收 
     */
    private String refusalAllowed;


    /**
     * 微信视频号-预取号ID
     * 无业务含义,和产业平台微信视频号交互使用
     */
    private String thirdPreOrderId;


    /**
     * 揽收开始时间
     */
    private Date collectStartTime;

    /**
     * 揽收结束时间
     */
    private Date collectEndTime;

    /**
     * 分批派送
     * 多包裹是否允许先到先送
     */
    private Boolean deliveryInBatches;

    /**
     * 时效围栏id
     */
    private String fenceId;

    /**
     * 时效围栏类型
     */
    private String fenceType;

    /**
     * 非本人签收 是否强制外呼标识，枚举值：0-否 1-是
     */
    private String nonSelfSignCall;

    /**
     * 托寄物管控规则：false-不校验、true-校验
     */
    private Boolean consignmentControlRules;

    /**
     * 揽收类型 1-上门揽件；2-自送
     */
    private PickupTypeEnum pickupType;

    /**
     * 包装标准，1-等于JDL标准 2-高于JDL标准 3-低于JDL标准
     */
    private String packagingStandards;


    /**
     * 数据传输要求
     * 1-不需要(或者不传此服务标识key)
     * 2-收货人信息加密
     * 3-发货人信息加密
     * 4-收发货人信息全加密
     */
    private String dataTransferRequirements;

    /**
     * 托寄物类型: 书籍、电子产品、服装
     */
    private String consignmentType;

    /**
     * 分网结果
     */
    private Integer deliveryNetworkResults;
    /**
     * 集货地编码
     */
    private String consolidationPointCode;

    /**
     * 集货地名称
     */
    private String consolidationPointName;
    /**
     * TC专用预计揽收开始时间
     */
    private Date tcExpectPickupStartTime;
    /**
     * TC专用预计揽收结束时间
     */
    private Date tcExpectPickupEndTime;

    /**
     * 商家运输方式编码
     */
    private String customerTransportType;

    /**
     * 商家运输方式名称
     */
    private String customerTransportName;

    /**
     * 服务要求
     */
    private Map<String, String> serviceRequirements;


    /**
     * 流向类型，0或空为跳过流向处理过程（按大陆处理），1为不跳过（目前只涉及港澳）
     */
    private String flowType;

    private ShipModeEnum shipMode;

    /**
     * 电商平台物流服务，如抖音的"音需达"
     */
    private String platformLogisticsService;

    /**
     * 产品分单使用货品数量类型 0计划数量，1预占数量
     */
    private String productSplitCargoQuantityType;

    /**
     * 客户自定义无接触收货方式
     */
    private String customerContactlessType;

    /**
     * 高值
     */
    private String highValue;

    /**
     * 冷冻品
     */
    private String frozen;

    /**
     * 冷藏品
     */
    private String refrigerate;

    /**
     * 易碎品
     */
    private String frangible;

    /**
     * 生鲜品
     */
    private String fresh;

    /**
     * 超重 超过30kg
     */
    private String overweight;

    /**
     * 即时配送方式
     */
    private String instantDeliveryType;

    /**
     * GIS围栏ID
     */
    private String gisFenceId;

    public void assignCollectStartTime(Date collectStartTime) {
        this.collectStartTime = collectStartTime;
    }

    public void assignCollectEndTime(Date collectEndTime) {
        this.collectEndTime = collectEndTime;
    }

    public void assignFenceId(String fenceId) {
        this.fenceId = fenceId;
    }

    public void assignFenceType(String fenceType) {
        this.fenceType = fenceType;
    }

    /**
     * 外部承运商编码
     */
    private String externalShipNo;

    /**
     * 站仓标识
     */
    private String distributionWarehouse;

    public void assignDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public void assignBatchStartTime(String batchStartTime) {
        this.batchStartTime = batchStartTime;
    }

    public void assignBatchEndTime(String batchEndTime) {
        this.batchEndTime = batchEndTime;
    }

    public void assignReturnType(final ReturnTypeEnum returnType) {
        this.returnType = returnType;
    }

    public void assignExpectDeliveryResource(final ExpectDeliveryResourceEnum expectDeliveryResource) {
        this.expectDeliveryResource = expectDeliveryResource;
    }

    public void assignBdOwnerNo(final String bdOwnerNo) {
        this.bdOwnerNo = bdOwnerNo;
    }

    public void assignBillingType(final BillingTypeEnum billingType) {
        this.billingType = billingType;
    }

    public void assignExpectDeliveryStartTime(Date expectDeliveryStartTime) {
        this.expectDeliveryStartTime = expectDeliveryStartTime;
    }

    public void assignExpectDeliveryEndTime(Date expectDeliveryEndTime) {
        this.expectDeliveryEndTime = expectDeliveryEndTime;
    }

    public void assignTransportationType(TransportationTypeEnum transportationType) {
        this.transportationType = transportationType;
    }

    public void assignWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    public void assignPresortInfo(PresortInfo presortInfo) {
        this.presortInfo = presortInfo;
    }

    public void assignShipperNo(String shipperNo) {
        this.shipperNo = shipperNo;
    }

    public void assignShipperId(Long shipperId) {
        this.shipperId = shipperId;
    }

    public void assignShipperType(ShipperTypeEnum shipperType) {
        this.shipperType = shipperType;
    }

    public void assignShipperName(String shipperName) {
        this.shipperName = shipperName;
    }

    public void assignIgnorePromise(boolean ignorePromise) {
        this.ignorePromise = ignorePromise;
    }

    public void assignContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public void assignContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public void assignContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public void assignHeavyGoodsUpstairs(Boolean heavyGoodsUpstairs) {
        this.heavyGoodsUpstairs = heavyGoodsUpstairs;
    }

    public void assignCustomPresortList(List<CustomPresort> customPresortList) {
        this.customPresortList = customPresortList;
    }

    public void assignHalfReceive(HalfReceiveEnum halfReceive) {
        this.halfReceive = halfReceive;
    }

    public void assignThirdPartExpressType(String thirdPartExpressType) {
        this.thirdPartExpressType = thirdPartExpressType;
    }

    public void assignCityDeliveryType(boolean cityDeliveryType) {
        this.cityDeliveryType = cityDeliveryType;
    }

    public void assignExternalCarrierCode(String externalCarrierCode) {
        this.externalCarrierCode = externalCarrierCode;
    }

    public void assignCityDeliveryStartDate(Date cityDeliveryStartDate) {
        this.cityDeliveryStartDate = cityDeliveryStartDate;
    }

    public void assignCityDeliveryEndDate(Date cityDeliveryEndDate) {
        this.cityDeliveryEndDate = cityDeliveryEndDate;
    }

    public void assignCityDeliveryDeadline(String cityDeliveryDeadline) {
        this.cityDeliveryDeadline = cityDeliveryDeadline;
    }

    public void assignExpectDeliveryStartDate(Date expectDeliveryStartDate) {
        this.expectDeliveryStartDate = expectDeliveryStartDate;
    }

    public void assignDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public void assignTransportCount(String transportCount) {
        this.transportCount = transportCount;
    }

    public void assignLoadSequence(String loadSequence) {
        this.loadSequence = loadSequence;
    }

    public void assignCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public void assignTransportModeEnum(TransportModeEnum transportModeEnum) {
        this.transportModeEnum = transportModeEnum;
    }

    public void assignTmsDeliveryType(TmsDeliveryTypeEnum tmsDeliveryType) {
        this.tmsDeliveryType = tmsDeliveryType;
    }

    public void assignParentWaybillNo(String parentWaybillNo) {
        this.parentWaybillNo = parentWaybillNo;
    }

    public void assignFirstWaybillIssue(boolean firstWaybillIssue) {
        this.firstWaybillIssue = firstWaybillIssue;
    }


    public void assignPackageProduceMode(PackageProduceModeEnum packageProduceMode) {
        this.packageProduceMode = packageProduceMode;
    }

    public void assignThirdShipment(final ThirdShipment thirdShipment) {
        this.thirdShipment = thirdShipment;
    }

    public void assignThirdWayBill(String thirdWayBill) {
        this.thirdWayBill = thirdWayBill;
    }

    public void assignEndStationNo(String stationNo) {
        this.endStationNo = stationNo;
    }

    public void assignDispatchReproduce(Boolean dispatchReproduce) {
        this.dispatchReproduce = dispatchReproduce;
    }

    public void assignTransportPresetNo(boolean transportPresetNo) {
        this.transportPresetNo = transportPresetNo;
    }
    public void assignEndCenterNo(String endCenterNo) {
        this.endCenterNo = endCenterNo;
    }

    public void assignEndCenterName(String endCenterName) {
        this.endCenterName = endCenterName;
    }

    public void assignBunchQty(String bunchQty) {
        this.bunchQty = bunchQty;
    }

    public void assignKilometers(String kilometers) {
        this.kilometers = kilometers;
    }

    public void assignSignBillType(SignBillTypeEnum signBillType) {
        this.signBillTypeEnum = signBillType;
    }
    public void assignSignType(String signType) {
        this.signType = signType;
    }
    public void assignOperationMode(final OperationModeEnum operationMode) {
        this.operationMode = operationMode;
    }
    public void assignDeliveryStaffName(String deliveryStaffName){
        this.deliveryStaffName = deliveryStaffName;
    }

    public void assignVehicleNumber(String vehicleNumber){
        this.vehicleNumber = vehicleNumber;
    }

    public void assignOperateTime(String operateTime){
        this.operateTime = operateTime;
    }

    public void assignLuxurySecurity(Boolean luxurySecurity){
        this.luxurySecurity = luxurySecurity;
    }

    public void assignDeliveryForcePhoto(Boolean deliveryForcePhoto){
        this.deliveryForcePhoto = deliveryForcePhoto;
    }

    public void assignForceContact(Boolean forceContact){
        this.forceContact = forceContact;
    }

    public void assignThirdExpressProduct(String thirdExpressProduct){this.thirdExpressProduct = thirdExpressProduct;}

    public void assignDeliveryIntoWarehouse(Boolean deliveryIntoWarehouse) {
        this.deliveryIntoWarehouse = deliveryIntoWarehouse;
    }

    public void assignThirdPreOrderId(String thirdPreOrderId) {
        this.thirdPreOrderId = thirdPreOrderId;
    }

    public void assignPlatformLogisticsService(String platformLogisticsService) {
        this.platformLogisticsService = platformLogisticsService;
    }

    public void assignPlanBeginEnterTime(String planBeginEnterTime) {
        this.planBeginEnterTime = planBeginEnterTime;
    }

    public void assignShipMode(ShipModeEnum shipMode) {
        this.shipMode = shipMode;
    }

    public void assignDeliveryPerformanceChannel(DeliveryPerformanceChannelEnum deliveryPerformanceChannel){this.deliveryPerformanceChannel = deliveryPerformanceChannel;}

    public void assignPickupType(PickupTypeEnum pickupType) {
        this.pickupType = pickupType;
    }

    public void assignWarmLayer(String warmLayer) {
        this.warmLayer = warmLayer;
    }

    public void assignDeliveryNetworkResults(Integer deliveryNetworkResults) {
        this.deliveryNetworkResults = deliveryNetworkResults;
    }

    public void assignExternalShipNo(String externalShipNo) {
        this.externalShipNo = externalShipNo;
    }

    public void assignZiCaiJDProductLine(ProductLineEnum ziCaiJDProductLine) {
        this.ziCaiJDProductLine = ziCaiJDProductLine;
    }

    public void assignHighValue(String highValue) {
        this.highValue = highValue;
    }

    public void assignFrozen(String frozen) {
        this.frozen = frozen;
    }

    public void assignRefrigerate(String refrigerate) {
        this.refrigerate = refrigerate;
    }

    public void assignFrangible(String frangible) {
        this.frangible = frangible;
    }

    public void assignFresh(String fresh) {
        this.fresh = fresh;
    }

    public void assignOverweight(String overweight) {
        this.overweight = overweight;
    }

    public void assignConsolidationPointCode(String consolidationPointCode) {
        this.consolidationPointCode = consolidationPointCode;
    }

    public void assignConsolidationPointName(String consolidationPointName) {
        this.consolidationPointName = consolidationPointName;
    }

    public void assignDistributionWarehouse(String distributionWarehouse) {
        this.distributionWarehouse = distributionWarehouse;
    }

    public void assignServiceRequirements(Map<String, String> serviceRequirements) {
        this.serviceRequirements = serviceRequirements;
    }
}
