<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sc-ofw-outbound</artifactId>
        <groupId>com.jdl.sc</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sc-ofw-outbound-main</artifactId>
    <packaging>war</packaging>

    <properties>
        <tomcat.version>8.5.35</tomcat.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>core-javassist-plus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>core-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-application</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>org.mortbay.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-horiz-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-b2b-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-coldchain-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-i18n-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-ka-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-las-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-coldchain-ka-extension</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-vertical-instant-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>
        <!-- spring boot end -->

        <!-- Tomcat安全加固 - 防御jsp木马 - 开始 -->
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>jd-security-tomcat</artifactId>
            <version>1.13.WEBAPP</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-jasper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-catalina</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--外置Tomcat7 8 9版本配置方法-->
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-jasper</artifactId>
            <version>${tomcat.version}</version><!-- 与线上tomcat版本号一致 -->
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-catalina</artifactId>
            <version>${tomcat.version}</version><!-- 与线上tomcat版本号一致 -->
        </dependency>
        <!-- Tomcat安全加固 - 防御jsp木马 - 结束 -->

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.jdl.sc</groupId>
            <artifactId>sc-ofw-outbound-domain</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-bundle</artifactId>
            <version>1.19.4</version>
        </dependency>
    </dependencies>
    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.active>dev</profile.active>
                <mvn.log.level>debug</mvn.log.level>
                <mvn.log.main.level>debug</mvn.log.main.level>
                <mvn.log.include.location>true</mvn.log.include.location>
                <mvn.log.batrix.caller>batrix_caller</mvn.log.batrix.caller>
                <mvn.log.batrix.bootstrap.servers>test-nameserver.jmq.jd.local:50088</mvn.log.batrix.bootstrap.servers>
            </properties>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <profile.active>test</profile.active>
                <mvn.log.level>debug</mvn.log.level>
                <mvn.log.main.level>debug</mvn.log.main.level>
                <mvn.log.include.location>true</mvn.log.include.location>
                <mvn.log.batrix.caller>batrix_caller</mvn.log.batrix.caller>
                <mvn.log.batrix.bootstrap.servers>test-nameserver.jmq.jd.local:50088</mvn.log.batrix.bootstrap.servers>
            </properties>
        </profile>
        <!-- UAT -->
        <profile>
            <id>uat</id>
            <properties>
                <profile.active>uat</profile.active>
                <mvn.log.level>info</mvn.log.level>
                <mvn.log.main.level>debug</mvn.log.main.level>
                <mvn.log.include.location>true</mvn.log.include.location>
                <mvn.log.batrix.caller>batrix_caller_uat</mvn.log.batrix.caller>
                <mvn.log.batrix.bootstrap.servers>nameserver.jmq.jd.local:80</mvn.log.batrix.bootstrap.servers>
            </properties>
        </profile>
        <!-- AB -->
        <profile>
            <id>ab</id>
            <properties>
                <profile.active>ab</profile.active>
                <mvn.log.level>info</mvn.log.level>
                <mvn.log.main.level>debug</mvn.log.main.level>
                <mvn.log.include.location>true</mvn.log.include.location>
                <mvn.log.batrix.caller>batrix_caller_uat</mvn.log.batrix.caller>
                <mvn.log.batrix.bootstrap.servers>nameserver.jmq.jd.local:80</mvn.log.batrix.bootstrap.servers>
            </properties>
        </profile>
        <!-- 正式环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
                <mvn.log.level>info</mvn.log.level>
                <mvn.log.main.level>info</mvn.log.main.level>
                <mvn.log.include.location>false</mvn.log.include.location>
                <mvn.log.batrix.caller>batrix_caller</mvn.log.batrix.caller>
                <mvn.log.batrix.bootstrap.servers>nameserver.jmq.jd.local:80</mvn.log.batrix.bootstrap.servers>
            </properties>
        </profile>
        <!-- 回归环境 -->
        <profile>
            <id>regression</id>
            <properties>
                <profile.active>regression</profile.active>
                <mvn.log.level>info</mvn.log.level>
                <mvn.log.main.level>debug</mvn.log.main.level>
                <mvn.log.include.location>true</mvn.log.include.location>
                <mvn.log.batrix.caller>batrix_caller</mvn.log.batrix.caller>
                <mvn.log.batrix.bootstrap.servers>test-nameserver.jmq.jd.local:50088</mvn.log.batrix.bootstrap.servers>
            </properties>
        </profile>
    </profiles>
    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/profiles/${profile.active}</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.0.5.RELEASE</version>
                <configuration>
                    <mainClass>com.jdl.sc.ofw.out.main.ProjectWebApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18.1</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <artifactId>maven-war-plugin</artifactId>-->
            <!--                <version></version>-->
            <!--                <configuration>-->
            <!--                    <failOnMissingWebXml>false</failOnMissingWebXml>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <encoding>utf-8</encoding>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>