package com.jdl.sc.ofw.out.main.jsf;

import com.jd.jsf.gd.util.RpcContext;
import com.jdl.sc.core.jsf.LogContext;
import com.jdl.sc.core.jsf.filter.JsfLogService;
import com.jdl.sc.core.log.collect.AuditLogEntity;
import com.jdl.sc.core.log.collect.AuditLogger;
import com.jdl.sc.core.log.collect.AuditType;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.core.utils.NetUtil;
import com.jdl.sc.ofw.out.common.batrix.BatrixContext;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
public class JsfLogServiceImpl implements JsfLogService {
    @Value("${ducc.core.interface:}")
    private Set<String> coreInterfaces;

    @Value("${jone.application.name:}")
    private String appName;

    @Override
    public void collect(LogContext logContext) {
        try {
            if (!coreInterfaces.contains(logContext.getInterfaceName())) {
                return;
            }
            AuditLogger.info(AuditLogEntity.builder()
                    .tid(MDC.get("traceId"))
                    .localAppName(appName)
                    .remoteIp(RpcContext.getContext().getRemoteHostName())
                    .localIp(NetUtil.getLocalIp())
                    .orderNo(BatrixContext.getOrder())
                    .businessUnit(BatrixContext.getBusinessUnit())
                    .interfaceName(logContext.getInterfaceName())
                    .type(AuditType.JSF_CONSUMER)
                    .requestContent(logContext.getRequestParam())
                    .responseContent(logContext.getResponseParam())
                    .operateTime(DateUtil.dateToString(logContext.getCreateTime()))
                    .build());
        } catch (Exception e) {
            log.error("核心接口出入参收集失败，单号：{}，接口：{}，异常：{}", logContext.getOrderNo(), logContext.getInterfaceName(), e.getMessage());
        }
    }
}
