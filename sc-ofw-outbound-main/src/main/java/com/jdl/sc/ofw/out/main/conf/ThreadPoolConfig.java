package com.jdl.sc.ofw.out.main.conf;

import com.jd.laf.config.spring.annotation.LafUcc;
import com.jdl.sc.core.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Configuration
public class ThreadPoolConfig {


    /**
     * 大报文解析线程池
     */
    @Bean("clobThreadPoolExecutor")
    public ThreadPoolTaskExecutor threadPoolExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(8);
        // 空闲线程最大存活时间，默认60秒
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(40);
        executor.setThreadNamePrefix("custom-clobThreadPoolExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());    // 交由调用者的线程执行
        // 等待所有任务结束后再关闭线程池，默认false
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待所有任务结束最长等待时间，默认0秒
        executor.setAwaitTerminationSeconds(25);
        // 执行初始化
        executor.initialize();
        return executor;
    }


    @Resource(name = "clobThreadPoolExecutor")
    private ThreadPoolTaskExecutor clobThreadPoolExecutor;

    @LafUcc
    @Value("${ducc.clobThreadPoolExecutor.conf:{}}")
    public void setThreadPoolExecutor(String duccConf){
        log.info("ducc 动态调整 大报文线程池配置, duccConf:{}", duccConf);
        if (StringUtils.isBlank(duccConf)) {
            return;
        }
        try {
            DuccThreadPoolConf conf = JsonUtil.parse(duccConf, DuccThreadPoolConf.class);
            if (Objects.nonNull(conf.getCorePoolSize())) {
                clobThreadPoolExecutor.setCorePoolSize(conf.getCorePoolSize());
            }
            if (Objects.nonNull(conf.getMaxPoolSize())){
                clobThreadPoolExecutor.setMaxPoolSize(conf.getMaxPoolSize());
            }
            if (Objects.nonNull(conf.getQueueCapacity())){
                clobThreadPoolExecutor.setQueueCapacity(conf.getQueueCapacity());
            }
            if (Objects.nonNull(conf.getWaitForTasksToCompleteOnShutdown())){
                clobThreadPoolExecutor.setWaitForTasksToCompleteOnShutdown(conf.getWaitForTasksToCompleteOnShutdown());
            }
            if (Objects.nonNull(conf.getAwaitTerminationSeconds())){
                clobThreadPoolExecutor.setAwaitTerminationSeconds(conf.getAwaitTerminationSeconds());
            }
        }catch (Exception e){
            log.error("ducc 动态调整 大报文线程池配置 异常duccConf:{}", duccConf, e);
        }
    }
}
