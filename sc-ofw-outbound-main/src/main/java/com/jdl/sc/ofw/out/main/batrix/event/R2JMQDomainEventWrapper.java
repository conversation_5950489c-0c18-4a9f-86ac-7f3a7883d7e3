package com.jdl.sc.ofw.out.main.batrix.event;

import cn.jdl.batrix.core.flow.event.JMQDomainEvent;
import com.jd.jmq.client.api.MQClientManager;
import com.jd.jmq.client.consumer.MessageConsumer;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.consumer.TopicSetting;
import com.jd.jmq.common.message.Message;
import com.jdl.sc.core.javassist.plus.ClassPlus;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.utils.CollectionUtils;
import com.jdl.sc.core.utils.ReflectionUtils;
import com.jdl.sc.core.utils.StringUtils;
import com.jdl.sc.core.utils.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Component
/**
 *-Djtm.path.mapping=cn.jdl.batrix.core.flow.event.JMQDomainEvent#:#com.jdl.sc.ofw.out.main.batrix.event.R2JMQDomainEventWrapper
 */
@Lazy(false)
public class R2JMQDomainEventWrapper implements MessageListener {
    static {
        codePlus();
    }

    private MessageListener getListener(String eventAction) {
        List<MessageConsumer> consumers = ReflectionUtils.getFieldValue(MQClientManager.class, MQClientManager.getInstance(), List.class, "consumerList");
        for (MessageConsumer consumer : consumers) {
            Set<TopicSetting> topicSettings = consumer.getTopicSettings();
            for (TopicSetting ts : topicSettings) {
                log.info("topic:{},Listener:{}", ts.getTopic(), ts.getListener().getClass().getName());
                if (ts.getListener() instanceof JMQDomainEvent) {
                    JMQDomainEvent dom = (JMQDomainEvent) ts.getListener();
                    String ac = ReflectionUtils.getFieldValue(JMQDomainEvent.class, dom, String.class, "actionCode");
                    boolean ok = Objects.equals(ac, eventAction);
                    log.info("actionCode:{},eventAction:{},是否一致:{}", ac, eventAction, ok);
                    if (ok) {
                        return dom;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void onMessage(List<Message> list) throws Exception {
        MDC.put("traceId", TraceIdUtils.getTraceId(null));
        try {
            if (log.isInfoEnabled()) {
                log.info("回放开始啦:{}", JsonUtil.toJsonSafe(list));
            }
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            Map map = JsonUtil.parse(list.get(0).getText(), Map.class);
            Object eventAction = map.get("eventAction");
            if (eventAction == null) {
                log.info("没有找到eventAction:{},", list.get(0).getText());
                return;
            }
            MessageListener listener = getListener(eventAction.toString());
            if (listener == null) {
                log.info("没有找到listener,eventAction:{}", eventAction);
                return;
            }
            listener.onMessage(list);
            log.info("回放成功啦");
        } catch (Exception e) {
            log.error("回放错误了", e);
            throw e;
        } finally {
            MDC.clear();
        }
    }

    private static void codePlus() {
        String mapping = System.getProperty("jtm.path.mapping");
        if (StringUtils.isBlank(mapping)) {
            return;
        }
        try {
            log.info("Code plus start");
            ClassPlus.forName("com.jdl.sc.ofw.out.domain.model.vo.Fulfillment")
                    .addAnnoToMethodSafe("getCustomField", "com.jd.pfinder.module.r2.core.fastjson2.annotation.JSONField", "serialize", false)
                    .addAnnoToMethodSafe("getCustomField", "com.jd.pfinder.module.r2.core.fastjson.annotation.JSONField", "serialize", false)
                    .addAnnoToMethodSafe("getCustomField", "com.jd.pfinder.module.r2.core.com.fasterxml.jackson.annotation.JsonIgnore", "value", true)
                    .toClassSafe();

            ClassPlus.forName("cn.jdl.batrix.sdk.base.BDomainModel")
                    .addConstructorSafe()
                    .toClassSafe();

            ClassPlus.forName("com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext")
                    .addConstructorSafe()
                    .toClassSafe();

            ClassPlus.forName("com.jdl.sc.ofw.out.domain.model.vo.NonstandardProducts")
                    .addMethodSafe("public java.util.Map getEnrolledServices() { return this.enrolledServices;}")
                    .toClassSafe();
            Class clazz = ClassPlus.forName("com.jd.pfinder.module.r2.core.common.utils.JSONUtil").toClassSafe();
            if (clazz != null) {
                Field field = ReflectionUtils.getField(clazz, "OBJECT_MAPPER");
                Object object = field.get(null);
                if (object != null) {
                    Class[] args = {
                            ClassPlus.forName("com.jd.pfinder.module.r2.core.com.fasterxml.jackson.annotation.PropertyAccessor").toClassSafe(),
                            ClassPlus.forName("com.jd.pfinder.module.r2.core.com.fasterxml.jackson.annotation.JsonAutoDetect$Visibility").toClassSafe()
                    };
                    Method method = ReflectionUtils.getMethod(object.getClass(), "setVisibility", args);
                    args = method.getParameterTypes();
                    Field field1 = ReflectionUtils.getField(args[0], "ALL");
                    Field field2 = ReflectionUtils.getField(args[1], "NONE");
                    method.invoke(object, field1.get(null), field2.get(null));

                    field1 = ReflectionUtils.getField(args[0], "FIELD");
                    field2 = ReflectionUtils.getField(args[1], "ANY");
                    method.invoke(object, field1.get(null), field2.get(null));
                }
            } else {
                log.warn("没有找到CLass:{}", "com.jd.pfinder.module.r2.core.common.utils.JSONUtil");
            }

            log.info("Code plus complete");
        } catch (Exception e) {
            log.error("Code plus error", e);
        }
    }

    /*public static void main(String[] args) {
        String json = "{\"business\":\"cn_jdl_sc-isv.sc_ofc_outbound_sale.fulfillmentGenerate\",\"businessIdentity\":{\"businessUnit\":\"cn_jdl_sc-isv\",\"businessType\":\"sc_ofc_outbound_sale\",\"businessScene\":\"fulfillmentGenerate\"},\"extStrategy\":\"cn_jdl_sc-isv.fulfillmentGenerate\",\"model\":{\"batrixInfo\":{\"businessUnit\":\"cn_jdl_sc-isv\",\"businessType\":\"sc_ofc_outbound_sale\",\"businessScene\":\"fulfillmentGenerate\",\"yId\":\"JDL\"},\"orderNo\":\"ESL00000021839546227\",\"orderType\":\"100\",\"orderBusinessType\":1,\"basicInfo\":{\"preSaleStage\":0,\"agentSales\":false},\"customer\":{\"sellerNo\":\"ECP0000000000523\",\"sellerName\":\"青岛啤酒电子商务有限公司\",\"accountNo\":\"EBU0000000000700\",\"accountName\":\"青岛啤酒电子商务有限公司\",\"customerLevel\":\"1\"},\"cargos\":[{\"name\":\"青岛啤酒白啤11度500*3*4牛卡纸罐啤（20版）\",\"no\":\"EMG4418434561279\",\"level\":\"100\",\"quantity\":{\"value\":2,\"unit\":\"箱\"},\"occupyQuantity\":{\"value\":2,\"unit\":\"箱\"},\"refGoodsType\":\"1\",\"refGoodsNo\":\"R1\",\"isvCargoNo\":\"6901035614221K\",\"uniqueCode\":\"0\",\"length\":280.0,\"width\":205.0,\"height\":175.0,\"volume\":10045.000,\"weight\":6.55,\"marineContraband\":false,\"thirdCategoryName\":\"啤酒\",\"virtualType\":\"0\",\"checkUniSnCode\":\"0\",\"brandName\":\"青岛品牌\",\"firstCategoryNo\":\"12259\",\"firstCategoryName\":\"酒类\",\"secondCategoryNo\":\"14716\",\"secondCategoryName\":\"啤酒\",\"thirdCategoryNo\":\"15602\",\"products\":{\"codeToProductMap\":{}},\"insurePrice\":0,\"sex\":\"1\"},{\"name\":\"24款丰腰白啤杯0.3L\",\"no\":\"EMG4418506248335\",\"level\":\"100\",\"quantity\":{\"value\":1,\"unit\":\"个\"},\"occupyQuantity\":{\"value\":1,\"unit\":\"个\"},\"refGoodsType\":\"1\",\"refGoodsNo\":\"R2\",\"isvCargoNo\":\"24KBPB\",\"uniqueCode\":\"0\",\"length\":220.0,\"width\":85.0,\"height\":82.0,\"volume\":1533.400,\"weight\":0.38,\"marineContraband\":false,\"thirdCategoryName\":\"啤酒\",\"virtualType\":\"0\",\"checkUniSnCode\":\"0\",\"firstCategoryNo\":\"12259\",\"firstCategoryName\":\"酒类\",\"secondCategoryNo\":\"14716\",\"secondCategoryName\":\"啤酒\",\"thirdCategoryNo\":\"15602\",\"products\":{\"codeToProductMap\":{}},\"insurePrice\":0,\"sex\":\"1\"},{\"name\":\"青岛啤酒10度500*9福禧罐啤（电商尊享）\",\"no\":\"EMG4418176781343\",\"level\":\"100\",\"quantity\":{\"value\":1,\"unit\":\"箱\"},\"occupyQuantity\":{\"value\":1,\"unit\":\"箱\"},\"refGoodsType\":\"1\",\"refGoodsNo\":\"R3\",\"isvCargoNo\":\"6901035624398\",\"uniqueCode\":\"0\",\"length\":206.0,\"width\":206.0,\"height\":176.0,\"volume\":7468.736,\"weight\":4.92,\"marineContraband\":false,\"thirdCategoryName\":\"啤酒\",\"virtualType\":\"0\",\"checkUniSnCode\":\"0\",\"brandNo\":\"BD202307200003\",\"brandName\":\"青岛品牌\",\"firstCategoryNo\":\"12259\",\"firstCategoryName\":\"酒类\",\"secondCategoryNo\":\"14716\",\"secondCategoryName\":\"啤酒\",\"thirdCategoryNo\":\"15602\",\"products\":{\"codeToProductMap\":{}},\"insurePrice\":0,\"sex\":\"1\"}],\"goods\":[{\"type\":\"1\",\"no\":\"ESG4418579233912\",\"name\":\"24款丰腰白啤杯0.3L\",\"price\":{\"currencyCode\":\"CNY\"},\"amount\":{\"currencyCode\":\"CNY\"},\"quantity\":{\"value\":1},\"channelGoodsNo\":\"ESG4418579233912\",\"refCargoNo\":\"R2\",\"saleGoodsNo\":\"438\"},{\"type\":\"1\",\"no\":\"ESG4418405016969\",\"name\":\"青岛啤酒10度500*9福禧罐啤（电商尊享）\",\"price\":{\"currencyCode\":\"CNY\"},\"amount\":{\"currencyCode\":\"CNY\"},\"quantity\":{\"value\":1},\"channelGoodsNo\":\"ESG4418405016969\",\"refCargoNo\":\"R3\",\"brandName\":\"青岛品牌\",\"saleGoodsNo\":\"1011003011201001\"},{\"type\":\"1\",\"no\":\"ESG4418522452970\",\"name\":\"青岛啤酒白啤11度500*3*4牛卡纸罐啤（20版）\",\"price\":{\"currencyCode\":\"CNY\"},\"amount\":{\"currencyCode\":\"CNY\"},\"quantity\":{\"value\":2},\"channelGoodsNo\":\"ESG4418522452970\",\"refCargoNo\":\"R1\",\"brandName\":\"青岛品牌\",\"saleGoodsNo\":\"101110301a401005\"}],\"consignee\":{\"address\":{\"provinceName\":\"河北省\",\"cityName\":\"秦皇岛市\",\"countyName\":\"昌黎县\",\"townName\":\"龙家店镇\",\"detailAddress\":\"河北省秦皇岛市昌黎县龙家店镇龙家店村\",\"provinceNameGis\":\"河北\",\"provinceNoGis\":\"5\",\"cityNameGis\":\"秦皇岛市\",\"cityNoGis\":\"248\",\"countyNameGis\":\"昌黎县\",\"countyNoGis\":\"263\",\"townNameGis\":\"龙家店镇\",\"townNoGis\":\"48421\"},\"name\":\"叶**\",\"phone\":\"13000000000\",\"mobile\":\"15781354765-9252\",\"consigneeNameEnc\":\"##8QR5YsuYd0IXpnA74f74NmQ1PL6nP/Fs9r8+vW/J6auPfM7DsfdLsIzsFlaYqpBsgXdUDJXry+jBDBWHnwGFUxQMzrQNBhk3VRHsKwcwLA==*CgYIASAHKAESPgo8S8pppzd4mNxcCX+MNB2c3GAAl1jpFVoKODr3aR83dx+8jYwZKuKU8k3mmMsblefnlw5CjBOqo8tdB9N/GgA=#1##\",\"consigneeMobileEnc\":\"$$nNSeDWZe7KUlkyhhrzaByyj0Huclicg1qcwlAgsUBLcYMWV1D2lprvCJ3qHpPDGhtVEVuJOOb0SjfPruk/jTIjTn89972EoqiODMZmT5daCb*CgYIASAHKAESPgo8wIaXIR0P/jQVRyeRn6P/yq8Fb3Fe1BugPjYYtFZGGPxKvcPPQbI5fwK2XNbt1EQuqWff1DZpQL8W5YhCGgA=$1$$\",\"consigneeAddrEnc\":\"##FDg53Yv0qgybf1MPSSUAH2+dTzO7vjFVAKAMINdiwlDTUOq/Yw+rl46HwMm5y4tXc8A3sT2gE7RL77qeRiC9RSohi9liXg1CXR8YtifpkU9sug==*CgYIASAHKAESPgo86qllfzMQGqDY9g5eGvg5NWdqLLai4qf8+gMSNvC7deIY1oHWHEtXHWRyBpiSsDcyOMgZPsOI84m8kO5CGgA=#1##\",\"originProvinceName\":\"河北省\",\"originCityName\":\"秦皇岛市\",\"originCountyName\":\"昌黎县\",\"originTownName\":\"龙家店镇\",\"virtualNumberExpirationDate\":\"2024-08-16 16:12:54\",\"encryptMode\":\"2\"},\"shipment\":{\"shipperId\":1,\"shipperNo\":\"CYS0000010\",\"shipperType\":1,\"shipperName\":\"京东配送\",\"returnType\":1,\"bdOwnerNo\":\"010K1484486\",\"wayBill\":\"JDAZ13757234095\",\"ignorePromise\":true,\"contactlessReceiveType\":0,\"halfReceive\":1,\"deliveryType\":1,\"billingType\":1,\"presortInfo\":{},\"transportationType\":1,\"transportModeEnum\":\"0\",\"logisticsOriginalPackage\":0,\"deductibleCompensation\":\"0\",\"customPresortList\":[],\"cityDeliveryType\":false,\"expectCityDeliveryType\":false,\"tmsDeliveryType\":\"2\",\"checkGoodsTypeEnum\":\"0\",\"needExternalWaybillOnly\":false,\"firstWaybillIssue\":false,\"printExtendInfo\":{\"sendMode\":\"null\",\"receiveMode\":\"null\",\"thirdPayment\":\"null\"},\"heavyGoodsUpstairs\":false,\"hidePrivacyType\":false,\"signBillTypeEnum\":\"0\",\"CNTemplate\":false,\"packageProduceMode\":\"0\",\"deliveryPerformanceChannel\":\"2\",\"express\":\"0\",\"activationCardService\":\"0\",\"transportPresetNo\":false,\"goodsIsolateType\":\"0\",\"deliveryIntoWarehouse\":false,\"luxurySecurity\":false,\"deliveryForcePhoto\":false,\"forceContact\":false,\"deliveryService\":\"0\",\"deliveryInBatches\":false,\"nonSelfSignCall\":\"1\",\"consignmentControlRules\":false},\"warehouse\":{\"warehouseNo\":\"110016715\",\"warehouseId\":16715,\"warehouseName\":\"青岛青岛啤酒KA仓2号库\",\"warehouseType\":1,\"stockControl\":true,\"originalWarehouseNo\":\"110016715\",\"erpWarehouseNo\":\"151\",\"distributionNo\":\"610\"},\"finance\":{\"paymentType\":0,\"settlementType\":1},\"refOrderInfo\":{\"waybillNo\":[null]},\"channel\":{\"pin\":\"jd_649a117abce0a\",\"channelSource\":\"ISV\",\"channelShopNo\":\"100\",\"channelOrderCreateTime\":1721201732000,\"channelOrderNo\":\"6932293700385707397\",\"channelNo\":\"299\",\"channelName\":\"抖音\",\"shopNo\":\"ESP0020008179737\",\"shopName\":\"青岛啤酒抖音官方旗舰店\",\"customerOrderNo\":\"XS2024071720381196001\",\"bdSellerNo\":\"0030001\",\"isvSource\":\"ISV0020000000079\",\"systemCaller\":\"JOS\"},\"products\":{\"codeToProductMap\":{\"sc-m-0001\":{\"no\":\"sc-m-0001\",\"name\":\"中小件商务仓\",\"type\":1,\"businessLine\":1,\"attributes\":{},\"filled\":1,\"productLine\":\"sc\"},\"ed-a-0032\":{\"no\":\"ed-a-0032\",\"name\":\"微笑面单\",\"type\":2,\"businessLine\":2,\"attributes\":{\"hiddenContent\":\"noRequirement\"},\"filled\":1,\"productLine\":\"ed\"},\"ed-m-0001\":{\"no\":\"ed-m-0001\",\"name\":\"京东标快\",\"type\":1,\"businessLine\":2,\"filled\":1,\"productLine\":\"ed\"},\"sc\":{\"no\":\"sc\",\"name\":\"供应链\",\"type\":10,\"filled\":1},\"SC-L2-0001\":{\"no\":\"SC-L2-0001\",\"name\":\"商务仓\",\"type\":20,\"filled\":1},\"SC-L3-0002\":{\"no\":\"SC-L3-0002\",\"name\":\"中小件商务仓+京东标快\",\"type\":30,\"filled\":1}}},\"nonstandardProducts\":{\"enrolledServices\":{\"expectOutboundTime\":\"2024-07-17 16:12:53\",\"shopping_Template\":\"000\",\"specialSafeguard\":\"1\",\"acrossDc\":\"1\",\"promiseTime211\":\"000\"}},\"solution\":{\"no\":\"sc-s-0013\",\"name\":\"中小件商务仓配\",\"attributes\":{\"deliveryService\":\"0\",\"unpackingFlag\":\"0\",\"heavyGoodsUpstairsFlag\":\"0\",\"insured\":\"false\",\"hidePrivacyType\":\"0\",\"nonSelfSignCall\":\"1\",\"firstWaybillIssue\":\"0\",\"specialSignRequirements\":\"{\\\"activationService\\\":\\\"0\\\"}\",\"packageProduceMode\":\"0\"}},\"orderMark\":\"00000000000000000000020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\",\"createTime\":1721203973000,\"fulfillment\":{\"occupyResult\":true,\"packDetailsCollect\":2,\"productionByPack\":false,\"partialFulfillment\":false,\"partialPayment\":false,\"cargoFulfillmentWay\":true,\"professionType\":\"00\",\"skipMobileDecrypt\":false,\"orderUrgencyType\":\"0\",\"trustJDMetrics\":false,\"customField\":\"jfs/kveclp/0/1721203973901/6850/23e5fb35a42787fb50a81be194618088\",\"tmDeliveryByJgFlag\":false,\"clpsCainiaoElecFlag\":false,\"tmOrderFlag\":\"0\",\"packageSignFlag\":false,\"invokedCainiaoPlatform\":false,\"collectUniqueCodeFlag\":false,\"quarantineCert\":false,\"crossDockType\":\"0\",\"printInfoFromSeller\":true,\"occupyResultType\":3},\"invoice\":{\"invoiceDetailList\":[{}],\"invoiceSource\":2,\"invoiceBiz\":\"0\"},\"yn\":0,\"orderSign\":{\"controlOrder\":\"0\"},\"largePackage\":false}}";
        com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext context = JsonUtil.readValueSafe(json, com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext.class);
        com.jdl.sc.ofw.out.domain.model.vo.Fulfillment.assignFieldProcessMap("customField", new com.jdl.sc.ofw.out.domain.trigger.JimKvLoadProcess());
        com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel model = context.getModel();
        String a = com.jd.pfinder.module.r2.core.common.utils.JSONUtil.toJSONString(model);
        com.jd.pfinder.module.r2.core.common.utils.JSONUtil.toJSONString(model);
        com.jd.pfinder.module.r2.core.common.codec.hessian.HessianCodec codec = new com.jd.pfinder.module.r2.core.common.codec.hessian.HessianCodec();
        byte[] bytes = codec.encode(context);
        codec.decode(bytes);
    }*/
}
