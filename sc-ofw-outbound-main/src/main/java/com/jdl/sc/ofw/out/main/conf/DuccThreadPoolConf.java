package com.jdl.sc.ofw.out.main.conf;

import lombok.Data;

/**
 *  ThreadPoolExecutor  DUCC 动态配置项
 */
@Data
public class DuccThreadPoolConf {

    /**
     * 核心线程数
     */
    private Integer corePoolSize;

    /**
     * 最大线程数
     */
    private Integer maxPoolSize;

    /**
     * 线程队列大小
     */
    private Integer queueCapacity;

    /**
     * 等待所有任务结束后再关闭线程池
     */
    private Boolean waitForTasksToCompleteOnShutdown;

    /**
     * 等待所有任务结束最长等待时间 单位秒
     */
    private Integer awaitTerminationSeconds;
}
