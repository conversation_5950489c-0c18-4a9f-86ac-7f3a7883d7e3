package com.jdl.sc.ofw.out.main;

import com.jd.jsf.gd.util.JSFContext;
import com.jd.security.tomcat.JDJspServlet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.PreDestroy;

@ImportResource(value = {
        "classpath:spring/spring-main.xml"
})
//@MapperScan(basePackages = {"com.jdl.sc.ofw.out.horiz.infra.repository.dao", "com.jdl.sc.ofw.out.horiz.infra.doublewrite.dao"})
@PropertySources(value = {
//        @PropertySource(value = {"classpath:important.properties"}, encoding = "utf-8", factory = JDSecurityPropertySourceFactory.class),
        @PropertySource({
                "classpath:conf/app.properties",
                "classpath:conf/jsf.properties",
                "classpath:conf/mq.properties",
                "classpath:conf/hbase.properties",
                "classpath:conf/ducc.properties",
                "classpath:conf/batrix.properties",
                "classpath:conf/promise-alias.properties",
                "classpath:conf/oss.properties",
                "classpath:basic.properties"
        })
})
@ComponentScan(basePackages = {"com.jdl.sc.ofw.out", "com.jdl.sc.order"})
@EnableScheduling
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@Slf4j
public class ProjectWebApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(ProjectWebApplication.class, args);
        log.info("<<<================== springboot started!!! ==================>>>");
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        log.info("<<<================== tomcat started!!! ==================>>>");
        return super.configure(application);
    }

    /**
     * 使用外部tomcat启动的情况
     * （Tomcat安全加固 - 防御jsp木马：https://cf.jd.com/pages/viewpage.action?pageId=107475162）
     *
     * @return
     */
    @Bean
    @Conditional(OuterTomcatCondition.class)
    public ServletRegistrationBean jdJspServletRegistration() {
        JDJspServlet servlet = new JDJspServlet();
        servlet.setSpringBoot(true);
        ServletRegistrationBean registration = new ServletRegistrationBean();
        registration.setServlet(servlet);
        // jsp功能开关 开启jsp功能 请将false修改为true（配置为true安全防护将失效，不建议修改）
        registration.addInitParameter("enableJsp", "false");
        registration.addInitParameter("fork", "false");
        registration.addInitParameter("xpoweredBy", "false");
        registration.addInitParameter("springboot", "true");
        registration.setLoadOnStartup(3);
        registration.addUrlMappings("*.jsp");
        registration.addUrlMappings("*.jspx");
        registration.setName("jsp");
        return registration;
    }

    @PreDestroy
    public void destroy() {
        JSFContext.destroy();
    }

    public static class OuterTomcatCondition implements Condition {
        @Override
        public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
            return !getClass().getClassLoader().equals(org.apache.catalina.startup.Tomcat.class.getClassLoader());
        }
    }
}

