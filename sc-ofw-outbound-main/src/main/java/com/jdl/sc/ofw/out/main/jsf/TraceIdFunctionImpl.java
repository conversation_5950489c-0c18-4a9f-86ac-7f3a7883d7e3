package com.jdl.sc.ofw.out.main.jsf;


import com.jd.jsf.gd.msg.RequestMessage;

import java.util.function.Function;

public class TraceIdFunctionImpl implements Function<RequestMessage, String> {
    @Override
    public String apply(RequestMessage requestMessage) {
        String traceId = null;
        if (requestMessage.getInvocationBody().getArgs() == null) {
            return traceId;
        }
        for (Object arg : requestMessage.getInvocationBody().getArgs()) {
            if (arg instanceof cn.jdl.batrix.spec.RequestProfile) {
                traceId = ((cn.jdl.batrix.spec.RequestProfile) arg).getTraceId();
                break;
            }
        }
        return traceId;
    }
}
