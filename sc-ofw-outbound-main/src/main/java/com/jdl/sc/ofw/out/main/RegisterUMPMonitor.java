/**
 * 负责初始化ump 系统存活监控
 */
package com.jdl.sc.ofw.out.main;

import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;


@Slf4j
public class RegisterUMPMonitor implements InitializingBean {
    private String jvmKey;

    private String heartBeatKey;


    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            if (StringUtils.isNotEmpty(jvmKey)) {
                Profiler.registerJVMInfo(jvmKey);
            }
            log.info("initialized ump registerJVMInfo .[jvmKey={}]", jvmKey);
        } catch (Exception e) {
            log.error("initialized ump registerJVMInfo failed", e);
        }
        try {
            if (StringUtils.isNotEmpty(heartBeatKey)) {
                Profiler.InitHeartBeats(heartBeatKey);
            }
            log.info("initialized ump InitHeartBeats.[heartBeatKey={}]", heartBeatKey);
        } catch (Exception e) {
            log.error("initialized ump InitHeartBeats failed", e);
        }
    }

    public void setJvmKey(String jvmKey) {
        this.jvmKey = jvmKey;
    }

    public void setHeartBeatKey(String heartBeatKey) {
        this.heartBeatKey = heartBeatKey;
    }
}
