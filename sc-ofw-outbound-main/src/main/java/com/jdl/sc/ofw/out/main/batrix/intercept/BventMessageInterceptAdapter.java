package com.jdl.sc.ofw.out.main.batrix.intercept;

import cn.jdl.batrix.core.base.BventMessageIntercept;
import cn.jdl.batrix.core.flow.domain.BOutputMessage;
import cn.jdl.batrix.core.utils.BObjectUtils;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Options;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdl.sc.core.cache.jimdb.JimClient;
import com.jdl.sc.core.jsf.JsfConstants;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.core.log.collect.AuditLogEntity;
import com.jdl.sc.core.log.collect.AuditLogger;
import com.jdl.sc.core.log.collect.AuditType;
import com.jdl.sc.core.sync.ReentrantLock;
import com.jdl.sc.core.utils.Arrays;
import com.jdl.sc.core.utils.CollectionUtils;
import com.jdl.sc.core.utils.DateUtil;
import com.jdl.sc.core.utils.NetUtil;
import com.jdl.sc.ofw.out.common.exception.AcquireLockException;
import com.jdl.sc.ofw.out.common.exception.DomainServiceException;
import com.jdl.sc.ofw.out.common.exception.IdempotentException;
import com.jdl.sc.ofw.out.domain.ability.OrderWorkflowLockAbility;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.repository.OutboundModelRepository;
import com.jdl.sc.ofw.out.common.batrix.BatrixContext;
import com.jdl.sc.ofw.out.common.batrix.BatrixInfo;
import com.jdl.sc.ofw.out.horiz.infra.batrix.InputMessageParser;
import com.jdl.sc.ofw.out.horiz.infra.batrix.OutputMessageParser;
import com.jdl.sc.ofw.out.horiz.infra.trace.TraceIdManager;
import com.jdl.sc.ofw.out.horiz.infra.util.SwitchKeyUtils;
import com.jdl.sc.ofw.outbound.spec.enums.errorspec.IdempotentEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * FileName: BventMessageInterceptAdapter
 *
 * @author: huafeng.qu
 * @date: 2023/1/31 8:42 下午
 * Description: Bratix异步消息拦截器,拦截业务处理实现公共功能如并发锁等(注意同步流程无法通过此处拦截)
 */
@Component
@Slf4j
public class BventMessageInterceptAdapter implements BventMessageIntercept {

    public BventMessageInterceptAdapter() {
        AviatorEvaluator.setOption(Options.NIL_WHEN_PROPERTY_NOT_FOUND, true);
    }

    /**
     * 是否新增MDC信息
     */
    private static final String CONTEXT_ADD_MDC = "mdc";

    /**
     * MDC来源batrix
     */
    private static final String ADD_MDC_TRUE = "true";


    /**
     * 分布式锁服务
     */
    @Resource
    private OrderWorkflowLockAbility lockAbility;

    /**
     * 业务对象持久化服务
     */
    @Resource
    private OutboundModelRepository outboundModelRepository;

    @Resource
    private JimClient jimClient;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Value("${jone.application.name}")
    private String appName;

    /**
     * BATRIX中记录当前异步事件的key
     */
    private static final String BATRIX_HEADER_ACTION_KEY = "eventAction";


    @Override
    public void beforeOnMessage(InputMessage inputMessage) throws Exception {
        String traceId = MDC.get(JsfConstants.LOG4J_MDC_TRACE_ID);
        if (StringUtils.isBlank(traceId)) {
            TraceIdManager.batrixTraceIdInit(inputMessage.getHeader("traceId", String.class));
            BatrixContext.putString(CONTEXT_ADD_MDC, ADD_MDC_TRUE);
            MDC.put(JsfConstants.LOG4J_MDC_FROM_APP, "batrix");
        }
        log.info("async 前置拦截器，batrix_event:{},", inputMessage.getHeader(BATRIX_HEADER_ACTION_KEY));
        // 并发锁
        ReentrantLock lock =  this.concurrentLock(inputMessage);
        // 幂等验证
//        this.idempotentCheck(inputMessage);
        // 从数据库中读最新的model。[履约过程中可能被其他服务修改数据]
        this.updateModel(inputMessage);
        if (InputMessageParser.isFirst(inputMessage) || log.isDebugEnabled()) {
            log.info("batrix_event:{},消息:{}", inputMessage.getHeader(BATRIX_HEADER_ACTION_KEY),  JsonUtil.toJsonSafe(inputMessage));
        }
        // 创建上下文对象
        this.createContext(inputMessage);
        BatrixContext.saveLock(lock);
    }

    /**
     * 创建上下文对象
     * @param inputMessage
     */
    private void createContext(InputMessage inputMessage) {
        try {
            BatrixInfo batrixInfo = InputMessageParser.getBatrixInfoFromMessage(inputMessage);
            BatrixContext.saveBatrixInfo(batrixInfo);
        } catch (Exception e) {
            log.warn("生成上下文对象失败,inputMessage:{}", JsonUtil.toJsonSafe(inputMessage), e);
        }
    }


    @Override
    public boolean exceptionOnMessage(InputMessage inputMessage, BOutputMessage outputMessage) throws Exception {
        if(outputMessage.isException()) {
            // 幂等中断直接返回返回。不重试
            if(outputMessage.getBException() instanceof IdempotentException) {
                return false;
            }
        }
        return BventMessageIntercept.super.exceptionOnMessage(inputMessage, outputMessage);
    }

    @Override
    public void afterOnMessage(InputMessage inputMessage, BOutputMessage bOutputMessage) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("async 后置拦截器，batrix_event:{},,是否返回异常:{},输出消息{}", inputMessage.getHeader(BATRIX_HEADER_ACTION_KEY),
                    OutputMessageParser.isReturnError(bOutputMessage),
                    bOutputMessage != null && bOutputMessage.getBException() != null ? bOutputMessage.getBException().getMessage() : JsonUtil.toJsonSafe(bOutputMessage));
        } else {
            log.info("async 后置拦截器，batrix_event:{},,是否返回异常:{}", inputMessage.getHeader(BATRIX_HEADER_ACTION_KEY),  OutputMessageParser.isReturnError(bOutputMessage));
        }
        try {
            if (OutputMessageParser.isNeedPersist(bOutputMessage)) {
                log.info("此状态需要持久化");
                this.persistenceModel(bOutputMessage);
                this.writeAuditLog();
            }
            if(!this.isNeedRepeat(bOutputMessage)) {
                this.idempotentMark(inputMessage);
            }
            this.clearModelInfo(inputMessage);
            this.unConcurrentLock(inputMessage);
        } finally {
        }
    }


    /**
     * 清除model信息，只保留订单号，大报文性能问题
     * ---目前消息中只有运单号有作用。异步消息时会跟据订单号重新从中间件中获取最新的bena对象。
     * @param inputMessage
     */
    private void clearModelInfo(InputMessage inputMessage) {
        OrderFulfillmentContext context = InputMessageParser.getContextFromMessage(inputMessage);
        String orderNo = context.getModel().getOrderNo();
        OrderFulfillmentModel clearModel = OrderFulfillmentModel.builder().orderNo(orderNo).build();
        context.assignModel(clearModel);
    }


    /**
     * model持久化完成后，写入model变更审记日志
     */
    private void writeAuditLog() {

        if (CollectionUtils.isEmpty(BatrixContext.getModelChangeLog())) {
            return;
        }
        AuditLogger.info(AuditLogEntity.builder()
                .tid(MDC.get("traceId"))
                .localAppName(appName)
                .remoteIp("")
                .localIp(NetUtil.getLocalIp())
                .orderNo(BatrixContext.getOrder())
                .businessUnit(BatrixContext.getBusinessUnit())
                .interfaceName(BatrixContext.getBatrixInfo().getEventAction())
                .type(AuditType.BATRIX_UPDATE_EVENT)
                .requestContent("batrix-async-sale-out")
                .responseContent(JsonUtil.toJsonSafe(BatrixContext.getModelChangeLog()))
                .operateTime(DateUtil.dateToString(new Date()))
                .build());
    }





    /**
     * 判断返回结果是否被中断。被中断的异步节点需要重复执行，不打幂等标记(暂停不返回异常，但返回block标记)
     *
     * @param bOutputMessage
     * @return
     */
    public boolean isNeedRepeat(BOutputMessage bOutputMessage) {
        // 业务执行时抛出Exception会向上抛出，拦截器接收的BOutputMessage为空
        if (bOutputMessage == null) {
            return true;
        }
        return bOutputMessage.isBlock();
    }



    /**
     * 加并发锁
     *
     * @param inputMessage
     */
    private ReentrantLock concurrentLock(InputMessage  inputMessage) {
        String orderNo = InputMessageParser.getOrderNoFromMessage(inputMessage);
        ReentrantLock lock = lockAbility.acquireLock(orderNo, "BventMessageInterceptAdapter.concurrentLock");
        return lock;
    }

    /**
     * 释放并发锁
     */
    private void unConcurrentLock(InputMessage inputMessage) {
        try {
            if(BatrixContext.getLock() == null) {
                log.warn("获取锁为空");
            } else {
                lockAbility.unlock(BatrixContext.getLock());
            }
        } catch (Exception e) {
            log.error("并发锁解锁异常,inputMessage:{}", JsonUtil.toJsonSafe(inputMessage), e);
            return;
        }
    }


    /**
     * 业务对象持久化
     *
     * @param bOutputMessage
     */
    private void persistenceModel(BOutputMessage bOutputMessage) throws Exception {
        OrderFulfillmentContext context = bOutputMessage.getBody(OrderFulfillmentContext.class);
        if (context == null) {
            return;
        }
        OrderFulfillmentModel model = context.getModel();
        if (model == null) {
            return;
        }
        outboundModelRepository.persist(model);
    }


    /**
     * 验证当前节点幂等性
     * @param inputMessage
     * @return
     */
    private boolean idempotentCheck(InputMessage inputMessage) {
        BatrixInfo batrixInfo = InputMessageParser.getBatrixInfoFromMessage(inputMessage);
        if(StringUtils.isBlank(batrixInfo.getTraceId()) || StringUtils.isBlank(batrixInfo.getEventAction())) {
            log.warn("幂等验证时从inputMessage中获取traceId和eventAction失败，跳过幂等检查,inputMessage:{}", JsonUtil.toJsonSafe(inputMessage));
            return true;
        }
        String idempotentKey = batrixInfo.getEventAction() + batrixInfo.getTraceId();
        String idempotentValue = jimClient.get(idempotentKey);
        if(StringUtils.isNotBlank(idempotentValue)) {
            log.warn("batrix幂等校验生效，此订单被重复执行，已拦截，traceId-eventAction:{}", idempotentKey);
            throw new IdempotentException(IdempotentEnum.FLOW_NODE_SYSTEM_EXCEPTION);
        }
        return true;
    }

    private void idempotentMark(InputMessage inputMessage) {
        try {
            BatrixInfo batrixInfo = InputMessageParser.getBatrixInfoFromMessage(inputMessage);
            if(StringUtils.isBlank(batrixInfo.getTraceId()) || StringUtils.isBlank(batrixInfo.getEventAction())) {
                log.warn("幂等验证时从inputMessage中获取traceId和eventAction失败，跳过幂等检查,inputMessage:{}", JsonUtil.toJsonSafe(inputMessage));
                return ;
            }
            String idempotentKey = batrixInfo.getEventAction() + batrixInfo.getTraceId();
            jimClient.set(idempotentKey, "1", 600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("幂等打票发生异常，inputMessage:{}", JsonUtil.toJsonSafe(inputMessage), e);
        }
    }

    /**
     * 从持久化中间件读取model对象
     * @param inputMessage
     */
    private void updateModel(InputMessage inputMessage) {
        OrderFulfillmentContext context = InputMessageParser.getContextFromMessage(inputMessage);
        OrderFulfillmentModel orderFulfillmentModel = outboundModelRepository.getModelByOrderNo(context.getModel().getOrderNo());
        context.assignModel(orderFulfillmentModel);
    }

    /**
     * 生成异步监听事件UMPKEY  prod.ofw-outboudn.listener.sc_basic_event_isv
     * @param topicCode
     * @param actionCode
     * @return
     */
    @Override
    public List<CallerInfo> callerInfoRegister(String topicCode, String actionCode) {
        String umpKey = activeProfile + "." + appName + "." + "listener" + "." + actionCode;
        CallerInfo callerInfo = Profiler.registerInfo(umpKey);
        return Arrays.asList(callerInfo);
    }

    /**
     * 自义定报警异常。并发锁异常不进行报警
     * @param e
     * @param umpKeys
     */
    @Override
    public void callerInfoError(Throwable e, List<CallerInfo> umpKeys) {
        if(e instanceof AcquireLockException) {
            return ;
        }
        BventMessageIntercept.super.callerInfoError(e, umpKeys);
    }

    @Override
    public void callerInfoEnd(List<CallerInfo> umpKeys) {
        try {
            BventMessageIntercept.super.callerInfoEnd(umpKeys);
            if (!log.isInfoEnabled()) {
                return;
            }
            if (!BObjectUtils.isEmpty(umpKeys)) {
                for (CallerInfo umpKey : umpKeys) {
                    long elapsedTime = umpKey.getElapsedTime();
                    long levelTitle = 0;
                    long maxValue = elapsedTime;
                    if (elapsedTime != 0) {
                        levelTitle = (int) Math.abs(Math.pow(10, String.valueOf(elapsedTime).length() - 1));
                        maxValue = elapsedTime / levelTitle;
                    }
                    if (umpKey == null) {
                        continue;
                    }
                    log.info("batrix listener key:{},cost:{}ms，levelTitle:{},maxValue:{}", umpKey.getKey(), umpKey.getElapsedTime(), levelTitle, maxValue);
                }
            }
        } finally {
            this.clearContext();
        }

    }


    /**
     * 清空上下文信息与traceId
     */
    private void clearContext() {
        String added = BatrixContext.getString(CONTEXT_ADD_MDC);
        if (ADD_MDC_TRUE.equals(added)) {
            MDC.clear();
        }
        BatrixContext.clear();
        TraceIdManager.clearTraceId();
    }


}
