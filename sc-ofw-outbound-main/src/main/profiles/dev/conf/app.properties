server.port=80

retialorderfinancial.appid=ztg
retialorderfinancial.apptoken=123456

#IOrderWareRelationJsfService\u670D\u52A1\u5165\u53C2\u4E2DclientInfo\u7684\u7CFB\u7EDF\u540D\u79F0
ofc.clientInfo.systemName=monitor
#IOrderWareRelationJsfService\u670D\u52A1\u5165\u53C2\u4E2DclientInfo\u7684token
ofc.clientInfo.token=monitor

jfs.url=node-zk-0gtovkftnv-az1-0.jvessel-open-hb.jdcloud.com:2181,node-zk-0gtovkftnv-az1-1.jvessel-open-hb.jdcloud.com:2181,node-zk-0gtovkftnv-az1-2.jvessel-open-hb.jdcloud.com:2181
dtc.jfs.url=node-zk-0gtovkftnv-az1-0.jvessel-open-hb.jdcloud.com:2181,node-zk-0gtovkftnv-az1-1.jvessel-open-hb.jdcloud.com:2181,node-zk-0gtovkftnv-az1-2.jvessel-open-hb.jdcloud.com:2181
jimdb.jimUrl=redis://:<EMAIL>

#\u00E6\u00AD\u00A3\u00E5\u00BC\u008F\u00EF\u00BC\u009Ahttp://rvr.staig.jd.com/services/interfaceOrderCancel
wms.webservice.url=http://receiver.staig.jd.net/services/interfaceOrderCancel

cache.client.prefix=sc_ofw:
cacheSupport.default.timeout=5
cacheSupport.default.timeoutUnit=MINUTES

hold.order.retry.interval=5

pop-trans-pipe-filter-key=pop_trans_pipe
isv-trans-pipe-filter-key=isv_trans_pipe
appraisal-trans-pipe-filter-key=appraisal_trans_pipe
appraisal-sale-trans-pipe-filter-key=appraisal_sale_trans_pipe
dy-waybill-trans-pipe-filter-key=dy_waybill_trans_pipe

#dtc JimKV
jimKv.dtc.jimUrl=jimdb://2766733494887625361/9010003
jimKv.dtc.serviceEndpoint=http://jimkv-10002-master-hc04-lb.jimkv.svc.hc04.n.jd.local:443
jimKv.dtc.readTimeout=500



#OFC JimKV\u96C6\u7FA4\u914D\u7F6E
jimKv.ofc.jimUrl=jimdb://2766733494887625361/9010003
jimKv.ofc.serviceEndpoint=http://jimkv-10002-master-hc04-lb.jimkv.svc.hc04.n.jd.local:443/
jimKv.ofc.readTimeout=500