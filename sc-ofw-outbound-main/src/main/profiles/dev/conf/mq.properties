#jmq config
jmq.address=jmq-testcluster.jd.local:50088
jmq.user=scOfwOutbound
jmq.password=43E632FF
jmq.app=scOfwOutbound
jmq.epoll=false

jmq.uniqueEnv.address=jmq-testcluster.jd.local:50088
jmq.uniqueEnv.user=scOfwOutbound
jmq.uniqueEnv.password=43E632FF
jmq.uniqueEnv.app=scOfwOutbound
jmq.uniqueEnv.epoll=false

jmqM.app=ofwOutbound
jmqM.user=ofwOutbound
jmqM.password=edda45d96dc54cc0b18fdeb8a3f6dda1
jmqM.address=test-nameserver.jmq.jd.local:50088
jmqM.epoll=false

jmq.moving.transport=jmq4.transport

jmq4.app=ofwOutbound
jmq4.user=ofwOutbound
jmq4.password=edda45d96dc54cc0b18fdeb8a3f6dda1
jmq4.address=test-nameserver.jmq.jd.local:50088

mq.producer.status.callback.topic=ofw_status_callback_test
mq.producer.exception.topic=ofw_exception_test
mq.producer.cancel.result.topic=ofw_cancel_result_test

mq.producer.tms.topic=placeholder_topic
mq.producer.tms.tmsDispatch.topic=eclp_dispatch_order_cancel
mq.producer.wms.topic=placeholder_topic

mq.producer.zhongyou.callback.topic=zhongyou_callback_test
jmq4.producer.sync.ofw.es.topic=receive_order_sync_ofw_es
jmq4.producer.model.persist.topic=ofw_model_persist_test
jmq2.appointment.result.notification.topic=APPOINTMENT_RESULT_NOTIFICATION
jmq4.producer.wait.book.dispatch.topic=ofc_order_hold_wait_book_dispatch

jmq4.producer.mutongInnerOrder.topic=mt_sc_ofc_inner_order_receive_test
jmq4.producer.mutongCancelOrder.topic=mt_sc_ofc_inner_order_cancel_test
mq.consumer.fresh.fulfillment.complete.topic=fresh_fulfillment_complete_event