jsf.index=i.jsf.jd.com
# alias
jsf.vipShop.wayBill.alias=eclp-test_edi_getTransportNos_vip:1.0.0
jsf.vipShopEnc.wayBill.alias=easymock_ofc_edi_getPrintTemplate_vip
jsf.vipShopEnc.queryReceiveInfo.alias=easymock_ofc_edi_decryptReceiverInfo_vip
jsf.port=21600

jsf.cp.alias=eclp-test:1.0.0
jsf.soPddWaybillApi.alias=eclp-test:1.0.0
jsf.goods.alias=eclp-test:1.0.0
jsf.goodsGategory.alias=eclp-test:1.0.0
jsf.ProductCenterService.alias=open-lpc-test
jsf.ProductCenterService.new.alias=open-lpc-test
jsf.waybillIntegrateReceiveApi.alias=ldop-delivery-test
jsf.ldop.waybillOperatorApi.alias=ldop-center-test
jsf.ldop.waybillUpdate.alias=ldop-center-test
jsf.dtc.modify.alias=STAIG_SYNC_SENDER_test_gn
jsf.eclp2.rtw.alias=eclp2_rtw
jsf.ordermarking.alias=fce-dos-ofc-auto
jsf.bd.WaybillReverseApi=ldop-center-test
jsf.DataReceiver.alias=STAIG_RVR

jsf.outbound.resume.alias=#{'${app.grayscale:false}'?'ofw-outbound-resume-test_backup':'ofw-outbound-resume-test'}
jsf.outbound.route.alias=#{'${app.grayscale:false}'?'ofw-outbound-route-test_backup':'ofw-outbound-route-test'}
# token
token.shipService=lsjCBQmWTCfmSzF128
token.warehouseService=lsjCBQmWTCfmSzF94
token.senderAddressBookService=tm9007ebeb5e41dbbb26c8048359fd24
token.strategy.ovas=testOvas2
token.deptService=lsjCBQmWTCfmSzF317
token.dictionaryService=lsjCBQmWTCfmSzF202
token.logisticsServiceService=lsjCBQmWTCfmSzF359
token.airStrategyMainService=lsjCBQmWTCfmSzF444
token.airWarehouseItemService=lsjCBQmWTCfmSzF109
token.airLineItemService=lsjCBQmWTCfmSzF291
token.individuationPrintConsumablesService=lsjCBQmWTCfmSzF105
token.shopService=lsjCBQmWTCfmSzF377
token.addressService=lsjCBQmWTCfmSzF325
# gis\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u00BD\u04FF\uFFFD
token.gisV3=exmapjd416
jsf.orderfinancial.alias=ztcs

#orderMiddleware
jsf.ioms.orderMiddleware.alias=easymock_ofw
jsf.ioms.orderMiddleware.token=h6vlkfem45oh8y3zt4lf9mtzr5uw0e1u

jsf.ofc.orderWareRelation.alias=ytest_mock

jsf.cprt.alias=cprt-test

carrier.alias=easymock_ofw

jsf.BaseCrossPackageTagWS.alias=base-cross-package-test
jsf.BaseCrossPackageTagWS.token=123456

jsf.oms.alias=jdl-oms-test:0.0.1
jsf.oms.GetOrderService.token=b9fc8f72149c48439c84f6bca5881696
jsf.oms.QueryOrderService.alias=jdl-oms-test:0.0.1
jsf.oms.QueryOrderService.token=B80bBdWmRE
jsf.orderShips.alias=3pl_trace_test
jsf.orderShips.token=123456
jsf.orderShips.validateStr=123456
jsf.customPresort.alias=lbs_fence_test

jsf.weblib.alias=test
jsf.weblib.token=D66F1CE82F8524E8ADAEEBD6358334B1

gis.presort.appkey=FAKE_KEY

jsf.oms.extension.alias=test
jsf.ovas.alias=eclp-test:1.0.0
jsf.ovas.token=testOvas2

jsf.alphaWaybill.alias=ldop-alpha-waybill-test
jsf.alphaWaybill.waybillCheck.timeout=5000
jsf.alphaGetWaybill.alias=ldop-alpha-lp-test
jsf.alphaWaybill.check.timeout=1000


jsf.gisV3.alias=addresstranslation:v1
jsf.gisV3.token=exmapjd416

jsf.timeout.QueryOrderService=5000
jsf.timeout.GetOrderService=5000
jsf.BatchGetOrderService.timeout=5000
jsf.timeout.ReacceptOutboundOrderService=5000
jsf.timeout.ProductCenterService=5000
jsf.timeout.RtwService=5000
jsf.timeout.OrderMarkingService=5000
jsf.timeout.PromiseKaService=5000
jsf.timeout.AddressService=5000
jsf.timeout.DeptOvasService=5000
jsf.timeout.DeptService=5000
jsf.timeout.DictionaryService=5000
jsf.timeout.LogisticsServiceService=5000
jsf.timeout.AirStrategyMainService=5000
jsf.timeout.AirWarehouseItemService=5000
jsf.timeout.AirLineItemService=5000
jsf.timeout.IndividuationPrintConsumablesService=5000
jsf.timeout.GoodsBatchQueryService=5000
jsf.timeout.ShipService=5000
jsf.timeout.WarehouseService=5000
jsf.timeout.SenderAddressBookService=5000
jsf.timeout.ShopService=5000
jsf.timeout.DataReceiver=5000
jsf.timeout.WaybillIntegrateReceiveApi=5000
jsf.timeout.WarehouseConfigService=5000
jsf.timeout.OrderQueryResource=5000
jsf.timeout.GoodsBarcodeService=5000
jsf.timeout.GoodsMaterialRelService=5000
jsf.timeout.OrderMiddlewareJSFService=5000
jsf.timeout.IOrderWareRelationJsfService=5000
jsf.timeout.PrintMetadataService=5000
jsf.timeout.StrategyRecomService=5000
jsf.timeout.EclpCarrierService=5000
jsf.timeout.BaseCrossPackageTagWS=5000
jsf.timeout.ProductBaseService=5000
jsf.timeout.WaybillOperatorApi=5000
jsf.timeout.WaybillUpdateApi=5000
jsf.timeout.DataSyncSenderService=5000
jsf.timeout.CustomPresortService=5000
jsf.timeout.OrderShipsServiceJsf=5000
jsf.timeout.WeblabService=5000
jsf.timeout.DeliveryProductService=5000
jsf.timeout.InsurePriceInfoService=5000
jsf.timeout.OvasCenterService=5000
jsf.timeout.SoPriority=3000
jsf.timeout.QiMenQuery=30000
jsf.timeout.JdGoodsGategoryService=500
jsf.timeout.RuleService=2000

jsf.sellerTemplateRelService.alias=eclp-test:1.0.0
jsf.sellerTemplateRelService.token=lsjCBQmWTCfmSzF421


jsf.so.priority.alias=eclp-test:1.0.0
jsf.waybillQueryApi.alias=WAYBILL_JSF_TEST
jsf.intserv.alias=easymock_clps_test2

jsf.token.customerService=lsjCBQmWTCfmSzF320
jsf.eds.order.cancel.alias=dev-group
jsf.eds.order.cancel.token=sdfsdfsdf_iocjk#98%
eds.appKey=77022957c75345c097137cf775d02b66

jsf.jgwaybill.alias=zhongyouex-basic-test
jsf.ediProcessor.alias=eclp_edi_decryptAddress_mj:1.0.0

jsf.ztdCompanyClothes.alias=zhongyouex-basic-test

jsf.wmsCarrier.alias=eclp-test:1.0.0
jsf.wmsCarrier.token=lsjCBQmWTCfmSzF422


jsf.ediProcessor.tiktok.alias=easymock_eclp-test_edi_queryReceiverInfo_tiktok2
jsf.ediProcessor.doFindOrderInfo.alias=easymock_ks_doFindOrderInfo:1.0.0
jsf.ediProcessor.meituan.alias=easymock_meiTuan_edi_createLabel_prod:1.0.0

jsf.preseparate.alias=PRESORT-JSF
jsf.preseparate.token=9008373f4af4a27
jsf.address.range.alias=promise-dos-test-78
jsf.blockerApplyWS.alias=ETMS-BLOCKER-TEST

combineService.jsf.alias=easymock_ofwtest
waybill.jsf.alias=eclp-test:1.0.0

jsf.orderHold.alias=test

jsf.cityAgingNewApi.alias=VRS-TEST-DEV

jsf.orderCollect.alias=test

jsf.query.order.relation.alias=jdl-order-relation-test:0.0.1
jsf.query.order.relation.token=123456

jsf.query.order.detail.alias=jdl-oms-search-test
jsf.query.order.detail.token=4becc51ea61b4323923a8de44002e50d

jsf.eclp.jimkv.load.service.alias=eclp-test:1.0.0
jsf.eclp.jimkv.load.service.token=123456
jsf.open.sp.isc.service.alias=open-isc-test
jsf.open.sp.isc.service.timeout=3000
jsf.waybillInterceptWS.alias=ETMS-BLOCKER-TEST

jsf.service.plus.alias=open_uat
jsf.service.plus.checkSku.timeout=500
jsf.service.plus.commitOrder.timeout=500
jsf.service.plus.changeAppointment.timeout=1500
jsf.service.plus.cancelOrder.timeout=500

jsf.wfp.alias=test
jsf.wfp.token=7EC715B34DD5EC5A26C70357B6B72C19
jsf.waybillInterceptApi.alias=ldop-alpha-waybill-test

jsf.pms.alias=jdl-pms-testb2c
jsf.pms.token=j2Eim43kND6F5yrAslCAY01pp9ns2P9b

jsf.preSortCal.alias=ldop-center-presort-test

jsf.basicSiteAbilityWS.alias=basic-ql-jsf
jsf.basicSiteAbilityWS.token=123456
jsf.basicSiteAbilityWS.timeout=1000

jsf.productMappingService.alias=jdl-product-test
jsf.productMappingService.timeout=100


jsf.waybillQueryApi.timeout=3000
jsf.preseparate.timeout=3000
jsf.blockerApplyWS.timeout=3000
jsf.gisV3.timeout=3000
jsf.cp.shipper.timeout=3000
jsf.cp.seller.dict.timeout=3000
jsf.wmsCarrier.timeout=3000
jsf.ztdCompanyClothes.timeout=3000
jsf.jgwaybill.timeout=3000
jsf.sellerTemplateRelService.timeout=3000
jsf.cp.electronic.form.timeout=3000
jsf.so.waybill.info.timeout=3000
jsf.alphaWaybill.timeout=10000
jsf.eclp.jimkv.load.service.timeout=3000
jsf.cp.customer.timeout=3000

jsf.providerQueryApi.alias=ldop-alpha-lp-test
jsf.timeout.providerQueryApi=100


jsf.orderInfoJosService.alias=COO-QL-ECLP
jsf.orderInfoJosService.timeout=3000

jsf.boundlessApi.alias=ldop-alpha-waybill-test

jsf.poService.alias=eclp2-test:1.0.0
jsf.poService.timeout=3000
jsf.exceptionOrderProcessApi.alias=jdtest
jsf.exceptionOrderProcessApi.timeout=5000

jsf.ldop.basic.alias=ldop-basic-test
jsf.basicTraderAPI.timeout=1000
jsf.orderCancel.alias=jdtest
jsf.timeout.orderCancel=5000

jsf.distributionServices.alias=easymock_ofw
jsf.timeout.distributionServices=1500

jsf.thirdDistributeQueryApi.alias=ldop-alpha-lp-test
jsf.thirdDistributeQueryApi.timeout=3000

jsf.platformStandardService.alias=csc-test:1.0.0
jsf.platformStandardService.timeout=5000

jsf.waybillCancelApi.timeout=5000
jsf.waybillCancelApi.alias=ldop-alpha-waybill-test

jsf.bms.account.alias=bms-account-test
jsf.bms.account.token=123456
jsf.bms.account.timeout=1000
bms.account.appId=410
bms.account.appSecret=123456

jsf.exceptionBasicInfoService.alias=eclp-exception-test:1.0.0
jsf.exceptionBasicInfoService.timeout=3000

jsf.tcOrderCancelService.alias=Test
jsf.tcOrderCancelService.timeout=3000
jsf.wbms.client.api.alias=idservice-test
jsf.wbms.client.api.token=qazrfvujmasdbnm
jsf.wbms.client.api.timeout=3000

jsf.oms.addon.alias=jdl-order-addon-test
jsf.oms.addon.timeout=3000
jsf.volumeComputationService.alias=jdtest
jsf.volumeComputationService.token=222222

jsf.transPipe.received.timeout=1000
jsf.transPipe.received.retries=1

jsf.timeout.eclpEdiService=3000
jsf.eclpEdiService.alias=eclp-test:1.0.0
jsf.waybillCrowdCancelApi.alias=EXPRESS_O2O
jsf.waybillCrowdCancelApi.timeout=2100

jsf.waybill.id.alias=idservice-jsf
jsf.waybill.id.timeout=500
token.waybill.id=qazrfvujmasdbnm

jsf.promiseService.alias=jdtest
jsf.promiseService.timeout=2000