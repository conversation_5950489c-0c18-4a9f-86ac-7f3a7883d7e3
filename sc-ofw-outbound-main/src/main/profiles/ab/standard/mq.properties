jmq.receiving.mode=ALL

mq.consumer.topic.popOrderReceive=JDL_ORDER_AB_SC_POP
mq.consumer.topic.isvOrderReceive=JDL_ORDER_AB_SC_ISV
mq.consumer.topic.omniOrderReceive=JDL_ORDER_AB_SC_JDR_OMNI
mq.consumer.topic.appraisalOrderReceive=NOT_ASSIGNED


mq.consumer.topic.modelPersist=ofw_model_persist_ab
mq.consumer.topic.popOrderReceive.shadow=shadow_JDL_ORDER_AB_SC_POP
mq.consumer.topic.isvOrderReceive.shadow=shadow_JDL_ORDER_AB_SC_ISV
mq.consumer.topic.bookDispatchReturn=FAKE

mq.consumer.topic.waybillDispatchReturn=ldop_center_dispatch_info

jmq4.producer.presort.info.sync.topic=FAKE

mq.consumer.topic.purchaseReturn=FAKE

mq.consumer.topic.alphaWaybillInterceptResult=FAKE

mq.consumer.topic.outboundOrderInfo=FAKE
mq.consumer.topic.outboundOrderCancelInfo=FAKE
mq.consumer.topic.decryptMessage=FAKE
mq.consumer.topic.fulfillment.complete.event=FAKE
mq.consumer.topic.collectAndDispatch=send_ofc_schedule_result_prod
mq.consumer.topic.wmsStatusRelay=eclp_so_status_wms_relay_ab
mq.consumer.syn.tms.unlock.delivery.order=syn_tms_unlock_delivery_order
mq.consumer.topic.tms.service.feedback=tms_service_feedback