#jmq config
jmq.address=jmq-cluster.jd.local:80
jmq.user=scOfwOutbound
jmq.password=4D78E90F
jmq.app=scOfwOutbound
jmq.epoll=false

jmq.uniqueEnv.address=jmq-cluster.jd.local:80
jmq.uniqueEnv.user=scOfwOutboundAb
jmq.uniqueEnv.password=4FDD6119
jmq.uniqueEnv.app=scOfwOutboundAb
jmq.uniqueEnv.epoll=false

jmqM.address=jmq-cluster.jd.local:80
jmqM.user=scOfwOutbound
jmqM.password=4D78E90F
jmqM.app=scOfwOutbound
jmqM.epoll=false

jmq.moving.transport=jmq.transport

jmq4.app=ofwOutboundAb
jmq4.user=ofwOutboundAb
jmq4.password=8997b2c222de4ee5a32fbcdbf1dffee5
jmq4.address=nameserver.jmq.jd.local:80

mq.producer.status.callback.topic=ofw_status_callback_ab
mq.producer.exception.topic=ofw_exception_ab
mq.producer.cancel.result.topic=ofw_cancel_result_ab

mq.producer.tms.topic=ofw_outbound_receive_waybill_message
mq.producer.tms.tmsDispatch.topic=eclp_dispatch_order_cancel_MOCK
mq.producer.wms.topic=ofw_outbound_receive_wms_message

mq.producer.zhongyou.callback.topic=zhongyou_callback_ab
jmq4.producer.sync.ofw.es.topic=receive_order_sync_ofw_es_ab
jmq4.producer.model.persist.topic=ofw_model_persist_ab
jmq2.appointment.result.notification.topic=FAKE
jmq4.producer.wait.book.dispatch.topic=FAKE

jmq4.producer.mutongInnerOrder.topic=FAKE
jmq4.producer.mutongCancelOrder.topic=FAKE
mq.consumer.fresh.fulfillment.complete.topic=fresh_fulfillment_complete_event