<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd"
       default-lazy-init="false" default-autowire="byName">

     <import resource="jsf/*.xml" />
     <import resource="jmq/jmq-i18n-consumer-receive${app.grayscale.xml.suffix:}.xml" />
     <context:property-placeholder
             location="classpath:b2b_contract/*.properties,classpath:${category.name}/*.properties"/>
     <import resource="classpath:/category/b2b_contract/spring-b2b-main.xml"/>

     <context:component-scan base-package="com.jdl.sc.ofw.vertical.i18n" />
</beans>
