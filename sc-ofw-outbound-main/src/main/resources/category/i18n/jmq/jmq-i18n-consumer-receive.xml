<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
 http://www.springframework.org/schema/beans/spring-beans.xsd
 http://code.jd.com/schema/jmq
 http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <jmq:consumer id="jmq4Consumer" transport="jmq4.transport" pullTimeout="2000">
        <jmq:listener topic="${mq.consumer.topic.i18nPopOrderReceive}" listener="i18nOrderCenterPopListener"/>
        <jmq:listener topic="${mq.consumer.topic.i18nIsvOrderReceive}" listener="i18nOrderCenterIsvListener"/>
        <jmq:listener topic="${mq.consumer.topic.i18nCustomsClearance}" listener="i18nCustomsClearanceListener"/>
        <jmq:listener topic="${mq.consumer.topic.i18nFopCreateWaybillResult}" listener="i18nFopCreateWaybillResultListener"/>
        <jmq:listener topic="${mq.consumer.topic.i18nFopCustomsClearanceResult}" listener="i18nFopCustomsClearanceListener"/>
    </jmq:consumer>
</beans>