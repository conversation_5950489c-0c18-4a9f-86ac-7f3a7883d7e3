<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- 查询pop订单清关信息 -->
    <jsf:consumer id="orderTraceInfoJsfService" interface="com.jd.pop.customs.center.service.soa.trace.outer.OrderTraceInfoJsfService"
                  protocol="jsf" alias="${jsf.i18n.center.alias}" timeout="${jsf.timeout.i18n.center}" retries="0">
    </jsf:consumer>

    <!-- 查询isv订单清关信息 -->
    <jsf:consumer id="eclpdlzOrderTraceInfoJsfService" interface="com.jd.pop.customs.center.service.soa.dlz.EclpdlzOrderTraceInfoJsfService"
                  protocol="jsf" alias="${jsf.i18n.center.alias}" timeout="${jsf.timeout.i18n.center}" retries="0">
    </jsf:consumer>

    <!-- 关务系统取消 -->
    <jsf:consumer id="cancelOrderJsfService" interface="com.jd.pop.customs.soa.cancelOrder.CancelOrderJsfService"
                  protocol="jsf" alias="${jsf.i18n.platform.alias}" timeout="${jsf.timeout.i18n.platform}" retries="0">
    </jsf:consumer>

    <!--  国际 FOP 控量服务  -->
    <jsf:consumer id="cmsShuntJsfService" interface="com.jd.pop.customs.center.service.soa.newman.CmsShuntJsfService"
                  alias="${jsf.cmsShuntJsfService.alias}" timeout="${jsf.cmsShuntJsfService.timeout}"
                  protocol="jsf" check="false" serialization="hessian" retries="1"/>

    <!--  国际 FOP 修改服务  -->
    <jsf:consumer id="fopGatewayServiceBillOrderModifyProvider" interface="com.jd.fop.service.bill.api.service.FopGatewayServiceBillOrderModifyProvider"
                  alias="${jsf.fopGatewayServiceBillOrderModifyProvider.alias}" timeout="${jsf.fopGatewayServiceBillOrderModifyProvider.timeout}"
                  protocol="jsf" check="false" serialization="hessian"/>
</beans>
