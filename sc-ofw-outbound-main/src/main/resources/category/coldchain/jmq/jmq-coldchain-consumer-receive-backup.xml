<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
 http://www.springframework.org/schema/beans/spring-beans.xsd
 http://code.jd.com/schema/jmq
 http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <jmq:consumer id="jmq4ConsumerIsv" transport="jmq4.transport" pullTimeout="2000">
        <jmq:listener topic="grayscale_${mq.consumer.topic.coldChainIsvOrderReceive}" listener="coldChainOrderCenterIsvListener"/>
        <jmq:listener topic="grayscale_${mq.consumer.topic.coldChainPopOrderReceive}" listener="coldChainOrderCenterPopListener"/>
    </jmq:consumer>
</beans>