<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- provider 配置开始-->
    <bean id="traceIdFunctionImpl" class="com.jdl.sc.ofw.out.main.jsf.TraceIdFunctionImpl"/>
    <bean id="providerLoggingFilter" class="com.jdl.sc.core.jsf.filter.ProviderLoggingFilter" scope="prototype">
        <property name="traceIdFunction" ref="traceIdFunctionImpl"/>
    </bean>
    <jsf:filter id="providerLogging" ref="providerLoggingFilter" providers="*"
                consumers=""/>
    <!-- provider 配置结束-->
    <!-- consumer 压测MOCK配置-->
    <import resource="jsf/spring-jsf-pressure-mock.xml" />
    <!-- consumer 配置开始-->
    <jsf:filter id="consumerLoggingFilter" class="com.jdl.sc.core.jsf.filter.ConsumerLoggingFilter" providers=""
                consumers="*"/>
    <bean id="collectJsfLogFilter" class="com.jdl.sc.core.jsf.filter.CollectJsfLogFilter" scope="prototype">
        <property name="jsfLogService" ref="jsfLogServiceImpl"/>
    </bean>
    <jsf:filter id="jsfLogFilter" ref="collectJsfLogFilter" providers=""
                consumers="*"/>

    <!-- consumer 配置结束-->

    <import resource="jsf/spring-jsf-ref.xml"/>
    <import resource="jsf/spring-jsf-ref-edi.xml"/>
    <import resource="jsf/spring-jsf-pub.xml"/>
    <import resource="jsf/spring-oms-consumer.xml"/>

</beans>
