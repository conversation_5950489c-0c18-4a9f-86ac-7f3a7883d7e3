<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">
    <!--  jsf 注册中心  -->
    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.index}"/>

    <!-- jsf consumer 自动添加ump监控。errorExcludes指定异常不报错，比如获取全局锁失败-->
    <bean id="consumerUmpFilter" class="com.jdl.sc.core.jsf.filter.ConsumerUmpFilter" scope="prototype">
        <property name="errorExcludes">
            <list>
                <!--                <value>java.lang.RuntimeException</value>-->
            </list>
        </property>
    </bean>
    <jsf:filter id="jsfConsumerUmpFilter" ref="consumerUmpFilter" providers="" consumers="*"/>

    <bean id="consumerTimeOutFilter" class="com.jdl.sc.ofw.out.horiz.infra.rpc.util.ConsumerTimeOutFilter" scope="prototype">
    </bean>
    <jsf:filter id="jsfConsumerTimeOutFilter" ref="consumerTimeOutFilter" providers="" consumers="*"/>
    <!-- 配置方案读取服务 -->
    <jsf:consumer id="productCenterService" interface="com.jd.open.sp.lpc.service.ProductCenterService"
                  protocol="jsf" check="false" alias="${jsf.ProductCenterService.alias}"
                  timeout="${jsf.timeout.ProductCenterService}">
        <jsf:parameter hide="true" key="readConfigPlan.logIO" value="true,false"/>
    </jsf:consumer>

    <!--退货入库接口，查询未解锁的商品明细-->
    <jsf:consumer id="rtwEclp2Service" interface="com.jd.eclp2.wms.rtw.service.RtwService" retries="1"
                  protocol="jsf" alias="${jsf.eclp2.rtw.alias}" check="false" timeout="${jsf.timeout.RtwService}">
    </jsf:consumer>

    <jsf:consumer id="orderMarkingService" interface="com.jd.fce.dos.service.contract.OrderMarkingService"
                  protocol="jsf" alias="${jsf.ordermarking.alias}" retries="3"
                  check="false" timeout="${jsf.timeout.OrderMarkingService}">
    </jsf:consumer>

    <jsf:consumer id="promiseKaService" interface="com.jd.promise.service.contract.PromiseKaService"
                  protocol="jsf" alias="${jsf.promiseJit.alias}"
                  retries="3"
                  check="false" timeout="${jsf.timeout.PromiseKaService}">
    </jsf:consumer>
    <jsf:consumer id="addressService" interface="com.jd.eclp.master.misc.service.AddressService" retries="1"
                  protocol="jsf" alias="${jsf.cp.alias}" timeout="${jsf.timeout.AddressService}">
        <jsf:parameter key="token" value="${token.addressService}" hide="true"/>

    </jsf:consumer>
    <!--事业部增值服务查询-->
    <jsf:consumer id="deptOvasService" interface="com.jd.eclp.ovas.service.DeptOvasService" protocol="jsf"
                  alias="${jsf.cp.alias}" timeout="${jsf.timeout.DeptOvasService}" retries="1">
        <jsf:parameter key="token" hide="true" value="${token.strategy.ovas}"/>
    </jsf:consumer>

    <jsf:consumer id="deptService" interface="com.jd.eclp.master.dept.service.DeptService" retries="1"
                  serialization="hessian"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" timeout="${jsf.timeout.DeptService}">
        <jsf:parameter key="token" value="${token.deptService}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="dictionaryService" interface="com.jd.eclp.master.dict.service.DictionaryService" retries="1"
                  serialization="hessian" protocol="jsf" check="false" alias="${jsf.cp.alias}"
                  timeout="${jsf.timeout.DictionaryService}">
        <jsf:parameter key="token" value="${token.dictionaryService}"/>
    </jsf:consumer>
    <jsf:consumer id="logisticsServiceService" interface="com.jd.eclp.master.logistics.service.LogisticsServiceService"
                  timeout="${jsf.timeout.LogisticsServiceService}" protocol="jsf" check="false" alias="${jsf.cp.alias}"
                  retries="1">
        <jsf:parameter key="token" value="${token.logisticsServiceService}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="airStrategyMainService" interface="com.jd.eclp.master.air.strategy.service.AirStrategyMainService"
                  timeout="${jsf.timeout.AirStrategyMainService}"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" retries="1">
        <jsf:parameter key="token" value="${token.airStrategyMainService}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="airWarehouseItemService"
                  interface="com.jd.eclp.master.air.strategy.service.AirWarehouseItemService"
                  timeout="${jsf.timeout.AirWarehouseItemService}"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" retries="1">
        <jsf:parameter key="token" value="${token.airWarehouseItemService}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="airLineItemService" interface="com.jd.eclp.master.air.strategy.service.AirLineItemService"
                  timeout="${jsf.timeout.AirLineItemService}"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" retries="1">
        <jsf:parameter key="token" value="${token.airLineItemService}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="individuationPrintConsumablesService"
                  interface="com.jd.eclp.master.print.consumables.service.IndividuationPrintConsumablesService"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}"
                  timeout="${jsf.timeout.IndividuationPrintConsumablesService}">
        <jsf:parameter key="token" value="${token.individuationPrintConsumablesService}" hide="true"/>
    </jsf:consumer>

    <!--商品批量查询接口-->
    <jsf:consumer id="goodsQueryBatchService" interface="com.jd.eclp.master.goods.service.GoodsBatchQueryService"
                  alias="${jsf.goods.alias}" retries="1"
                  protocol="jsf" check="false" timeout="${jsf.timeout.GoodsBatchQueryService}"/>

    <!--商品分类查询接口-->
    <jsf:consumer id="jdGoodsGategoryService" interface="com.jd.eclp.master.goods.service.JdGoodsGategoryService"
                  alias="${jsf.goodsGategory.alias}"
                  protocol="jsf" check="false" timeout="${jsf.timeout.JdGoodsGategoryService}"/>

    <jsf:consumer id="shipService" interface="com.jd.eclp.master.shipper.service.ShipService"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" timeout="${jsf.timeout.ShipService}">
        <jsf:parameter key="token" value="${token.shipService}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="shipperAgingConfigService"
                  interface="com.jd.eclp.master.shipper.service.ShipperAgingConfigService"
                  timeout="${jsf.cp.shipper.timeout}" retries="1" protocol="jsf" check="false" alias="${jsf.cp.alias}">
    </jsf:consumer>

    <jsf:consumer id="warehouseService" interface="com.jd.eclp.master.warehouse.service.WarehouseService" retries="1"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" timeout="${jsf.timeout.WarehouseService}">
        <jsf:parameter key="token" value="${token.warehouseService}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="senderAddressBookService"
                  interface="com.jd.eclp.master.senderAddressBook.service.SenderAddressBookService" retries="1"
                  alias="${jsf.cp.alias}" protocol="jsf" check="false"
                  timeout="${jsf.timeout.SenderAddressBookService}">
        <jsf:parameter key="token" hide="true" value="${token.senderAddressBookService}"/>
    </jsf:consumer>

    <jsf:consumer id="shopService" interface="com.jd.eclp.master.shop.service.ShopService" retries="1"
                  serialization="hessian" check="false" alias="${jsf.cp.alias}" timeout="${jsf.timeout.ShopService}">
        <jsf:parameter key="token" value="${token.shopService}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="dataReceiverService" interface="com.jd.staig.receiver.rpc.DataReceiver"
                  alias="${jsf.DataReceiver.alias}" timeout="${jsf.timeout.DataReceiver}" retries="3" protocol="jsf">
    </jsf:consumer>

    <jsf:consumer id="waybillIntegrateReceiveApi" interface="com.jd.ldop.delivery.api.WaybillIntegrateReceiveApi"
                  protocol="jsf" alias="${jsf.waybillIntegrateReceiveApi.alias}"
                  timeout="${jsf.timeout.WaybillIntegrateReceiveApi}">
    </jsf:consumer>

    <jsf:consumer id="warehouseConfigService" interface="com.jd.eclp.biz.config.service.WarehouseConfigService"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" timeout="${jsf.timeout.WarehouseConfigService}"/>

    <jsf:consumer id="orderQueryResource" interface="com.jd.orderbank.export.rest.OrderQueryResource" retries="1"
                  protocol="jsf" check="false" alias="${jsf.orderfinancial.alias}"
                  timeout="${jsf.timeout.OrderQueryResource}"/>

    <jsf:consumer id="goodsBarcodeService" interface="com.jd.eclp.master.goods.service.GoodsBarcodeService" retries="1"
                  alias="${jsf.goodsGategory.alias}" protocol="jsf" check="false" timeout="${jsf.timeout.GoodsBarcodeService}"/>

    <jsf:consumer id="goodsMaterialRelService" interface="com.jd.eclp.master.goods.service.GoodsMaterialRelService"
                  protocol="jsf" check="false" alias="${jsf.goodsGategory.alias}"
                  timeout="${jsf.timeout.GoodsMaterialRelService}" retries="1"/>

    <jsf:consumer id="orderMiddlewareJSFService"
                  interface="com.jd.ioms.jsf.export.OrderMiddlewareJSFService" retries="1"
                  protocol="jsf" alias="${jsf.ioms.orderMiddleware.alias}" connectTimeout="1000"
                  timeout="${jsf.timeout.OrderMiddlewareJSFService}"
    >
        <jsf:parameter key="token" value="${jsf.ioms.orderMiddleware.token}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="orderWareRelationJsfService" interface="com.jd.ofc.escort.export.jsf.IOrderWareRelationJsfService"
                  alias="${jsf.ofc.orderWareRelation.alias}" protocol="jsf"
                  timeout="${jsf.timeout.IOrderWareRelationJsfService}"
                  serialization="hessian"/>


    <!--云打印2.0 获取模板信息服务-->
    <jsf:consumer id="printMetadataService" interface="com.jd.cprt.service.PrintMetadataService"
                  protocol="jsf" check="false" alias="${jsf.cprt.alias}" timeout="${jsf.timeout.PrintMetadataService}"/>
    <!--云打印2.0 同步占位符服务-->

    <jsf:consumer id="strategyRecomService" interface="com.jd.eclp.ovas.service.StrategyRecomService"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}" timeout="${jsf.timeout.StrategyRecomService}">
        <jsf:parameter key="token" hide="true" value="${token.strategy.ovas}"/>
    </jsf:consumer>
    <jsf:consumer id="eclpCarrierService" interface="com.jd.fce.eclp.os.contract.service.EclpCarrierService"
                  alias="${carrier.alias}" protocol="jsf" serialization="hessian"
                  timeout="${jsf.timeout.EclpCarrierService}">
    </jsf:consumer>
    <jsf:consumer id="baseCrossPackageTagWS" interface="com.jd.ql.basic.ws.BaseCrossPackageTagWS"
                  protocol="jsf" alias="${jsf.BaseCrossPackageTagWS.alias}"
                  timeout="${jsf.timeout.BaseCrossPackageTagWS}" retries="1"
                  check="false">
        <jsf:parameter key="token" value="${jsf.BaseCrossPackageTagWS.token}" hide="true"/>
    </jsf:consumer>

    <!-- 产品中心新接口 -->
    <jsf:consumer id="productBaseService" interface="cn.jdl.pms.basic.api.ProductBaseService"
                  serialization="hessian"
                  register="true" protocol="jsf" alias="${jsf.ProductCenterService.new.alias}"
                  timeout="${jsf.timeout.ProductBaseService}" retries="2">
    </jsf:consumer>

    <jsf:consumer id="productSurchargeInfoService" interface="cn.jdl.pms.basic.api.ProductSurchargeInfoService"
                  serialization="hessian"
                  register="true" protocol="jsf" alias="${jsf.ProductCenterService.new.alias}"
                  timeout="${jsf.timeout.ProductBaseService}" retries="2">
    </jsf:consumer>


    <jsf:consumer id="waybillOperatorApi" interface="com.jd.ldop.center.api.waybill.client.WaybillOperatorApi"
                  protocol="jsf" alias="${jsf.ldop.waybillOperatorApi.alias}"
                  timeout="${jsf.timeout.WaybillOperatorApi}"
                  check="false">
    </jsf:consumer>

    <jsf:consumer id="waybillUpdateApi" interface="com.jd.ldop.center.api.update.WaybillUpdateApi"
                  protocol="jsf" alias="${jsf.ldop.waybillUpdate.alias}" check="false" retries="0"
                  timeout="${jsf.timeout.WaybillUpdateApi}">
    </jsf:consumer>

    <jsf:consumer id="waybillInfoUpdateApi" interface="com.jd.ldop.center.api.receive.WaybillInfoUpdateApi"
                  timeout="10000" retries="0" protocol="jsf" check="false" alias="${jsf.ldop.waybillUpdate.alias}"/>

    <jsf:consumer id="generalWaybillUpdateApi" interface="com.jd.ldop.center.api.waybill.GeneralWaybillUpdateApi"
                  timeout="10000" retries="0" protocol="jsf" check="false" alias="${jsf.ldop.waybillUpdate.alias}"/>

    <jsf:consumer id="dataSyncSenderService" interface="com.jd.staig.sender.rpc.service.DataSyncSenderService"
                  protocol="jsf" alias="${jsf.dtc.modify.alias}" timeout="${jsf.timeout.DataSyncSenderService}"
                  retries="0">
    </jsf:consumer>

    <jsf:consumer id="customPresortService" interface="com.jd.lbs.geofencing.api.customfence.CustomPresortService"
                  protocol="jsf" alias="${jsf.customPresort.alias}" timeout="${jsf.timeout.CustomPresortService}" retries="1">
    </jsf:consumer>
    <jsf:consumer id="orderShipsServiceJsf" interface="com.jd.etms.third.jsf.OrderShipsServiceJsf"
                  protocol="jsf" alias="${jsf.orderShips.alias}" timeout="${jsf.timeout.OrderShipsServiceJsf}"
                  retries="0">
        <jsf:parameter key="token" value="${jsf.orderShips.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="weblabService" interface="com.jdl.weblab.api.WeblabService"
                  protocol="jsf" alias="${jsf.weblib.alias}" timeout="${jsf.timeout.WeblabService}" retries="1">
        <jsf:parameter key="token" value="${jsf.weblib.token}" hide="true"/>
    </jsf:consumer>

    <!--配运产品适配-->
    <jsf:consumer id="deliveryProductService" interface="com.jdl.sc.oms.extension.api.service.DeliveryProductService"
                  alias="${jsf.oms.extension.alias}" protocol="jsf" serialization="hessian"
                  timeout="${jsf.timeout.DeliveryProductService}">
    </jsf:consumer>
    <!--保价-->
    <jsf:consumer id="insurePriceInfoService" interface="com.jdl.sc.oms.extension.api.service.InsurePriceInfoService"
                  alias="${jsf.oms.extension.alias}" protocol="jsf" serialization="hessian"
                  timeout="${jsf.timeout.InsurePriceInfoService}">
    </jsf:consumer>
    <!-- 配置中心服务 -->
    <jsf:consumer id="ovasCenterService" interface="com.jd.eclp.ovas.service.OvasCenterService" protocol="jsf"
                  check="false" alias="${jsf.ovas.alias}" timeout="${jsf.timeout.OvasCenterService}" retries="1"
                  loadbalance="shortestresponse">
        <jsf:parameter key="token" value="${jsf.ovas.token}" hide="true"/>
    </jsf:consumer>
    <!-- 查询订单优先级系数 -->
    <jsf:consumer id="soPriorityConfigService" interface="com.jd.eclp.bbp.so.service.SoPriorityConfigService" retries="1"
                  protocol="jsf" alias="${jsf.so.priority.alias}" timeout="${jsf.timeout.SoPriority}">
    </jsf:consumer>

    <jsf:consumer id="sellerTemplateRelService" interface="com.jd.eclp.master.seller.service.SellerTemplateRelService" retries="1"
                  protocol="jsf" check="false" alias="${jsf.sellerTemplateRelService.alias}" timeout="${jsf.sellerTemplateRelService.timeout}">
        <jsf:parameter key="token" value="${jsf.sellerTemplateRelService.token}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="waybillReceiveApi" interface="com.jd.ldop.alpha.waybill.api.WaybillReceiveApi"
                  protocol="jsf" alias="${jsf.alphaWaybill.alias}" timeout="1000" retries="3">
    </jsf:consumer>
    <jsf:consumer id="waybillCheckApi" interface="com.jd.ldop.alpha.waybill.api.WaybillCheckApi"
                  protocol="jsf" alias="${jsf.alphaWaybill.alias}" timeout="${jsf.alphaWaybill.waybillCheck.timeout}" retries="1">
    </jsf:consumer>
    <jsf:consumer id="waybillBigShotApi" interface="com.jd.ldop.alpha.waybill.api.WaybillBigShotApi"
                  protocol="jsf" alias="${jsf.alphaWaybill.alias}" timeout="${jsf.alphaWaybill.timeout}" retries="3">
    </jsf:consumer>

    <jsf:consumer id="bigShotQueryApi" interface="com.jd.ldop.alpha.waybill.api.BigShotQueryApi"
                  timeout="3000" alias="${jsf.alphaWaybill.alias}" retries="3" protocol="jsf"/>

    <!-- 三方配送通过京东获得运单号 -->
    <jsf:consumer id="thirdProviderConfApi" interface="com.jd.ldop.alpha.lp.api.ThirdProviderConfApi"
                  protocol="jsf" alias="${jsf.alphaGetWaybill.alias}" timeout="1000" retries="3" check="false">
    </jsf:consumer>

    <jsf:consumer id="externalAddressToJDAddressService"
                  interface="com.jd.addresstranslation.api.address.ExternalAddressToJDAddressService"
                  timeout="${jsf.gisV3.timeout}" alias="${jsf.gisV3.alias}" serialization="hessian" protocol="jsf" retries="3">
        <jsf:parameter key="token" value="${jsf.gisV3.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="waybillQueryApi" interface="com.jd.etms.waybill.api.WaybillQueryApi" retries="1"
                  protocol="jsf" alias="${jsf.waybillQueryApi.alias}" timeout="${jsf.waybillQueryApi.timeout}">
    </jsf:consumer>

    <jsf:consumer id="waybillTraceApi" interface="com.jd.etms.waybill.api.WaybillTraceApi" retries="1" async="true"
                  protocol="jsf" alias="${jsf.waybillQueryApi.alias}" timeout="${jsf.waybillQueryApi.timeout}">
    </jsf:consumer>

    <jsf:consumer id="soPddWaybillApi" interface="com.jd.eclp.bbp.so.service.SoPddWaybillApi" retries="1"
                  protocol="jsf" check="false" alias="${jsf.soPddWaybillApi.alias}" timeout="${jsf.cp.electronic.form.timeout}"/>

    <jsf:consumer id="integrationService" interface="com.jd.clps.ucs.intserv.IntegrationService"
                  protocol="jsf" check="false" alias="${jsf.intserv.alias}"/>

    <jsf:consumer id="customerService" interface="com.jd.eclp.master.customer.service.CustomerService"
                  protocol="jsf" alias="${jsf.cp.alias}" timeout="${jsf.cp.customer.timeout}" retries="1" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.token.customerService}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="sellerDictService" interface="com.jd.eclp.master.dict.service.SellerDictService"
                  alias="${jsf.cp.alias}" protocol="jsf" timeout="${jsf.cp.seller.dict.timeout}" retries="1">
    </jsf:consumer>

    <!--  纯配订单中心接口  -->
    <jsf:consumer id="commonModifyCancelOrderApi"
                  interface="cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi"
                  protocol="jsf" check="false" alias="${jsf.eds.order.cancel.alias}">
        <jsf:parameter key="token" hide="true" value="${jsf.eds.order.cancel.token}"/>
    </jsf:consumer>
    <!-- 众邮订单获取马甲公司信息 -->
    <jsf:consumer id="ztdCompanyClothesApi" interface="com.zhongyouex.basic.api.ZtdCompanyClothesApi"
                  timeout="${jsf.ztdCompanyClothes.timeout}" retries="1"
                  protocol="jsf" check="false" alias="${jsf.ztdCompanyClothes.alias}"/>

    <jsf:consumer id="pddPrintRecordApi" interface="com.zhongyouex.pdd.api.service.PddPrintRecordApi"
                  timeout="500" retries="3" protocol="jsf"  check="false" alias="${jsf.ztdCompanyClothes.alias}"/>

    <!--   查询承运商接口 -->
    <jsf:consumer id="wmsCarrierService" interface="com.jd.eclp.master.wmscarrier.service.WmsCarrierService" retries="1"
                  alias="${jsf.wmsCarrier.alias}" protocol="jsf" check="false" serialization="hessian" timeout="${jsf.wmsCarrier.timeout}">
        <jsf:parameter key="token" hide="true" value="${jsf.wmsCarrier.token}"/>
    </jsf:consumer>

    <jsf:consumer id="jGWaybillApi" interface="com.zhongyouex.jg.api.service.JGWaybillApi" retries="1"
                  timeout="${jsf.jgwaybill.timeout}"
                  protocol="jsf" check="false" alias="${jsf.jgwaybill.alias}" serialization="hessian"/>

    <jsf:consumer id="soService" interface="com.jd.eclp.bbp.so.service.SoService"
                  protocol="jsf" check="false" alias="${jsf.cp.alias}"/>

    <jsf:consumer id="expressDispatchServiceApi" interface="com.jd.bluedragon.preseparate.jsf.ExpressDispatchServiceAPI"
                  protocol="jsf" alias="${jsf.preseparate.alias}" timeout="${jsf.preseparate.timeout}" retries="1">
        <jsf:parameter key="token" value="${jsf.preseparate.token}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="addressRangeService" interface="com.jd.promise.dos.contract.AddressRangeService"
                  alias="${jsf.address.range.alias}"/>

    <jsf:consumer id="blockerApplyWS" interface="com.jd.etms.blocker.webservice.BlockerApplyWS" timeout="${jsf.blockerApplyWS.timeout}"
                  alias="${jsf.blockerApplyWS.alias}" retries="3" protocol="jsf" check="false">
    </jsf:consumer>

    <jsf:consumer id="combineService" interface="com.jd.clps.ucs.combine.service.CombineService"
                  protocol="jsf" check="false" alias="${combineService.jsf.alias}"/>

    <jsf:consumer id="soWaybillInfoService" interface="com.jd.eclp.bbp.so.service.SoWaybillInfoService" retries="1"
                  protocol="jsf" check="false" alias="${waybill.jsf.alias}" timeout="${jsf.so.waybill.info.timeout}"/>

    <!--生产协同-->
    <jsf:consumerGroup id="transPipeJDWmsService"
                  interface="com.jdl.sc.lfc.trans.pipe.api.wms.TransPipeJDWmsService"
                  protocol="jsf" check="false" alias="${jsf.transPipe.group.alias}" timeout="15000"/>
    <jsf:consumerGroup id="transPipeWaybillService"
                  interface="com.jdl.sc.lfc.trans.pipe.api.waybill.TransPipeWaybillService"
                  protocol="jsf" check="false" alias="${jsf.transPipe.group.alias}" timeout="${jsf.transPipe.waybill.timeout:8000}" />



    <jsf:consumer id="orderHoldService" interface="com.jdl.sc.order.hold.api.OrderHoldService"
                  alias="${jsf.orderHold.alias}" protocol="jsf" check="false" serialization="hessian" timeout="2000">
    </jsf:consumer>
    <jsf:consumer id="orderHoldQueryService" interface="com.jdl.sc.order.hold.api.OrderHoldQueryService"
                  alias="${jsf.orderHold.alias}" retries="3" protocol="jsf" check="false" serialization="hessian" timeout="500">
    </jsf:consumer>
    <jsf:consumer id="reDecisionQueryService" interface="com.jdl.sc.order.hold.api.ReDecisionQueryService"
                  alias="${jsf.orderHold.alias}" retries="3" protocol="jsf" check="false" serialization="hessian" timeout="500">
    </jsf:consumer>
    <!-- 集单 -->
    <jsf:consumer id="orderCollectService" interface="com.jdl.sc.order.collect.api.OrderCollectService"
                  alias="${jsf.orderCollect.alias}" retries="3" protocol="jsf" check="false" serialization="hessian">
    </jsf:consumer>

    <!--时效-->
    <jsf:consumer id="cityAgingNewApi" interface="com.jd.etms.vrs.api.cityaging.CityAgingNewApi"
                  register="true" alias="${jsf.cityAgingNewApi.alias}">
    </jsf:consumer>

    <jsf:consumer id="queryOrderRelationService" interface="cn.jdl.oms.relation.service.QueryOrderRelationService"
                  alias="${jsf.query.order.relation.alias}" protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.query.order.relation.token}"/>
    </jsf:consumer>

    <!-- 根据三方单号查询订单详情 -->
    <jsf:consumer id="getOrderDetailService" interface="cn.jdl.oms.search.third.api.GetOrderDetailService"
                  alias="${jsf.query.order.detail.alias}" protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.query.order.detail.token}"/>
    </jsf:consumer>

    <!--仓明细查询-->
    <jsf:consumer id="warehouseFulfillmentInfoQueryService"
                  interface="com.jdl.warehouse.fulfillment.query.api.WarehouseFulfillmentInfoQueryService"
                  retries="0" register="true" protocol="jsf" alias="${jsf.wfp.alias}" check="false">
        <jsf:parameter key="token" value="${jsf.wfp.token}" hide="true"/>
    </jsf:consumer>

    <!-- 服务+ -->
    <jsf:consumer id="ofcOrderService"
                  interface="com.jd.serviceplus.order.api.OFCOrderService"
                  register="true" protocol="jsf" alias="${jsf.service.plus.alias}" check="false">
        <jsf:method name="checkSku" async="false" timeout="${jsf.service.plus.checkSku.timeout}" retries="1"/>
        <jsf:method name="commitOrder" async="false" timeout="${jsf.service.plus.commitOrder.timeout}" retries="1"/>
        <jsf:method name="changeAppointment" async="false" timeout="${jsf.service.plus.changeAppointment.timeout}" retries="1"/>
        <jsf:method name="cancelOrder" async="false" timeout="${jsf.service.plus.cancelOrder.timeout}" retries="1"/>
    </jsf:consumer>

    <jsf:consumer id="platformStandardService" interface="com.jd.csc.channel.intserv.PlatformStandardService"
                  timeout="${jsf.platformStandardService.timeout}" async="true"
                  retries="0" protocol="jsf" check="false" alias="${jsf.platformStandardService.alias}">
        <jsf:method name="getWaybillCode" async="false" />
        <jsf:method name="getWaybillPrintInfo" async="false" />
    </jsf:consumer>

    <jsf:consumer id="waybillCancelApi" interface="com.jd.ldop.alpha.waybill.api.WaybillCancelApi"
                  timeout="${jsf.waybillCancelApi.timeout}" async="true"
                  retries="0" protocol="jsf" check="false" alias="${jsf.waybillCancelApi.alias}">
    </jsf:consumer>

    <!--三方承运商拦截接口-->
    <jsf:consumer id="waybillInterceptApiAsync" interface="com.jd.ldop.alpha.waybill.api.WaybillInterceptApi"
                  retries="0" register="true"  async="true" protocol="jsf" alias="${jsf.waybillInterceptApi.alias}"
                  check="false">
    </jsf:consumer>

    <!--产品计算-->
    <jsf:consumer id="productCalculationService" interface="com.jdl.sc.oms.extension.api.service.ProductCalculationService"
                  alias="${jsf.oms.extension.alias}" protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- ECLP JIMKV 读取服务 -->
    <jsf:consumer id="eclpJIMKVLoadService" interface="com.jd.eclp.jimkv.service.EclpJIMKVLoadService" retries="2"
                  protocol="jsf" alias="${jsf.eclp.jimkv.load.service.alias}" compress="snappy" timeout="${jsf.eclp.jimkv.load.service.timeout}" >
        <jsf:parameter key="token" value="${jsf.eclp.jimkv.load.service.token}" hide="true"/>
    </jsf:consumer>

    <!-- 预收款校验服务  -->
    <jsf:consumer id="popSoAuthenticationService" interface="com.jd.open.sp.isc.service.PopAuthenticationService"
                  protocol="jsf" alias="${jsf.open.sp.isc.service.alias}" timeout="${jsf.open.sp.isc.service.timeout}" >
    </jsf:consumer>

    <!--解除拦截-->
    <jsf:consumer id="waybillInterceptWS" interface="com.jd.etms.blocker.webservice.WaybillInterceptWS"
                  alias="${jsf.waybillInterceptWS.alias}" protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--产品中心供给服务-->
    <jsf:consumer id="productRecommendationService" interface="cn.jdl.pms.api.ProductRecommendationService"
                  alias="${jsf.pms.alias}" protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.pms.token}" hide="true" />
    </jsf:consumer>

    <jsf:consumer id="ruleService" interface="com.jd.eclp.ovas.service.rule.RuleService" protocol="jsf"
                  check="false" alias="${jsf.ovas.alias}" timeout="${jsf.timeout.RuleService}" retries="1"
                  loadbalance="shortestresponse">
        <jsf:parameter key="token" value="${jsf.ovas.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="preSortCalcExecutorApi" interface="com.jd.ldop.center.api.presort.PreSortCalcExecutorApi"
                  protocol="jsf" alias="${jsf.preSortCal.alias}">
    </jsf:consumer>

    <jsf:consumer id="basicSiteAbilityWS" interface="com.jd.ql.basic.ws.BasicSiteAbilityWS" alias="${jsf.basicSiteAbilityWS.alias}" protocol="jsf"
                  timeout="${jsf.basicSiteAbilityWS.timeout}">
        <jsf:parameter key="token" value="${jsf.basicSiteAbilityWS.token}" hide="true" />
    </jsf:consumer>

    <!-- 切量工具 -->
    <jsf:consumer id="waybillWebLab" interface="com.jdl.sc.weblab.api.WaybillWebLab"
                  protocol="jsf" timeout="300"
                  retries="2" serialization="hessian" alias="${jsf.orderHold.alias}" >
    </jsf:consumer>

    <!--产品中心关系查询服务-->
    <jsf:consumer id="productMappingService" interface="com.jdl.product.api.ProductMappingService"
                  protocol="jsf" alias="${jsf.productMappingService.alias}" timeout="${jsf.timeout.RuleService}" retries="1">
    </jsf:consumer>
    <!--三方承运商查询服务 -->
    <jsf:consumer id="providerQueryApi" interface="com.jd.ldop.alpha.lp.api.ProviderQueryApi"
                  protocol="jsf" check="false" alias="${jsf.providerQueryApi.alias}" timeout="${jsf.timeout.providerQueryApi}" retries="1">
    </jsf:consumer>
    <jsf:consumer id="orderInfoJosService" interface="com.jd.bluedragon.dms.receive.jos.OrderInfoJosService"
                  protocol="jsf" alias="${jsf.orderInfoJosService.alias}" timeout="${jsf.orderInfoJosService.timeout}" retries="1">
    </jsf:consumer>

    <jsf:consumer id="outageQueryApi" interface="com.jd.ldop.alpha.waybill.api.OutageQueryApi"
                  protocol="jsf"
                  retries="2" serialization="hessian" alias="${jsf.boundlessApi.alias}" >
    </jsf:consumer>
    <jsf:consumer id="poService" interface="com.jd.eclp2.po.service.PoService" retries="1"
                  protocol="jsf" alias="${jsf.poService.alias}" timeout="${jsf.poService.timeout}">
    </jsf:consumer>
    <jsf:consumer id="exceptionOrderProcessApi" interface="com.jd.lbcc.delivery.api.ExceptionOrderProcessApi" retries="1"
                  protocol="jsf" alias="${jsf.exceptionOrderProcessApi.alias}" timeout="${jsf.exceptionOrderProcessApi.timeout}">
    </jsf:consumer>
    <!--商家基础资料-->
    <jsf:consumer id="basicTraderAPI"
                  interface="com.jd.ldop.basic.api.BasicTraderAPI" retries="1" timeout="${jsf.basicTraderAPI.timeout}"
                  protocol="jsf" check="false" alias="${jsf.ldop.basic.alias}" serialization="hessian" />
    <jsf:consumer id="orderCancelService" interface="com.jd.staig.receiver.rpc.OrderCancelService"
                  alias="${jsf.orderCancel.alias}" timeout="${jsf.timeout.orderCancel}" protocol="jsf">
    </jsf:consumer>

    <!-- 外发中心-京配转三方配置查询 -->
    <jsf:consumer id="thirdDistributeQueryApi" interface="com.jd.ldop.alpha.lp.api.ThirdDistributeQueryApi" serialization="hessian"
                  protocol="jsf" alias="${jsf.thirdDistributeQueryApi.alias}" timeout="${jsf.thirdDistributeQueryApi.timeout}" retries="1">
    </jsf:consumer>
    <jsf:consumer id="distributionServices" interface="com.jd.open.service.order.allocation.DistributionServices" serialization="hessian"
                  timeout="${jsf.timeout.distributionServices}" retries="1" protocol="jsf" check="false" alias="${jsf.distributionServices.alias}">
    </jsf:consumer>
    <!--财务账户余额风控接口-->
    <jsf:consumer id="bmsAccountFacadeService" interface="com.jd.bms.account.api.outer.BmsAccountRiskFacadeService"
                  protocol="jsf" alias="${jsf.bms.account.alias}" timeout="${jsf.bms.account.timeout}" retries="1" check="false">
        <jsf:parameter key="token" value="${jsf.bms.account.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="exceptionBasicInfoService" interface="com.jd.eclp.exception.api.ExceptionBasicInfoService" serialization="hessian"
                  timeout="${jsf.exceptionBasicInfoService.timeout}" retries="1"
                  protocol="jsf" check="false" alias="${jsf.exceptionBasicInfoService.alias}">
    </jsf:consumer>

    <!-- TC-取消下单接口 -->
    <jsf:consumer id="tcOrderCancelService" interface="com.jd.tc.seller.wb.api.service.order.OrderCancelService" serialization="hessian"
                  protocol="jsf" alias="${jsf.tcOrderCancelService.alias}" timeout="${jsf.tcOrderCancelService.timeout}" retries="0">
    </jsf:consumer>

    <jsf:consumer id="waybillCodeGenericGenerateApi" interface="com.jd.cp.wbms.wcs.client.api.WaybillCodeGenericGenerateApi"
                  protocol="jsf" serialization="hessian" timeout="${jsf.wbms.client.api.timeout}" retries="3"
                  alias="${jsf.wbms.client.api.alias}" >
        <jsf:parameter key="token" value="${jsf.wbms.client.api.token}" hide="true" />
    </jsf:consumer>

    <jsf:consumer id="volumeComputationService" interface="com.jd.omc.jsf.VolumeComputationService" serialization="hessian"
                  protocol="jsf" alias="${jsf.volumeComputationService.alias}" retries="0">
        <jsf:parameter key="token" value="${jsf.volumeComputationService.token}" hide="true"/>
    </jsf:consumer>
    <!-- 订单中心提供的 订单时效校验接口 -->
    <jsf:consumer id="productPromiseCheckService" interface="cn.jdl.oms.addon.service.ProductPromiseCheckService"
                  alias="${jsf.oms.addon.alias}" timeout="${jsf.oms.addon.timeout}" retries="1" protocol="jsf" serialization="hessian">
    </jsf:consumer>
    <!-- 同城速配拦截接口 -->
    <jsf:consumer id="waybillCrowdCancelApi" interface="com.jd.express.o2o.api.WaybillCrowdCancelApi" serialization="hessian"
                  protocol="jsf" alias="${jsf.waybillCrowdCancelApi.alias}" timeout="${jsf.waybillCrowdCancelApi.timeout}" retries="0">
    </jsf:consumer>

    <!--VIP取消流程-EDI接口-->
    <jsf:consumer id="vipCancelEclpEdiService" interface="com.jd.eclp.edi.service.EclpEdiService"
                  timeout="${jsf.timeout.eclpEdiService}" retries="1" protocol="jsf" check="false" alias="${jsf.eclpEdiService.alias}">
    </jsf:consumer>

    <jsf:consumer id="waybillIdService" interface="com.jd.ql.idservice.api.WaybillIdService"
                  alias="${jsf.waybill.id.alias}" timeout="${jsf.waybill.id.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${token.waybill.id}" hide="true"/>
    </jsf:consumer>
    <!-- 外单前置仓时效接口 -->
    <jsf:consumer id="promiseService" interface="com.jdl.promise.external.front.store.contract.promise.api.PromiseService" serialization="hessian"
                  protocol="jsf" alias="${jsf.promiseService.alias}" timeout="${jsf.promiseService.timeout}" retries="1">
    </jsf:consumer>
</beans>
