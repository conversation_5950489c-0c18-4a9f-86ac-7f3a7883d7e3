package com.jdl.sc.ofw.out.domain.service;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.CancelOutboundOrderOfcResponse;
import cn.jdl.oms.core.model.ChannelInfo;
import com.jdl.sc.ofw.out.app.service.oms.CancelOutboundOrderOfcServiceImpl;
import com.jdl.sc.ofw.out.horiz.infra.util.RequestProfileUtil;
import com.jdl.sc.ofw.out.main.ProjectWebApplication;
import com.jdl.sc.ofw.outbound.api.OutboundService;
import com.jdl.sc.ofw.outbound.spec.enums.CancelResultStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProjectWebApplication.class)
@Ignore
public class CancelOutboundIntegrationIT {

    @Resource
    CancelOutboundOrderOfcServiceImpl cancelOutboundOrderOfcService;

    @Resource(name = "outboundServiceImpl")
    OutboundService outboundService;

    RequestProfile requestProfile = new RequestProfile();
    CancelOutboundOrderOfcRequest cancelOutboundOrderOfcRequest = new CancelOutboundOrderOfcRequest();

    @Before
    public void setUp(){
        requestProfile = RequestProfileUtil.createOmsProfile();
        BusinessIdentity businessIdentity = new BusinessIdentity();
        ChannelInfo channelInfo = new ChannelInfo();
        cancelOutboundOrderOfcRequest.setBusinessIdentity(businessIdentity);
        cancelOutboundOrderOfcRequest.setChannelInfo(channelInfo);
        businessIdentity.setBusinessUnit("cn_jdl_sc-pop");
        cancelOutboundOrderOfcRequest.setOrderNo("ESL0010000000812");
        cancelOutboundOrderOfcRequest.setRequestId("3444255954944");
        cancelOutboundOrderOfcRequest.setCancelType(1);
        cancelOutboundOrderOfcRequest.setCancelMode(0);
        cancelOutboundOrderOfcRequest.setOperator("popTest");
        cancelOutboundOrderOfcRequest.setRemark("");
        channelInfo.setSystemCaller("POP");
        channelInfo.setChannelOperateTime(new Date());
    }

    @Test
    public void testCancelOrder(){
        CancelOutboundOrderOfcResponse res = cancelOutboundOrderOfcService.cancelOrder(requestProfile, cancelOutboundOrderOfcRequest);
        Assert.assertTrue(CancelResultStateEnum.SUCCESS.getCode()==res.getData().getCancelResult());
    }

}
