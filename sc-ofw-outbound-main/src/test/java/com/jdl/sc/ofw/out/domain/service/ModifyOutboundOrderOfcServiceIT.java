package com.jdl.sc.ofw.out.domain.service;

import cn.jdl.ofc.api.oms.common.model.ModifyOutboundOrderOfcRequest;
import cn.jdl.ofc.supplychain.api.oms.ModifyOutboundOrderOfcService;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.horiz.infra.util.RequestProfileUtil;
import com.jdl.sc.ofw.out.main.ProjectWebApplication;
import com.jdl.sc.test.TestFileLoader;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProjectWebApplication.class)
@Slf4j
@Ignore
public class ModifyOutboundOrderOfcServiceIT {

    @Resource
    private ModifyOutboundOrderOfcService modifyOutboundOrderOfcService;

    @Test
    public void modifyOrderTest() throws Exception {
        String requestJson = "{\n" +
                "    \"businessIdentity\":{\n" +
                "        \"businessScene\":\"modify\",\n" +
                "        \"businessStrategy\":\"BOutboundSaleISVSupplyChain\",\n" +
                "        \"businessType\":\"outbound_sale\",\n" +
                "        \"businessUnit\":\"cn_jdl_sc-isv\",\n" +
                "        \"fulfillmentUnit\":\"JDL.ORDER.SC_ISV\"\n" +
                "    },\n" +
                "\"refOrderInfo\": {\n" +
                "    \"extendProps\": {\n" +
                "      \"subOrderNos\": \"67022884992301\"\n" +
                "    }\n" +
                "  },"+
                "    \"consigneeInfo\":{\n" +
                "        \"addressInfo\":{\n" +
                "            \"address\":\"北京市北京市海淀区海淀街道海淀街道海淀大街2号四通大厦7楼703室(近海淀路小区)中关村e世界\"\n" +
                "        },\n" +
                "        \"consigneeMobile\":\"123****8900\",\n" +
                "        \"consigneeName\":\"邓**\",\n" +
                "        \"extendProps\":{\n" +
                "            \"encryptMode\":\"1\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"extendProps\":{\n" +
                "\n" +
                "    },\n" +
                "    \"operator\":\"抖音加密平台\",\n" +
                "    \"orderNo\":\"ESL00000010003141008\",\n" +
                "    \"productInfos\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"smartPatternInfo\":{\n" +
                "        \"extendProps\":{\n" +
                "\n" +
                "        }\n" +
                "    }\n" +
                "}";

        String requestHeavyJson = "{\"businessIdentity\":{\"businessScene\":\"modify\",\"businessType\":\"outbound_sale\",\"businessUnit\":\"cn_jdl_sc-isv\",\"fulfillmentUnit\":\"JDL.ORDER.SC_ISV\"},\"consigneeInfo\":{\"addressInfo\":{\"address\":\"经海路与科创十一街交叉口京东总部2号楼蓝双双\",\"cityName\":\"大兴区\",\"cityNo\":\"2810 \",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\"},\"consigneeMobile\":\"***********\",\"consigneeName\":\"王大富\"},\"consignorInfo\":{},\"extendProps\":{\"resumeCode\":\"8050256\",\"orderMark\":\"10011000010010000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\",\"pauseId\":\"3435\"},\"operator\":\"SYSTEM\",\"orderNo\":\"ESL00000010000210218\",\"shipmentInfo\":{\"serviceRequirements\":{\"heavyGoodsUpstairsFlag\":\"0\",\"unpackingFlag\":\"1\",\"insured\":\"false\",\"shouldPayMoney\":\"123.45\"}},\"smartPatternInfo\":{\"extendProps\":{\"outboundPriority\":\"2\",\"outboundUrgency\":\"1\",\"expectOutboundTime\":\"2022-10-19 12:34:56\",\"orderTemporaryStorage\":\"1\"},\"stockOperationRule\":{\"occupyResult\":1}}}";

        final String loadTestFile = TestFileLoader.loadTestFile("data/outbound/modify/modifyRequest.json");
        ModifyOutboundOrderOfcRequest modifyOutboundOrderOfcRequest = JsonUtil.readValue(loadTestFile, ModifyOutboundOrderOfcRequest.class);

        modifyOutboundOrderOfcService.modifyOrder(RequestProfileUtil.createOmsProfile(), modifyOutboundOrderOfcRequest);
    }
}
