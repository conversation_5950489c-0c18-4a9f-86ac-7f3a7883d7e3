package com.jdl.sc.ofw.out.horiz.ext.pause;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.domain.model.vo.Customer;
import com.jdl.sc.ofw.out.domain.rpc.ProductCenterServiceAcl;
import com.jdl.sc.ofw.out.horiz.extension.pause.InventoryPauseDefaultExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class InventoryPauseDefaultExtTest {

    @InjectMocks
    private InventoryPauseDefaultExt inventoryPauseDefaultExt;

    @Mock
    private ProductCenterServiceAcl productCenterServiceAcl;

    @Test
    public void testNonHoldOrder(){

        Mockito.when(productCenterServiceAcl.readPositiveValue(any(), any()))
                .thenReturn("0");

        OrderFulfillmentModel model = OrderFulfillmentModel.builder()
                .customer(Customer.builder().build())
                .build();
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit("unit");
        businessIdentity.setBusinessScene("scene");
        OrderFulfillmentContext context = new OrderFulfillmentContext(businessIdentity, model, null);
        try {
            inventoryPauseDefaultExt.execute(context);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
