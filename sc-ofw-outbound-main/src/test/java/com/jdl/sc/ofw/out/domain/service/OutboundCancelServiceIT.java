package com.jdl.sc.ofw.out.domain.service;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import com.jdl.sc.core.json.JsonUtil;
import com.jdl.sc.ofw.out.common.constant.BusinessScene;
import com.jdl.sc.ofw.out.domain.dto.CancelCommand;
import com.jdl.sc.ofw.out.domain.model.OrderFulfillmentModel;
import com.jdl.sc.ofw.out.domain.model.bc.OrderFulfillmentContext;
import com.jdl.sc.ofw.out.main.ProjectWebApplication;
import com.jdl.sc.ofw.outbound.spec.enums.CancelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProjectWebApplication.class)
@Slf4j
@Ignore
public class OutboundCancelServiceIT {
    @Resource
    private OutboundCancelService cancelService;

    @Test
    public void batrixCancel() throws Exception {
        CancelCommand cancelCommand = CancelCommand.builder()
                .orderNo("ESL00000010001050260")
                .cancelType(CancelTypeEnum.SYNC)
                .requestId("123444")
                .operator("test")
                .operateTime(new Date())
                .build();


        final OrderFulfillmentModel model = JsonUtil.readValueSafe(modelStr(), OrderFulfillmentModel.class);
        OrderFulfillmentContext context = OrderFulfillmentContext.builder()
                .businessIdentity(new BusinessIdentity("cn_jdl_sc", "outbound_sale_sc_ofc", BusinessScene.CANCEL))
                .model(model)
                .build();



        log.info(JsonUtil.toJsonSafe(cancelService.cancel(cancelCommand,context)));
    }

    public String modelStr(){
        String str = "{\"order_no\":\"ESL00000010001050260\",\"data\":\"{\\\"batrixInfo\\\":{\\\"businessUnit\\\":\\\"cn_jdl_sc-pop\\\",\\\"businessType\\\":\\\"sc_ofc_outbound_sale\\\",\\\"businessScene\\\":\\\"fulfillmentGenerate\\\",\\\"yId\\\":\\\"JDL\\\"},\\\"orderNo\\\":\\\"ESL00000010001050260\\\",\\\"orderType\\\":\\\"100\\\",\\\"orderBusinessType\\\":1,\\\"basicInfo\\\":{\\\"preSaleStage\\\":1,\\\"agentSales\\\":false},\\\"customer\\\":{\\\"sellerNo\\\":\\\"ECP0000000002087\\\",\\\"sellerName\\\":\\\"vmi\\\",\\\"accountNo\\\":\\\"EBU0000000000569\\\",\\\"accountName\\\":\\\"vmi22\\\",\\\"customerLevel\\\":\\\"0\\\"},\\\"cargos\\\":[{\\\"name\\\":\\\"test_splz1   测试 玫瑰红色 M\\\",\\\"no\\\":\\\"EMG4398059117457\\\",\\\"level\\\":\\\"100\\\",\\\"quantity\\\":{\\\"value\\\":3.0,\\\"unit\\\":\\\"袋\\\"},\\\"occupyQuantity\\\":{\\\"value\\\":3,\\\"unit\\\":\\\"袋\\\"},\\\"refGoodsType\\\":\\\"1\\\",\\\"refGoodsNo\\\":\\\"ESG4398058530249\\\",\\\"isvCargoNo\\\":\\\"***********\\\",\\\"uniqueCode\\\":\\\"0\\\",\\\"length\\\":1.0,\\\"width\\\":1.0,\\\"height\\\":1.0,\\\"volume\\\":0.001,\\\"weight\\\":1.0,\\\"marineContraband\\\":false,\\\"thirdCategoryName\\\":\\\"衬衫\\\",\\\"goodsExtendVolume\\\":1}],\\\"goods\\\":[{\\\"type\\\":\\\"1\\\",\\\"no\\\":\\\"ESG4398058530249\\\",\\\"name\\\":\\\"test_splz1   测试 玫瑰红色 M\\\",\\\"price\\\":{\\\"amount\\\":132.0,\\\"currencyCode\\\":\\\"CNY\\\"},\\\"amount\\\":{\\\"amount\\\":307.02,\\\"currencyCode\\\":\\\"CNY\\\"},\\\"quantity\\\":{\\\"value\\\":3.0,\\\"unit\\\":\\\"件\\\"},\\\"channelGoodsNo\\\":\\\"***********\\\",\\\"refCargoNo\\\":\\\"ESG4398058530249\\\"}],\\\"consignee\\\":{\\\"address\\\":{\\\"provinceName\\\":\\\"北京\\\",\\\"provinceNo\\\":\\\"1\\\",\\\"cityName\\\":\\\"朝阳区\\\",\\\"cityNo\\\":\\\"72\\\",\\\"countyName\\\":\\\"三环以内\\\",\\\"countyNo\\\":\\\"2799\\\",\\\"townNo\\\":\\\"0\\\",\\\"detailAddress\\\":\\\"北京朝阳区三环以内未访问\\\",\\\"provinceNameGis\\\":\\\"北京\\\",\\\"provinceNoGis\\\":\\\"1\\\",\\\"cityNameGis\\\":\\\"朝阳区\\\",\\\"cityNoGis\\\":\\\"72\\\",\\\"countyNameGis\\\":\\\"三环以内\\\",\\\"countyNoGis\\\":\\\"2799\\\"},\\\"name\\\":\\\"AutoTest\\\",\\\"phone\\\":\\\"01088888888\\\",\\\"mobile\\\":\\\"13512345678\\\",\\\"email\\\":\\\"<EMAIL>\\\"},\\\"shipment\\\":{\\\"shipperId\\\":1,\\\"shipperNo\\\":\\\"CYS0000010\\\",\\\"shipperType\\\":1,\\\"shipperName\\\":\\\"京东配送\\\",\\\"expectDeliveryStartTime\\\":1684425600000,\\\"expectDeliveryEndTime\\\":1684425600000,\\\"returnType\\\":1,\\\"bdOwnerNo\\\":\\\"27K21560\\\",\\\"wayBill\\\":\\\"JDVC00050732680\\\",\\\"endStationNo\\\":\\\"\\\",\\\"endStationName\\\":\\\"\\\",\\\"ignorePromise\\\":true,\\\"contactlessReceiveType\\\":0,\\\"halfReceive\\\":1,\\\"deliveryType\\\":1,\\\"billingType\\\":1,\\\"presortInfo\\\":{\\\"siteName\\\":\\\"*石景山站\\\",\\\"siteNo\\\":\\\"39\\\",\\\"roadArea\\\":\\\"2\\\",\\\"siteType\\\":4,\\\"aoiCode\\\":\\\"wangyingying93测试\\\"},\\\"transportationType\\\":1,\\\"logisticsOriginalPackage\\\":1,\\\"deductibleCompensation\\\":\\\"3,4\\\",\\\"customPresortList\\\":[],\\\"cityDeliveryType\\\":false,\\\"expectCityDeliveryType\\\":false,\\\"tmsDeliveryType\\\":\\\"2\\\",\\\"checkGoodsTypeEnum\\\":\\\"0\\\",\\\"needExternalWaybillOnly\\\":false,\\\"firstWaybillIssue\\\":false,\\\"printExtendInfo\\\":{},\\\"contactPhone\\\":\\\"13520231004\\\",\\\"contactAddress\\\":\\\"test1121213213121wewtestupdate1\\\",\\\"heavyGoodsUpstairs\\\":false,\\\"hidePrivacyType\\\":false,\\\"signBillTypeEnum\\\":\\\"0\\\",\\\"CNTemplate\\\":false,\\\"express\\\":\\\"0\\\",\\\"goodsIsolateType\\\":\\\"0\\\"},\\\"warehouse\\\":{\\\"warehouseNo\\\":\\\"110008916\\\",\\\"warehouseId\\\":8916,\\\"warehouseName\\\":\\\"55测试仓\\\",\\\"warehouseType\\\":1,\\\"stockControl\\\":false,\\\"originalWarehouseNo\\\":\\\"110008916\\\"},\\\"finance\\\":{\\\"paymentType\\\":0,\\\"settlementType\\\":1,\\\"orderAmount\\\":{\\\"amount\\\":69.00,\\\"currencyCode\\\":\\\"CNY\\\"},\\\"freight\\\":0.00,\\\"discountAmount\\\":20.00,\\\"jdlMark\\\":\\\"1\\\"},\\\"refOrderInfo\\\":{\\\"waybillNo\\\":[null]},\\\"channel\\\":{\\\"pin\\\":\\\"sop_order\\\",\\\"channelSource\\\":\\\"POP\\\",\\\"channelShopNo\\\":\\\"58726\\\",\\\"channelOrderCreateTime\\\":1612256794000,\\\"channelOrderNo\\\":\\\"***********\\\",\\\"channelNo\\\":\\\"1\\\",\\\"channelName\\\":\\\"京东商城\\\",\\\"shopNo\\\":\\\"ESP0000000004185\\\",\\\"shopName\\\":\\\"促销在用，请勿修改店铺，谢谢\\\",\\\"customerOrderNo\\\":\\\"***********\\\",\\\"bdSellerNo\\\":\\\"0010001\\\",\\\"systemCaller\\\":\\\"POP\\\"},\\\"products\\\":{\\\"codeToProductMap\\\":{\\\"ed-m-0002\\\":{\\\"no\\\":\\\"ed-m-0002\\\",\\\"name\\\":\\\"特快送\\\",\\\"type\\\":1,\\\"businessLine\\\":2,\\\"attributes\\\":{}},\\\"ed-a-0032\\\":{\\\"no\\\":\\\"ed-a-0032\\\",\\\"name\\\":\\\"微笑面单\\\",\\\"type\\\":2,\\\"businessLine\\\":2,\\\"attributes\\\":{\\\"hiddenContent\\\":\\\"[\\\\\\\"receiverMobile\\\\\\\",\\\\\\\"receiverName\\\\\\\"]\\\"}}}},\\\"nonstandardProducts\\\":{\\\"enrolledServices\\\":{\\\"flashSaleGoodsType\\\":\\\"0\\\",\\\"selfPickupType\\\":\\\"0\\\",\\\"nextDayAgingPromotion\\\":\\\"0\\\",\\\"expectOutboundTime\\\":\\\"2023-05-18 15:06:55\\\",\\\"orderTemporaryStorage\\\":\\\"1\\\",\\\"selfSign\\\":\\\"1\\\",\\\"shopping_Template\\\":\\\"000\\\",\\\"printOutboundDeliveryOrder\\\":\\\"1\\\",\\\"conre\\\":\\\"1\\\",\\\"specialSafeguard\\\":\\\"1\\\",\\\"package\\\":\\\"1\\\",\\\"promiseTime211\\\":\\\"000\\\"}},\\\"solution\\\":{\\\"no\\\":\\\"sc-s-0005\\\",\\\"name\\\":\\\"供应链测试2\\\",\\\"attributes\\\":{\\\"guaranteeMoney\\\":\\\"59.700\\\",\\\"insured\\\":\\\"true\\\",\\\"fitDeliveryProducts\\\":\\\"8.88\\\",\\\"shouldPayMoney\\\":\\\"0.00\\\",\\\"vulnerableType\\\":\\\"1\\\"}},\\\"orderMark\\\":\\\"00002100000011000000110000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000\\\",\\\"createTime\\\":1684387615000,\\\"fulfillment\\\":{\\\"occupyResult\\\":true,\\\"packDetailsCollect\\\":2,\\\"productionByPack\\\":true,\\\"partialFulfillment\\\":false,\\\"partialPayment\\\":false,\\\"cargoFulfillmentWay\\\":false,\\\"skipMobileDecrypt\\\":false,\\\"orderUrgencyType\\\":\\\"0\\\",\\\"trustJDMetrics\\\":false,\\\"tmDeliveryByJgFlag\\\":false,\\\"clpsCainiaoElecFlag\\\":false,\\\"tmOrderFlag\\\":\\\"0\\\",\\\"packageSignFlag\\\":false,\\\"invokedCainiaoPlatform\\\":false,\\\"collectUniqueCodeFlag\\\":false,\\\"quarantineCert\\\":false},\\\"yn\\\":0}\",\"audit\":\"{\\\"operateType\\\":1,\\\"operator\\\":\\\"sop_order\\\",\\\"operateTime\\\":1684387616000,\\\"retries\\\":0}\",\"status\":\"200001\"}";
        return str;
    }
}
