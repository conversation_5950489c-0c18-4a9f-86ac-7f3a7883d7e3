<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jdl.sc</groupId>
    <artifactId>sc-ofw-outbound</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1-SNAPSHOT</version>
    <modules>
        <module>core/sc-ofw-outbound-domain</module>
        <module>core/sc-ofw-outbound-extension</module>
        <module>core/sc-ofw-outbound-service</module>
        <module>core/sc-ofw-outbound-spec</module>
        <module>core/sc-ofw-outbound-extension-api</module>
        <module>sc-ofw-outbound-common</module>
        <module>horizontal/sc-ofw-outbound-horiz-extension</module>
        <module>horizontal/sc-ofw-outbound-horiz-infrastructure</module>
        <module>sc-ofw-outbound-api</module>
        <module>sc-ofw-outbound-application</module>
        <module>sc-ofw-outbound-main</module>
        <module>repository/sc-ofw-outbound-repository</module>
        <module>repository/sc-ofw-outbound-repository-support</module>
        <module>jacoco-report</module>
        <module>sc-ofw-deprecated</module>
        <module>vertical/b2b/sc-ofw-outbound-vertical-b2b-extension</module>
        <module>vertical/b2b/sc-ofw-outbound-vertical-b2b-infrastructure</module>
        <module>vertical/coldchain/sc-ofw-outbound-vertical-coldchain-extension</module>
        <module>vertical/coldchain/sc-ofw-outbound-vertical-coldchain-infrastructure</module>
        <module>vertical/i18n/sc-ofw-outbound-vertical-i18n-extension</module>
        <module>vertical/i18n/sc-ofw-outbound-vertical-i18n-infrastructure</module>
        <module>vertical/ka/sc-ofw-outbound-vertical-ka-extension</module>
        <module>vertical/ka/sc-ofw-outbound-vertical-ka-infrastructure</module>
        <module>vertical/las/sc-ofw-outbound-vertical-las-extension</module>
        <module>vertical/las/sc-ofw-outbound-vertical-las-infrastructure</module>
        <module>vertical/medium/sc-ofw-outbound-vertical-medium-extension</module>
        <module>vertical/coldchainka/sc-ofw-outbound-vertical-coldchain-ka-extension</module>
        <module>vertical/coldchainka/sc-ofw-outbound-vertical-coldchain-ka-infrastructure</module>
        <module>vertical/instant/sc-ofw-outbound-vertical-instant-extension</module>
    </modules>

    <name>SC :: OFW :: Outbound</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <lombok.version>1.18.14</lombok.version>
        <slf4j.version>1.7.25</slf4j.version>
        <log4j2.version>2.18.0-jdsec.rc2</log4j2.version>
        <disruptor.version>3.4.4</disruptor.version>
        <junit.version>4.12</junit.version>
        <jacoco.version>0.8.5</jacoco.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <oms.spi.version>1.0.0-SNAPSHOT</oms.spi.version>

        <!--batrix.version-->
        <batrix.core.version>1.2.1-SNAPSHOT</batrix.core.version>
        <!--batrix.version-->
        <batrix.sdk.version>1.0.9-SNAPSHOT</batrix.sdk.version>

        <spring.boot.starter.version>2.0.5.RELEASE</spring.boot.starter.version>
        <mysql.connector.version>8.0.13</mysql.connector.version>
        <mybatis.version>3.5.6</mybatis.version>

        <!--jackson-->
        <jackson.version>2.12.2</jackson.version>
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>

        <hibernate.validator.version>6.0.12.Final</hibernate.validator.version>
        <javax.validation.version>2.0.1.Final</javax.validation.version>
        <jsf.version>1.7.7-HOTFIX-T2</jsf.version>
        <jmq.version>20221231</jmq.version>
        <handlebars.version>4.3.0</handlebars.version>
        <kafka.clients.version>2.1.1</kafka.clients.version>
        <sc.core.version>1.0.3-SNAPSHOT</sc.core.version>
        <pfinder.version>1.1.5-FINAL</pfinder.version>
        <ump.version>20220630.1</ump.version>
        <thirdtrack.version>0.1.12-SNAPSHOT</thirdtrack.version>
        <xmlsec.version>2.3.0</xmlsec.version>
        <gaofeng.version>2.4.1-SNAPSHOT</gaofeng.version>
        <ldop.alpha.lp.version>1.1.8-RELEASE</ldop.alpha.lp.version>
        <etms.blocker.client.version>2.2-SNAPSHOT</etms.blocker.client.version>
        <eclp.b2c.version>2.2-SNAPSHOT</eclp.b2c.version>
        <oms.relation.version>1.0.0-SNAPSHOT</oms.relation.version>
        <com.jd.pop.customs>1.0-SNAPSHOT</com.jd.pop.customs>
        <eclp.jimkv.version>1.0-SNAPSHOT</eclp.jimkv.version>
        <jetty.version>9.3.30.v20211001</jetty.version>
        <eca.api.version>2.0.0.0-SNAPSHOT</eca.api.version>
        <product.query.api.version>0.0.1-RELEASE</product.query.api.version>
        <etms.receive.api.version>1.3.67-SNAPSHOT</etms.receive.api.version>
        <powermock.version>2.0.9</powermock.version>
        <coldchain.cap.api.version>1.0.0.0-SNAPSHOT</coldchain.cap.api.version>
        <jimkv.cli.api.version>2.2.8-shade</jimkv.cli.api.version>
        <wbms.wcs.client.api>1.0.0-SNAPSHOT</wbms.wcs.client.api>
        <express.o2o.version>o2o-0.1.1-SNAPSHOT</express.o2o.version>
        <eclp.edi.provider.version>0.0.1-SNAPSHOT</eclp.edi.provider.version>
        <miniwms.api.version>2.0.8-mt-SNAPSHOT</miniwms.api.version>
        <ql.idservice.api>1.0-SNAPSHOT</ql.idservice.api>
    </properties>

    <!--按环境配置依赖-->
    <profiles>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <ser.sku.open.api>1.5.2-pre-SNAPSHOT</ser.sku.open.api>
                <open.isc.version>1.0.0-TEST-SNAPSHOT</open.isc.version>
                <etms.blocker.client.version>2.1-test-SNAPSHOT</etms.blocker.client.version>
                <warehouse.fulfillment.version>1.1-test-SNAPSHOT</warehouse.fulfillment.version>
                <eclp.master.api.version>0.0.1-TEST-SNAPSHOT</eclp.master.api.version>
                <pms.basic.version>1.0.2-TEST-SNAPSHOT</pms.basic.version>
                <com.jdwl.scm.tms.version>1.0-TEST-SNAPSHOT</com.jdwl.scm.tms.version>
                <com.eclp.wms.po.version>1.0.0-TEST-SNAPSHOT</com.eclp.wms.po.version>
                <sc.hold.api.version>1.1.0-SNAPSHOT</sc.hold.api.version>
                <eclp.exception.version>0.0.1-TEST-SNAPSHOT</eclp.exception.version>
                <ldop.alpha.waybill.version>1.7.9-TEST-SNAPSHOT</ldop.alpha.waybill.version>
                <csc.channel.intserv.version>1.0.0-GRAY-SNAPSHOT</csc.channel.intserv.version>
                <oms.search.version>2.0.2-SNAPSHOT</oms.search.version>
                <open.order.allocation.api.version>0.0.1-TEST-SNAPSHOT</open.order.allocation.api.version>
                <bms.account.rpc.outer.version>0.0.1-TEST-SNAPSHOT</bms.account.rpc.outer.version>
                <goods.center.domain.versoin>0.0.1-TEST-SNAPSHOT</goods.center.domain.versoin>
                <jdl.oms.addon.client.service>1.0.0-SNAPSHOT</jdl.oms.addon.client.service>
                <promise.ka.contract.version>2.4.0-outStore-SNAPSHOT</promise.ka.contract.version>
                <sc.oms.extension.version>1.0.3-SNAPSHOT</sc.oms.extension.version>
                <fop.service.api.version>1.2.0-yufacn-SNAPSHOT</fop.service.api.version>
                <ql.basic.version>2.0.15-TEST-SNAPSHOT</ql.basic.version>
                <las.servicecenter.api>2.0.1-TEST-SNAPSHOT</las.servicecenter.api>
                <external.front.store.client.version>1.0.0-SNAPSHOT</external.front.store.client.version>
            </properties>
        </profile>
        <profile>
            <id>regression</id>
            <properties>
                <ser.sku.open.api>1.5.2-pre-SNAPSHOT</ser.sku.open.api>
                <open.isc.version>1.0.0-TEST-SNAPSHOT</open.isc.version>
                <etms.blocker.client.version>2.1-test-SNAPSHOT</etms.blocker.client.version>
                <warehouse.fulfillment.version>1.1-test-SNAPSHOT</warehouse.fulfillment.version>
                <eclp.master.api.version>0.0.1-TEST-SNAPSHOT</eclp.master.api.version>
                <pms.basic.version>1.0.2-TEST-SNAPSHOT</pms.basic.version>
                <com.jdwl.scm.tms.version>1.0-TEST-SNAPSHOT</com.jdwl.scm.tms.version>
                <com.eclp.wms.po.version>1.0.0-TEST-SNAPSHOT</com.eclp.wms.po.version>
                <sc.hold.api.version>1.1.0-SNAPSHOT</sc.hold.api.version>
                <eclp.exception.version>0.0.1-TEST-SNAPSHOT</eclp.exception.version>
                <ldop.alpha.waybill.version>1.7.9-TEST-SNAPSHOT</ldop.alpha.waybill.version>
                <csc.channel.intserv.version>1.0.0-GRAY-SNAPSHOT</csc.channel.intserv.version>
                <oms.search.version>2.0.2-SNAPSHOT</oms.search.version>
                <open.order.allocation.api.version>0.0.1-TEST-SNAPSHOT</open.order.allocation.api.version>
                <bms.account.rpc.outer.version>0.0.1-TEST-SNAPSHOT</bms.account.rpc.outer.version>
                <goods.center.domain.versoin>0.0.1-TEST-SNAPSHOT</goods.center.domain.versoin>
                <jdl.oms.addon.client.service>1.0.0-SNAPSHOT</jdl.oms.addon.client.service>
                <promise.ka.contract.version>2.4.0-outStore-SNAPSHOT</promise.ka.contract.version>
                <sc.oms.extension.version>1.0.3-SNAPSHOT</sc.oms.extension.version>
                <fop.service.api.version>1.2.0-yufacn-SNAPSHOT</fop.service.api.version>
                <ql.basic.version>2.0.15-TEST-SNAPSHOT</ql.basic.version>
                <las.servicecenter.api>2.0.1-TEST-SNAPSHOT</las.servicecenter.api>
                <external.front.store.client.version>1.0.0-SNAPSHOT</external.front.store.client.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <ser.sku.open.api>1.5.2-pre-SNAPSHOT</ser.sku.open.api>
                <open.isc.version>1.0.0-TEST-SNAPSHOT</open.isc.version>
                <etms.blocker.client.version>2.1-test-SNAPSHOT</etms.blocker.client.version>
                <warehouse.fulfillment.version>1.1-test-SNAPSHOT</warehouse.fulfillment.version>
                <eclp.master.api.version>0.0.1-TEST-SNAPSHOT</eclp.master.api.version>
                <pms.basic.version>1.0.2-TEST-SNAPSHOT</pms.basic.version>
                <com.jdwl.scm.tms.version>1.0-TEST-SNAPSHOT</com.jdwl.scm.tms.version>
                <com.eclp.wms.po.version>1.0.0-TEST-SNAPSHOT</com.eclp.wms.po.version>
                <sc.hold.api.version>1.1.0-SNAPSHOT</sc.hold.api.version>
                <eclp.exception.version>0.0.1-TEST-SNAPSHOT</eclp.exception.version>
                <ldop.alpha.waybill.version>1.7.9-TEST-SNAPSHOT</ldop.alpha.waybill.version>
                <csc.channel.intserv.version>1.0.0-GRAY-SNAPSHOT</csc.channel.intserv.version>
                <oms.search.version>2.0.2-SNAPSHOT</oms.search.version>
                <open.order.allocation.api.version>0.0.1-TEST-SNAPSHOT</open.order.allocation.api.version>
                <bms.account.rpc.outer.version>0.0.1-TEST-SNAPSHOT</bms.account.rpc.outer.version>
                <goods.center.domain.versoin>0.0.1-TEST-SNAPSHOT</goods.center.domain.versoin>
                <jdl.oms.addon.client.service>1.0.0-SNAPSHOT</jdl.oms.addon.client.service>
                <promise.ka.contract.version>2.4.0-outStore-SNAPSHOT</promise.ka.contract.version>
                <sc.oms.extension.version>1.0.3-SNAPSHOT</sc.oms.extension.version>
                <fop.service.api.version>1.2.0-yufacn-SNAPSHOT</fop.service.api.version>
                <ql.basic.version>2.0.15-TEST-SNAPSHOT</ql.basic.version>
                <las.servicecenter.api>2.0.1-TEST-SNAPSHOT</las.servicecenter.api>
                <external.front.store.client.version>1.0.0-SNAPSHOT</external.front.store.client.version>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <ser.sku.open.api>1.5.2-pre-SNAPSHOT</ser.sku.open.api>
                <open.isc.version>1.0.0-UAT-SNAPSHOT</open.isc.version>
                <etms.blocker.client.version>2.2.5-SNAPSHOT</etms.blocker.client.version>
                <warehouse.fulfillment.version>1.1-uat-SNAPSHOT</warehouse.fulfillment.version>
                <eclp.master.api.version>0.0.3-GRAY-SNAPSHOT</eclp.master.api.version>
                <pms.basic.version>1.0.2-TEST-SNAPSHOT</pms.basic.version>
                <com.jdwl.scm.tms.version>1.0.0-SNAPSHOT</com.jdwl.scm.tms.version>
                <com.eclp.wms.po.version>1.0.0-SNAPSHOT</com.eclp.wms.po.version>
                <sc.hold.api.version>1.1.0-SNAPSHOT</sc.hold.api.version>
                <eclp.exception.version>0.0.1-GRAY-SNAPSHOT</eclp.exception.version>
                <ldop.alpha.waybill.version>1.6.5-UAT-SNAPSHOT</ldop.alpha.waybill.version>
                <csc.channel.intserv.version>1.0.0-GRAY-SNAPSHOT</csc.channel.intserv.version>
                <oms.search.version>2.0.4-RELEASE</oms.search.version>
                <open.order.allocation.api.version>0.0.1-GRAY-SNAPSHOT</open.order.allocation.api.version>
                <bms.account.rpc.outer.version>0.0.1-UAT-SNAPSHOT</bms.account.rpc.outer.version>
                <goods.center.domain.versoin>0.0.2-GRAY-SNAPSHOT</goods.center.domain.versoin>
                <jdl.oms.addon.client.service>1.0.0-SNAPSHOT</jdl.oms.addon.client.service>
                <promise.ka.contract.version>2.4.0-outStore-SNAPSHOT</promise.ka.contract.version>
                <sc.oms.extension.version>1.0.3-SNAPSHOT</sc.oms.extension.version>
                <fop.service.api.version>1.2.0-yufacn-SNAPSHOT</fop.service.api.version>
                <ql.basic.version>2.0.15-uat-SNAPSHOT</ql.basic.version>
                <las.servicecenter.api>2.0.0-uat-SNAPSHOT</las.servicecenter.api>
                <external.front.store.client.version>1.0.0-SNAPSHOT</external.front.store.client.version>
            </properties>
        </profile>
        <profile>
            <id>ab</id>
            <properties>
                <ser.sku.open.api>1.5.2-pre-SNAPSHOT</ser.sku.open.api>
                <open.isc.version>1.0.0-UAT-SNAPSHOT</open.isc.version>
                <etms.blocker.client.version>2.2.5-SNAPSHOT</etms.blocker.client.version>
                <warehouse.fulfillment.version>1.1-test-SNAPSHOT</warehouse.fulfillment.version>
                <eclp.master.api.version>0.0.3-GRAY-SNAPSHOT</eclp.master.api.version>
                <pms.basic.version>1.0.2-TEST-SNAPSHOT</pms.basic.version>
                <com.jdwl.scm.tms.version>1.0.0-SNAPSHOT</com.jdwl.scm.tms.version>
                <com.eclp.wms.po.version>1.0.0-SNAPSHOT</com.eclp.wms.po.version>
                <sc.hold.api.version>1.1.0-SNAPSHOT</sc.hold.api.version>
                <eclp.exception.version>0.0.1-SNAPSHOT</eclp.exception.version>
                <ldop.alpha.waybill.version>1.7.9-TEST-SNAPSHOT</ldop.alpha.waybill.version>
                <csc.channel.intserv.version>1.0.0-GRAY-SNAPSHOT</csc.channel.intserv.version>
                <oms.search.version>2.0.4-RELEASE</oms.search.version>
                <open.order.allocation.api.version>0.0.1-GRAY-SNAPSHOT</open.order.allocation.api.version>
                <bms.account.rpc.outer.version>0.0.1-TEST-SNAPSHOT</bms.account.rpc.outer.version>
                <goods.center.domain.versoin>0.0.1-TEST-SNAPSHOT</goods.center.domain.versoin>
                <jdl.oms.addon.client.service>1.0.0-SNAPSHOT</jdl.oms.addon.client.service>
                <promise.ka.contract.version>2.2.0-outOrder-product-********-SNAPSHOT</promise.ka.contract.version>
                <sc.oms.extension.version>1.0.3-SNAPSHOT</sc.oms.extension.version>
                <promise.ka.contract.version>2.4.0-outStore-SNAPSHOT</promise.ka.contract.version>
                <fop.service.api.version>1.2.0-yufacn-SNAPSHOT</fop.service.api.version>
                <ql.basic.version>2.0.15-uat-SNAPSHOT</ql.basic.version>
                <las.servicecenter.api>2.0.1-TEST-SNAPSHOT</las.servicecenter.api>
                <external.front.store.client.version>1.0.0-SNAPSHOT</external.front.store.client.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <ser.sku.open.api>1.5.3-SNAPSHOT</ser.sku.open.api>
                <open.isc.version>1.0.0-SNAPSHOT</open.isc.version>
                <etms.blocker.client.version>2.2.5-SNAPSHOT</etms.blocker.client.version>
                <warehouse.fulfillment.version>1.1-SNAPSHOT</warehouse.fulfillment.version>
                <eclp.master.api.version>0.0.3-SNAPSHOT</eclp.master.api.version>
                <pms.basic.version>0.0.26-RELEASE</pms.basic.version>
                <com.jdwl.scm.tms.version>1.0.0-SNAPSHOT</com.jdwl.scm.tms.version>
                <com.eclp.wms.po.version>1.0.0-SNAPSHOT</com.eclp.wms.po.version>
                <sc.hold.api.version>1.1.0</sc.hold.api.version>
                <eclp.exception.version>0.0.2-SNAPSHOT</eclp.exception.version>
                <ldop.alpha.waybill.version>1.7.6-RELEASE</ldop.alpha.waybill.version>
                <csc.channel.intserv.version>1.0.0-SNAPSHOT</csc.channel.intserv.version>
                <oms.search.version>2.0.4-RELEASE</oms.search.version>
                <open.order.allocation.api.version>0.0.10</open.order.allocation.api.version>
                <bms.account.rpc.outer.version>0.0.1-SNAPSHOT</bms.account.rpc.outer.version>
                <goods.center.domain.versoin>0.0.3-SNAPSHOT</goods.center.domain.versoin>
                <jdl.oms.addon.client.service>1.0.0-RELEASE</jdl.oms.addon.client.service>
                <promise.ka.contract.version>2.4.0</promise.ka.contract.version>
                <sc.oms.extension.version>1.0.3-SNAPSHOT</sc.oms.extension.version>
                <fop.service.api.version>1.2.0-MCA-SNAPSHOT</fop.service.api.version>
                <ql.basic.version>2.0.15-online-SNAPSHOT</ql.basic.version>
                <las.servicecenter.api>2.0.0-SNAPSHOT</las.servicecenter.api>
                <external.front.store.client.version>1.0.0</external.front.store.client.version>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>ser-sku-open-api</artifactId>
                <version>${ser.sku.open.api}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.csc</groupId>
                <artifactId>csc-channel-intserv</artifactId>
                <version>${csc.channel.intserv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.warehouse.fulfillment</groupId>
                <artifactId>warehouse-fulfillment-query-api</artifactId>
                <version>${warehouse.fulfillment.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.etms.receive</groupId>
                <artifactId>etms-receive-api</artifactId>
                <version>${etms.receive.api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>itext-asian</artifactId>
                        <groupId>com.itextpdf</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>itext-xtra</artifactId>
                        <groupId>com.itextpdf</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>itextpdf</artifactId>
                        <groupId>com.itextpdf</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.etms.blocker</groupId>
                <artifactId>etms-blocker-client</artifactId>
                <version>${etms.blocker.client.version}</version>
            </dependency>
            <!-- templates -->
            <dependency>
                <groupId>com.github.jknack</groupId>
                <artifactId>handlebars</artifactId>
                <version>${handlebars.version}</version>
            </dependency>
            <!-- templates end -->

            <!-- JD -->
<!--            <dependency>-->
<!--                <groupId>com.wangyin.schedule</groupId>-->
<!--                <artifactId>schedule-client-starter</artifactId>-->
<!--                <version>${schedule.wangyin.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${ump.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.pfinder</groupId>
                <artifactId>pfinder-profiler-sdk</artifactId>
                <version>${pfinder.version}</version>
            </dependency>
            <!-- sc-core start -->
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-json</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-ump</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-utils</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-preheat</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-jsf</artifactId>
                <version>${sc.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-cache</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-sync</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-log</artifactId>
                <version>${sc.core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>transmittable-thread-local</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-javassist-plus</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-oss</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <!-- sc-core end -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.clients.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.0.1-jre</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>${jmq.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>profiler</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- project models-->
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-spec</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-extension-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-repository</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-repository-support</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jdl.ofc</groupId>
                <artifactId>ofc-supplychain-oms-client</artifactId>
                <version>${oms.spi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-horiz-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-horiz-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-b2b-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-b2b-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-coldchain-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-coldchain-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-i18n-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-i18n-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-ka-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-ka-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-las-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-las-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-coldchain-ka-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-coldchain-ka-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-outbound-vertical-instant-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-ofw-deprecated</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- batrix -->
            <dependency>
                <groupId>cn.jdl.batrix</groupId>
                <artifactId>jdl-batrix-core</artifactId>
                <version>${batrix.core.version}</version>
            </dependency>
            <!--batrix sdk 轻量级结构引用   BusinessIdentity 类所在包-->
            <dependency>
                <groupId>cn.jdl.batrix</groupId>
                <artifactId>jdl-batrix-sdk</artifactId>
                <version>${batrix.sdk.version}</version>
            </dependency>

            <!-- jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- spring  -->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.starter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- third party sdk -->
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jcl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <!--引入validation的场景启动器-->
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.0.12.Final</version>
                <exclusions>
                    <exclusion>
                        <artifactId>validation-api</artifactId>
                        <groupId>javax.validation</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--校验-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax.validation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.etms.third</groupId>
                <artifactId>ql-thirdtrack-interface</artifactId>
                <version>${thirdtrack.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.santuario</groupId>
                <artifactId>xmlsec</artifactId>
                <version>${xmlsec.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.lbs.geofencing</groupId>
                <artifactId>geofencing-api</artifactId>
                <version>${gaofeng.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-oms-extension-api</artifactId>
                <version>${sc.oms.extension.version}</version>
            </dependency>
            <!--alpha-->
            <dependency>
                <groupId>com.jd.ldop.alpha</groupId>
                <artifactId>ldop-alpha-waybill-api</artifactId>
                <version>${ldop.alpha.waybill.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.ldop.alpha</groupId>
                <artifactId>ldop-alpha-lp-api</artifactId>
                <version>${ldop.alpha.lp.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.el</groupId>
                        <artifactId>javax.el-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--gis-->
            <dependency>
                <groupId>com.jd.addresstranslation</groupId>
                <artifactId>addressTranslation-api</artifactId>
                <version>1.7.10-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.etms.preseparate</groupId>
                <artifactId>preseparate-saf-client</artifactId>
                <version>3.6.3-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>   <!-- 青龙预分拣接口升级 end -->
            </dependency>
            <dependency>
                <groupId>com.jd.promise</groupId>
                <artifactId>promise-dos-contract</artifactId>
                <version>0.1.9-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-order-hold-api</artifactId>
                <version>${sc.hold.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-order-collect-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.etms.receive</groupId>
                <artifactId>eclp-b2c-api</artifactId>
                <version>${eclp.b2c.version}</version>
            </dependency>
            <!-- 订单关系查询 -->
            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-relation-client-service</artifactId>
                <version>${oms.relation.version}</version>
            </dependency>
            <!-- 根据三方单号查询订单详情 -->
            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-search-client</artifactId>
                <version>${oms.search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.eclp</groupId>
                <artifactId>eclp-jimkv-api</artifactId>
                <version>${eclp.jimkv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.open.sp</groupId>
                <artifactId>open-isc-api</artifactId>
                <version>${open.isc.version}</version>
            </dependency>
            <!-- 产品中心 -->
            <dependency>
                <groupId>cn.jdl.pms</groupId>
                <artifactId>jdl-pms-basic-api</artifactId>
                <version>${pms.basic.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jdl.express</groupId>
                <artifactId>jdl-eca-api</artifactId>
                <version>${eca.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.product</groupId>
                <artifactId>jdl-product-query-api</artifactId>
                <version>${product.query.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.cp.wbms.wcs</groupId>
                <artifactId>wbms-wcs-client</artifactId>
                <version>${wbms.wcs.client.api}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-server</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-servlet</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-security</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-io</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-jmx</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-webapp</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util-ajax</artifactId>
                <version>${jetty.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>3.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-weblab-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jfs</groupId>
                <artifactId>jfs-java-sdk</artifactId>
                <version>1.3.2-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>junit</artifactId>
                        <groupId>junit</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.12.0</version>
            </dependency>
            <!-- 事业部编码校验 -->
            <dependency>
                <groupId>com.jd.eclp</groupId>
                <artifactId>eclp-master-api</artifactId>
                <version>${eclp.master.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.dos</groupId>
                        <artifactId>dos-area-contract</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- B2B合同物流 -->
            <dependency>
                <groupId>com.jdwl.scm.tms</groupId>
                <artifactId>scm-basic-contractLogistics-api</artifactId>
                <version>${com.jdwl.scm.tms.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.eclp2.wms</groupId>
                <artifactId>eclp2-wms-po-api</artifactId>
                <version>${com.eclp.wms.po.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--冷链纯配接入层-->
            <dependency>
                <groupId>cn.jdl.coldchain</groupId>
                <artifactId>cap-api</artifactId>
                <version>${coldchain.cap.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--jimkv-->
            <dependency>
                <groupId>com.jd.jimkv.cli</groupId>
                <artifactId>jimkv-cli-api</artifactId>
                <version>${jimkv.cli.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.open.order.allocation</groupId>
                <artifactId>open-order-allocation-api</artifactId>
                <version>${open.order.allocation.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.bms</groupId>
                <artifactId>bms-account-rpc-outer-api</artifactId>
                <version>${bms.account.rpc.outer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>sc-lfc-trans-pipe-spec</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.eclp.exception</groupId>
                <artifactId>eclp-exception-api</artifactId>
                <version>${eclp.exception.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.tc.seller.wb</groupId>
                <artifactId>workbench-api</artifactId>
                <version>1.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.eclp</groupId>
                <artifactId>goods-center-domain</artifactId>
                <version>${goods.center.domain.versoin}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>omc-jsf-api</artifactId>
                <version>2.3.8-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-addon-client-service</artifactId>
                <version>${jdl.oms.addon.client.service}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.promise</groupId>
                <artifactId>promise-ka-contract</artifactId>
                <version>${promise.ka.contract.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.fop</groupId>
                <artifactId>fop-service-api</artifactId>
                <version>${fop.service.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.express</groupId>
                <artifactId>express-o2o-api</artifactId>
                <version>${express.o2o.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--jimdb相关-->
            <dependency>
                <groupId>com.jd.jmiss</groupId>
                <artifactId>jim-cli-api</artifactId>
                <version>3.1.2-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.jmiss</groupId>
                <artifactId>jim-cli-config</artifactId>
                <version>3.1.2-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.jmiss</groupId>
                <artifactId>jim-cli-driver</artifactId>
                <version>3.1.2-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.jmiss</groupId>
                <artifactId>jim-cli-jedis</artifactId>
                <version>3.1.2-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.eclp.edi</groupId>
                <artifactId>eclp-edi-provider</artifactId>
                <version>${eclp.edi.provider.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.miniwms</groupId>
                <artifactId>miniwms-master-api</artifactId>
                <version>${miniwms.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.ql.basic</groupId>
                <artifactId>ql-basic-facade</artifactId>
                <version>${ql.basic.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.las</groupId>
                <artifactId>las-servicecenter-service-api</artifactId>
                <version>${las.servicecenter.api}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.ql.idservice</groupId>
                <artifactId>idservice-agent</artifactId>
                <version>${ql.idservice.api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jdl.promise</groupId>
                <artifactId>external-front-store-client</artifactId>
                <version>${external.front.store.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring.boot.starter.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <defaultGoal>package</defaultGoal>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>

            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>
</project>
